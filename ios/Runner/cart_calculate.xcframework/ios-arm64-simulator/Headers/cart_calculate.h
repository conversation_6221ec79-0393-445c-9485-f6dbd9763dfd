#include <stdarg.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

#define TOP_ORDER_ITEM_PARENT_ID 0

#define MEASURED 1

#define WEIGHED 2

#define COMBO 3

#define TOPPING 4

#define CONTAINER 5

#define TABLE_EAT 1

#define TAKE_OUT 2

#define DELIVERY 3

#define TAKE_AWAY 4

#define USE_PACKAGE_ITEMS 1

#define CHARGE_WHOLE_PACKAGE 2

#define ZERO 0

#define ONE 1

#define TWO 2

#define EXCLUSIVE 1

#define INCLUSIVE 2

#define ITEM 1

#define SERVICE 2

#define PRACTICE 3

#define PERCENTAGE 1

#define FIXED 2

/**
 *
 *Java/Flutter通用库
 *
 */
void add_cart(const char *add_dish_str, const char *orders_dto_str);

char *cart_list_summary(const char *input_str);

void free_string(char *ptr);

int32_t confirm_order(int32_t a, int32_t b);

void init_rust_logger(int32_t level, int32_t platform);
