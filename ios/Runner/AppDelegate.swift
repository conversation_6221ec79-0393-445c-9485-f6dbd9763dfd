import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
  //先调用一次为了加载静态库
    RustBridge.getCartSummary(from: "")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}


class RustBridge {
    static func getCartSummary(from json: String) -> String {
        guard let cString = json.cString(using: .utf8) else {
            return "Invalid input"
        }

        let resultPointer = cart_list_summary(cString)
        if let result = resultPointer {
            return String(cString: result)
        } else {
            return "null result"
        }
    }
}
