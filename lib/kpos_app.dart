import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/pull_refresh/kp_refresh_footer.dart';
import 'package:kpos/common/components/pull_refresh/kp_refresh_header.dart';
import 'package:kpos/common/constant/app_constants.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/language_settings_service/language_settings_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_global_sub_service.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:kpos/theme/app_theme.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:user_interaction_detector/user_interaction_detector.dart';

import 'common/services/web_socket/stomp/stomp_service.dart';

final GlobalKey<NavigatorState> globalNavigatorKey = GlobalKey<NavigatorState>();

class KPosApp extends ConsumerStatefulWidget {
  const KPosApp({super.key});

  @override
  ConsumerState createState() => _KPosAppState();
}

class _KPosAppState extends ConsumerState<KPosApp> {

  @override
  void initState() {
    super.initState();

    EasyRefresh.defaultHeaderBuilder = () => KPRefreshHeader();
    EasyRefresh.defaultFooterBuilder = () => KPRefreshFooter();

    final stomp = ref.read(stompServiceProvider);
    ///本地
    // stomp.connect(
    //     url:
    //         'ws://***********:18087/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');
    ///dev
    // stomp.connect(url: 'ws://*************:8080/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');
    ///uat
    // stomp.connect(url: 'wss://pos-cloud-operation.uat.kpay-group.com/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');
    ///test
    stomp.connect(url: 'ws://pos-cloud-operation.kpay-test.com/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');
    ///test-ip
    // stomp.connect(url: 'ws://***********:8080/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');

    ///全局订阅服务
    ref.read(stompGlobalSubServiceProvider);
  }

  @override
  Widget build(BuildContext context) {
    TDTheme.needMultiTheme(true);
    final goRouter = ref.watch(goRouterProvider);
    Widget app = OKToast(
      child: MaterialApp.router(
        theme: ThemeData(
          extensions: [
            TDThemeData.fromJson('default', AppTheme.defaultThemeConfig)!
          ],
          checkboxTheme: CheckboxThemeData(
            fillColor: WidgetStateProperty.resolveWith((states) {
              if (states.contains(WidgetState.selected)) {
                return const Color(0xFFFF8500);
              }
              return Colors.white;
            }),
            checkColor: WidgetStateProperty.all(Colors.white), // 勾为白色
            side: WidgetStateBorderSide.resolveWith((states) {
              if (!states.contains(WidgetState.selected)) {
                return const BorderSide(
                  color: Color(0xFFC4C6CD),
                );
              }
              return null;
            }),
          ),
          unselectedWidgetColor: KPColors.iconGrayTertiary,
        ),
        debugShowCheckedModeBanner: false,
        routerConfig: goRouter,
        localizationsDelegates: KPLocale.localizationsDelegates,
        supportedLocales: KPLocale.supportedLocales,
        locale: ref.watch(currentLocaleProvider),
        builder: (contextB, child) => SessionInactivity(
        // 监听用户操作
        onSessionTimeout: () {
          print("超时了");
          goRouter.push(FullLockScreenRoute().location);
        },
        duration: const Duration(seconds: AppConstants.userInactivityTimeoutSeconds), // 超时30秒
        child: child!,
        ),
      ),
    );
    return app;
  }
}