{"@@locale": "en", "login": "Log in", "languageZhSG": "简体中文", "languageZhHK": "繁体中文", "languageEn": "English", "shortcutLanguage": "Switch language", "userAgreement": "User Agreement", "userAgreementHighlight": " User Agreement", "privacyPolicy": "Privacy policy", "privacyPolicyHighlight": "Privacy policy", "loginAgreement": "I have read and agree to the {userAgreement} & {privacyPolicy}", "loginAgreementAgreeTarget": "I have read and agree", "home": "home", "table": "Table", "action": "Action", "available": "Available", "pendingOrder": "Pending Order", "unpaid": "Unpaid", "needsCleanup": "Needs Cleanup", "reserved": "Reserved", "all": "All", "numberOfCovers": "Number of covers", "mine": "mine", "confirm": "confirm", "confirmCaps": "Confirm", "cancel": "cancel", "cancelCaps": "Cancel", "tip": "tip", "agreementConfirmDialogTips": "Have you read and agreed to {userAgreement} and {privacyPolicy}", "accountInvalidate": "Please enter a valid email address", "passwordInvalidate": "Please enter a valid password", "account": "account", "refreshNoMoreData": "no more data", "email": "Email", "password": "Password", "forgetPassword": "Forget Password?", "passwordError": "Password Error", "helpCenter": "Help center", "loginOut": "Log Out", "tryAgain": "Try again", "changeUser": "Change user", "unbindStore": "UnbindStore", "welcome": "Welcome", "associateStoreTip": "Select the store to be associated", "notBound": "Not bound", "continueBind": "Continue", "search": "Search", "searchEmployeeName": "Search employee name", "unbind": "Unbind", "pleaseMakeSure": "Please make sure:", "unbindMainPosTip1": "Unbinding will clear the cashier POS equipment. You need to rebind the store before the cashier POS equipment can operate normally.", "unbindMainPosTip2": "Please ensure that the original cash register device data has been uploaded to the cloud, otherwise, data loss may occur.", "unbindPosTitle": "Enter account and password to unbind store", "unbindPosSuccess": "After unbinding, this device needs to reconnect to the main POS to resume use. Unbinding a single device does not affect the normal operation of other secondary POS devices.", "startConnecting": "Start connecting", "chooseSubDeviceTitle": "Sub-device", "chooseSubDeviceDesc": "Bind an existing main device and use it as a Sub-device", "chooseSubDeviceTip": "The current main POS is offline. The new device cannot log in as a secondary POS.", "chooseMainDeviceTitle": "Main-device", "chooseMainDeviceDesc": "Remove the original main device and use it as the new main device", "chooseMainDeviceTip": "The main POS already exists. Do you want to continue?", "chooseBindDeviceTitle": "Choose how to bind this device", "existingMainDevice": "Existing Main device:", "online": "Online", "connectAsMainDevice": "Connect as a main device", "connectAsSubDevice": "Connect as a sub device", "connectAsMainDeviceTip1": "After the original main device is removed, the current device will take over as the main devic. other sub-device needs to reconnect to the main device.", "connectAsMainDeviceTip2": "Please make sure the original main device is online.", "connectAsMainDeviceTip3": "Please confirm that the main device register data has been uploaded to the backend, otherwise data may be lost.", "connectAsSubDeviceTip1": "Main cashier remains online.", "connectAsSubDeviceTip2": "Need to be connected to the same network segment as the main cashier.", "connectingDevice": "Connecting device...", "connectingCloudDesc": "Please keep the network connected and do not disconnect. This process will take some time.", "connectingCloudTip": "Connecting to the cloud...", "connectingMainDesc": "Please make sure the main device is online,It is going to take only few seconds.", "connectingMainTip": "Retrieving main device IP...", "manuallyEnterIPAddress": "Manually enter IP address", "connectionFailed": "Connection failed", "connectionFailedDesc": "Please check the network and try again", "connectingSuccessful": "Connecting successful", "connectingMainPosSuccessful": "Connect to main POS successfully", "boundMainDeviceTip": "The main device: {deviceName} has been bound.", "enterIPAddress": "Enter IP address", "ipAddress": "IP Address", "mainPos": "Main Pos", "subPos": "Sub Pos", "getIPAddressTip": "How to find: Main POS → Settings → IP address", "inputPinLogin": "Enter the PIN code to log in", "fetchingStoreInfo": "Fetching store information", "fetchingStoreData": "Fetching store menu, orders, and related data", "storeDataFetched": "Store data fetch completed", "more": "More", "tableSetting": "Table Setting", "cashManagement": "Cash Management", "noTableTip": "There are no tables in the current table status.Please change the table status to be filtered.", "seats": "seats", "enterNumOfPeople": "Please enter the number of people", "sharingTable": "Sharing Table", "groupTables": "Group Tables", "tableMerging": "Table merging", "transferTable": "Transfer Table", "clearingTable": "Clearing Table", "tableCleanConfirm": "Are you sure table has been cleaned?", "tableCleanConfirmTip": "Please Check for forgotten items before clearing.", "tableCleanSuccessfully": "Table cleared successfully", "tableClearingTip": "Select the \"{title}\" table you want to clear. It will become available.", "datePickerOK": "OK", "datePickerCancel": "CANCEL", "payInOut": "Pay in/out", "payIn": "Pay in", "payOut": "Pay out", "tableClearingCompleted": "Table Clearing completed", "reminder": "Reminder", "withdrawalConfirmTip": "Are you sure you want to withdrawal table {tableTitle}?", "tableSettings": "Table settings", "tableLayout": "Table Layout", "floorPlansView": "Floor plans view", "gridView": "Grid view", "tableTimeDisplay": "Table time display", "usageTimePeriod": "Usage time period", "usageTime": "Usage time", "tableCleaning": "Table cleaning", "autoCleanTableTip": "Automatically clear table after checkout", "dineIn": "Dine-in", "selfPickup": "Self-pickup", "takeAway": "Take away", "delivery": "Delivery", "members": "Members", "serviceFee": "Service fee", "discount": "Discount", "reprint": "Reprint", "batchAction": "Batch action", "clearCart": "Clear Cart", "commissionStaff": "Commission staff", "reason": "Reason", "ordersNotes": "Orders notes", "customNotes": "Custom notes", "orderDiscount": "Order discount", "inputCancelReasonTip": "Please enter the reason for cancellation", "clear": "Clear", "clearCartTip": "Are you sure you want to clear all items from the cart?", "areaCode": "Area code", "phoneNumber": "Phone Number", "searchMemberTip": "Search member name, email, or phone", "noResultsFound": "No results found", "memberLoginSuccessTip": "Member login successful", "mr": "Mr.", "ms": "Ms.", "other": "Other", "memberRegisterSuccessTip": "Member registration successful", "customDiscount": "Custom discount", "reductionAmount": "Reduction amount", "noDiscountForAddOns": "No discount for add-ons: pearl", "dishesServed": "Dishes served", "urgeDishes": "Urge dishes", "packaging": "Packaging", "returnDish": "Cancelled", "selectedAll": "Selected All", "onHold": "On hold", "requestService": "Request service", "bundleOff": "Bundle off", "bundleTax": "Bundle tax", "notes": "Notes", "service": "Service", "offline": "offline", "pickUpOrder": "Pick up order", "totalItems": "Total {count} items", "quantity": "quantity", "payCaps": "Pay", "sendCaps": "Send", "pickUpNumber": "Pick-up number", "pickUpNumberEmptyTip": "Pick-up number must be filled in", "discountReasonInputTip": "Please import discount reason", "numberKeyboardLimited": "Limited {title} {range}", "cash": "Cash", "addProductFirst": "Please add the product first", "tableTransferOutTip": "Select the table to transfer out", "tableTransferInTip": "Select the table to transfer in", "tableTransferFinalTip": "Continuing will transfer table {outTable} to {inTable}", "tableTransferSuccess": "Table transfer successful", "unbindSuccess": "Unbind success", "tableSharingTip": "Select tables for table sharing", "noTableSelected": "No table selected", "selectedTableTip": "Selected table: {name}", "tableSharingSuccess": "Table sharing successful", "create": "Create", "confirmCreate": "Confirm Create", "orderNotOpenedTip": "Order {name} has not been opened yet", "willCancelOrderTip": "Exiting now will automatically cancel this order", "tableOpenTable": "Open table", "tableExit": "Exit", "shareSomeOrders": "Share {number} orders", "selectMultipleTables": "Select multiple tables to link", "selectOneMoreTable": "Please select 1 more table to proceed with table link.", "pleaseConfirm": "Please confirm", "tableLinkingSuccess": "Table linking successful", "groupedTables": "Grouped Tables", "tableGroupConfirmTip": "By continuing, tables {tables} will be linked. You can then order and pay together.", "ungroupTables": "Ungroup tables", "tablesWithCount": "{count} tables", "confirmUngroupTip": "Confirm ungrouping {count} tables in {name}", "ungroupRemind": "The table links will be removed, but the existing orders will remain intact.", "tableUngroupSuccess": "Table ungrouping successful", "hasOrderToSharingTip": "{inName} has an existing order, continuing will sharing  table {outName} with {inName}.", "sharingContinueTip": "Continuing will sharing table {outName} with {inName}", "groupName": "Group name", "salesPrice": "Sales Price", "ipError": "IP Error", "loadingText": "Loading...", "orderSavedSuccess": "Order saved successfully", "clickAgainToModifyProduct": "Click again to modify prices, discounts, and status", "orders": "Orders", "register": "Register", "confirmSendOrder": "Confirm to Send Order", "confirmSendOrderTip": "This table is linked with {tables}. you can choose to add items for one or multiple joined tables.", "orderForThisTableOnly": "Order for this table only", "orderForAllGroup": "Order for all {count} grouped tables", "customizeTablesToOrderFor": "Customize tables to order for", "batchSendOrder": "Batch send order", "sendOrder": "Send order", "confirmCancelReturn": "Confirm Cancel Return", "confirmCancelReturnTip": "After canceling the return, please notify the kitchen to prepare the dish again.", "printCancellationSlip": "Print Cancellation Slip", "returnedDishSuccess": "Returned the dish successfully", "cancelOrder": "Cancel order", "printWithdrawOrder": "Print withdraw Order", "cancelOrderTip": "Once cancel, the order cannot be edited or recovered.", "cancelOrderSuccess": "Cancel order successfully", "setFeeSuccess": "Set service fee successfully", "orderedDishes": "Ordered dishes", "addDishes": "Add dishes", "soldOut": "Sold out", "confirmSoldOut": "Confirm sold out", "combo": "Combo"}