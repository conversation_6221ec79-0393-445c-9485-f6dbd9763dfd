/// 简单的表单验证工具类
class SimpleValidators {
  
  /// 必填验证
  /// 检查值是否为空或null
  static String? required(dynamic value, {String? errorText}) {
    if (value == null || 
        (value is String && value.trim().isEmpty) ||
        (value is int && value == 0) ||
        (value is List && value.isEmpty)) {
      return errorText ?? '此字段为必填项';
    }
    return null;
  }

  /// 数值范围验证
  /// 检查数值是否在指定范围内
  static String? range(num? value, num min, num max, {String? errorText}) {
    if (value == null) return null;
    if (value < min || value > max) {
      return errorText ?? '值应在 $min 到 $max 之间';
    }
    return null;
  }

  /// 最小值验证
  static String? min(num? value, num minValue, {String? errorText}) {
    if (value != null && value < minValue) {
      return errorText ?? '值不能小于 $minValue';
    }
    return null;
  }

  /// 最大值验证
  static String? max(num? value, num maxValue, {String? errorText}) {
    if (value != null && value > maxValue) {
      return errorText ?? '值不能大于 $maxValue';
    }
    return null;
  }

  /// 邮箱验证
  static String? email(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    );
    
    if (!emailRegex.hasMatch(value)) {
      return errorText ?? '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 手机号验证（中国大陆）
  static String? phoneNumber(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    
    if (!phoneRegex.hasMatch(value)) {
      return errorText ?? '请输入有效的手机号码';
    }
    return null;
  }

  /// 最小长度验证
  static String? minLength(String? value, int minLength, {String? errorText}) {
    if (value != null && value.length < minLength) {
      return errorText ?? '至少需要 $minLength 个字符';
    }
    return null;
  }

  /// 最大长度验证
  static String? maxLength(String? value, int maxLength, {String? errorText}) {
    if (value != null && value.length > maxLength) {
      return errorText ?? '不能超过 $maxLength 个字符';
    }
    return null;
  }

  /// 数字验证
  static String? numeric(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    if (num.tryParse(value) == null) {
      return errorText ?? '请输入有效的数字';
    }
    return null;
  }

  /// 正整数验证
  static String? positiveInteger(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    final intValue = int.tryParse(value);
    if (intValue == null || intValue <= 0) {
      return errorText ?? '请输入正整数';
    }
    return null;
  }

  /// 组合多个验证器
  /// 依次执行所有验证器，遇到第一个错误就返回
  static String? Function(T?) compose<T>(List<String? Function(T?)> validators) {
    return (T? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }

  /// 自定义正则表达式验证
  static String? pattern(String? value, String pattern, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    final regex = RegExp(pattern);
    if (!regex.hasMatch(value)) {
      return errorText ?? '格式不正确';
    }
    return null;
  }

  /// 密码强度验证
  static String? strongPassword(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    // 至少8位，包含大小写字母、数字
    final strongPasswordRegex = RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$'
    );
    
    if (!strongPasswordRegex.hasMatch(value)) {
      return errorText ?? '密码至少8位，包含大小写字母和数字';
    }
    return null;
  }

  /// URL验证
  static String? url(String? value, {String? errorText}) {
    if (value == null || value.isEmpty) return null;
    
    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || (!uri.isScheme('http') && !uri.isScheme('https'))) {
        return errorText ?? '请输入有效的URL';
      }
    } catch (e) {
      return errorText ?? '请输入有效的URL';
    }
    return null;
  }
}