class SnowflakeIdGenerator {
  // 工厂构造函数
  factory SnowflakeIdGenerator() => _instance;

  // 私有命名构造函数
  SnowflakeIdGenerator._internal() {
    // 初始化
    _lastTimestamp = -1;
  }

  static final SnowflakeIdGenerator _instance = SnowflakeIdGenerator._internal();

  // 这里是你的ID生成逻辑
  static const int twepoch = 1609459200000;
  static const int workerIdBits = 5;
  static const int datacenterIdBits = 5;
  static const int sequenceBits = 12;

  static const int maxWorkerId = -1 ^ (-1 << workerIdBits);
  static const int maxDatacenterId = -1 ^ (-1 << datacenterIdBits);

  static const int workerIdShift = sequenceBits;
  static const int datacenterIdShift = sequenceBits + workerIdBits;
  static const int timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
  static const int sequenceMask = -1 ^ (-1 << sequenceBits);

  int workerId = 1;
  int datacenterId = 1;
  int _sequence = 0;
  int _lastTimestamp = -1;

  int nextId() {
    var timestamp = DateTime.now().millisecondsSinceEpoch;

    if (timestamp < _lastTimestamp) {
      throw StateError("Clock moved backwards.");
    }

    if (_lastTimestamp == timestamp) {
      _sequence = (_sequence + 1) & sequenceMask;
      if (_sequence == 0) {
        timestamp = _tilNextMillis(_lastTimestamp);
      }
    } else {
      _sequence = 0;
    }

    _lastTimestamp = timestamp;

    return ((timestamp - twepoch) << timestampLeftShift)
    | (datacenterId << datacenterIdShift)
    | (workerId << workerIdShift)
    | _sequence;
  }

  int _tilNextMillis(int lastTimestamp) {
    var timestamp = DateTime.now().millisecondsSinceEpoch;
    while (timestamp <= lastTimestamp) {
      timestamp = DateTime.now().millisecondsSinceEpoch;
    }
    return timestamp;
  }
}
