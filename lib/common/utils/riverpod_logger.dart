import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RiverpodLogger extends ProviderObserver {
  const RiverpodLogger();

  @override
  void didUpdateProvider(
      ProviderBase<Object?> provider,
      Object? previousValue,
      Object? newValue,
      ProviderContainer container
      ) {
    debugPrint('''
    {
      "provider":"${provider.name ?? provider.runtimeType}",
      "previousValue":"$previousValue",
      "newValue":"$newValue"
    }
    '''
    );
  }

  @override
  void didDisposeProvider(ProviderBase<Object?> provider, ProviderContainer container) {
    debugPrint('''
    {
      "didDisposeProvider":"${provider.name ?? provider.runtimeType}",
    }
    '''
    );
  }
}