import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:window_manager/window_manager.dart';

class PlatformWindowUtil {
  static Future<void> init() async {
    WidgetsFlutterBinding.ensureInitialized();

    await windowManager.ensureInitialized();

    WindowOptions windowOptions = const WindowOptions(
      center: true,
      titleBarStyle: TitleBarStyle.normal,
    );

    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.setResizable(false);

      await windowManager.maximize();
      await windowManager.show();
      await windowManager.focus();
    });
  }
}
