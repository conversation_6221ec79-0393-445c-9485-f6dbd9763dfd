import 'package:flutter/material.dart';

class ColorUtil {
  static Color parse(String hexColor) {
    try {
      hexColor = hexColor.replaceFirst('#', '');
      if (hexColor.length == 3) {
        hexColor = hexColor.split('').map((c) => '$c$c').join();
        hexColor = 'FF$hexColor';
      } else if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      } else if (hexColor.length == 8) {
      } else {
        throw FormatException('Invalid hex color format');
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return Colors.white;
    }
  }
}
