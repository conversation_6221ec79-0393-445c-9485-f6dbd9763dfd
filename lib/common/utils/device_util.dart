import 'dart:io';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:network_info_plus/network_info_plus.dart' as net_info;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:terminal_serial_number/terminal_serial_number.dart';
import 'package:uuid/uuid.dart';
import 'package:win32_registry/win32_registry.dart';

class DeviceUtil {
  static const double tablet = 600;

  static const double windowMinWidth = 1024;

  static const double windowMinHeight = 768;

  static String ip = '0.0.0.0';

  static late String _terminalSystemInfo;

  String get terminalSystemInfo => _terminalSystemInfo;

  ///设备唯一id
  static late String _terminalSerialNumber;

  String get terminalSerialNumber => _terminalSerialNumber;

  static Future<void> init() async {
    await _loadTerminalSystemInfo();
    await _loadIp();
    await _loadTerminalSerialNumber();
  }

  static double get deviceWidth {
    final size = PlatformDispatcher.instance.views.first.physicalSize;
    final pixelRatio = PlatformDispatcher.instance.views.first.devicePixelRatio;
    return size.width / pixelRatio;
  }

  static double get deviceHeight {
    final size = PlatformDispatcher.instance.views.first.physicalSize;
    final pixelRatio = PlatformDispatcher.instance.views.first.devicePixelRatio;
    return size.height / pixelRatio;
  }

  // 判断设备是否是手机,可以根据需要调整这个宽度阈值
  static bool isMobile() {
    return deviceWidth < tablet;
  }

  static bool isDesktop() {
    return PlatformUtil.isWindows || PlatformUtil.isMacOS;
  }

  static Future<void> _loadIp() async {
    final info = net_info.NetworkInfo();
     ip = await info.getWifiIP() ?? '0.0.0.0';
  }

  static _loadTerminalSystemInfo() async {
    final String name;
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      name = "${androidInfo.brand}-${androidInfo.model}";
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      name = iosInfo.utsname.machine;
    } else if (Platform.isWindows) {
      final windowsInfo = await deviceInfo.windowsInfo;
      name = windowsInfo.productName;
    } else {
      name = "unknown";
    }
    _terminalSystemInfo = name;
  }

  static _loadTerminalSerialNumber() async {
    print("开始生成设备唯一Id");
    String? serialNumber;
    if (Platform.isAndroid || Platform.isIOS) {
      serialNumber = await TerminalSerialNumber().getUUID();
    }  else if (Platform.isWindows) {
      //重装系统会变
      final key = Registry.openPath(
        RegistryHive.localMachine,
        path: r'SOFTWARE\Microsoft\Cryptography',
      );
      serialNumber = key.getValueAsString('MachineGuid');
    }
    if (serialNumber == null) {
      final prefs = await SharedPreferences.getInstance();
      final cachedId = prefs.getString('_terminalSerialNumber');
      if (cachedId != null && cachedId.isNotEmpty) {
        print("从偏好设置生成");
        serialNumber = cachedId;
      } else {
        final generatedId = const Uuid().v4();
        await prefs.setString('_terminalSerialNumber', generatedId);
        serialNumber = generatedId;
        print("自己生成");
      }
    }
    print('设备唯一id$serialNumber');
    _terminalSerialNumber = serialNumber;
  }

}
