import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class CommonUtil {

  ///时长转换为字符串展示
  static String formatDuration(Duration d) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(d.inHours);
    final minutes = twoDigits(d.inMinutes.remainder(60));
    final seconds = twoDigits(d.inSeconds.remainder(60));
    return '$hours:$minutes:$seconds';
  }

  ///将时间戳转换为日期字符串
  static String formatTimestamp(int timestamp, {String pattern = 'yyyy-MM-dd HH:mm:ss'}) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat(pattern).format(date);
  }

  ///计算某个时间戳到现在的时长
  static Duration durationFromTimestamp(int timestamp) {
    final now = DateTime.now();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return now.difference(date);
  }
}
