import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import '../kp_stepper.dart';

/// KPStepper的FormBuilder包装器 
class KPFormBuilderStepper extends FormBuilderFieldDecoration<int> {
  
  // KPStepper的所有原始参数
  final int min;
  final int max;
  final int step;
  final Color? buttonColor;
  final Color? disabledButtonColor;
  final Color? iconColor;
  final double iconSize;
  
  // 自定义回调
  final ValueChanged<int>? onStepperChanged;
  final ValueChanged<int>? showNumberKeyboardEvent;

  /// 构造函数
  KPFormBuilderStepper({
    // 向上传递key参数
    super.key,
    required super.name,
    super.validator,
    super.decoration = const InputDecoration(),
    super.onChanged,
    super.valueTransformer,
    super.enabled = true,
    super.onSaved,
    super.autovalidateMode = AutovalidateMode.disabled,
    super.onReset,
    super.focusNode,
    super.restorationId,
    int? initialValue,
    
    // KPStepper参数
    this.min = 0,
    this.max = 999,
    this.step = 1,
    this.buttonColor,
    this.disabledButtonColor = const Color(0xFFEEEEF0),
    this.iconColor,
    this.iconSize = 16,
    this.onStepperChanged, // 可选回调函数
    this.showNumberKeyboardEvent,
  }) : super( // 构造函数初始化列表
    initialValue: initialValue,
    // 关键：提供builder参数
    builder: (FormFieldState<int?> field) {
      final state = field as _KPFormBuilderStepperState;
      
      // 构建包含KPStepper和错误提示的完整组件
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPStepper组件
          KPStepper(
            value: state.value ?? (state.widget.min > 0 ? state.widget.min : 1),
            min: state.widget.min,
            max: state.widget.max,
            step: state.widget.step,
            onChanged: state.enabled ? state._handleKPStepperChange : null,
            showNumberKeyboardEvent: state._handleNumberKeyboard,
            buttonColor: state.widget.buttonColor,
            disabledButtonColor: state.widget.disabledButtonColor,
            iconColor: state.widget.iconColor,
            iconSize: state.widget.iconSize,
            disabled: !state.enabled,
          ),
          
          // 错误提示
          if (state.hasError)
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 8),
              child: Text(
                state.errorText ?? '',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFFE2483D),
                ),
              ),
            ),
        ],
      );
    },
  );

  @override
  FormBuilderFieldDecorationState<KPFormBuilderStepper, int> createState() =>
      _KPFormBuilderStepperState();
}

/// State类 - 只处理逻辑，不处理UI构建
class _KPFormBuilderStepperState 
    extends FormBuilderFieldDecorationState<KPFormBuilderStepper, int> {
  
  @override
  void initState() {
    super.initState();
    // 设置合理的初始值
    if (value == null) {
      setValue(widget.min > 0 ? widget.min : 1);
    }
  }

  /// 处理KPStepper的值变化
  void _handleKPStepperChange(int newValue) {
    // 更新FormBuilder状态
    setValue(newValue);
    
    // 触发回调
    widget.onChanged?.call(newValue);
    widget.onStepperChanged?.call(newValue);
  }

  /// 处理数字键盘事件
  void _handleNumberKeyboard(int value) {
    widget.showNumberKeyboardEvent?.call(value);
  }

  /// 获取widget实例的便捷方法
  KPFormBuilderStepper get widget => super.widget as KPFormBuilderStepper;
}