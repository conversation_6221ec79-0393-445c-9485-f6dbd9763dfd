import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

class KPTextField extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode? focusNode;
  final String? labelText;
  final String? hintText;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final bool isRequired;
  final int? maxLength;
  final String? Function(String?)? validator;
  final VoidCallback? onBlur;
  final String? Function(String? text)? errorTextBuilder;

  const KPTextField({
    super.key,
    required this.controller,
    this.focusNode,
    this.labelText,
    this.hintText,
    this.inputFormatters,
    this.keyboardType,
    this.isRequired = false,
    this.maxLength,
    this.validator,
    this.onBlur,
    this.errorTextBuilder,
  });

  @override
  State<KPTextField> createState() => _KPTextFieldState();
}

class _KPTextFieldState extends State<KPTextField> {
  bool _hasFocus = false;
  late FocusNode _focusNode;
  bool _shouldShowHelperText = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      _shouldShowHelperText = !_hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final errorText = widget.errorTextBuilder!= null ? widget.errorTextBuilder!(widget.controller.text) : null;
    final isError =
        errorText != null  && !_hasFocus && _shouldShowHelperText;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: widget.controller,
          focusNode: _focusNode,
          decoration: InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.always,
            label: widget.isRequired
                ? RichText(
                    text: TextSpan(
                      text: widget.labelText,
                      style: KPFontStyle.bodySmall.copyWith(
                        fontWeight: FontWeight.w400,
                        color: KPColors.textGraySecondary,
                      ),
                      children: const [
                        TextSpan(
                          text: ' *',
                          style: TextStyle(
                            color: KPColors.textBrandDefault,
                          ),
                        ),
                      ],
                    ),
                  )
                : Text(widget.labelText ?? '', style: KPFontStyle.bodySmall.copyWith(
              fontWeight: FontWeight.w400,
              color: KPColors.textGraySecondary,
            )),
            hintText: widget.hintText,
            labelStyle: KPFontStyle.bodySmall.copyWith(
              fontWeight: FontWeight.w400,
              color: KPColors.textGraySecondary,
              overflow: TextOverflow.visible,
            ),
            hintStyle: KPFontStyle.bodyLarge.copyWith(
              fontWeight: FontWeight.w400,
              color: KPColors.textGraySecondary,
            ),
            floatingLabelStyle: const TextStyle(
              color: KPColors.textBrandDefault,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(
                  color:
                      isError ? Colors.red : KPColors.borderGrayLightDarkest),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(KPRadius.s),
              borderSide: BorderSide(
                  color:
                      isError ? Colors.red : KPColors.borderGrayLightDarkest),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: KPColors.textBrandDefault),
            ),
            errorBorder: OutlineInputBorder(
              // 错误状态边框
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              // 聚焦错误状态边框
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: Colors.red),
            ),
            errorMaxLines: 2,
            contentPadding: const EdgeInsets.fromLTRB(12, 16, 12, 8),
            // 增加底部padding
            isCollapsed: false,
            isDense: true,
          ),
          inputFormatters: widget.inputFormatters,
          keyboardType: widget.keyboardType,
          maxLength: widget.maxLength,
          buildCounter: (context,
                  {required currentLength, required isFocused, maxLength}) =>
              null,
          validator: widget.validator,
          onEditingComplete: () {
            if (widget.onBlur != null) {
              widget.focusNode?.unfocus();
            }
          },
          onTapOutside: (_) {
            if (widget.onBlur != null) {
              widget.onBlur!();
              widget.focusNode?.unfocus();
            }
          },
        ),
        if(widget.errorTextBuilder != null )
        Visibility(
          visible: _shouldShowHelperText,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding:
            const EdgeInsets.only(top: KPSpacing.s, left: KPSpacing.xl),
            child: Text(
              errorText ?? '',
              style: KPFontStyle.bodySmall.copyWith(
                color: KPColors.textRedDefault,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        )
      ],
    );
  }
}
