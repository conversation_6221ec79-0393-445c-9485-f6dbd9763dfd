import 'dart:ui' as ui show BoxHeightStyle, BoxWidthStyle;

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

/// 输入框状态
enum InputState {
  ///启用、失焦
  enabled,

  ///关闭
  disabled,

  ///失焦错误
  error,

  ///获焦
  focused,

  ///获焦错误
  focusError,
}

/// A Material Design text field input.
class KPFormBuilderTextField extends FormBuilderFieldDecoration<String> {
  /// Controls the text being edited.
  ///
  /// If null, this widget will create its own [TextEditingController].
  final TextEditingController? controller;

  /// {@macro flutter.widgets.editableText.keyboardType}
  final TextInputType? keyboardType;

  /// The type of action button to use for the keyboard.
  ///
  /// Defaults to [TextInputAction.newline] if [keyboardType] is
  /// [TextInputType.multiline] and [TextInputAction.done] otherwise.
  final TextInputAction? textInputAction;

  /// {@macro flutter.widgets.editableText.textCapitalization}
  final TextCapitalization textCapitalization;

  /// The style to use for the text being edited.
  ///
  /// This text style is also used as the base style for the [decoration].
  ///
  /// If null, defaults to the `subtitle1` text style from the current [Theme].
  final TextStyle? style;

  /// {@macro flutter.widgets.editableText.strutStyle}
  final StrutStyle? strutStyle;

  /// {@macro flutter.widgets.editableText.textAlign}
  final TextAlign textAlign;

  /// {@macro flutter.widgets.inputDecorator.textAlignVertical}
  final TextAlignVertical? textAlignVertical;

  /// {@macro flutter.widgets.editableText.textDirection}
  final TextDirection? textDirection;

  /// {@macro flutter.widgets.editableText.autofocus}
  final bool autofocus;

  /// {@macro flutter.widgets.editableText.obscuringCharacter}
  final String obscuringCharacter;

  /// {@macro flutter.widgets.editableText.obscureText}
  final bool obscureText;

  /// {@macro flutter.widgets.editableText.autocorrect}
  final bool autocorrect;

  /// {@macro flutter.services.textInput.smartDashesType}
  final SmartDashesType? smartDashesType;

  /// {@macro flutter.services.textInput.smartQuotesType}
  final SmartQuotesType? smartQuotesType;

  /// {@macro flutter.services.textInput.enableSuggestions}
  final bool enableSuggestions;

  /// {@macro flutter.widgets.editableText.maxLines}
  final int? maxLines;

  /// {@macro flutter.widgets.editableText.minLines}
  final int? minLines;

  /// {@macro flutter.widgets.editableText.expands}
  final bool expands;

  /// {@macro flutter.widgets.EditableText.contextMenuBuilder}
  ///
  /// If not provided, will build a default menu based on the platform.
  ///
  /// See also:
  ///
  ///  * [AdaptiveTextSelectionToolbar], which is built by default.
  final EditableTextContextMenuBuilder? contextMenuBuilder;

  /// {@macro flutter.widgets.editableText.showCursor}
  final bool? showCursor;

  /// If [maxLength] is set to this value, only the "current input length"
  /// part of the character counter is shown.
  static const int noMaxLength = -1;

  /// The maximum number of characters (Unicode grapheme clusters) to allow in
  /// the text field.
  ///
  /// If set, a character counter will be displayed below the
  /// field showing how many characters have been entered. If set to a number
  /// greater than 0, it will also display the maximum number allowed. If set
  /// to [TextField.noMaxLength] then only the current character count is displayed.
  ///
  /// After [maxLength] characters have been input, additional input
  /// is ignored, unless [maxLengthEnforcement] is set to
  /// [MaxLengthEnforcement.none].
  ///
  /// The text field enforces the length with a [LengthLimitingTextInputFormatter],
  /// which is evaluated after the supplied [inputFormatters], if any.
  ///
  /// This value must be either null, [TextField.noMaxLength], or greater than 0.
  /// If null (the default) then there is no limit to the number of characters
  /// that can be entered. If set to [TextField.noMaxLength], then no limit will
  /// be enforced, but the number of characters entered will still be displayed.
  ///
  /// Whitespace characters (e.g. newline, space, tab) are included in the
  /// character count.
  ///
  /// If [maxLengthEnforcement] is [MaxLengthEnforcement.none], then more than
  /// [maxLength] characters may be entered, but the error counter and divider
  /// will switch to the [decoration]'s [InputDecoration.errorStyle] when the
  /// limit is exceeded.
  ///
  /// {@macro flutter.services.lengthLimitingTextInputFormatter.maxLength}
  final int? maxLength;

  final MaxLengthEnforcement? maxLengthEnforcement;

  /// {@macro flutter.widgets.editableText.onEditingComplete}
  final VoidCallback? onEditingComplete;

  /// {@macro flutter.widgets.editableText.onSubmitted}
  ///
  /// See also:
  ///
  ///  * [EditableText.onSubmitted] for an example of how to handle moving to
  ///    the next/previous field when using [TextInputAction.next] and
  ///    [TextInputAction.previous] for [textInputAction].
  final ValueChanged<String?>? onSubmitted;

  /// {@macro flutter.widgets.editableText.inputFormatters}
  final List<TextInputFormatter>? inputFormatters;

  /// {@macro flutter.widgets.editableText.cursorWidth}
  final double cursorWidth;

  /// {@macro flutter.widgets.editableText.cursorHeight}
  final double? cursorHeight;

  /// {@macro flutter.widgets.editableText.cursorRadius}
  final Radius? cursorRadius;

  /// The color to use when painting the cursor.
  ///
  /// Defaults to [TextSelectionThemeData.cursorColor] or [CupertinoTheme.primaryColor]
  /// depending on [ThemeData.platform].
  final Color? cursorColor;

  /// Controls how tall the selection highlight boxes are computed to be.
  ///
  /// See [ui.BoxHeightStyle] for details on available styles.
  final ui.BoxHeightStyle selectionHeightStyle;

  /// Controls how wide the selection highlight boxes are computed to be.
  ///
  /// See [ui.BoxWidthStyle] for details on available styles.
  final ui.BoxWidthStyle selectionWidthStyle;

  /// The appearance of the keyboard.
  ///
  /// This setting is only honored on iOS devices.
  ///
  /// If unset, defaults to the theme brightness.
  final Brightness? keyboardAppearance;

  /// {@macro flutter.widgets.editableText.scrollPadding}
  final EdgeInsets scrollPadding;

  /// {@macro flutter.widgets.editableText.enableInteractiveSelection}
  final bool enableInteractiveSelection;

  /// {@macro flutter.widgets.scrollable.dragStartBehavior}
  final DragStartBehavior dragStartBehavior;

  /// {@macro flutter.rendering.editable.selectionEnabled}
  bool get selectionEnabled => enableInteractiveSelection;

  /// {@template flutter.material.textfield.onTap}
  /// Called for each distinct tap except for every second tap of a double tap.
  ///
  /// The text field builds a [GestureDetector] to handle input events like tap,
  /// to trigger focus requests, to move the caret, adjust the selection, etc.
  /// Handling some of those events by wrapping the text field with a competing
  /// GestureDetector is problematic.
  ///
  /// To unconditionally handle taps, without interfering with the text field's
  /// internal gesture detector, provide this callback.
  ///
  /// If the text field is created with [enabled] false, taps will not be
  /// recognized.
  ///
  /// To be notified when the text field gains or loses the focus, provide a
  /// [focusNode] and add a listener to that.
  ///
  /// To listen to arbitrary pointer events without competing with the
  /// text field's internal gesture detector, use a [Listener].
  /// {@endtemplate}
  final GestureTapCallback? onTap;

  /// {@template flutter.material.textfield.onTapOutside}
  /// A callback to be invoked when a tap is detected outside of this TapRegion
  /// and any other region with the same groupId, if any.
  ///
  /// The PointerDownEvent passed to the function is the event that caused the
  /// notification. If this region is part of a group (i.e. groupId is set),
  /// then it's possible that the event may be outside of this immediate region,
  /// although it will be within the region of one of the group members.
  /// {@endtemplate}
  final TapRegionCallback? onTapOutside;

  final MouseCursor? mouseCursor;

  /// Callback that generates a custom [InputDecorator.counter] widget.
  ///
  /// See [InputCounterWidgetBuilder] for an explanation of the passed in
  /// arguments.  The returned widget will be placed below the line in place of
  /// the default widget built when [counterText] is specified.
  ///
  /// The returned widget will be wrapped in a [Semantics] widget for
  /// accessibility, but it also needs to be accessible itself.  For example,
  /// if returning a Text widget, set the [semanticsLabel] property.
  ///
  /// {@tool snippet}
  /// ```dart
  /// Widget counter(
  ///   BuildContext context,
  ///   {
  ///     int currentLength,
  ///     int maxLength,
  ///     bool isFocused,
  ///   }
  /// ) {
  ///   return Text(
  ///     '$currentLength of $maxLength characters',
  ///     semanticsLabel: 'character count',
  ///   );
  /// }
  /// ```
  /// {@end-tool}
  ///
  /// If buildCounter returns null, then no counter and no Semantics widget will
  /// be created at all.
  final InputCounterWidgetBuilder? buildCounter;

  /// {@macro flutter.widgets.editableText.scrollPhysics}
  final ScrollPhysics? scrollPhysics;

  /// {@macro flutter.widgets.editableText.scrollController}
  final ScrollController? scrollController;

  /// {@macro flutter.widgets.editableText.autofillHints}
  /// {@macro flutter.services.autofill.autofillHints}
  final Iterable<String>? autofillHints;

  ///{@macro flutter.widgets.text_selection.TextMagnifierConfiguration.intro}
  ///
  ///{@macro flutter.widgets.magnifier.intro}
  ///
  ///{@macro flutter.widgets.text_selection.TextMagnifierConfiguration.details}
  final TextMagnifierConfiguration? magnifierConfiguration;

  /// By default `false`
  final bool readOnly;

  /// {@macro flutter.widgets.editableText.contentInsertionConfiguration}
  final ContentInsertionConfiguration? contentInsertionConfiguration;

  final String? labelText;

  final Widget? label;

  final String? helpText;

  final bool autoValidate;

  /// 后缀按钮
  final Widget? suffixIcon;

  final Color? backgroundColor;

  /// 是否显示清除按钮
  ///
  /// 为 true 时 [suffixIcon] 应当为 null
  final bool showCleanSuffixIcon;

  /// 是否忽略输入框的点击事件
  final bool ignorePoint;

  /// Creates a Material Design text field input.
  KPFormBuilderTextField({
    super.key,
    required super.name,
    this.labelText,
    this.label,
    this.helpText,
    this.suffixIcon,
    this.showCleanSuffixIcon = false,
    this.backgroundColor,
    super.validator,
    super.decoration,
    super.onChanged,
    super.valueTransformer,
    super.enabled,
    super.onSaved,
    super.autovalidateMode = AutovalidateMode.disabled,
    this.autoValidate = false,
    super.onReset,
    super.focusNode,
    super.restorationId,
    String? initialValue,
    this.readOnly = false,
    this.ignorePoint = false,
    this.maxLines = 1,
    this.obscureText = false,
    this.textCapitalization = TextCapitalization.none,
    this.scrollPadding = const EdgeInsets.all(8.0),
    this.enableInteractiveSelection = true,
    this.maxLengthEnforcement,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.autocorrect = true,
    this.cursorWidth = 2.0,
    this.cursorHeight,
    this.keyboardType,
    this.style,
    this.controller,
    this.textInputAction,
    this.strutStyle,
    this.textDirection,
    this.maxLength,
    this.onEditingComplete,
    this.onSubmitted,
    this.inputFormatters,
    this.cursorRadius,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.expands = false,
    this.minLines,
    this.showCursor,
    this.onTap,
    this.onTapOutside,
    this.enableSuggestions = false,
    this.textAlignVertical,
    this.dragStartBehavior = DragStartBehavior.start,
    this.scrollController,
    this.scrollPhysics,
    this.selectionWidthStyle = ui.BoxWidthStyle.tight,
    this.smartDashesType,
    this.smartQuotesType,
    this.selectionHeightStyle = ui.BoxHeightStyle.tight,
    this.autofillHints,
    this.obscuringCharacter = '•',
    this.mouseCursor,
    this.contextMenuBuilder = _defaultContextMenuBuilder,
    this.magnifierConfiguration,
    this.contentInsertionConfiguration,
  })  : assert(minLines == null || minLines > 0),
        assert(maxLines == null || maxLines > 0),
        assert(showCleanSuffixIcon == false || suffixIcon == null),
        assert(
          (minLines == null) || (maxLines == null) || (maxLines >= minLines),
          'minLines can\'t be greater than maxLines',
        ),
        assert(
          !expands || (minLines == null && maxLines == null),
          'minLines and maxLines must be null when expands is true.',
        ),
        assert(!obscureText || maxLines == 1,
            'Obscured fields cannot be multiline.'),
        assert(maxLength == null || maxLength > 0),
        super(
          initialValue: controller != null ? controller.text : initialValue,
          builder: (FormFieldState<String?> field) {
            final state = field as _KPFormBuilderTextFieldState;

            final suffixIcon = state.buildSuffixIcon();

            Widget child = Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    state.effectiveFocusNode.requestFocus();
                  },
                  child: Container(
                    height: 56,
                    padding:
                        const EdgeInsets.symmetric(vertical: 3, horizontal: 12),
                    decoration: BoxDecoration(
                      color: state.enabled
                          ? Colors.white
                          : backgroundColor ?? const Color(0x00f3f3f4),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.fromBorderSide(state._getOutlinedBorder()),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            statesController: state._stateController,
                            restorationId: restorationId,
                            controller: state._effectiveController,
                            focusNode: state.effectiveFocusNode,
                            decoration: state.decoration,
                            keyboardType: keyboardType ?? TextInputType.text,
                            textInputAction: textInputAction,
                            style: style ?? const TextStyle(color: Color(0xFF323843),fontWeight: FontWeight.w400,overflow: TextOverflow.ellipsis),
                            strutStyle: strutStyle,
                            textAlign: textAlign,
                            textAlignVertical: textAlignVertical,
                            textDirection: textDirection,
                            textCapitalization: textCapitalization,
                            autofocus: autofocus,
                            readOnly: readOnly,
                            showCursor: showCursor,
                            obscureText: obscureText,
                            autocorrect: autocorrect,
                            enableSuggestions: enableSuggestions,
                            maxLengthEnforcement: maxLengthEnforcement ??
                                MaxLengthEnforcement.enforced,
                            maxLines: maxLines,
                            minLines: minLines,
                            expands: expands,
                            maxLength: maxLength,
                            onTap: onTap,
                            onTapOutside: onTapOutside,
                            onEditingComplete: onEditingComplete,
                            onSubmitted: (text) {
                              state.validateText();
                              onSubmitted?.call(text);
                            },
                            inputFormatters: inputFormatters,
                            enabled: state.enabled,
                            cursorWidth: cursorWidth,
                            cursorHeight: cursorHeight,
                            cursorRadius: cursorRadius,
                            cursorColor: cursorColor ?? const Color(0xFFFF8500),
                            scrollPadding: scrollPadding,
                            keyboardAppearance: keyboardAppearance,
                            enableInteractiveSelection:
                                enableInteractiveSelection,
                            buildCounter: buildCounter,
                            dragStartBehavior: dragStartBehavior,
                            scrollController: scrollController,
                            scrollPhysics: scrollPhysics,
                            selectionHeightStyle: selectionHeightStyle,
                            selectionWidthStyle: selectionWidthStyle,
                            smartDashesType: smartDashesType,
                            smartQuotesType: smartQuotesType,
                            mouseCursor: mouseCursor,
                            contextMenuBuilder: contextMenuBuilder,
                            obscuringCharacter: obscuringCharacter,
                            autofillHints: autofillHints,
                            magnifierConfiguration: magnifierConfiguration,
                            contentInsertionConfiguration:
                                contentInsertionConfiguration,
                          ),
                        ),
                        if (suffixIcon != null) suffixIcon
                      ],
                    ),
                  ),
                ),
                Offstage(
                  offstage: state._errorText?.isEmpty ?? true,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      state._errorText ?? '',
                      style: const TextStyle(fontSize: 12, height: 1.33, color: Color(0xFFE2483D)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                Offstage(
                  offstage: !state._haveHelperText,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      helpText ?? '',
                      style: const TextStyle(fontSize: 12, height: 1.33, color: Color(0xFF858A99)),
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            );

            if (ignorePoint) {
              child = IgnorePointer(child: child);
            }

            return child;
          },
        );

  static Widget _defaultContextMenuBuilder(
    BuildContext context,
    EditableTextState editableTextState,
  ) {
    return AdaptiveTextSelectionToolbar.editableText(
      editableTextState: editableTextState,
    );
  }

  @override
  FormBuilderFieldDecorationState<KPFormBuilderTextField, String>
      createState() => _KPFormBuilderTextFieldState();
}

class _KPFormBuilderTextFieldState
    extends FormBuilderFieldDecorationState<KPFormBuilderTextField, String> {
  bool validationFail = false;
  late InputState _state = InputState.enabled;

  final _stateController = WidgetStatesController();

  ValueNotifier? _cleanSuffixIconVisibilityController;

  String? _errorText;

  TextEditingController? _controller;

  TextEditingController get _effectiveController =>
      widget.controller ?? (_controller ??= TextEditingController(text: value));

  @override
  InputDecoration get decoration => widget.decoration.copyWith(
        filled: true,
        fillColor: widget.enabled
            ? Colors.white
            : widget.backgroundColor ?? const Color(0xFFF3F3F4),
        errorText: null,
        enabled: widget.enabled,
        counterText: '',
        labelText: widget.labelText,
        labelStyle: widget.decoration.labelStyle ??
            const TextStyle(
              color: Color(0xFF858A99),
              fontSize: 16,
              height: 1.5,
              fontWeight: FontWeight.w400,
            ),
        label: widget.label,
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        contentPadding: widget.decoration.contentPadding ??
            const EdgeInsets.only(
              top: 4,
              right: 12,
            ),
        hoverColor: Colors.transparent,
      );

  /// 末尾的按钮
  ///
  /// [showCleanSuffixIcon] 为 true 显示清空按钮 （需要显示时）
  /// showCleanSuffixIcon 为 false 显示 [widget.suffixIcon];
  Widget? buildSuffixIcon() {
    if (widget.showCleanSuffixIcon) {
      return ValueListenableBuilder(
        valueListenable: _cleanSuffixIconVisibilityController!,
        builder: (context, cleanEnable, icon) {
          return Offstage(
            offstage: !cleanEnable,
            child: icon!,
          );
        },
        child: IconButton(
          icon: const Icon(
            Icons.cancel,
            color: Color(0xC6C4CD),
            size: 16,
          ),
          onPressed: () {
            didChange(null);
          },
        ),
      );
    } else {
      return widget.suffixIcon;
    }
  }

  @override
  void initState() {
    super.initState();

    if (widget.showCleanSuffixIcon) {
      _cleanSuffixIconVisibilityController = ValueNotifier(
          _effectiveController.text.isNotEmpty && effectiveFocusNode.hasFocus);
    }

    _stateController.addListener(() {
      // debugPrint("_stateController changed value = ${_stateController.value}");
    });

    //setting this to value instead of initialValue here is OK since we handle initial value in the parent class
    _effectiveController.addListener(_handleControllerChanged);
    effectiveFocusNode.addListener(_focusNodeListener);
  }

  @override
  void dispose() {
    _stateController.dispose();
    effectiveFocusNode.removeListener(_focusNodeListener);
    _controller?.dispose();
    _cleanSuffixIconVisibilityController?.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(KPFormBuilderTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果validator变化，重新验证
    if (widget.validator != oldWidget.validator) {
      // validateText();
    }
  }

  @override
  void reset() {
    super.reset();
    _effectiveController.text = initialValue ?? '';
  }

  @override
  void didChange(String? value) {
    super.didChange(value);

    if (_effectiveController.text != value) {
      _effectiveController.text = value ?? '';
    }
  }

  @override
  void invalidate(
    String errorText, {
    bool shouldFocus = true,
    bool shouldAutoScrollWhenFocus = false,
  }) {
    super.invalidate(errorText,
        shouldFocus: shouldFocus,
        shouldAutoScrollWhenFocus: shouldAutoScrollWhenFocus);
    _state = errorText.isNotEmpty ? InputState.error : InputState.enabled;
    _errorText = errorText;
  }

  void _handleControllerChanged() {
    // Suppress changes that originated from within this class.
    //
    // In the case where a controller has been passed in to this widget, we
    // register this change listener. In these cases, we'll also receive change
    // notifications for changes originating from within this class -- for
    // example, the reset() method. In such cases, the FormField value will
    // already have been set.
    if (_effectiveController.text != (value ?? '')) {
      didChange(_effectiveController.text);
    }

    if (effectiveFocusNode.hasFocus) {
      switch (widget.autovalidateMode) {
        case AutovalidateMode.disabled:
          break;
        case AutovalidateMode.onUserInteraction:
          validateText();
        case AutovalidateMode.always:
          validateText();
        case AutovalidateMode.onUnfocus:
          // TODO: Handle this case.
      }
      setState(() {});
    }
    _checkCleanSuffixIconVisibility();
  }

  bool get _haveHelperText => widget.helpText != null;

  void _focusNodeListener() {
    if (effectiveFocusNode.hasFocus) {
      if (validationFail) {
        _state = InputState.focusError;
        _errorText = widget.validator?.call(_effectiveController.text);
      } else {
        _state = InputState.focused;
        _errorText = '';
      }
    } else {
      validateText();
      if (validationFail) {
        _state = InputState.error;
        _errorText = widget.validator?.call(_effectiveController.text);
      } else {
        _state = InputState.enabled;
        _errorText = '';
      }
    }
    setState(() {});
    _checkCleanSuffixIconVisibility();
  }

  void _checkCleanSuffixIconVisibility() {
    if (widget.showCleanSuffixIcon) {
      _cleanSuffixIconVisibilityController?.value =
          _effectiveController.text.isNotEmpty && effectiveFocusNode.hasFocus;
    }
  }

  ///校验文本
  void validateText() {
    String? errorText = widget.validator?.call(_effectiveController.text);
    if (errorText == null) {
      validationFail = false;
      _state = InputState.focused;
      _errorText = '';
    } else {
      validationFail = true;
      _state = InputState.focusError;
      _errorText = errorText;
    }
    setState(() {});
  }

  ///输入框不同状态对应的border
  BorderSide _getOutlinedBorder() {
    switch (_state) {
      case InputState.focused:
        return const BorderSide(
          color: Color(0xFFFF8500),
          width: 2,
        );
      case InputState.focusError:
        return const BorderSide(
          color: Color(0xFFE2483D),
          width: 2,
        );
      case InputState.error:
        return const BorderSide(
          color: Color(0xFFE2483D),
          width: 1,
        );
      case InputState.disabled:
      case InputState.enabled:
        return const BorderSide(
          color: Color(0xFFDCDDE1),
          width: 1,
        );
    }
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
        .add(DiagnosticsProperty<InputDecoration>('decoration', decoration));
  }
}
