import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

class KPStepper extends StatefulWidget {
  final int value;

  final ValueChanged<int>? onChanged;

  final ValueChanged<int>? showNumberKeyboardEvent;

  final int min;

  final int max;

  final int step;

  final Color? buttonColor;

  final Color? disabledButtonColor;

  final Color? iconColor;

  final bool disabled;

  final double iconSize;

  const KPStepper(
      {super.key,
      required this.value,
      this.onChanged,
      this.min = 0,
      this.max = 999,
      this.step = 1,
      this.buttonColor,
      this.disabledButtonColor,
      this.iconColor = KPColors.textGrayPrimary,
      this.disabled = false,
      this.showNumberKeyboardEvent,
      this.iconSize = 16});

  @override
  State<KPStepper> createState() => _KPStepperState();
}

class _KPStepperState extends State<KPStepper> {
  late int _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.value;
  }

  @override
  void didUpdateWidget(KPStepper oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _currentValue = widget.value;
    }
  }

  void _increment() {
    final newValue = _currentValue + widget.step;
    if (newValue <= widget.max) {
      setState(() => _currentValue = newValue);
      widget.onChanged?.call(newValue);
    }
  }

  void _decrement() {
    final newValue = _currentValue - widget.step;
    if (newValue >= widget.min) {
      setState(() => _currentValue = newValue);
      widget.onChanged?.call(newValue);
    }
  }

  void _openInputNumberDialog() {
    widget.showNumberKeyboardEvent?.call(_currentValue);
  }

  @override
  Widget build(BuildContext context) {
    final buttonColor = widget.buttonColor ?? Colors.white;
    final disabledButtonColor =
        widget.disabledButtonColor ?? KPColors.fillGrayLightDarkest;
    final iconColor = widget.iconColor ?? Colors.white;

    final isDecrementDisabled = widget.disabled || _currentValue <= widget.min;
    final isIncrementDisabled = widget.disabled || _currentValue >= widget.max;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildButton(
            icon: Icons.remove,
            onPressed: isDecrementDisabled ? null : _decrement,
            color: isDecrementDisabled ? disabledButtonColor : buttonColor,
            iconColor: iconColor,
            iconSize: widget.iconSize,
            disabled: isDecrementDisabled),
        Container(
            constraints: const BoxConstraints(minWidth: 48),
            child: SizedBox(
              height: widget.iconSize + 14,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 8),
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                    color: KPColors.fillGrayLightLight,
                )),
                child: InkWell(
                    onTap: _openInputNumberDialog,
                    child: Center(
                      child: Text(
                        '$_currentValue',
                        style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: KPColors.textGrayPrimary),
                        textAlign: TextAlign.center,
                      ),
                    )),
              ),
            )),
        _buildButton(
            icon: Icons.add,
            onPressed: isIncrementDisabled ? null : _increment,
            color: isIncrementDisabled ? disabledButtonColor : buttonColor,
            iconColor: iconColor,
          iconSize: widget.iconSize,
          disabled: isIncrementDisabled,
        ),
      ],
    );
  }

  Widget _buildButton(
      {required IconData icon,
      required VoidCallback? onPressed,
      required Color? color,
      required Color iconColor,
      required double iconSize,
      disabled = false}) {
    late BorderRadius borderRadius;
    if (icon == Icons.add) {
      borderRadius = const BorderRadius.only(
          topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0));
    } else {
      borderRadius = const BorderRadius.only(
          topLeft: Radius.circular(4.0), bottomLeft: Radius.circular(4.0));
    }
    return GestureDetector(
        onTap: disabled ? null : onPressed,
        child: Container(
          decoration: BoxDecoration(
              color: color,
              border: Border.all(
                color: KPColors.borderGrayLightDarkest,
              ),
              borderRadius: borderRadius),
          child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Icon(
                  size: iconSize,
                  icon,
                  color: disabled
                      ? KPColors.fillGrayDarkLighter
                      : KPColors.textGrayPrimary)),
        ));
  }
}
