import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/popup_single_date_picker.dart';

class KPDatePicker extends StatefulWidget {
  final String labelText;
  final String hintText;
  final Function(DateTime? selectedDate)? onConfirm;
  final Function()? onCancel;
  final Color selectedBorderColor;
  final Color unselectedBorderColor;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final String dateFormat;
  final bool enabled;

  const KPDatePicker({
    super.key,
    required this.labelText,
    this.hintText = '',
    this.onConfirm,
    this.onCancel,
    this.selectedBorderColor = const Color(0xFFE0E0E0),
    this.unselectedBorderColor = KPColors.borderGrayLightDarkest,
    this.prefixIcon,
    this.suffixIcon,
    this.firstDate,
    this.lastDate,
    this.dateFormat = 'MM/dd/yyyy',
    this.enabled = true,
  });

  @override
  State<KPDatePicker> createState() => _KPDatePickerState();
}

class _KPDatePickerState extends State<KPDatePicker> {
  late TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();
  DateTime? _selectedDate;
  bool _isSelected = false;
  bool _hasSelectedDate = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.hintText);
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    setState(() {
      _isSelected = _focusNode.hasFocus;
    });

    if (_focusNode.hasFocus) {
      _showDatePicker();
    }
  }

  Future<void> _showDatePicker() async {
    if (!widget.enabled) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final anchorPoint = Offset(position.dx, position.dy + size.height);

    final result = await PopupDatePicker.show(
      context,
      initialDate: _selectedDate,
      anchorPoint: anchorPoint,
      margin: 0,
    );

    if (result != null) {
      setState(() {
        _selectedDate = result;
        _hasSelectedDate = true;

        final locale = Localizations.localeOf(context);
        final isZhLanguage = locale.languageCode == 'zh';
        final formatter = isZhLanguage
            ? DateFormat('yyyy年MM月dd日')
            : DateFormat(widget.dateFormat);

        _controller.text = formatter.format(result);

        if (widget.onConfirm != null) {
          widget.onConfirm!(result);
        }
      });
    } else {
      if (widget.onCancel != null) {
        widget.onCancel!();
      }
    }

    _focusNode.unfocus();
    setState(() => _isSelected = false);
  }

  @override
  Widget build(BuildContext context) {
    Color borderColor;
    const double borderWidth = 1.0;

    if (_isSelected || _hasSelectedDate) {
      borderColor = widget.selectedBorderColor;
    } else if (!widget.enabled) {
      borderColor = widget.unselectedBorderColor.withOpacity(0.5);
    } else {
      borderColor = widget.unselectedBorderColor;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.enabled ? _showDatePicker : null,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(6), color: Colors.white),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            readOnly: true,
            enabled: widget.enabled,
            decoration: InputDecoration(
              labelText: widget.labelText,
              filled: true,
              fillColor: Colors.white,
              hintText: widget.hintText,
              labelStyle: TextStyle(
                color: _isSelected || _hasSelectedDate
                    ? widget.selectedBorderColor
                    : Colors.grey[600],
                fontSize: 14,
              ),
              prefixIcon: widget.prefixIcon,
              suffixIcon: widget.suffixIcon != null
                  ? SizedBox(
                width: 24,
                height: 24,
                child: Center(child: widget.suffixIcon),
              )
                  : IconButton(
                icon: Icon(
                  Icons.calendar_today,
                  color: _isSelected || _hasSelectedDate
                      ? widget.selectedBorderColor
                      : Colors.grey[600],
                ),
                onPressed: widget.enabled ? _showDatePicker : null,
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: const TextStyle(fontSize: 14, color: Color(0xFF323843)),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }
}