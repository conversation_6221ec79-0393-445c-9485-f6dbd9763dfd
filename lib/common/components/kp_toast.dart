import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class KPToast {
  static void show({
    required String content,
    ToastPosition position = ToastPosition.center,
    Duration duration = const Duration(milliseconds: 2500),
    bool isGreen = false,
    VoidCallback? onDismiss,
    Color? backgroundColor,
    Color? textColor,
  }) {
    showToast(
      content,
      duration: duration,
      radius: 4,
      backgroundColor: backgroundColor??(isGreen?KPColors.fillGreenNormal:const Color(0xFF313033)),
      position: position,
      textPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
      textStyle: TextStyle(
        fontSize: 14,
        color: textColor??const Color(0xFFF4EFF4),
        fontWeight: FontWeight.w400,
      ),
      textMaxLines: 10,
      onDismiss: onDismiss
    );
  }

  static void showLoading({required BuildContext context, String? text}) {
    if (text == null || text.isEmpty) {
      TDToast.showLoadingWithoutText(context: context);
    } else {
      TDToast.showLoading(context: context, text: text);
    }
  }

  static void dismissLoading() {
    TDToast.dismissLoading();
  }
}
