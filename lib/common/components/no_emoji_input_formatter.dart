import 'package:flutter/services.dart';

///TextInputFormatter 禁止Emoji输入
class NoEmojiInputFormatter extends TextInputFormatter {
  static final _emojiRegex = RegExp(
    r'[\u{1F600}-\u{1F64F}]|' // Emoticons
    r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
    r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
    r'[\u{1F1E6}-\u{1F1FF}]|' // Regional country flags
    r'[\u{2600}-\u{26FF}]|'   // Misc symbols
    r'[\u{2700}-\u{27BF}]|'   // Dingbats
    r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
    r'[\u{1FA70}-\u{1FAFF}]|' // Symbols and Pictographs Extended-A
    r'[\u{1F018}-\u{1F270}]|' // Various asian characters
    r'[\u{238C}-\u{2454}]|'   // Misc items
    r'[\u{20D0}-\u{20FF}]',   // Combining Diacritical Marks for Symbols
    unicode: true,
  );

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final filtered = newValue.text.replaceAll(_emojiRegex, '');
    // 保持光标位置
    int baseOffset = filtered.length < newValue.selection.baseOffset
        ? filtered.length
        : newValue.selection.baseOffset;
    return newValue.copyWith(
      text: filtered,
      selection: TextSelection.collapsed(offset: baseOffset),
    );
  }
}