import 'package:flutter/material.dart';
import 'package:kpos/assets/assets.gen.dart';

///通用下拉菜单组件
///
/// 示例 KPDropDownMenu<DiningType>(
///             items: const [DiningType.dineIn,DiningType.selfPickup,DiningType.takeAway,DiningType.delivery,],
///             itemText: (type) => type.label,
///             value: selectedDiningType,
///            itemTextStyle: const TextStyle(
///               fontSize: 14,
///               color: Color(0xFF323843),
///               fontWeight: FontWeight.w500,
///             ),
///             onChanged: (type) => setState(() {
///               if(type!=null){
///                 selectedDiningType = type;
///               }
///             }),
///             menuMaxHeight: 300,
///             menuWidth: 200,
///             dropdownColor: Colors.grey[50],
///           ),
class KPDropDownMenu<T> extends StatefulWidget {
  final List<T> items;
  final String Function(T item) itemText;
  final T value;
  final ValueChanged<T?>? onChanged;
  final double menuMaxHeight;
  final double menuWidth;
  final TextStyle? itemTextStyle;
  final Color? dropdownColor;
  final AlignmentGeometry? alignment;

  const KPDropDownMenu({
    super.key,
    required this.items,
    required this.itemText,
    required this.value,
    this.onChanged,
    this.menuMaxHeight = 300,
    this.menuWidth = 200,
    this.itemTextStyle,
    this.dropdownColor,
    this.alignment,
  });

  @override
  State<KPDropDownMenu<T>> createState() => _KPDropDownMenuState<T>();
}

class _KPDropDownMenuState<T> extends State<KPDropDownMenu<T>> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;

  void _toggleMenu() {
    if (_overlayEntry == null) {
      _showMenu();
    } else {
      _hideMenu();
    }
  }

  void _showMenu() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideMenu,
        child: Stack(
          children: [
            Positioned(
              width: widget.menuWidth,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0, size.height + 2),
                child: Material(
                  color: widget.dropdownColor ?? Colors.white,
                  elevation: 4,
                  borderRadius: BorderRadius.circular(4),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: widget.menuMaxHeight,
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: widget.items.length,
                      itemBuilder: (context, index) {
                        final item = widget.items[index];
                        return InkWell(
                          onTap: () {
                            widget.onChanged?.call(item);
                            _hideMenu();
                          },
                          child: Container(
                            padding: const EdgeInsets.only(left: 12,bottom: 16,top: 16),
                            alignment:
                                widget.alignment ?? Alignment.centerLeft,
                            child: Text(
                              widget.itemText(item),
                              style: widget.itemTextStyle ??
                                  Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleMenu,
        child: Container(
          padding: const EdgeInsets.only(left: 12, top: 6,bottom: 6,right: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE7E8EB)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.itemText(widget.value),
                style: widget.itemTextStyle ??
                    Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 8),
              const Icon(
                Icons.arrow_drop_down,
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 下拉菜单项模型
/// 
/// 用于表示下拉菜单中的选项，包含id和name属性
/// id：选项的唯一标识符，用于与后端交互
/// name：选项的显示名称，用于在界面上展示
class DropdownItem {
  final String id;
  final String name;

  DropdownItem({
    required this.id,
    required this.name,
  });
}

/// 带有TextField样式的下拉菜单组件
/// 
/// 该组件具有与KPDateRangePicker类似的视觉效果，在未选择时显示灰色边框，
/// 选择中和已选择时显示橙色边框。
///
/// 示例:
/// ```dart
/// // 创建下拉选项列表
/// final List<DropdownItem> typeOptions = [
///   DropdownItem(id: "all", name: "全部类型"),
///   DropdownItem(id: "income", name: "收款"),
///   DropdownItem(id: "expense", name: "付款"),
/// ];
///
/// // 当前选中的值
/// DropdownItem? selectedType;
///
/// // 使用组件
/// KPTextFieldDropDown(
///   items: typeOptions,
///   value: selectedType,
///   labelText: '类型',
///   hintText: '请选择类型',
///   onChanged: (item) {
///     setState(() {
///       selectedType = item;
///       // 可以使用item.id与后端交互
///       print("选中的ID: ${item?.id}, 名称: ${item?.name}");
///     });
///   },
/// )
/// ```
class KPTextFieldDropDown extends StatefulWidget {
  /// 下拉选项列表
  final List<DropdownItem> items;
  
  /// 当前选中的值
  final DropdownItem? value;
  
  /// 值变化时的回调
  final ValueChanged<DropdownItem?>? onChanged;
  
  /// 标签文本
  final String labelText;
  
  /// 提示文本
  final String hintText;
  
  /// 选中状态边框颜色
  final Color selectedBorderColor;
  
  /// 未选中状态边框颜色
  final Color unselectedBorderColor;
  
  /// 前缀图标
  final Widget? prefixIcon;
  
  /// 后缀图标
  final Widget? suffixIcon;
  
  /// 菜单最大高度
  final double menuMaxHeight;
  
  /// 菜单宽度
  final double menuWidth;
  
  /// 选项文本样式
  final TextStyle? itemTextStyle;
  
  /// 下拉菜单背景色
  final Color? dropdownColor;
  
  /// 是否启用
  final bool enabled;

  /// 是否使用固定边框颜色
  final bool useFixedBorder;

  /// 固定边框颜色
  final Color fixedBorderColor;

  const KPTextFieldDropDown({
    Key? key,
    required this.items,
    this.value,
    required this.labelText,
    this.hintText = '',
    this.onChanged,
    this.selectedBorderColor = const Color(0xFFFF8500), // 橙色作为选中状态颜色
    this.unselectedBorderColor = const Color(0xFFE0E0E0), // 灰色作为非选中状态颜色
    this.prefixIcon,
    this.suffixIcon,
    this.menuMaxHeight = 300,
    this.menuWidth = 200,
    this.itemTextStyle,
    this.dropdownColor,
    this.enabled = true,
    this.useFixedBorder = false, // 默认不使用固定边框颜色
    this.fixedBorderColor = const Color(0xFF858A99), // 默认固定边框颜色为蓝色
  }) : super(key: key);

  @override
  State<KPTextFieldDropDown> createState() => _KPTextFieldDropDownState();
}

class _KPTextFieldDropDownState extends State<KPTextFieldDropDown> {
  /// 文本控制器
  late TextEditingController _controller;
  
  /// 焦点节点
  final FocusNode _focusNode = FocusNode();
  
  /// 是否处于选中状态（菜单弹出期间）
  bool _isSelected = false;
  
  /// 是否已经选择了选项
  bool _hasSelected = false;
  
  /// 层链接，用于定位下拉菜单
  final LayerLink _layerLink = LayerLink();
  
  /// 覆盖条目
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    // 初始化控制器，设置初始文本
    _controller = TextEditingController(
      text: widget.value != null ? widget.value!.name : widget.hintText
    );
    // 如果初始值不为null，标记为已选择
    _hasSelected = widget.value != null;
    // 添加焦点监听
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void didUpdateWidget(KPTextFieldDropDown oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当value变化时更新文本
    if (widget.value != oldWidget.value) {
      _controller.text = widget.value != null ? widget.value!.name : widget.hintText;
      _hasSelected = widget.value != null;
    }
  }

  /// 焦点变化监听
  void _onFocusChange() {
    setState(() {
      _isSelected = _focusNode.hasFocus;
    });

    if (_focusNode.hasFocus) {
      _showDropdownMenu();
    }
  }

  /// 显示下拉菜单
  void _showDropdownMenu() {
    // 如果组件禁用，则不显示菜单
    if (!widget.enabled) return;
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideDropdownMenu,
        child: Stack(
          children: [
            Positioned(
              width: widget.menuWidth,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0, size.height + 2),
                child: Material(
                  color: widget.dropdownColor ?? Colors.white,
                  elevation: 4,
                  borderRadius: BorderRadius.circular(6),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: widget.menuMaxHeight,
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: widget.items.length,
                      itemBuilder: (context, index) {
                        final item = widget.items[index];
                        return InkWell(
                          onTap: () {
                            _selectItem(item);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            alignment: Alignment.centerLeft,
                            child: Text(
                              item.name,
                              style: widget.itemTextStyle ?? const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF323843),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 隐藏下拉菜单
  void _hideDropdownMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    // 取消焦点
    _focusNode.unfocus();
    
    // 更新选中状态（但保持_hasSelected状态）
    setState(() {
      _isSelected = false;
    });
  }

  /// 选择一个选项
  void _selectItem(DropdownItem item) {
    setState(() {
      _hasSelected = true;
      _controller.text = item.name;
    });
    
    // 调用回调函数
    widget.onChanged?.call(item);
    
    // 隐藏下拉菜单
    _hideDropdownMenu();
  }

  @override
  Widget build(BuildContext context) {
    // 只确定边框颜色，保持边框宽度一致
    Color borderColor;
    const double borderWidth = 1.0; // 所有状态下边框宽度都保持为1.0
    
    if (widget.useFixedBorder) {
      // 使用固定边框颜色
      borderColor = widget.fixedBorderColor;
    } else if (_isSelected || _hasSelected) {
      // 菜单弹出期间或已选择选项 - 选中状态
      borderColor = widget.selectedBorderColor;
    } else if (!widget.enabled) {
      // 禁用状态
      borderColor = widget.unselectedBorderColor.withOpacity(0.5);
    } else {
      // 默认状态
      borderColor = widget.unselectedBorderColor;
    }
    
    return CompositedTransformTarget(
      link: _layerLink,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.enabled ? () {
            if (!_focusNode.hasFocus) {
              _focusNode.requestFocus();
            }
          } : null,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            // 移除这里的边框设置，只保留TextField的边框，避免重影
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              // 移除border属性
            ),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              readOnly: true, // 设为只读，防止键盘弹出
              enabled: widget.enabled,
              decoration: InputDecoration(
                labelText: widget.labelText,
                hintText: widget.hintText,
                // 根据状态调整标签颜色
                labelStyle: TextStyle(
                  color: widget.useFixedBorder 
                    ? widget.fixedBorderColor 
                    : (_isSelected || _hasSelected 
                      ? widget.selectedBorderColor 
                      : Colors.grey[600]),
                  fontSize: 14,
                ),
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.suffixIcon ?? IconButton(
                  icon: Icon(
                    Icons.arrow_drop_down,
                    color: widget.useFixedBorder 
                      ? widget.fixedBorderColor 
                      : (_isSelected || _hasSelected 
                        ? widget.selectedBorderColor 
                        : Colors.grey[600]),
                  ),
                  onPressed: widget.enabled ? () {
                    if (!_focusNode.hasFocus) {
                      _focusNode.requestFocus();
                    } else {
                      _hideDropdownMenu();
                    }
                  } : null,
                ),
                // 下面设置边框样式，所有状态下保持相同的边框粗细，只改变颜色
                // 默认边框样式
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: borderColor, width: borderWidth),
                  borderRadius: BorderRadius.circular(6),
                ),
                // 启用状态的边框样式
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: borderColor, width: borderWidth),
                  borderRadius: BorderRadius.circular(6),
                ),
                // 获得焦点时的边框样式
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: borderColor, width: borderWidth),
                  borderRadius: BorderRadius.circular(6),
                ),
                // 禁用状态的边框样式
                disabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: borderColor, width: borderWidth),
                  borderRadius: BorderRadius.circular(6),
                ),
                // 内容填充
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF323843),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _overlayEntry?.remove();
    super.dispose();
  }
}
