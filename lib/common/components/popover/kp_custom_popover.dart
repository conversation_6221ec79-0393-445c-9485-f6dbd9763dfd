import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

///用于一些不固定内容宽高的气泡（暂时只支持上方弹出）
class KPCustomPopover {
  static OverlayEntry? _currentEntry;

  static AnimationController? aniController;
  static Animation<double>? animation;

  static void show({
    required BuildContext context,
    required BuildContext targetContext,
    required GlobalKey contentKey,
    required Widget contentWidget,
    Color? barrierColor = Colors.transparent,
    VoidCallback? onDismiss,
    double offset = 8,
    EdgeInsetsGeometry padding = const EdgeInsets.all(12),
  }) {
    hide(); // 先关闭已有气泡

    // 获取目标 widget 的 RenderBox
    final RenderBox targetBox = targetContext.findRenderObject() as RenderBox;
    // 获取 Overlay 的 RenderBox
    final RenderBox overlayBox = Overlay.of(context).context.findRenderObject() as RenderBox;

    // 目标 widget 相对于屏幕的坐标
    final Offset targetOffset = targetBox.localToGlobal(Offset.zero);
    // Overlay 相对于屏幕的坐标
    final Offset overlayOffset = overlayBox.localToGlobal(Offset.zero);

    final Rect targetBoxRect = targetBox.semanticBounds;

    // 计算相对于 Overlay 的 top  (8是箭头高度)
    final double top = targetOffset.dy - overlayOffset.dy - offset -padding.vertical-8;
    // 计算目标组件距离右边界的距离
    final double right = overlayBox.size.width - (targetOffset.dx - overlayOffset.dx + targetBox.size.width);

    // final notifier = ValueNotifier<Offset>(Offset(rightInOverlay,top));
    final notifier = ValueNotifier<double>(top);

    final overlay = Overlay.of(context);

    _currentEntry = OverlayEntry(
      builder: (ctx) {
        return StatefulBuilder(
          builder: (context, setState) {
            aniController = AnimationController(
              duration: const Duration(milliseconds: 200),
              vsync: Navigator.of(context),
            )..forward();
            animation = CurvedAnimation(parent: aniController!, curve: Curves.easeIn);

            return ValueListenableBuilder(
              valueListenable: notifier,
              builder: (_, newOffset, __) {
                return Stack(
                  children: [
                    Positioned.fill(
                      child: GestureDetector(
                        onTap: () {
                          aniController!.reverse().then((_) {
                            hide();
                            if (onDismiss != null) onDismiss();
                          });
                        },
                        child: Container(color: barrierColor),
                      ),
                    ),
                    Positioned(
                      right: right,
                      top: newOffset,
                      child: FadeTransition(
                        opacity: animation!,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              padding: padding,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                color: KPColors.iconGrayPrimary,
                                boxShadow: const [
                                  BoxShadow(color: Color(0x0d000000), offset: Offset(0, 6), blurRadius: 30, spreadRadius: 5),
                                  BoxShadow(color: Color(0x0a000000), offset: Offset(0, 16), blurRadius: 24, spreadRadius: 2),
                                  BoxShadow(color: Color(0x14000000), offset: Offset(0, 8), blurRadius: 10, spreadRadius: -5),
                                ],
                              ),
                              child: contentWidget,
                            ),
                            // 三角形
                            Container(
                              margin: const EdgeInsets.only(right: 20),
                              child: CustomPaint(
                                size: const Size(16, 8),
                                painter: TrianglePainter(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
    overlay.insert(_currentEntry!);
    //在overlay插入后，获取内容 widget 的尺寸并更新位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取内容 widget 的 RenderBox
      final RenderBox contentBox = contentKey.currentContext!.findRenderObject() as RenderBox;
      final Rect contentRect = contentBox.semanticBounds;
      notifier.value = notifier.value-contentRect.height;
    });
  }

  static void hide() {
    _currentEntry?.remove();
    _currentEntry = null;
    aniController?.dispose();
    aniController = null;
  }
}

class BubbleLayoutDelegate extends SingleChildLayoutDelegate {
  final GlobalKey targetKey;

  BubbleLayoutDelegate({
    required this.targetKey,
  });

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    return BoxConstraints.loose(constraints.biggest);
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    final RenderBox targetBox = targetKey.currentContext!.findRenderObject() as RenderBox;
    final targetPosition = targetBox.localToGlobal(Offset.zero);
    final targetSize = targetBox.size;

    double dx = targetPosition.dx + targetSize.width / 2 - childSize.width / 2;
    double dy = targetPosition.dy + targetSize.height;
    return Offset(dx, dy);
  }

  @override
  bool shouldRelayout(covariant BubbleLayoutDelegate oldDelegate) {
    return targetKey != oldDelegate.targetKey;
  }
}

class TrianglePainter extends CustomPainter {
  TrianglePainter();

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = KPColors.iconGrayPrimary;
    final borderPaint = Paint()
      ..color = KPColors.iconGrayPrimary
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;

    final path = Path();

    path.moveTo(0, 0);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width, 0);

    path.close();

    // 绘制填充
    canvas.drawPath(path, paint);

    // 绘制边框
    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}