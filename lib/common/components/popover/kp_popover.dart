import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/kp_number_keyboard.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class KPPopover {
  /// 弹出气泡
  ///
  /// @param context 构建上下文
  /// @param width	内容宽度（包含padding，实际宽度：width - paddingLeft - paddingRight）
  /// @param height	内容高度（包含padding，实际高度：height - paddingTop - paddingBottom）
  /// @param content 显示内容
  /// @param contentWidget	自定义内容
  /// @param offset	偏移，默认值 4
  /// @param theme 弹出气泡主题
  /// @param placement 浮层出现位置
  /// @param showArrow 是否显示浮层箭头，默认值 false
  /// @param arrowSize 箭头大小，默认值 8
  /// @param padding	内容内边距，默认值 EdgeInsets.all(0)
  /// @param closeOnClickOutside	点击外部空白区域是否关闭
  /// @param overlayColor	覆盖颜色
  /// @param onTap	点击事件
  /// @param onLongTap	长按事件
  /// @param completeAction	选择完成事件
  static void showPopover({
    required BuildContext context,
    double? width,
    double? height,
    Widget? contentWidget,
    String? content,
    bool? showArrow = false,
    double offset = 4,
    TDPopoverTheme? theme = TDPopoverTheme.light,
    bool closeOnClickOutside = true,
    TDPopoverPlacement? placement = TDPopoverPlacement.bottomLeft,
    double arrowSize = 8,
    EdgeInsetsGeometry? padding = const EdgeInsets.all(0),
    Color? overlayColor,
    OnTap? onTap,
    OnLongTap? onLongTap,
    Function(dynamic value)? onComplete,
  }) {
    assert(content != null || contentWidget != null, 'content 和 contentWidget至少传一个');
    TDPopover.showPopover(
      showArrow: showArrow,
      arrowSize: arrowSize,
      context: context,
      content: content,
      padding: padding,
      width: width,
      height: height,
      contentWidget: contentWidget,
      offset: offset,
      placement: placement,
      theme: theme,
      closeOnClickOutside: closeOnClickOutside,
      overlayColor: overlayColor,
      onTap: onTap,
      onLongTap: onLongTap,
    ).then((value) {
      if (onComplete == null) return;
      onComplete(value);
    });
  }

  ///以 Popover 形式弹出的数字键盘
  static void showNumberKeyboard({
    required BuildContext context,
    required String title,
    required String hintText,
    required final Function(double?) onConfirmPressed,
    double offset = 0,
    double? initValue,
    TDPopoverPlacement? placement,
    bool isAutoClamp = false,
    bool showDot = false,
  }) {
    final renderBox = context.findRenderObject() as RenderBox?;
    double distanceToBottom = 0;
    if (renderBox != null && renderBox.hasSize) {
      final position = renderBox.localToGlobal(Offset.zero);
      final screenHeight = MediaQuery.of(context).size.height;
      final widgetBottom = position.dy + renderBox.size.height;
      distanceToBottom = screenHeight - widgetBottom;
    }
    showPopover(
      context: context,
      width: 360,
      height: 352,
      overlayColor: Colors.transparent,
      offset: offset,
      placement: distanceToBottom>352?TDPopoverPlacement.bottom:TDPopoverPlacement.left,
      contentWidget: Container(
        width: 360,
        height: 352,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: KPColors.fillGrayLightLightest,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 6,
              spreadRadius: 2,
              offset: const Offset(0, 2),
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 2,
              spreadRadius: 0,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: KPNumberKeyboard(
          buttonWidth: 101,
          buttonHeight: 54,
          title: title,
          hintText: hintText,
          value: initValue,
          showBottomButton: false,
          onConfirmPressed: onConfirmPressed,
          isAutoClamp: isAutoClamp,
          showDot: showDot,
        ),
      ),
    );
  }
}