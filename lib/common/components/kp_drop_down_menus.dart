import 'package:flutter/material.dart';

/// 通用下拉框组件
///
/// 示例:
/// ```dart
/// KPDropdown<String>(
///   value: selectedValue,
///   items: ['选项1', '选项2', '选项3'],
///   onChanged: (value) {
///     setState(() {
///       selectedValue = value;
///     });
///   },
///   hint: '请选择',
///   width: 200,
/// )
/// ```
class KPDropDownMenus<T> extends StatefulWidget {
  final T? value;
  final List<T> items;
  final ValueChanged<T?>? onChanged;
  final String? hint;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final TextStyle? textStyle;
  final String Function(T)? displayText;
  final String Function(T)? selectedTextBuilder;

  const KPDropDownMenus({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.hint,
    this.width,
    this.height,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 4,
    this.textStyle,
    this.displayText,
    this.selectedTextBuilder,
  });

  @override
  State<KPDropDownMenus<T>> createState() => _KPDropdownState<T>();
}

class _KPDropdownState<T> extends State<KPDropDownMenus<T>> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isMenuOpen = false;

  void _toggleMenu() {
    if (_isMenuOpen) {
      _hideMenu();
    } else {
      _showMenu();
    }
  }

  void _showMenu() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _hideMenu,
        child: Stack(
          children: [
            Positioned(
              width: 130,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0, size.height + 2),
                child: Material(
                  color: widget.backgroundColor ?? Colors.white,
                  elevation: 4,
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  child: Container(
                    constraints: const BoxConstraints(
                      maxHeight: 300,
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: widget.items.length,
                      itemBuilder: (context, index) {
                        final item = widget.items[index];
                        return InkWell(
                          onTap: () {
                            widget.onChanged?.call(item);
                            _hideMenu();
                          },
                          child: Container(
                            padding: widget.padding ??
                                const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            child: Text(
                              widget.displayText?.call(item) ?? item.toString(),
                              style: widget.textStyle,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() => _isMenuOpen = true);
  }

  void _hideMenu() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() => _isMenuOpen = false);
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: _toggleMenu,
        child: Container(
          width: widget.width,
          height: widget.height,
          padding: widget.padding ??
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? Colors.white,
            border: Border.all(
              color: widget.borderColor ?? const Color(0xFFE7E8EB),
            ),
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Text(
                  widget.value != null
                      ? (widget.selectedTextBuilder?.call(widget.value as T)
                      ?? widget.displayText!(widget.value as T))
                      : widget.hint ?? '',
                  style: widget.textStyle,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                _isMenuOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }
}