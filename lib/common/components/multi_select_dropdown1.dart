import 'package:flutter/material.dart';

class MultiSelectDropdown1 extends StatefulWidget {
  final String title;
  final List<String> items;
  final List<String> selectedItems;
  final Function(List<String>) onChanged;
  final double? menuHeight;
  final InputDecoration? decoration;
  final Color? primaryColor;
  final bool allowSearch;

  const MultiSelectDropdown1({
    super.key,
    required this.title,
    required this.items,
    required this.selectedItems,
    required this.onChanged,
    this.menuHeight = 250,
    this.decoration,
    this.primaryColor,
    this.allowSearch = true,
  });

  @override
  State<MultiSelectDropdown1> createState() => _MultiSelectDropdownState();
}

class _MultiSelectDropdownState extends State<MultiSelectDropdown1> {
  final TextEditingController _searchController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  final FocusNode _focusNode = FocusNode();
  OverlayEntry? _overlayEntry;
  late List<String> _filteredItems;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus && _overlayEntry == null) {
      _showOverlay();
    }
  }

  void _showOverlay() {
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, size.height + 5),
          child: Material(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              constraints: BoxConstraints(maxHeight: widget.menuHeight ?? 250),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (widget.allowSearch)
                    Padding(
                      padding: const EdgeInsets.all(12),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: '搜索...',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          prefixIcon: Icon(Icons.search, color: Colors.grey[500]),
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            _filteredItems = widget.items
                                .where((item) => item.toLowerCase()
                                .contains(value.toLowerCase()))
                                .toList();
                          });
                        },
                      ),
                    ),
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (widget.selectedItems.contains(item)) {
                                widget.selectedItems.remove(item);
                              } else {
                                widget.selectedItems.add(item);
                              }
                              widget.onChanged(widget.selectedItems);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: widget.selectedItems.contains(item)
                                  ? primaryColor.withOpacity(0.1)
                                  : Colors.transparent,
                            ),
                            child: Row(
                              children: [
                                Checkbox(
                                  value: widget.selectedItems.contains(item),
                                  onChanged: (bool? selected) {
                                    setState(() {
                                      if (selected == true) {
                                        widget.selectedItems.add(item);
                                      } else {
                                        widget.selectedItems.remove(item);
                                      }
                                      widget.onChanged(widget.selectedItems);
                                    });
                                  },
                                  activeColor: primaryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  item,
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _searchController.clear();
    _overlayEntry?.remove();
    _overlayEntry = null;
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if (_overlayEntry == null) {
                _showOverlay();
              } else {
                _hideOverlay();
              }
            },
            child: Focus(
              focusNode: _focusNode,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _overlayEntry != null
                        ? primaryColor
                        : Colors.grey[300]!,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ...widget.selectedItems.map((item) => Chip(
                          label: Text(
                            item,
                            style: const TextStyle(fontSize: 14),
                          ),
                          backgroundColor: primaryColor.withOpacity(0.1),
                          deleteIconColor: primaryColor,
                          onDeleted: () {
                            setState(() {
                              widget.selectedItems.remove(item);
                              widget.onChanged(widget.selectedItems);
                            });
                          },
                        )),
                        if (widget.selectedItems.isEmpty)
                          Text(
                            '请选择',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 14,
                            ),
                          ),
                      ],
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Icon(
                        _overlayEntry != null
                            ? Icons.arrow_drop_up
                            : Icons.arrow_drop_down,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _searchController.dispose();
    _hideOverlay();
    super.dispose();
  }
}