import 'package:flutter/cupertino.dart';
import '../../assets/assets.gen.dart';

class KPRefreshButton extends StatefulWidget {
  final Key? controllerKey;

  const KPRefreshButton({this.controllerKey}) : super(key: controller<PERSON><PERSON>);

  @override
  KPRefreshButtonState createState() => KPRefreshButtonState(); // 改成公有 State 类
}

class KPRefreshButtonState extends State<KPRefreshButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  bool _isRotating = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
  }

  void start() {
    if (!_isRotating) {
      _controller.repeat(); // 持续旋转
      _isRotating = true;
    }
  }

  void stop() {
    if (_isRotating) {
      _controller.stop();
      _controller.reset(); // 回到初始角度
      _isRotating = false;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Assets.images.refreshCw.svg(width: 16, height: 16),
    );
  }
}
