import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

import 'no_emoji_input_formatter.dart';

class KPRemark extends StatefulWidget {
  final String? title;
  final List<String> remarkList;
  final String hintText;
  final bool? isContentEmpty;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final ValueChanged<List<String>>? onRemarkChanged;
  final bool isShowAsterisk;
  final int maxLength;
  final Key? remarkFormKey;
  final Color remarkChipBackgroundColor;
  final Color remarkChipSelectedColor;
  final List<TextInputFormatter>? inputFormatters;
  final TextStyle? titleTextStyle;
  final TextStyle? asteriskTextStyle;
  final TextStyle? remarkChipTextStyle;
  final RoundedRectangleBorder? remarkChipShape;
  final InputDecoration? inputDecoration;
  final MaxLengthEnforcement maxLengthEnforcement;
  final String formBuilderFieldName;
  final bool isNeedDecorationLabel;
  final String? inputErrorText;
  final Function()? onTextFieldTap;
  final List<String>? initialSelectedTags;

  const KPRemark({
    super.key,
    this.title,
    this.onRemarkChanged,
    this.controller,
    this.focusNode,
    this.remarkFormKey,
    this.inputFormatters,
    this.titleTextStyle,
    this.asteriskTextStyle,
    this.remarkChipTextStyle,
    this.remarkChipShape,
    this.inputDecoration,
    this.maxLength = 64,
    this.remarkList = const [],
    this.isShowAsterisk = false,
    this.remarkChipBackgroundColor = Colors.white,
    this.remarkChipSelectedColor = KPColors.fillBrandLightest,
    this.maxLengthEnforcement = MaxLengthEnforcement.enforced,
    this.isNeedDecorationLabel = false,
    required this.formBuilderFieldName,
    required this.hintText,
    this.isContentEmpty = false,
    this.inputErrorText,
    this.onTextFieldTap,
    this.initialSelectedTags,
  });

  @override
  State<KPRemark> createState() => _KPRemarkState();
}

class _KPRemarkState extends State<KPRemark> {
  late GlobalKey<FormBuilderState> _formKey;
  
  @override
  void initState() {
    super.initState();
    _formKey = widget.remarkFormKey as GlobalKey<FormBuilderState>? ?? GlobalKey<FormBuilderState>();

    debugPrint('🔧 KPRemark 初始化 - initialSelectedTags: ${widget.initialSelectedTags}');

    // 🔧 如果有初始选中的标签，在下一帧设置到表单字段中
  // 🔧 如果有初始选中的标签，在下一帧设置到表单字段中
  if (widget.initialSelectedTags != null && widget.initialSelectedTags!.isNotEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final field = _formKey.currentState?.fields[widget.formBuilderFieldName];
      if (field != null) {
        field.didChange(widget.initialSelectedTags!);
        // debugPrint('🔧 ✅ KPRemark 初始化备注标签字段值: ${widget.initialSelectedTags}');
      } else {
        // debugPrint('🔧 ❌ KPRemark 找不到表单字段: ${widget.formBuilderFieldName}');
      }
    });
  }
  }

  @override
  void didUpdateWidget(KPRemark oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 🔧 关键修复：当 initialSelectedTags 发生变化时，更新表单字段的值
    if (widget.initialSelectedTags != oldWidget.initialSelectedTags) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final field = _formKey.currentState?.fields[widget.formBuilderFieldName];
        if (field != null) {
          field.didChange(widget.initialSelectedTags ?? []);
          // print('🔧 更新备注标签字段值: ${widget.initialSelectedTags}');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final FocusNode textInputFocusNode = widget.focusNode ?? FocusNode();
    final TextEditingController remarkEditController =
        widget.controller ?? TextEditingController();
    final List<TextInputFormatter> textInputFormatters =
        widget.inputFormatters ?? [NoEmojiInputFormatter()];
        
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: widget.title ?? "",
                style: widget.titleTextStyle ??
                    KPFontStyle.headingXSmall
                        .copyWith(color: KPColors.textGrayPrimary),
              ),
              TextSpan(
                text: widget.isShowAsterisk ? " *" : "",
                style: widget.asteriskTextStyle ??
                    KPFontStyle.headingXSmall
                        .copyWith(color: KPColors.textRedDefault),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        FormBuilder(
          key: _formKey,
          initialValue: {
            widget.formBuilderFieldName: widget.initialSelectedTags ?? [],
          },
          child: FormBuilderField<List<String>>(
            name: widget.formBuilderFieldName,
            builder: (FormFieldState<List<String>> field) {
              return Wrap(
                spacing: 8,
                runSpacing: 4,
                children: widget.remarkList.map((remark) {
                  final isSelected = field.value?.contains(remark) ?? false;
                  return ChoiceChip(
                    label: Text(
                      remark,
                      style: widget.remarkChipTextStyle ??
                          KPFontStyle.headingXSmall.copyWith(
                            color: isSelected
                                ? KPColors.textBrandDefault
                                : KPColors.textGrayPrimary,
                          ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      final currentValue = List<String>.from(field.value ?? []);
                      if (selected) {
                        currentValue.add(remark);
                      } else {
                        currentValue.remove(remark);
                      }
                      field.didChange(currentValue);
                      widget.onRemarkChanged?.call(currentValue);
                      print('🔧 备注标签选择变化: $currentValue');
                    },
                    backgroundColor: widget.remarkChipBackgroundColor,
                    selectedColor: widget.remarkChipSelectedColor,
                    shape: widget.remarkChipShape ??
                        RoundedRectangleBorder(
                          side: BorderSide(
                            color: isSelected
                                ? KPColors.borderBrandDefault
                                : KPColors.borderGrayLightDark,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    showCheckmark: false,
                  );
                }).toList(),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          focusNode: textInputFocusNode,
          controller: remarkEditController,
          maxLength: widget.maxLength,
          maxLengthEnforcement: widget.maxLengthEnforcement,
          onTap: widget.onTextFieldTap,
          buildCounter: (
            BuildContext context, {
            required int currentLength,
            required bool isFocused,
            int? maxLength,
          }) =>
              null,
          decoration: widget.inputDecoration ??
              InputDecoration(
                labelText: widget.isNeedDecorationLabel ? widget.hintText : null,
                border: _getOutlineInputBorder(
                    color: KPColors.borderGrayLightDarkest),
                enabledBorder: _getOutlineInputBorder(
                    color: widget.isContentEmpty!?KPColors.textRedDefault: KPColors.borderGrayLightDarkest),
                focusedBorder:
                    _getOutlineInputBorder(color: KPColors.borderBrandDefault),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                hintText: widget.hintText,
                hintStyle: TextStyle(
                  fontSize: 16,
                  color: widget.isContentEmpty!?KPColors.textRedDefault:KPColors.textGraySecondary,
                ),
              ),
          inputFormatters: textInputFormatters,
        ),
        Visibility(
          visible: widget.isContentEmpty!&&widget.inputErrorText!=null,
          child: Padding(
            padding: const EdgeInsets.only(top: 4,left: 16),
            child: Text(
              widget.inputErrorText??'',
              style: const TextStyle(
                color: KPColors.textRedDefault,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  OutlineInputBorder _getOutlineInputBorder(
      {required Color color, double borderWith = 1, double borderRadius = 4}) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: color, width: borderWith),
      borderRadius: BorderRadius.circular(borderRadius),
    );
  }
}