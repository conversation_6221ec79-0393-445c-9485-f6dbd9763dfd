import 'package:flutter/material.dart';

import 'package:flutter/material.dart';

class KPBadge extends StatelessWidget {
  final Widget child;
  final int? value;
  final Color? color;
  final double offsetX;
  final double offsetY;

  const KPBadge({
    super.key,
    required this.child,
    this.value,
    this.color = Colors.red,
    this.offsetX = 0,
    this.offsetY = 0,
  });

  @override
  Widget build(BuildContext context) {
    final hasValue = value != null && value! > 0;
    final displayText =
        hasValue ? (value! > 99 ? '99+' : value.toString()) : null;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        if (hasValue)
          Positioned(
            right: offsetX,
            top: offsetY,
            child: Container(
              alignment: Alignment.center,
              padding: displayText!.length == 1
                  ? EdgeInsets.zero
                  : const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: displayText.length == 1
                  ? BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    )
                  : BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(20),
                    ),
              constraints: const BoxConstraints(
                minHeight: 16,
                minWidth: 16,
              ),
              child: Text(
                displayText,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
