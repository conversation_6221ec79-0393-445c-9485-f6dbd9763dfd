import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';

/// KPDialog 类
/// 提供各种对话框显示功能的静态方法集合
/// 基于 TDesign Flutter 库中的对话框组件进行封装，简化对话框的使用方式
class KPDialog {
  ///使用案例
  ///KPDialog.showExitConfirmDialog(
  ///                           context: context,
  ///                          title: context.locale.loginOut,
  ///                           confirmBtnText: context.locale.confirm,
  ///                           cancelBtnText: context.locale.cancel,
  ///                           confirmAction: () {
  ///                             // 用户点击确认按钮后的操作
  ///                           },
  ///                           cancelAction: () {
  ///                             // 用户点击取消按钮后的操作
  ///                           }
  ///                          );
  /// 显示退出确认对话框
  ///
  /// 该方法创建一个模态对话框，用于确认用户是否要退出应用。
  /// 对话框包含一个标题、一个图标和两个按钮（取消和确认）。
  /// 支持国际化，当用户切换语言时，对话框文本会自动更新。
  ///
  /// @param context 构建上下文，用于显示对话框和访问主题
  /// @param title 对话框标题文本，默认为国际化的"退出"
  /// @param titleKey 使用特定的国际化key，优先级高于title。这是一个函数，接收BuildContext参数并返回String，
  ///                 用于在对话框显示时动态获取最新的国际化文本，解决语言切换后文本不更新的问题
  /// @param confirmBtnText 确认按钮文本，默认为本地化的"确认"
  /// @param cancelBtnText 取消按钮文本，默认为本地化的"取消"
  /// @param confirmAction 确认按钮点击回调函数，当用户点击确认按钮时执行
  /// @param cancelAction 取消按钮点击回调函数，当用户点击取消按钮时执行
  static void showExitConfirmDialog({
    required BuildContext context,
    String? title,
    String Function(BuildContext)? titleKey,
    String? confirmBtnText,
    String? cancelBtnText,
    Function()? confirmAction,
    Function()? cancelAction,
  }) {
    // 使用showGeneralDialog显示模态对话框
    // barrierDismissible: false 表示点击对话框外部不会关闭对话框
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      // pageBuilder回调用于构建对话框内容
      // 注意：这里使用dialogContext而不是外部传入的context
      // 这样可以确保在对话框显示时获取最新的国际化文本
      pageBuilder: (BuildContext dialogContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        // 使用Dialog和ConstrainedBox来控制对话框的大小
        return Dialog(
          // 取消Dialog默认的内边距
          insetPadding: EdgeInsets.zero,
          // 使用ConstrainedBox限制对话框的大小
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: 313, // 固定宽度为313像素
              maxHeight: 212, // 固定高度为212像素
              minWidth: 313,
              minHeight: 212,
            ),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16), //圆角
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min, // 垂直方向上最小化
                children: [
                  // 标题部分，包含图标和文本
                  Padding(
                    padding: const EdgeInsets.fromLTRB(24, 24, 24, 24),
                    child: Column(
                      children: [
                        // 显示警告图标
                        Assets.images.iconAlertCircle.svg(width: 48, height: 48),
                        const SizedBox(height: 10),
                        // 标题文本
                        // 优先使用titleKey函数获取文本（如果提供）
                        // 其次使用title参数（如果提供）
                        // 最后使用默认的国际化文本
                        Text(
                          titleKey != null
                              ? titleKey(dialogContext)
                              : (title ?? dialogContext.locale.loginOut),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  // 底部按钮
                  Padding(
                    padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                    child: Row(
                      children: [
                        // 取消按钮
                        Expanded(
                          child: ElevatedButton(
                            // 点击取消按钮时关闭对话框并执行cancelAction回调
                            onPressed: () {
                              Navigator.of(dialogContext).pop();
                              if (cancelAction != null) {
                                cancelAction();
                              }
                            },
                            // 设置按钮样式
                            style: ElevatedButton.styleFrom(
                              elevation: 0, // 取消阴影
                              // 使用主题中的灰色作为背景色
                              backgroundColor: dialogContext.theme.grayColor7,
                              // 设置按钮形状为圆角矩形
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              // 设置按钮内边距
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            // 按钮文本
                            // 优先使用cancelBtnText参数（如果提供）
                            // 否则使用默认的国际化"取消"文本
                            child: Text(
                              cancelBtnText ?? dialogContext.locale.cancel,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        // 按钮之间的间距
                        const SizedBox(width: 12),
                        // 确认按钮
                        Expanded(
                          child: ElevatedButton(
                            // 点击确认按钮时关闭对话框并执行confirmAction回调
                            onPressed: () {
                              Navigator.of(dialogContext).pop();
                              if (confirmAction != null) {
                                confirmAction();
                              }
                            },
                            // 设置按钮样式
                            style: ElevatedButton.styleFrom(
                              elevation: 0, // 取消阴影
                              // 使用黑色作为背景色
                              backgroundColor: Colors.black,
                              overlayColor: Colors.white,
                              // 设置按钮形状为圆角矩形
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              // 设置按钮内边距
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            // 按钮文本
                            // 优先使用confirmBtnText参数（如果提供）
                            // 否则使用默认的国际化"确认"文本
                            child: Text(
                              confirmBtnText ?? dialogContext.locale.confirm,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 显示确认对话框，只有一个确认按钮的简单对话框
  ///
  /// @param context 构建上下文，用于显示对话框
  /// @param title 对话框标题文本
  /// @param content 对话框内容文本
  /// @param confirmTitle 确认按钮文本，如果不提供则使用本地化的"确认"文本
  static void showConfirmDialog({
    required BuildContext context,
    String? title,
    String? content,
    String? confirmTitle,
  }) {
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDConfirmDialog(
          title: title,
          content: content,
          buttonText: confirmTitle ?? KPLocale.of(context).confirm,
        );
      },
    );
  }

  /// 显示警告对话框，包含两个按钮（通常为取消和确认）,点击左侧返回 false 右侧返回 true
  ///
  /// @param context 构建上下文，用于显示对话框
  /// @param title 对话框标题文本
  /// @param content 对话框内容文本
  /// @param leftBtnTitle 左侧按钮文本，默认为"取消"
  /// @param rightBtnTitle 右侧按钮文本，默认为"确认"
  /// @param leftAction 左侧按钮点击回调函数
  /// @param rightAction 右侧按钮点击回调函数
  /// @param contentMaxHeight 内容区域最大高度，限制内容区域高度
  /// @param contentWidget 自定义内容组件，可以替代纯文本内容，提供更丰富的显示
  static Future<bool> showAlertDialog({
    required BuildContext context,
    required String title,
    String? content,
    String? leftBtnTitle,
    String? rightBtnTitle,
    Function()? leftAction,
    Function()? rightAction,
    Color? leftBtnBgColor,
    Color? rightBtnBgColor,
    Widget? contentWidget,
  }) async{
    final result = await showGeneralDialog(
        context: context,
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return Dialog(
            child: Container(
              width: 353,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(KPRadius.xxl),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
                  ),
                  const SizedBox(height: 12),
                  // 优先显示 contentWidget
                  if (contentWidget != null)
                    contentWidget
                  else if (content != null)
                    Text(
                      content,
                      textAlign: TextAlign.center,
                      style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
                    ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: (){
                          Navigator.of(context).pop(false);
                          if(leftAction!=null){
                            leftAction();
                          }
                        },
                        child: Container(
                          width: 146,
                          height: 48,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: leftBtnBgColor??KPColors.fillGrayLightLight,
                            borderRadius: BorderRadius.circular(KPRadius.m),
                          ),
                          child: Text(
                            leftBtnTitle??context.locale.cancelCaps,
                            style: KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayPrimary,),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: (){
                          if(rightAction!=null){
                            rightAction();
                          }
                        },
                        child: Container(
                          width: 146,
                          height: 48,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: rightBtnBgColor?? KPColors.fillRedNormal,
                            borderRadius: BorderRadius.circular(KPRadius.m),
                          ),
                          child: Text(
                            rightBtnTitle??context.locale.confirmCaps,
                            style: KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayInverse,),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
    return result == true;
  }

  /// 显示可以自动调整位置的警告对话框
  /// 主要用于处理键盘弹出时的情况，当键盘弹出时对话框会自动上移以避免被键盘遮挡
  ///
  /// @param context 构建上下文，用于显示对话框
  /// @param title 对话框标题文本
  /// @param content 对话框内容文本
  /// @param leftBtnTitle 左侧按钮文本，默认为"取消"
  /// @param rightBtnTitle 右侧按钮文本，默认为"确认"
  /// @param leftAction 左侧按钮点击回调函数
  /// @param rightAction 右侧按钮点击回调函数
  /// @param contentMaxHeight 内容区域最大高度，限制内容区域高度
  /// @param contentWidget 自定义内容组件，可以替代纯文本内容
  /// @param isNeedAutoTop 是否需要根据键盘自动上移，默认为false。实际上此参数目前并未使用，
  ///                      对话框总是会根据键盘高度自动调整位置
  static void showAlertDialogAutoTop({
    required BuildContext context,
    String? title,
    String? content,
    String? leftBtnTitle,
    String? rightBtnTitle,
    Function()? leftAction,
    Function()? rightAction,
    double? contentMaxHeight,
    Widget? contentWidget,
    //加一行判断是否需要根据自动上移,默认false
    bool isNeedAutoTop = false,
  }) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      // 允许点击背景关闭对话框
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      // 背景遮罩颜色
      transitionDuration: const Duration(milliseconds: 200),
      // 过渡动画持续时间
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        // 使用Builder获取最新的context，以便获取精确的键盘高度信息
        return Builder(
          builder: (dialogContext) {
            // 获取键盘高度
            final keyboardHeight = MediaQuery.of(dialogContext).viewInsets.bottom;
            // 使用Transform.translate组件整体平移对话框
            return Transform.translate(
              offset: Offset(
                  0,
                  keyboardHeight > 0
                      ? -keyboardHeight * 0.4
                      : 0), // 如果键盘弹出，对话框上移键盘高度的0.4倍
              child: TDAlertDialog(
                title: title,
                content: content,
                contentMaxHeight: contentMaxHeight ?? 0,
                contentWidget: contentWidget,
                leftBtn: TDDialogButtonOptions(
                  title: leftBtnTitle ?? KPLocale.of(context).cancel,
                  titleColor: Colors.black,
                  style: TDButtonStyle(backgroundColor: context.theme.grayColor7),
                  action: leftAction,
                ),
                rightBtn: TDDialogButtonOptions(
                  theme: TDButtonTheme.defaultTheme,
                  title: rightBtnTitle ?? KPLocale.of(context).confirm,
                  titleColor: Colors.white,
                  style: TDButtonStyle(backgroundColor: Colors.black),
                  action: rightAction,
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 显示自定义对话框
  static void showCustomDialog({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    Color barrierColor = Colors.black54,
    bool useRootNavigator = true,
    bool useSafeArea = true,
    Alignment alignment = Alignment.center,
    double? maxWidth,
  }) {
    showGeneralDialog(
      context: context,
      pageBuilder: (context, animation, secondaryAnimation) {
        final Widget pageChild = Builder(builder: (context) {
          return Dialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
            alignment: alignment,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: maxWidth ?? 500,
              ),
              child: useSafeArea ? SafeArea(child: child) : child,
            ),
          );
        });
        return useSafeArea
            ? SafeArea(child: pageChild)
            : pageChild;
      },
      barrierDismissible: barrierDismissible,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: barrierColor,
      transitionDuration: const Duration(milliseconds: 200),
      useRootNavigator: useRootNavigator,
    );
  }
}