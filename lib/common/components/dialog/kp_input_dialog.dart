import 'package:flutter/material.dart';
import 'package:kpos/common/components/form_fields/kp_form_builder_text_field.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

/// 输入对话框类型枚举
///
/// 定义了不同类型的输入模式，影响用户输入和文本格式化行为
enum InputDialogType {
  /// 普通输入模式
  ///
  /// 无特殊格式化处理，普通文本输入
  normal,

  /// IP地址输入模式
  ///
  /// 当输入三个数字后自动添加小数点，便于IP地址输入
  /// 格式化为: xxx.xxx.xxx.xxx
  ipAddress,
}

/// 自定义输入对话框组件
///
/// 提供一个轻量级的、适应键盘的输入对话框，支持多种输入类型和自定义配置
/// 特点:
/// - 支持不同输入类型(普通、IP地址等)
/// - 自动调整以适应键盘弹出
/// - 响应式设计，适配不同设备尺寸
/// - 可自定义按钮和提示文本
class KpInputDialog {
  /// 显示输入对话框
  ///
  /// 该方法创建并显示一个模态输入对话框，通过回调函数返回用户输入结果
  ///
  /// 参数:
  /// * [context] - 构建上下文，用于显示对话框和访问主题
  /// * [initialValue] - 输入框的初始值，默认为空
  /// * [title] - 对话框标题，默认为"Enter IP address"
  /// * [hintText] - 提示文本，显示在输入框下方
  /// * [cancelButtonText] - 取消按钮文本，默认为"取消"
  /// * [confirmButtonText] - 确认按钮文本，默认为"确认"
  /// * [keyboardType] - 键盘类型，默认为数字键盘
  /// * [label] - 输入框标签文本，默认为"IP Address"
  /// * [type] - 输入类型，影响输入行为和格式化，默认为normal
  /// * [onConfirm] - 确认按钮点击回调，传入用户输入的文本
  /// * [onCancel] - 取消按钮点击回调
  static void show({
    required BuildContext context,
    String? initialValue,
    String? title = "Enter IP address",
    String? hintText = "How to find: Main POS → Settings → IP address",
    String? cancelButtonText = "取消",
    String confirmButtonText = "确认",
    TextInputType keyboardType = TextInputType.number,
    String? label = "IP Address",
    InputDialogType type = InputDialogType.normal,
    Function(String?)? onConfirm,
    Function()? onCancel,
  }) {
    // 生成唯一name，避免FormBuilderField冲突
    final uniqueName = "ip_${DateTime.now().microsecondsSinceEpoch}";

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return _KpInputDialogContent(
          initialValue: initialValue,
          title: title,
          hintText: hintText,
          cancelButtonText: cancelButtonText,
          confirmButtonText: confirmButtonText,
          keyboardType: keyboardType,
          label: label,
          type: type,
          name: uniqueName,
          onConfirm: onConfirm,
          onCancel: onCancel,
        );
      },
    );
  }

  /// 设置不同输入类型的处理逻辑
  ///
  /// 根据指定的输入类型，为文本控制器添加相应的监听器和处理逻辑
  ///
  /// 参数:
  /// * [type] - 输入对话框类型，决定应用哪种处理逻辑
  /// * [textController] - 要应用处理逻辑的文本控制器
  static void _setupInputTypeHandlers(
      InputDialogType type, TextEditingController textController) {
    // 只有IP地址类型需要特殊处理
    if (type == InputDialogType.ipAddress) {
      // 记录前一次的文本值，用于检测删除操作
      String previousText = textController.text;

      // 添加文本变化监听器
      textController.addListener(() {
        final currentText = textController.text;
        final selection = textController.selection;

        // 检查当前操作是否为删除(文本长度减少)
        bool isDeleting = currentText.length < previousText.length;

        if (isDeleting) {
          // 处理删除小数点的情况
          if (previousText.endsWith('.') &&
              selection.baseOffset == previousText.length - 1) {
            // 如果正在删除小数点，无需特殊处理
            // Flutter的TextField默认行为已足够
          }
        } else {
          // 非删除操作时，应用IP地址格式化逻辑
          _formatIpAddress(textController, currentText, selection);
        }

        // 更新前一次文本记录，为下次比较做准备
        previousText = textController.text;
      });
    }
  }

  /// IP地址格式化逻辑
  ///
  /// 实现IP地址的自动格式化：当输入三个数字后自动添加小数点
  ///
  /// 参数:
  /// * [controller] - 文本控制器，用于更新文本和光标位置
  /// * [text] - 当前文本内容
  /// * [selection] - 当前光标选择位置
  static void _formatIpAddress(
      TextEditingController controller, String text, TextSelection selection) {
    // 检查文本是否为空
    if (text.isNotEmpty) {
      // 按小数点分割IP地址段
      final segments = text.split('.');

      // IP地址最多有4段(xxx.xxx.xxx.xxx)
      if (segments.length < 4) {
        // 获取最后一段(当前正在输入的段)
        final lastSegment = segments.last;

        // 当最后一段达到3个数字，且光标在末尾时，自动添加小数点
        if (lastSegment.length == 3 &&
            !text.endsWith('.') &&
            selection.baseOffset == text.length) {
          // 添加小数点
          controller.text = '$text.';

          // 将光标移到新添加的小数点之后
          controller.selection =
              TextSelection.collapsed(offset: controller.text.length);
        }
      }
    }
  }

  /// 构建标题部分
  ///
  /// 根据提供的标题文本创建标题组件，如标题为null则返回空组件
  ///
  /// 参数:
  /// * [title] - 标题文本，可为null
  ///
  /// 返回:
  /// 标题Widget或空Widget(如果title为null)
  static Widget _buildTitle(String? title) {
    // 如果没有标题，返回一个空的占位组件
    if (title == null) return const SizedBox.shrink();

    // 构建带底部间距的标题文本
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建输入框部分
  ///
  /// 创建带有边框和标签的文本输入字段
  ///
  /// 参数:
  /// * [context] - 构建上下文
  /// * [controller] - 文本控制器，用于管理输入文本
  /// * [focusNode] - 焦点节点，用于管理输入焦点
  /// * [label] - 输入框标签文本
  /// * [keyboardType] - 键盘类型(数字、文本等)
  /// * [name] - 表单字段名称
  ///
  /// 返回:
  /// 包含KPFormBuilderTextField的Container组件
  static Widget _buildInputField(
      BuildContext context,
      TextEditingController controller,
      FocusNode focusNode,
      String? label,
      TextInputType keyboardType,
      String name,
      ) {
    // 创建带边框的输入框容器
    return Container(
      decoration: BoxDecoration(
        // 添加边框
        border: Border.all(color: const Color(0xFFE6E7EB)),
        // 圆角边框
        borderRadius: BorderRadius.circular(6),
      ),
      // 使用自定义表单组件
      child: KPFormBuilderTextField(
        name: name, // 使用唯一 name
        focusNode: focusNode, // 关联焦点节点
        controller: controller, // 关联文本控制器
        // 创建标签文本
        label: Text(
          label ?? "IP Address", // 使用提供的标签或默认值
          style:
          TextStyle(fontSize: 16, color: context.theme.grayColor2 // 使用主题颜色
          ),
        ),
        keyboardType: keyboardType, // 设置键盘类型
        autofocus: true, // 自动获取焦点
      ),
    );
  }

  /// 构建提示文本部分
  ///
  /// 创建带有适当样式和边距的提示文本
  ///
  /// 参数:
  /// * [context] - 构建上下文，用于访问主题颜色
  /// * [hintText] - 提示文本内容，可为null
  ///
  /// 返回:
  /// 提示文本Widget或空Widget(如果hintText为null)
  static Widget _buildHintText(BuildContext context, String? hintText) {
    // 如果没有提示文本，返回空组件
    if (hintText == null) return const SizedBox.shrink();

    // 构建带边距的提示文本
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 20),
      child: Text(
        hintText,
        style: TextStyle(
          fontSize: 12, // 较小的字体大小
          color: context.theme.grayColor5, // 使用浅色，降低视觉层级
        ),
      ),
    );
  }

  /// 构建按钮部分
  ///
  /// 创建包含取消和确认按钮的按钮组
  ///
  /// 参数:
  /// * [context] - 对话框上下文，用于导航操作
  /// * [controller] - 文本控制器，用于获取输入文本
  /// * [cancelButtonText] - 取消按钮文本，如为null则不显示取消按钮
  /// * [confirmButtonText] - 确认按钮文本
  /// * [onConfirm] - 确认按钮点击回调
  /// * [onCancel] - 取消按钮点击回调
  ///
  /// 返回:
  /// 按钮组Widget，始终使用双按钮布局
  static Widget _buildButtons(
      BuildContext context,
      TextEditingController controller,
      String? cancelButtonText,
      String confirmButtonText,
      Function(String?)? onConfirm,
      Function()? onCancel,
      ) {
    // 始终使用双按钮布局 - 取消和确认按钮
    return Row(
      children: [
        // 取消按钮
        Expanded(
          child: TextButton(
            onPressed: () {
              // 关闭对话框
              Navigator.of(context).pop();
              // 执行取消回调
              if (onCancel != null) {
                onCancel();
              }
            },
            style: TextButton.styleFrom(
              backgroundColor: const Color(0xFFF5F5F5), // 浅灰色背景
              padding: const EdgeInsets.symmetric(vertical: 12), // 垂直内边距
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6), // 轻微圆角
              ),
            ),
            child: Text(
              cancelButtonText ?? "取消",
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333), // 深灰色文字
              ),
            ),
          ),
        ),
        const SizedBox(width: 12), // 按钮之间的间距
        // 确认按钮
        Expanded(
          child: Material(
            child: ElevatedButton(
              onPressed: () {
                // 获取输入文本
                final inputText = controller.text;
                // 关闭对话框
                Navigator.of(context).pop();
                // 执行确认回调
                if (onConfirm != null) {
                  onConfirm(inputText);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1E1E1E), // 黑色背景
                foregroundColor: Colors.white, // 白色文字
                padding: const EdgeInsets.symmetric(vertical: 12), // 垂直内边距
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6), // 轻微圆角
                ),
              ),
              child: Text(confirmButtonText,style: const TextStyle(fontSize: 16,fontWeight: FontWeight.bold)),
            ),
          ),
        ),
      ],
    );
  }
}

// 对话框内容 StatefulWidget，管理 controller/focusNode 生命周期
class _KpInputDialogContent extends StatefulWidget {
  final String? initialValue;
  final String? title;
  final String? hintText;
  final String? cancelButtonText;
  final String confirmButtonText;
  final TextInputType keyboardType;
  final String? label;
  final InputDialogType type;
  final String name;
  final Function(String?)? onConfirm;
  final Function()? onCancel;

  const _KpInputDialogContent({
    super.key,
    this.initialValue,
    this.title,
    this.hintText,
    this.cancelButtonText,
    required this.confirmButtonText,
    required this.keyboardType,
    this.label,
    required this.type,
    required this.name,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<_KpInputDialogContent> createState() => _KpInputDialogContentState();
}

class _KpInputDialogContentState extends State<_KpInputDialogContent> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    KpInputDialog._setupInputTypeHandlers(widget.type, _controller);
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.shortestSide >= 600;
    final dialogWidth = size.shortestSide > 376 ? 376.0 : size.width * 0.85;
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: size.height * 0.5,
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                KpInputDialog._buildTitle(widget.title),
                KpInputDialog._buildInputField(
                  context,
                  _controller,
                  _focusNode,
                  widget.label,
                  widget.keyboardType,
                  widget.name,
                ),
                KpInputDialog._buildHintText(context, widget.hintText),
                KpInputDialog._buildButtons(
                  context,
                  _controller,
                  widget.cancelButtonText,
                  widget.confirmButtonText,
                  widget.onConfirm,
                  widget.onCancel,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
