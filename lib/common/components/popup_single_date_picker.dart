import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:kpos/common/extension/build_context_extension.dart';

class PopupDatePicker {
  static Future<DateTime?> show(
      BuildContext context, {
        DateTime? initialDate,
        Offset? anchorPoint,
        double margin = 4.0,
      }) async {
    final DateTime now = DateTime.now();
    final DateTime selectedDate = initialDate ?? now;

    final result = await showDialog<DateTime>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        if (anchorPoint != null) {
          final screenSize = MediaQuery.of(context).size;
          const double dialogWidth = 320.0;
          const double dialogHeight = 410.0;

          double left = anchorPoint.dx;
          if (left + dialogWidth > screenSize.width) {
            left = screenSize.width - dialogWidth - 16;
          }
          if (left < 16) left = 16;

          double top = anchorPoint.dy + margin - 16*2 + 2;
          if (top + dialogHeight > screenSize.height) {
            top = anchorPoint.dy - dialogHeight - margin;
          }

          return Stack(
            children: [
              Positioned.fill(
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(color: Colors.transparent),
                ),
              ),
              Positioned(
                left: left,
                top: top,
                child: Material(
                  elevation: 4,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: dialogWidth,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: _PopupDatePickerDialog(
                      initialDate: selectedDate,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return _PopupDatePickerDialog(
          initialDate: selectedDate,
        );
      },
    );

    return result;
  }
}

class _PopupDatePickerDialog extends StatefulWidget {
  final DateTime initialDate;

  const _PopupDatePickerDialog({
    required this.initialDate,
  });

  @override
  State<_PopupDatePickerDialog> createState() => _PopupDatePickerDialogState();
}

class _PopupDatePickerDialogState extends State<_PopupDatePickerDialog> {
  late DateTime _selectedDate;
  late DateRangePickerController _controller;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _controller = DateRangePickerController();
    _controller.selectedDate = _selectedDate;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child:  Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 300,
              child: SfDateRangePicker(
                backgroundColor: Colors.white,
                controller: _controller,
                view: DateRangePickerView.month,
                selectionMode: DateRangePickerSelectionMode.single,
                monthViewSettings: const DateRangePickerMonthViewSettings(
                  firstDayOfWeek: 1,
                ),
                monthFormat: 'MMMM',
                headerStyle: const DateRangePickerHeaderStyle(
                  textAlign: TextAlign.center,
                  backgroundColor: Colors.white,
                  textStyle: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                showNavigationArrow: true,
                selectionColor: Colors.orange,
                enablePastDates: true,
                todayHighlightColor: Colors.transparent,
                monthCellStyle: DateRangePickerMonthCellStyle(
                    todayTextStyle: const TextStyle(color: Colors.black87),
                    todayCellDecoration: BoxDecoration(
                        color: Colors.transparent,
                        border: Border.all(color: Colors.transparent, width: 0)
                    )
                ),
                onSelectionChanged: (args) {
                  if (args.value is DateTime) {
                    setState(() {
                      _selectedDate = args.value as DateTime;
                    });
                  }
                },
                navigationDirection: DateRangePickerNavigationDirection.horizontal,
                allowViewNavigation: true,
                selectionShape: DateRangePickerSelectionShape.circle,
                selectionRadius: 20,
              ),
            ),
            Container(
              color: Colors.white,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      context.locale.cancel,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(_selectedDate),
                    child: Text(
                      context.locale.confirm,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}