import 'package:flutter/material.dart';
import 'package:kpos/common/components/responsive_center.dart';

import '../utils/device_util.dart';

class ResponsiveCenterContainer extends StatelessWidget {
  const ResponsiveCenterContainer({super.key, required this.child,this.contentWidth = 440, this.padding});

  final Widget child;

  final double contentWidth;

  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    final effectivePadding =
        padding ?? (DeviceUtil.isMobile() ? const EdgeInsets.symmetric(horizontal: 20) : const EdgeInsets.only(top: 60));
    return ResponsiveCenter(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final double maxWidth = constraints.maxWidth;
          double width = maxWidth < DeviceUtil.tablet ? maxWidth : contentWidth;
          return Container(
            padding: effectivePadding,
            alignment: Alignment.topCenter,
            width: width,
            child: child,
          );
        },
      ),
    );
  }
}
