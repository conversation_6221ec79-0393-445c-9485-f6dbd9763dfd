import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class ListContainer extends Container {
  static const _defaultDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(
      Radius.circular(8),
    ),
  );

  ListContainer({
    super.key,
    super.padding,
    super.color,
    super.decoration = _defaultDecoration,
    super.margin,
    super.clipBehavior = Clip.antiAliasWithSaveLayer,
    List<Widget> children = const <Widget>[],
  }) : super(
          child: ListView(
            shrinkWrap: true,
            primary: false,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            children: children,
          ),
        );

  ListContainer.builder({
    super.key,
    super.padding = EdgeInsets.zero,
    final EdgeInsetsGeometry listPadding = EdgeInsets.zero,
    super.color,
    super.decoration = _defaultDecoration,
    super.margin,
    super.clipBehavior = Clip.antiAliasWithSaveLayer,
    int? itemCount,
    required NullableIndexedWidgetBuilder itemBuilder,
    double? itemExtent,
    ItemExtentBuilder? itemExtentBuilder,
    Widget? prototypeItem,
  }) : super(
            child: ListView.builder(
          shrinkWrap: true,
          primary: false,
          padding: listPadding,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: itemCount,
          itemBuilder: itemBuilder,
          itemExtent: itemExtent,
          itemExtentBuilder: itemExtentBuilder,
          prototypeItem: prototypeItem,
        ));

  ListContainer.separated(
      {super.key,
      super.padding = EdgeInsets.zero,
      super.color,
      super.decoration = _defaultDecoration,
      super.margin,
      super.clipBehavior = Clip.antiAliasWithSaveLayer,
      required int itemCount,
      required NullableIndexedWidgetBuilder itemBuilder,
      IndexedWidgetBuilder separatorBuilder = _defaultSeparatorBuilder})
      : super(
            child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.zero,
                primary: false,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: itemBuilder,
                separatorBuilder: separatorBuilder,
                itemCount: itemCount));

  static Widget _defaultSeparatorBuilder(BuildContext context, int index) {
    return const Divider(
      indent: 16,
      endIndent: 16,
    );
  }
}
