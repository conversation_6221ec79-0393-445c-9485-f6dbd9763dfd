import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter/material.dart';

enum _KPFilledButtonVariant { filled, tonal }

/// https://m3.material.io/components/buttons/guidelines#d6699132-3536-4cca-9460-79b019d6116c
/// FilledButton目前暂不支持通过主题分别配置 filled 和 tonal 的样式 https://github.com/flutter/flutter/issues/118063
/// 自定义 FilledButton 实现 filled 和 tonal 两种样式
/// 默认文本样式 TextStyle(fontSize: 16, height: 1.5, fontWeight: FontWeight.w600)
/// 高度48 默认padding EdgeInsets.symmetric(vertical: 12, horizontal: 16)
/// 高度40 compactPaddingStyle
class KPFilledButton extends FilledButton {
  static ButtonStyle compactPaddingStyle = FilledButton.styleFrom(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16));

  final _KPFilledButtonVariant _variant;

  const KPFilledButton({
    super.key,
    required super.onPressed,
    super.onLongPress,
    super.onHover,
    super.onFocusChange,
    super.style,
    super.focusNode,
    super.autofocus = false,
    super.clipBehavior = Clip.none,
    super.statesController,
    required super.child,
  }) : _variant = _KPFilledButtonVariant.filled;

  factory KPFilledButton.icon({
    Key? key,
    required VoidCallback? onPressed,
    VoidCallback? onLongPress,
    ValueChanged<bool>? onHover,
    ValueChanged<bool>? onFocusChange,
    ButtonStyle? style,
    FocusNode? focusNode,
    bool? autofocus,
    Clip? clipBehavior,
    WidgetStatesController? statesController,
    required Widget icon,
    required Widget label,
  }) = _KPFilledButtonWithIcon;

  const KPFilledButton.tonal({
    super.key,
    required super.onPressed,
    super.onLongPress,
    super.onHover,
    super.onFocusChange,
    super.style,
    super.focusNode,
    super.autofocus = false,
    super.clipBehavior = Clip.none,
    super.statesController,
    required super.child,
  }) : _variant = _KPFilledButtonVariant.tonal;

  factory KPFilledButton.tonalIcon({
    Key? key,
    required VoidCallback? onPressed,
    VoidCallback? onLongPress,
    ValueChanged<bool>? onHover,
    ValueChanged<bool>? onFocusChange,
    ButtonStyle? style,
    FocusNode? focusNode,
    bool? autofocus,
    Clip? clipBehavior,
    WidgetStatesController? statesController,
    required Widget icon,
    required Widget label,
  }) {
    return _KPFilledButtonWithIcon.tonal(
      key: key,
      onPressed: onPressed,
      onLongPress: onLongPress,
      onHover: onHover,
      onFocusChange: onFocusChange,
      style: style,
      focusNode: focusNode,
      autofocus: autofocus,
      clipBehavior: clipBehavior,
      statesController: statesController,
      icon: icon,
      label: label,
    );
  }

  @override
  ButtonStyle? themeStyleOf(BuildContext context) {
    final shape = RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    );

    const textStyle =
        TextStyle(fontSize: 16, height: 1.5, fontWeight: FontWeight.w600);

    const minimumSize = Size(64.0, 40.0);
    const maximumSize = Size.infinite;
    const padding = EdgeInsets.symmetric(vertical: 12, horizontal: 16);
    Color backgroundColor;
    Color disabledBackgroundColor;
    Color foregroundColor;
    Color disabledForegroundColor;

    switch (_variant) {
      case _KPFilledButtonVariant.filled:
        // backgroundColor = const Color(0xFFF97613);
        backgroundColor = Theme.of(context).primaryColor;
        disabledBackgroundColor = const Color(0xFFFFCB8F);
        foregroundColor = Colors.white;
        disabledForegroundColor = Colors.white;
      case _KPFilledButtonVariant.tonal:
        backgroundColor = const Color(0xFFF3f3f4);
        disabledBackgroundColor = const Color(0xFFF3F3F4);
        foregroundColor = const Color(0xFF323843);
        disabledForegroundColor = Colors.grey;
    }
    return FilledButton.styleFrom(
      backgroundColor: backgroundColor,
      disabledBackgroundColor: disabledBackgroundColor,
      foregroundColor: foregroundColor,
      disabledForegroundColor: disabledForegroundColor,
      textStyle: textStyle,
      shape: shape,
      maximumSize: maximumSize,
      minimumSize: minimumSize,
      padding: padding,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}

class _KPFilledButtonWithIcon extends KPFilledButton {
  _KPFilledButtonWithIcon({
    super.key,
    required super.onPressed,
    super.onLongPress,
    super.onHover,
    super.onFocusChange,
    super.style,
    super.focusNode,
    bool? autofocus,
    Clip? clipBehavior,
    super.statesController,
    required Widget icon,
    required Widget label,
  }) : super(
            autofocus: autofocus ?? false,
            clipBehavior: clipBehavior ?? Clip.none,
            child: _FilledButtonWithIconChild(icon: icon, label: label));

  _KPFilledButtonWithIcon.tonal({
    super.key,
    required super.onPressed,
    super.onLongPress,
    super.onHover,
    super.onFocusChange,
    super.style,
    super.focusNode,
    bool? autofocus,
    Clip? clipBehavior,
    super.statesController,
    required Widget icon,
    required Widget label,
  }) : super.tonal(
            autofocus: autofocus ?? false,
            clipBehavior: clipBehavior ?? Clip.none,
            child: _FilledButtonWithIconChild(icon: icon, label: label));

  @override
  ButtonStyle defaultStyleOf(BuildContext context) {
    final bool useMaterial3 = Theme.of(context).useMaterial3;
    final ButtonStyle buttonStyle = super.defaultStyleOf(context);
    final double defaultFontSize =
        buttonStyle.textStyle?.resolve(const <WidgetState>{})?.fontSize ?? 14.0;
    final double effectiveTextScale =
        MediaQuery.textScalerOf(context).scale(defaultFontSize) / 14.0;

    final EdgeInsetsGeometry scaledPadding = useMaterial3
        ? ButtonStyleButton.scaledPadding(
            const EdgeInsetsDirectional.fromSTEB(16, 0, 24, 0),
            const EdgeInsetsDirectional.fromSTEB(8, 0, 12, 0),
            const EdgeInsetsDirectional.fromSTEB(4, 0, 6, 0),
            effectiveTextScale,
          )
        : ButtonStyleButton.scaledPadding(
            const EdgeInsetsDirectional.fromSTEB(12, 0, 16, 0),
            const EdgeInsets.symmetric(horizontal: 8),
            const EdgeInsetsDirectional.fromSTEB(8, 0, 4, 0),
            effectiveTextScale,
          );
    return buttonStyle.copyWith(
      padding: WidgetStatePropertyAll<EdgeInsetsGeometry>(scaledPadding),
    );
  }
}

class _FilledButtonWithIconChild extends StatelessWidget {
  const _FilledButtonWithIconChild({required this.label, required this.icon});

  final Widget label;
  final Widget icon;

  @override
  Widget build(BuildContext context) {
    double scale = MediaQuery.textScalerOf(context).scale(1);
    final double gap =
        scale <= 1 ? 8 : lerpDouble(8, 4, math.min(scale - 1, 1))!;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[icon, SizedBox(width: gap), Flexible(child: label)],
    );
  }
}
