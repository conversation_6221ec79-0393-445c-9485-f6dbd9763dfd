// 文件：custom_button.dart
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

/// TDesign 按钮
class KPButton extends StatelessWidget {
  final String text;
  final TDButtonType type;
  final TDButtonSize size;
  final double? width;
  final double? height;
  final TDButtonEvent onPressed;
  final TDButtonEvent? onLongPress;
  final TextStyle? textStyle;
  final TextStyle? disableTextStyle;
  final TDButtonStyle? style;
  final TDButtonStyle? activeStyle;
  final TDButtonStyle? disableStyle;
  final TDButtonShape shape;
  final EdgeInsetsGeometry padding;
  final TDButtonTheme? theme;
  final Widget? child;
  final bool disabled;
  final bool isBlock;
  final IconData? icon;
  final Widget? iconWidget;
  final double? iconTextSpacing;
  final EdgeInsetsGeometry? margin;
  final TDButtonIconPosition? iconPosition;

  const KPButton({
    super.key,
    this.text = '',
    this.type = TDButtonType.fill,
    this.size = TDButtonSize.medium,
    this.width,
    this.height,
    required this.onPressed,
    this.onLongPress,
    this.textStyle,
    this.disableTextStyle,
    this.style,
    this.activeStyle,
    this.disableStyle,
    this.shape = TDButtonShape.rectangle,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.theme,
    this.child,
    this.disabled = false,
    this.isBlock = false,
    this.icon,
    this.iconWidget,
    this.iconTextSpacing,
    this.margin,
    this.iconPosition = TDButtonIconPosition.left,
  });

  @override
  Widget build(BuildContext context) {
    return TDButton(
      type: type,
      text: text,
      size: size,
      width: width,
      height: height,
      shape: shape,
      theme: theme,
      onTap: onPressed,
      onLongPress: onLongPress,
      textStyle: textStyle,
      disableTextStyle: disableTextStyle,
      disableStyle: disableStyle,
      activeStyle: activeStyle,
      style: style,
      padding: padding,
      disabled: disabled,
      isBlock: isBlock,
      icon: icon,
      iconWidget: iconWidget,
      iconTextSpacing: iconTextSpacing,
      margin: margin,
      iconPosition: iconPosition,
      child: child,
    );
  }
}
