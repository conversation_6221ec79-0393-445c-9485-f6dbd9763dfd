import 'package:flutter/material.dart';

/// 带通知红点的图标按钮
/// 
/// 用于显示带有右上角红点提示的图标按钮，通常用于表示有未读通知或提醒
class NotificationIconButton extends StatelessWidget {
  /// 上下文
  final BuildContext context;

  /// 图标
  final Widget icon;
  
  /// 是否显示通知红点
  final bool showNotification;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 通知红点大小
  final double dotSize;
  
  /// 通知红点颜色
  final Color dotColor;
  
  /// 通知红点位置 - 上边距
  final double dotTop;
  
  /// 通知红点位置 - 右边距
  final double dotRight;
  
  /// 图标按钮尺寸
  final double? iconSize;
  
  /// 图标按钮颜色
  final Color? iconColor;

  /// 构造函数
  const NotificationIconButton({
    Key? key,
    required this.context,
    required this.icon,
    this.onPressed,
    this.showNotification = false,
    this.dotSize = 8.0,
    this.dotColor = Colors.red, // 默认使用红色
    this.dotTop = 8.0,
    this.dotRight = 8.0,
    this.iconSize,
    this.iconColor,
  }) : super(key: key);

  /// 使用图标数据构建通知按钮
  /// 
  /// 便捷构造器，可直接传入 [IconData] 而非 Widget
  factory NotificationIconButton.fromIconData({
    Key? key,
    required BuildContext context,  
    required IconData iconData,
    VoidCallback? onPressed,
    bool showNotification = false,
    double dotSize = 8.0,
    Color dotColor = Colors.red,
    double dotTop = 8.0,
    double dotRight = 8.0,
    double? iconSize,
    Color? iconColor,
  }) {
    return NotificationIconButton(
      key: key,
      context: context,
      icon: Icon(iconData, size: iconSize, color: iconColor),
      onPressed: onPressed,
      showNotification: showNotification,
      dotSize: dotSize,
      dotColor: dotColor,
      dotTop: dotTop,
      dotRight: dotRight,
    );
  }
  
  /// 使用SVG图标构建通知按钮
  /// 
  /// 便捷构造器，可直接传入SVG Widget
  factory NotificationIconButton.fromSvg({
    Key? key,
    required BuildContext context,
    required Widget svgIcon,
    VoidCallback? onPressed,
    bool showNotification = false,
    double dotSize = 8.0,
    Color dotColor = Colors.red,
    double dotTop = 8.0,
    double dotRight = 8.0,
  }) {
    return NotificationIconButton(
      key: key,
      context: context,
      icon: svgIcon,
      onPressed: onPressed,
      showNotification: showNotification,
      dotSize: dotSize,
      dotColor: dotColor,
      dotTop: dotTop,
      dotRight: dotRight,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none, // 允许子组件溢出
      children: [
        IconButton(
          icon: icon,
          onPressed: onPressed,
          iconSize: iconSize,
          color: iconColor,
        ),
        if (showNotification)
          Positioned(
            top: dotTop,
            right: dotRight,
            child: Container(
              width: dotSize,
              height: dotSize,
              decoration: BoxDecoration(
                color: dotColor,
                shape: BoxShape.circle,
              ),
            ),
          ),
      ],
    );
  }
}


/**
 * @author:     luozh
 * @CreateDate: 2025年05月21日14:35:08
 * @description: 
 * showNotification - 控制红点是否显示
 * dotSize - 自定义红点大小
 * dotColor - 自定义红点颜色
 * dotTop和dotRight - 精确控制红点位置
 */

// // 基本用法
// NotificationIconButton.fromIconData(
//   iconData: Icons.notifications,
//   showNotification: true,
//   onPressed: () {
//     // 处理点击事件
//   },
// );

// // 使用自定义图标
// NotificationIconButton(
//   icon: Assets.images.yourIcon.svg(width: 24, height: 24),
//   showNotification: hasUnreadNotifications,
//   onPressed: handleNotification,
//   dotSize: 10.0,
//   dotColor: Colors.orange,
// );