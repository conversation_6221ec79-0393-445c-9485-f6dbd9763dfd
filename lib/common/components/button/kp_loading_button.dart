import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class KPLoadingButton extends ConsumerWidget {
  final VoidCallback onPressed;
  final String text;
  final bool isLoading;
  final bool isDisabled;
  final double? width;
  final double height;
  final Color backgroundColor;
  final Color textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final double borderRadius;
  final Widget? customLoader;
  final EdgeInsetsGeometry? padding;
  final Color disabledColor;

  const KPLoadingButton({
    super.key,
    required this.onPressed,
    required this.text,
    required this.isLoading,
    this.isDisabled = false,
    this.width,
    this.height = 48,
    this.backgroundColor = Colors.black,
    this.textColor = Colors.white,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w700,
    this.borderRadius = 8,
    this.customLoader,
    this.padding,
    this.disabledColor = Colors.black,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bool effectiveDisabled = isLoading || isDisabled;

    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        onPressed: effectiveDisabled ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          disabledBackgroundColor: disabledColor,
          padding: padding ?? const EdgeInsets.symmetric(horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading) ...[
          _buildLoader(),
          const SizedBox(width: 9),
        ],
        Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: fontSize,
            fontWeight: fontWeight,
          ),
        ),
      ],
    );
  }

  Widget _buildLoader() {
    return customLoader ??
        SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2.5,
            valueColor: AlwaysStoppedAnimation(textColor),
          ),
        );
  }
}
