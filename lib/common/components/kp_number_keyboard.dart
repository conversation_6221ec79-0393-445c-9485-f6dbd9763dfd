import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

// 数字键盘状态提供者
class NumberKeyboardState extends ChangeNotifier {
  String _numberValue = '';
  bool _showCursor = false;
  bool _showLimitError = false;
  bool _hasUserInput = false;

  String get numberValue => _numberValue;
  bool get showCursor => _showCursor;
  bool get showLimitError => _showLimitError;
  bool get hasUserInput => _hasUserInput;

  void setNumberValue(String value) {
    _numberValue = value;
    notifyListeners();
  }

  void toggleCursor() {
    _showCursor = !_showCursor;
    notifyListeners();
  }

  void setShowLimitError(bool value) {
    _showLimitError = value;
    notifyListeners();
  }

  void setHasUserInput(bool value) {
    _hasUserInput = value;
    notifyListeners();
  }
}

// 为每个组件实例创建单独的Provider
final numberKeyboardStateProvider = ChangeNotifierProvider.family.autoDispose<NumberKeyboardState, String>((ref, id) {
  return NumberKeyboardState();
});

class KPNumberKeyboard extends ConsumerStatefulWidget {
  final double? value;
  //最大值
  final double? maxValue;
  //最小值
  final double? minValue;
  final Function(double?) onConfirmPressed;
  final VoidCallback? onCancelPressed;
  final bool showDot;
  final bool showBottomButton;
  final double buttonWidth;
  final double buttonHeight;
  final double buttonSpacing;
  final String title;
  final String hintText;
  final String Function(BuildContext)? titleKey;
  //是否自动处理边界值,比如范围 1-999，输入0时自动变为1，输入1000时自动变为999
  final bool isAutoClamp;

  const KPNumberKeyboard({
    super.key,
    required this.buttonWidth,
    required this.buttonHeight,
    required this.title,
    required this.onConfirmPressed,
    this.buttonSpacing = 8,
    this.titleKey,
    this.hintText = '',
    this.onCancelPressed,
    this.showDot = false,
    this.showBottomButton = true,
    this.minValue,
    this.maxValue,
    this.value,
    this.isAutoClamp = false,
  });

  @override
  ConsumerState<KPNumberKeyboard> createState() => _KPNumberKeyboardState();
}

class _KPNumberKeyboardState extends ConsumerState<KPNumberKeyboard> {
  late Timer _cursorTimer;
  // 为每个实例创建唯一ID
  late final String _keyboardId = UniqueKey().toString();

  // 获取当前键盘的状态Provider
  NumberKeyboardState get _keyboardState => ref.read(numberKeyboardStateProvider(_keyboardId));

  //有限定范围时的值处理
  void _updateValue(String newValue) {
    final keyboardState = _keyboardState;
    
    if (widget.minValue == null || widget.maxValue == null) return;
    // 处理删除操作
    if (newValue.isEmpty) {
      keyboardState.setNumberValue('');
      return;
    }

    // 验证数字格式
    if (!RegExp(r'^(\d+)?\.?\d*$').hasMatch(newValue)) return;

    // 处理前导零
    if (newValue.startsWith('0') && newValue.length > 1 && !newValue.startsWith('0.')) {
      newValue = newValue.substring(1);
    }

    // 解析数值
    double? parsed = double.tryParse(newValue);
    if (parsed == null) return;

    // 应用范围限制
    final clamped = parsed.clamp(widget.minValue!, widget.maxValue!);

    if(widget.isAutoClamp){
      // 生成显示值（整数处理）
      String displayValue;
      if (clamped == parsed) {
        displayValue = newValue;
      } else {
        displayValue =
            clamped.toStringAsFixed(clamped == clamped.floor() ? 0 : 1);
      }
      // 处理最小边界（如输入0）
      if (parsed < widget.minValue!) {
        displayValue = widget.minValue!.toStringAsFixed(0);
      }
      keyboardState.setNumberValue(displayValue);
    }else{
      keyboardState.setNumberValue(newValue);
      if (clamped == parsed) {
        keyboardState.setShowLimitError(false);
      } else {
        //超出范围，提示
        keyboardState.setShowLimitError(true);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _cursorTimer = Timer.periodic(const Duration(milliseconds: 600), (timer) {
      if (mounted) {
        _keyboardState.toggleCursor();
      }
    });
    
    // 设置初始值
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.value != null) {
        final keyboardState = _keyboardState;
        keyboardState.setNumberValue("${widget.value?.toInt()}");
        keyboardState.setHasUserInput(true);
      }
    });
  }

  @override
  void deactivate() {
    if (!widget.showBottomButton) {
      final numberValue = _keyboardState.numberValue;
      widget.onConfirmPressed(
          numberValue.isEmpty ? null : double.parse(numberValue));
    }
    super.deactivate();
  }

  @override
  void dispose() {
    _cursorTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 3 * widget.buttonWidth + (widget.buttonSpacing * 2),
          color: Colors.white,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildNumberShow(),
              const SizedBox(height: 16),
              _buildKeyRow(['1', '2', '3']),
              const SizedBox(height: 8),
              _buildKeyRow(['4', '5', '6']),
              const SizedBox(height: 8),
              _buildKeyRow(['7', '8', '9']),
              const SizedBox(height: 8),
              _buildKeyRow(['.', '0', 'X']),
              if (widget.showBottomButton) const SizedBox(height: 16),
              if (widget.showBottomButton) _buildButtonRow(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNumberShow() {
    // 使用Consumer监听状态变化，通过_keyboardId确保监听的是当前实例的状态
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(numberKeyboardStateProvider(_keyboardId));
        final numberValue = state.numberValue;
        final showLimitError = state.showLimitError;
        final hasUserInput = state.hasUserInput;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 56, // 容器总高度
              decoration: BoxDecoration(
                border: Border.all(
                  color: (!hasUserInput || (numberValue.isNotEmpty && !showLimitError))
                      ? KPColors.iconBrandDefault
                      : KPColors.textRedDefault,
                  width: 4,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // 边框上方的文字标签
                  Positioned(
                    left: 16,
                    top: -10,
                    child: Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Text(
                        widget.titleKey != null
                            ? widget.titleKey!(context)
                            : widget.title,
                        style: TextStyle(
                          color: (!hasUserInput || (numberValue.isNotEmpty && !showLimitError))
                              ? KPColors.iconBrandDefault
                              : KPColors.textRedDefault,
                          fontSize: 12,
                          backgroundColor: Colors.white,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  // 输入内容区域
                  Container(
                    width: double.maxFinite,
                    height: 56,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Stack(
                      children: [
                        Visibility(
                          visible: numberValue.isEmpty&&widget.hintText.isNotEmpty,
                          child: Text(
                            widget.hintText,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Visibility(
                              visible: numberValue.isNotEmpty,
                              child: Text(
                                numberValue,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF323843),
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            //指示器
                            Visibility(
                              visible: state.showCursor,
                              child: Container(
                                width: 1,
                                height: 16,
                                color: KPColors.iconBrandDefault,
                                margin: const EdgeInsets.only(left: 2),
                              ),
                            ),
                            const Expanded(child: SizedBox()),
                            Visibility(
                              visible: showLimitError,
                              child: const Icon(
                                Icons.error,
                                color: KPColors.textRedDefault,
                                size: 24,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            //数值范围限制文字
            Visibility(
              visible: showLimitError,
              child: Padding(
                padding: const EdgeInsets.only(top: 4, left: 16),
                child: Text(
                  context.locale.numberKeyboardLimited(
                      "${!widget.showDot ? widget.minValue?.toInt() : widget.minValue}-${!widget.showDot ? widget.maxValue?.toInt() : widget.maxValue}",
                      widget.title),
                  style: const TextStyle(
                    fontSize: 12,
                    color: KPColors.textRedDefault,
                  ),
                ),
              ),
            ),
          ],
        );
      }
    );
  }

  Widget _buildKeyRow(List<String> numbers) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: numbers.map((number) => _buildKeyButton(number)).toList(),
    );
  }

  Widget _buildKeyButton(String value) {
    if (value == '.' && !widget.showDot) {
      return SizedBox(
        width: widget.buttonWidth,
        height: widget.buttonHeight,
      );
    }
    return KPButton(
      onPressed: () {
        final keyboardState = _keyboardState;
        final numberValue = keyboardState.numberValue;
        final showLimitError = keyboardState.showLimitError;
        
        if (value == 'X') {
          //删除操作
          if (numberValue.isEmpty) return;
          keyboardState.setNumberValue(
              numberValue.substring(0, numberValue.length - 1));
          if (numberValue.length == 1) {
            keyboardState.setShowLimitError(false); // 清除错误状态
          }
        } else {
          if (showLimitError && numberValue.isNotEmpty) return;
          keyboardState.setNumberValue(numberValue + value);
          // 标记用户已有输入
          keyboardState.setHasUserInput(true);
        }
        _updateValue(_keyboardState.numberValue);
      },
      width: widget.buttonWidth,
      height: widget.buttonHeight,
      padding: EdgeInsets.zero,
      child: Container(
        alignment: Alignment.center,
        child: value == 'X'
            ? Assets.images.iconDeleteBack.svg(
                width: 24,
                height: 24,
                fit: BoxFit.cover,
              )
            : Text(
                value,
                style: const TextStyle(
                  fontSize: 20,
                  color: Color(0xFF323843),
                  fontWeight: FontWeight.w700,
                ),
              ),
      ),
    );
  }

  Widget _buildButtonRow() {
    return SizedBox(
      width: 3 * widget.buttonWidth + (widget.buttonSpacing * 2),
      height: 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          //取消
          if (widget.onCancelPressed != null)
            Expanded(
              child: KPButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  if (widget.onCancelPressed != null) {
                    widget.onCancelPressed!();
                  }
                },
                width: double.maxFinite,
                height: 48,
                style: TDButtonStyle(
                  backgroundColor: const Color(0xFFF3F3F4),
                ),
                padding: EdgeInsets.zero,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    KPLocale.of(context).cancelCaps,
                    style: const TextStyle(
                      color: Color(0xFF323843),
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
          if (widget.onCancelPressed != null) const SizedBox(width: 8),
          //确认
          Expanded(
            child: KPButton(
              width: double.maxFinite,
              height: 48,
              onPressed: () {
                final numberValue = _keyboardState.numberValue;
                widget.onConfirmPressed(
                    numberValue.isEmpty ? null : double.parse(numberValue));
              },
              style: TDButtonStyle(
                backgroundColor: const Color(0xFF20232B),
              ),
              padding: EdgeInsets.zero,
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  KPLocale.of(context).confirmCaps,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
