import 'package:flutter/material.dart';

class KPGroupListView<G> extends StatelessWidget {
  final List<G> groups;
  final List<dynamic> Function(G group) itemsGetter;
  final Widget? Function(G group)? headerBuilder;
  final Widget? Function(G group)? footerBuilder;
  final Widget Function(dynamic item, G group, int index) itemBuilder;

  const KPGroupListView({
    super.key,
    required this.groups,
    required this.itemsGetter,
    this.headerBuilder,
    this.footerBuilder,
    required this.itemBuilder,
  });

  @override
  Widget build(BuildContext context) {
    final slivers = <Widget>[];

    for (final group in groups) {
      final items = itemsGetter(group);

      final header = headerBuilder?.call(group);
      if (header != null) {
        slivers.add(SliverToBoxAdapter(child: header));
      }

      slivers.add(SliverList(
        delegate: SliverChildBuilderDelegate(
              (context, index) => itemBuilder(items[index], group, index),
          childCount: items.length,
        ),
      ));

      final footer = footerBuilder?.call(group);
      if (footer != null) {
        slivers.add(SliverToBoxAdapter(child: footer));
      }
    }

    return CustomScrollView(slivers: slivers);
  }
}

