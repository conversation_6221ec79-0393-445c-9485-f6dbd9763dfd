import 'package:flutter/material.dart';
import 'package:kpos/common/constant/app_sizes.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../assets/assets.gen.dart';

class ExceptionStateView extends StatelessWidget {
  final Object exception;

  final VoidCallback? onRetryTap;

  final BoxConstraints? constraints;

  const ExceptionStateView(
      {super.key, required this.exception, this.onRetryTap, this.constraints});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      constraints: constraints,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.images.dataStateErrorGeneral.svg(width: 180, height: 160),
          gapH20,
          Text(exception.toString()),
          if (onRetryTap != null)
            gapH20,
            TDButton(
              text: context.locale.tryAgain,
              onTap: onRetryTap,
              width: 100,
              height: 44,
            )
        ],
      ),
    );
  }
}
