import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/routing/app_routing.dart';

class KPLoading extends StatelessWidget {
  final String? text;
  final double size;
  final Color? color;
  final TextStyle? textStyle;

  static bool _isShowing = false;

  const KPLoading({
    super.key,
    this.text,
    this.size = 24,
    this.color,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? const Color(0xFFA2A6B1);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(effectiveColor),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          text ?? context.locale.loadingText,
          style: textStyle ??
              KPFontStyle.bodyLarge.copyWith(
                color: KPColors.textGrayTertiary,
                fontSize: 18,
                decoration: TextDecoration.none,
              ),
        ),
      ],
    );
  }

  /// 显示全局 loading
  static void show({String? text}) {
    if (_isShowing) return;
    final context = rootNavigatorKey.currentContext;
    if (context == null) return;
    _isShowing = true;
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (_) => Center(child: KPLoading(text: text)),
    ).then((v) {
      _isShowing = false;
    });
  }

  /// 关闭 loading
  static void dismiss() {
    if (!_isShowing) return;
    final navigator = rootNavigatorKey.currentState;
    navigator?.pop();
    _isShowing = false;
  }
}
