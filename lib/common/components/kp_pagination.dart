import 'package:flutter/material.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../assets/assets.gen.dart';

import 'package:flutter/material.dart';

class KPPagination extends StatefulWidget {
  final int totalPage;
  final int selectedPage;
  final ValueChanged<int> onPageSelected;

  const KPPagination({
    super.key,
    required this.totalPage,
    required this.selectedPage,
    required this.onPageSelected,
  });

  @override
  _KPPaginationState createState() => _KPPaginationState();
}

class _KPPaginationState extends State<KPPagination> {
  late final ValueNotifier<int> selectedPageNotifier;

  int get totalGroup => (widget.totalPage / 10).ceil();

  int get currentGroup => ((selectedPageNotifier.value - 1) ~/ 10) + 1;

  @override
  void initState() {
    super.initState();
    selectedPageNotifier = ValueNotifier(widget.selectedPage);
  }

  @override
  void didUpdateWidget(covariant KPPagination oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedPage != widget.selectedPage) {
      selectedPageNotifier.value = widget.selectedPage;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: selectedPageNotifier,
      builder: (context, selectedIndex, _) {
        final int start = ((selectedIndex - 1) ~/ 10) * 10 + 1;
        final int end = (start + 9).clamp(1, widget.totalPage);

        List<Widget> children = [];
        if (start > 1) {
          children.add(_buildArrowButton(
            icon: Assets.images.chevronLeft.svg(width: 16,height: 16),
            onTap: () {
              int newPage = (start - 10).clamp(1, widget.totalPage);
              selectedPageNotifier.value = newPage;
              widget.onPageSelected(newPage);
            },
            withRightDivider: true,
            dividerColor: context.theme.grayColor6,
          ));
        }
        for (int i = start; i <= end; i++) {
          final isSelected = selectedIndex == i;
          children.add(Stack(
            children: [
              GestureDetector(
                onTap: () {
                  selectedPageNotifier.value = i;
                  widget.onPageSelected(i);
                },
                child: Container(
                  width: 48,
                  height: 48,
                  alignment: Alignment.center,
                  margin: EdgeInsets.symmetric(horizontal: 2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected
                        ? Border.all(color: context.theme.brandNormalColor, width: 1)
                        : null,
                  ),
                  child: Text(
                    '$i',
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected
                          ? context.theme.brandNormalColor
                          : Colors.black,
                      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
              if (i <= end)
                Positioned(
                  right: 0,
                  top: 14,
                  bottom: 14,
                  child: Container(
                    width: 1,
                    color: context.theme.grayColor6,
                  ),
                ),
            ],
          ));
        }

        if (end < widget.totalPage) {
          children.add(_buildArrowButton(
            icon: Assets.images.chevronRight.svg(width: 16,height: 16),
            onTap: () {
              int newPage = (start + 10).clamp(1, widget.totalPage);
              selectedPageNotifier.value = newPage;
              widget.onPageSelected(newPage);
            },
            withRightDivider: false,
          ));
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: children,
          ),
        );
      },
    );
  }

  Widget _buildArrowButton({
    required Widget icon,
    required VoidCallback onTap,
    bool withRightDivider = false,
    Color? dividerColor,
  }) {
    return Stack(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Container(
            width: 48,
            height: 48,
            alignment: Alignment.center,
            child: icon,
          ),
        ),
        if (withRightDivider)
          Positioned(
            right: 0,
            top: 14,
            bottom: 14,
            child: Container(
              width: 1,
              color: dividerColor ?? Colors.grey,
            ),
          ),
      ],
    );
  }
}


