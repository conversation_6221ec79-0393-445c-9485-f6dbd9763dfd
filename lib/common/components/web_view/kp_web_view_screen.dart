import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:kpos/common/components/web_view/kp_progress_bar_web_view.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class KPWebViewScreen extends StatefulWidget {
  const KPWebViewScreen({
    super.key,
    required this.url,
    this.defaultTitle,
    this.onComplete});

  final String url;
  final String? defaultTitle;
  final Function(String? title)? onComplete;


  @override
  State<KPWebViewScreen> createState() => _KPWebViewScreenState();
}

class _KPWebViewScreenState extends State<KPWebViewScreen> {
  late final _titleController = ValueNotifier<String?>(widget.defaultTitle);

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        backgroundColor: Colors.white,
        title: ValueListenableBuilder(
            valueListenable: _titleController,
            builder: (BuildContext context, String? value, Widget? child) {
              return TDText(value ?? "");
            }),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const TDDivider(height: 1),
          Expanded(
              child:KPProgressBarWebView(
                  url: widget.url,
                  onComplete: (title) {
                    _titleController.value = title;
                  },
              ),
          ),
        ],
      ),
    );
  }
}
