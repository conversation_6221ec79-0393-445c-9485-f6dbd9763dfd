import 'dart:math';

import 'package:flutter/material.dart';
import 'package:kpos/common/components/kp_linear_gradient.dart';
import 'package:webview_flutter/webview_flutter.dart';

class KPProgressBarWebView extends StatefulWidget {
  final String url;
  final Function(String title)? onComplete;

  const KPProgressBarWebView({super.key, required this.url, this.onComplete});

  @override
  State<KPProgressBarWebView> createState() => _KPProgressBarWebViewState();
}

class _KPProgressBarWebViewState extends State<KPProgressBarWebView> {
  final _progressController = ValueNotifier(0);

  final _controller = WebViewController();

  @override
  void initState() {
    _controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
          NavigationDelegate(onProgress: _onProgressChanged));
    print(widget.url);
    _controller.loadRequest(Uri.parse(widget.url));
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _progressController.dispose();
  }

  void _onProgressChanged(int progress) {
    if (context.mounted) {
      _progressController.value = progress;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          color: Colors.white,
          child: WebViewWidget(controller: _controller),
        ),
        Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            child: ValueListenableBuilder(
                valueListenable: _progressController,
                builder: (BuildContext context, int progress, Widget? child) {
                  if (progress >= 100) {
                    return const SizedBox();
                  } else {
                    return Container(
                      width: max(progress.toDouble(), 30) /
                          100.0 *
                          MediaQuery.of(context).size.width,
                      decoration: const BoxDecoration(
                          gradient: kpProgressLinearGradient,
                          borderRadius: BorderRadius.horizontal(
                              right: Radius.circular(2))),
                    );
                  }
                })),
      ],
    );
  }
}
