import 'package:flutter/material.dart';
import 'package:kpos/common/utils/device_util.dart';

class ResponsiveCenter extends StatelessWidget {
  const ResponsiveCenter(
      {super.key,
      this.padding = EdgeInsets.zero,
      required this.child});

  final EdgeInsetsGeometry padding;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: DeviceUtil.deviceWidth),
        child: Padding(padding: padding, child: child),
      ),
    );
  }
}

class ResponsiveSliverCenter extends StatelessWidget {
  const ResponsiveSliverCenter(
      {super.key,
      this.padding = EdgeInsets.zero,
      required this.child});

  final EdgeInsetsGeometry padding;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: ResponsiveCenter(padding: padding, child: child),
    );
  }
}