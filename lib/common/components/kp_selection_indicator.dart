import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

import '../../assets/assets.gen.dart';

/// **选择数量指示器组件**
///
/// 一个低耦合、高复用的数量指示器组件，用于显示当前选择状态和数量信息。
///
/// **用法示例**
/// ```dart
/// KPSelectionIndicator(
///   currentCount: currentCount,
///   lowerLimit: group.minSelections,
///   upperLimit: group.maxSelections,
///   autoHide: true, // 满足条件时自动隐藏
/// )
/// ```
class KPSelectionIndicator extends StatelessWidget {
  /// 当前选择的数量
  final int currentCount;

  /// 最低要求数量
  final int lowerLimit;

  /// 最高限制数量
  final int upperLimit;

  /// 是否在满足条件时自动隐藏
  final bool autoHide;

  /// 自定义成功状态颜色主题
  final KPSelectionIndicatorColorTheme? successTheme;

  /// 自定义警告状态颜色主题
  final KPSelectionIndicatorColorTheme? warningTheme;

  /// 图标尺寸
  final double iconSize;

  /// 文本样式
  final TextStyle? textStyle;

  /// 内边距
  final EdgeInsets padding;

  /// 边框圆角
  final double borderRadius;

  const KPSelectionIndicator({
    super.key,
    required this.currentCount,
    required this.lowerLimit,
    required this.upperLimit,
    this.autoHide = false,
    this.successTheme,
    this.warningTheme,
    this.iconSize = 16.0,
    this.textStyle,
    this.padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
    this.borderRadius = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    // 核心逻辑修正：满足下限就算有效，而不是必须等于上限
  final bool isValid = currentCount >= lowerLimit && 
                      (upperLimit <= 0 || currentCount <= upperLimit);

    // 如果设置为自动隐藏且当前状态有效，则不显示任何内容。
    if (autoHide && isValid) {
      return const SizedBox.shrink();
    }

    // 根据是否有效，获取对应的主题颜色配置。
    final KPSelectionIndicatorColorTheme currentTheme = _getCurrentTheme(isValid);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: currentTheme.backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: isValid
          ? _buildSuccessIndicator(currentTheme)
          : _buildWarningIndicator(currentTheme),
    );
  }

  /// 获取当前状态下的文本样式。
  TextStyle _getTextStyle(KPSelectionIndicatorColorTheme theme) {
    return textStyle ??
        KPFontStyle.bodyMedium.copyWith(
          color: theme.textColor,
          fontWeight: FontWeight.w500,
        );
  }

  /// 获取当前状态下的主题配置。
  KPSelectionIndicatorColorTheme _getCurrentTheme(bool isValid) {
    if (isValid) {
      return successTheme ?? KPSelectionIndicatorColorTheme.defaultSuccess();
    } else {
      return warningTheme ?? KPSelectionIndicatorColorTheme.defaultWarning();
    }
  }

  /// 构建成功状态指示器（图标+数字）。
  Widget _buildSuccessIndicator(KPSelectionIndicatorColorTheme theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 左边：对勾图标
        Assets.images.iconCheckCircleGreen.svg(
          width: iconSize,
          height: iconSize,
        ),
        const SizedBox(width: 4),
        // 右边：当前数量
        Text(
          '$currentCount',
          style: _getTextStyle(theme),
        ),
      ],
    );
  }

  /// 构建警告状态指示器（分数显示）。
  Widget _buildWarningIndicator(KPSelectionIndicatorColorTheme theme) {
    return Text(
      '$currentCount/$upperLimit',
      style: _getTextStyle(theme),
    );
  }
}

/// **指示器颜色主题**
///
/// 定义了指示器在不同状态下的文本和背景颜色。
class KPSelectionIndicatorColorTheme {
  final Color textColor;
  final Color backgroundColor;

  const KPSelectionIndicatorColorTheme({
    required this.textColor,
    required this.backgroundColor,
  });

  /// 默认成功主题
  factory KPSelectionIndicatorColorTheme.defaultSuccess() {
    return KPSelectionIndicatorColorTheme(
      textColor: KPColors.textGreenDefault,
      backgroundColor: KPColors.textGreenDefault.withAlpha(30),
    );
  }

  /// 默认警告主题
  factory KPSelectionIndicatorColorTheme.defaultWarning() {
    return KPSelectionIndicatorColorTheme(
      textColor: KPColors.textRedDefault,
      backgroundColor: KPColors.textRedDefault.withAlpha(30),
    );
  }
}