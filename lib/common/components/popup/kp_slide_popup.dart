import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class KPSlidePopup {
  /// 从屏幕的某个方向滑动弹出的Dialog框的路由
  ///
  /// @param context 构建上下文
  /// @param contentWidget 内容组件
  /// @param modalBarrierColor 蒙层颜色
  /// @param isDismissible	点击蒙层能否关闭，默认值 true
  /// @param modalBarrierFull	是否全屏显示蒙层，默认值 false
  /// @param slideTransitionFrom	设置从屏幕的哪个方向滑出，默认值SlideTransitionFrom.bottom
  /// @param modalWidth	弹出框宽度
  /// @param modalHeight	弹出框高度
  /// @param modalTop	弹出框顶部距离，默认值 0
  /// @param modalLeft 弹出框左侧距离，默认值 0
  /// @param open	打开前事件
  /// @param opened	打开后事件
  /// @param close	关闭前事件
  /// @param barrierClick	蒙层点击事件，仅在 modalBarrierFull 为false时触发
  /// @param focusMove 是否有输入框获取焦点时整体平移避免输入框被遮挡
  static void showSlidePopup({
    required BuildContext context,
    required Widget contentWidget,
    SlideTransitionFrom slideTransitionFrom = SlideTransitionFrom.bottom,
    Color? modalBarrierColor,
    Function()? barrierClick,
    bool modalBarrierFull = true,
    bool isDismissible = true,
    double? modalWidth,
    double? modalHeight,
    double? modalLeft = 0,
    double? modalTop = 0,
    Function()? open,
    Function()? close,
    Function()? opened,
    bool focusMove = false,
  }) {
    Navigator.of(context).push(
      TDSlidePopupRoute(
        modalBarrierColor: modalBarrierColor??TDTheme.of(context).fontGyColor2,
        slideTransitionFrom: slideTransitionFrom,
        barrierClick: barrierClick,
        modalBarrierFull: modalBarrierFull,
        isDismissible: isDismissible,
        modalWidth: modalWidth,
        modalHeight: modalHeight,
        modalLeft: modalLeft,
        modalTop: modalTop,
        open: open,
        close: close,
        opened: opened,
        focusMove: focusMove,
        builder: (context) {
          return contentWidget;
        },
      ),
    );
  }

  static OverlayEntry? _currentEntry;
  // == 新增开始 == 支持多层弹窗的数据结构
  static List<OverlayEntry> _entryStack = [];
  static List<AnimationController> _animationControllers = [];
  // == 新增结束 ==
  
  /// 从某个位置弹出的侧边弹窗
  ///@param targetWightWidth 基于目标组件宽度位移弹出
  ///@param contentWidget 内容
  ///@param maskColor 遮罩颜色
  static void showFromTarget({
    required BuildContext context,
    required Widget contentWidget,
    required double targetWidth,
    Color? maskColor,
    Duration duration = const Duration(milliseconds: 300),
    VoidCallback? onMaskTap,
    // == 新增开始 == 新增可选参数，默认false保持原有行为
    bool allowMultipleLayers = false, // 是否允许多层弹窗
    bool fullOverlap = false, // 新增：是否完全重叠
    // == 新增结束 ==
  }) {
    print('🔍 showFromTarget called');
  print('🔍 allowMultipleLayers: $allowMultipleLayers');
  print('🔍 当前栈状态 - _entryStack.length: ${_entryStack.length}, _currentEntry != null: ${_currentEntry != null}');

    // == 修改开始 == 根据参数决定使用哪种模式
    if (allowMultipleLayers) {
      _showFromTargetMultiLayer(
        context: context,
        contentWidget: contentWidget,
        targetWidth: targetWidth,
        maskColor: maskColor,
        duration: duration,
        onMaskTap: onMaskTap,
        fullOverlap: fullOverlap,
      );
    } else {
      _showFromTargetSingleLayer(
        context: context,
        contentWidget: contentWidget,
        targetWidth: targetWidth,
        maskColor: maskColor,
        duration: duration,
        onMaskTap: onMaskTap,
      );
    }
    // == 修改结束 ==
  }

  // == 新增开始 == 原有的单层弹窗逻辑（完全不变）
  static void _showFromTargetSingleLayer({
    required BuildContext context,
    required Widget contentWidget,
    required double targetWidth,
    Color? maskColor,
    Duration duration = const Duration(milliseconds: 300),
    VoidCallback? onMaskTap,
  }) {
    if (_currentEntry != null) return;

    final overlay = Overlay.of(context);
    final animationController = AnimationController(
      vsync: Navigator.of(context),
      duration: duration,
    );
    final animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));

    _currentEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              right: targetWidth,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () {
                  if (onMaskTap != null) onMaskTap();
                  animationController.reverse().then((_) {
                    _currentEntry?.remove();
                    _currentEntry = null;
                  });
                },
                child: FadeTransition(
                  opacity: animation,
                  child: Container(
                    color: maskColor ?? TDTheme.of(context).fontGyColor2,
                  ),
                ),
              ),
            ),
            Positioned(
              right: targetWidth,
              top: 0,
              bottom: 0,
              child: FadeTransition(
                opacity: animation,
                child: contentWidget,
              ),
            ),
          ],
        );
      },
    );

    overlay.insert(_currentEntry!);
    animationController.forward();
  }

  // 新的多层弹窗逻辑
  static void _showFromTargetMultiLayer({
    required BuildContext context,
    required Widget contentWidget,
    required double targetWidth,
    Color? maskColor,
    Duration duration = const Duration(milliseconds: 300),
    VoidCallback? onMaskTap,
    bool fullOverlap = false, // 新增参数
  }) {
    print('🔍 _showFromTargetMultiLayer called');
     print('🔍 添加前栈长度: ${_entryStack.length}');

    final overlay = Overlay.of(context);
    final animationController = AnimationController(
      vsync: Navigator.of(context),
      duration: duration,
    );
    
    final animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOut,
    ));

    // 计算弹窗偏移位置
    // final currentOffset = _entryStack.length * 20.0;
    // final currentTargetWidth = targetWidth + currentOffset;
    // 修改：根据 fullOverlap 参数决定是否偏移
  final currentOffset = fullOverlap ? 0.0 : _entryStack.length * 20.0;
  final currentTargetWidth = targetWidth + currentOffset;

    final overlayEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              right: currentTargetWidth,
              top: 0,
              bottom: 0,
              child: GestureDetector(
                onTap: () {
                  if(onMaskTap != null) onMaskTap();
                  _dismissTopMost();
                },
                child: FadeTransition(
                  opacity: animation,
                  child: Container(
                    color: (maskColor ?? TDTheme.of(context).fontGyColor2)
                        .withOpacity(0.3),
                  ),
                ),
              ),
            ),
            Positioned(
              right: currentTargetWidth,
              top: 0,
              bottom: 0,
              child: FadeTransition(
                opacity: animation,
                child: contentWidget,
              ),
            ),
          ],
        );
      },
    );

    _entryStack.add(overlayEntry);
    _animationControllers.add(animationController);

    print('🔍 添加后栈长度: ${_entryStack.length}');
  print('🔍 控制器数量: ${_animationControllers.length}');

    overlay.insert(overlayEntry);
    animationController.forward();
  }

  static void _dismissTopMost() {
    print('🔍 _dismissTopMost called');
  print('🔍 栈长度: ${_entryStack.length}');

    if (_entryStack.isEmpty) return;
    final topEntry = _entryStack.last;
    final topController = _animationControllers.last;
    
    topController.reverse().then((_) {
      topEntry.remove();
      _entryStack.removeLast();
      _animationControllers.removeLast();
      topController.dispose();
      print('🔍 _dismissTopMost 完成，剩余栈长度: ${_entryStack.length}');
    });
  }

///遍历之前的栈, 关闭所有弹窗
static void dismissAll() {
  print('🔍 dismissAll called');
  print('🔍 _entryStack.length: ${_entryStack.length}');
  print('🔍 _animationControllers.length: ${_animationControllers.length}');
  print('🔍 _currentEntry != null: ${_currentEntry != null}');
  
  // 先处理多层弹窗栈
  if (_entryStack.isNotEmpty) {
    // 复制栈内容
    final entries = List<OverlayEntry>.from(_entryStack);
    final controllers = List<AnimationController>.from(_animationControllers);
    
    // 立即清空栈，避免其他方法干扰
    _entryStack.clear();
    _animationControllers.clear();
    
    // 直接移除所有entry，不执行动画
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final controller = controllers[i];
      
      try {
        // 直接移除entry，不管动画状态
        if (entry.mounted) {
          entry.remove();
          print('🔍 强制移除entry[$i]成功');
        } else {
          print('🔍 entry[$i]已经被移除');
        }
      } catch (e) {
        print('🔍 移除entry[$i]错误: $e');
      }
      
      // 销毁controller时检查状态
      try {
        // 检查controller是否还有效
        if (controller.toString().contains('DISPOSED')) {
          print('🔍 controller[$i]已经被销毁，跳过');
        } else {
          controller.dispose();
          print('🔍 销毁controller[$i]成功');
        }
      } catch (e) {
        print('🔍 销毁controller[$i]错误: $e');
      }
    }
  }
  
  // 处理单层弹窗
  if (_currentEntry != null) {
    try {
      if (_currentEntry!.mounted) {
        _currentEntry!.remove();
        print('🔍 移除_currentEntry成功');
      }
      _currentEntry = null;
    } catch (e) {
      print('🔍 移除_currentEntry错误: $e');
    }
  }
  
  // 清理回调
  _onDismissCallbacks.clear();
  
  print('🔍 dismissAll 完成');
}
// == 新增结束 ==

  ///基于目标组件的弹窗返回
  static void dismissFromTarget() {
    print('🔍 dismissFromTarget called');
  print('🔍 当前状态 - _entryStack.length: ${_entryStack.length}, _currentEntry != null: ${_currentEntry != null}');
    // == 修改开始 == 保持向后兼容性
    // 优先处理多层弹窗
    if (_entryStack.isNotEmpty) {
      _dismissTopMost();
    } 
    // 兼容原有单层弹窗
    else if (_currentEntry != null) {
      _currentEntry?.remove();
      _currentEntry = null;
    }
    
    // 通知弹窗已关闭
    if (_onDismissCallbacks.isNotEmpty) {
      final callbacks = List<VoidCallback>.from(_onDismissCallbacks);
      _onDismissCallbacks.clear();
      for (final callback in callbacks) {
        callback();
      }
    }
    // == 修改结束 ==
  }
  
  // 添加全局关闭回调列表
  static final List<VoidCallback> _onDismissCallbacks = [];
  
  // 添加关闭回调
  static void addDismissCallback(VoidCallback callback) {
    _onDismissCallbacks.add(callback);
  }
  
  // 移除关闭回调
  static void removeDismissCallback(VoidCallback callback) {
    _onDismissCallbacks.remove(callback);
  }
}