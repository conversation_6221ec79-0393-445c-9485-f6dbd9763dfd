import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:kpos/common/components/dialog/kp_dialog.dart';
import 'package:kpos/common/constant/app_sizes.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';

class KPRefreshFooter extends Footer {
  KPRefreshFooter(
      {super.triggerOffset = 60,
      super.clamping = false,
      super.position = IndicatorPosition.behind,
      super.processedDuration = Duration.zero,
      super.spring,
      super.readySpringBuilder,
      super.springRebound,
      FrictionFactor? frictionFactor,
      super.safeArea,
      super.infiniteOffset = 60,
      super.hitOver,
      super.infiniteHitOver,
      super.hapticFeedback,
      super.triggerWhenRelease,
      super.maxOverOffset});

  @override
  Widget build(BuildContext context, IndicatorState state) {
    if (state.result == IndicatorResult.noMore) {
      return const _NoMoreFooterIndicator();
    }
    return _FooterLoadingIndicator(height: state.offset);
  }
}

class _FooterLoadingIndicator extends StatefulWidget {
  const _FooterLoadingIndicator({super.key, required this.height});

  final double height;

  @override
  State<_FooterLoadingIndicator> createState() =>
      _FooterLoadingIndicatorState();
}

class _FooterLoadingIndicatorState extends State<_FooterLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late final _controller =
      AnimationController(vsync: this, duration: const Duration(seconds: 1));

  @override
  void initState() {
    _controller.repeat();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: Center(
        child: RotationTransition(
          alignment: Alignment.center,
          turns: _controller,
          child: Assets.images.refreshLoading.image(
              color: context.theme.brandNormalColor, width: 20.0, height: 20.0),
        ),
      ),
    );
  }
}

class _NoMoreFooterIndicator extends StatefulWidget {
  const _NoMoreFooterIndicator({super.key});

  @override
  State<_NoMoreFooterIndicator> createState() => _NoMoreFooterIndicatorState();
}

class _NoMoreFooterIndicatorState extends State<_NoMoreFooterIndicator> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Row(
        children: [
          Expanded(
              child: TDDivider(
            height: 1,
            color: Colors.black,
            text: context.locale.refreshNoMoreData,
            textStyle: const TextStyle(color: Colors.black),
          )),
        ],
      ),
    );
  }
}
