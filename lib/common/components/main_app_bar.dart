import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../assets/assets.gen.dart';

class MainAppBar extends ConsumerWidget implements PreferredSizeWidget  {
  const MainAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.white,
      leading: IconButton(
        icon: Assets.images.iconBackBlack.svg(width: 24,height: 24),
        onPressed: () => context.pop(),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);
}
