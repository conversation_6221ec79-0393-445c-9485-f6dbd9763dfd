import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/exception_state_view.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/networking/remote_service/api_interface.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

typedef KPAsyncValueWidgetBuilder = Widget? Function(BuildContext context, WidgetRef ref);

typedef KPAsyncValueDataWidgetBuilder<T> = Widget Function(
    BuildContext context, WidgetRef ref, T data);

typedef KPAsyncValueErrorWidgetBuilder<T> = Widget? Function(
    BuildContext context, WidgetRef ref, Object error, StackTrace stackTrace);

class KPAsyncValueWidget<T> extends ConsumerWidget {
  final ProviderBase<AsyncValue<T>>? asyncValueProvider;
  final AsyncValue<T>? asyncValue;
  final KPAsyncValueDataWidgetBuilder<T> dataBuilder;
  final KPAsyncValueErrorWidgetBuilder<T>? errorBuilder;
  final KPAsyncValueWidgetBuilder? loadingBuilder;
  final KPAsyncValueWidgetBuilder? emptyBuilder;
  final VoidCallback? onRetry;
  final bool skipLoadingOnRefresh;

  const KPAsyncValueWidget(
      {this.asyncValueProvider,
      this.asyncValue,
      required this.dataBuilder,
      this.errorBuilder,
      this.loadingBuilder,
      this.emptyBuilder,
      this.onRetry,
      this.skipLoadingOnRefresh = true,
      super.key})
      : assert(asyncValueProvider == null || asyncValue == null,
            "asyncValueProvider和asyncValue不能同时有值"),
        assert(asyncValueProvider != null || asyncValue != null,
            "asyncValueProvider和asyncValue不能同时有值");

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<T> value = asyncValue ?? ref.watch(asyncValueProvider!);
    return value.when(
      skipLoadingOnRefresh: value.hasError ? false : skipLoadingOnRefresh,
      data: (data) {
        if (_defaultEmptyCondition(data)) {
          if (emptyBuilder != null) {
            final custom = emptyBuilder!.call(context, ref);
            if (custom != null) return custom;
          }
          return _buildDefaultEmpty();
        }
        return dataBuilder.call(context, ref, data);
      },
      error: (error, st) {
        print(error);
        print(st);
        return errorBuilder?.call(context, ref, error, st) ??
            _defaultErrorBuilder(error, ref);
      },
      loading: () =>
          loadingBuilder?.call(context, ref) ?? _defaultLoadingBuilder(context, ref),
    );
  }

  Widget _buildDefaultEmpty() {
    return const TDEmpty(
      emptyText: '暂无数据',
    );
  }

  Widget _defaultErrorBuilder(Object error, WidgetRef ref) {
    VoidCallback? onRetryTap;
    if (onRetry != null) {
      onRetryTap = onRetry;
    } else if (asyncValueProvider != null) {
      onRetryTap = () => {ref.invalidate(asyncValueProvider!)};
    }
    return ExceptionStateView(exception: error, onRetryTap: onRetryTap);
  }

  Widget _defaultLoadingBuilder(BuildContext context, WidgetRef ref) {
    return Container(
      alignment: Alignment.center,
      child: RepaintBoundary(
        child: LayoutBuilder(
          builder: (context, constraints) {
            // 计算文字和间距的宽度
            final textStyle = KPFontStyle.bodyLarge.copyWith(
              color: KPColors.textGrayTertiary, 
              fontSize: 18
            );
            final textSpan = TextSpan(
              text: context.locale.loadingText,
              style: textStyle,
            );
            final textPainter = TextPainter(
              text: textSpan,
              textDirection: TextDirection.ltr,
            );
            textPainter.layout();
            final textWidth = textPainter.width;
            
            // 圆圈宽度 + 间距 + 文字宽度
            const circleWidth = 18.0;
            const spacing = 12.0;
            final totalWidth = circleWidth + spacing + textWidth;
            
            // 如果总宽度超过可用宽度，只显示圆圈
            if (totalWidth > constraints.maxWidth) {
              return const SizedBox(
                width: 18,
                height: 18,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Color(0xFFA2A6B1),
                ),
              );
            }
            
            // 否则显示完整的加载状态
            return Row(
        mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 18,
            height: 18,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Color(0xFFA2A6B1),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            context.locale.loadingText,
                  style: textStyle,
          ),
        ],
            );
          },
        ),
      ),
    );
  }

  bool _defaultEmptyCondition(T value) {
    if (value is List) {
      return (value as List).isEmpty;
    } else if (value is ApiPaginationData) {
      return (value as ApiPaginationData).data.isEmpty;
    }
    return false;
  }
}
