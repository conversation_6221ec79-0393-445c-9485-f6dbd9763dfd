import 'package:flutter/material.dart';
import 'package:kpos/common/utils/device_util.dart';

class ResponsiveTwoPartLayout extends StatelessWidget {
  final Widget startContent;
  final Widget endContent;
  final int startFlex;
  final int endFlex;
  final double breakpoint;
  final double spacing;
  final MainAxisAlignment rowMainAxisAlignment;
  final CrossAxisAlignment rowCrossAxisAlignment;
  final MainAxisAlignment columnMainAxisAlignment;
  final CrossAxisAlignment columnCrossAxisAlignment;

  const ResponsiveTwoPartLayout(
      {super.key,
      required this.startContent,
      required this.endContent,
      this.startFlex = 1,
      this.endFlex = 1,
      this.breakpoint = DeviceUtil.tablet,
      required this.spacing,
      this.rowMainAxisAlignment = MainAxisAlignment.start,
      this.rowCrossAxisAlignment = CrossAxisAlignment.start,
      this.columnMainAxisAlignment = MainAxisAlignment.start,
      this.columnCrossAxisAlignment = CrossAxisAlignment.start});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      if (constraints.maxWidth >= DeviceUtil.tablet) {
        return Row(
          mainAxisAlignment: rowMainAxisAlignment,
          crossAxisAlignment: rowCrossAxisAlignment,
          children: [
            Flexible(flex: startFlex, child: startContent),
            SizedBox(width: spacing),
            Flexible(flex: endFlex, child: endContent)
          ],
        );
      } else {
        return Column(
          mainAxisAlignment: columnMainAxisAlignment,
          crossAxisAlignment: columnCrossAxisAlignment,
          children: [
            Expanded(flex: startFlex, child: startContent),
            SizedBox(height: spacing),
            Expanded(flex: endFlex, child: endContent)
          ],
        );
      }
    });
  }
}
