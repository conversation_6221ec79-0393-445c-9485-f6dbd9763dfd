// ignore_for_file: constant_identifier_names

enum KPCurrency {
  USD('USD', 'US\$'),
  HKD('HKD', 'HK\$'),
  MYR('MYR', 'MYR'),
  JPY('JPY', 'JPY ¥'),
  SGD('SGD', 'S\$'),
  AUD('AUD', 'AU\$');

  final String code;
  final String symbol;

  const KPCurrency(this.code, this.symbol);

  static String symbolByCode(String? code) {
    if (code == null || code.isEmpty) {
      return '';
    }
    for (final currency in KPCurrency.values) {
      if (currency.code == code) {
        return currency.symbol;
      }
    }
    return '';
  }
}



