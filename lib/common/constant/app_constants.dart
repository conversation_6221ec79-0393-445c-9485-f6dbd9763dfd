import 'package:flutter/material.dart';

/// 应用常量配置
class AppConstants {
  /// 自动进入锁屏pin码页面时间,用户操作超时时间（30秒）
  static const int userInactivityTimeoutSeconds = 3000000000000;
  
  /// 导航栏基础高度（不包含状态栏）
  static const double kNavBarBaseHeight = 60.0;
  
  /// 获取完整导航栏高度
  /// 
  /// 计算方式：导航栏基础高度 + 状态栏高度
  /// @param context [BuildContext] 构建上下文，用于获取设备信息
  /// @return [double] 完整的导航栏高度（像素）
  static double getNavigationBarHeight(BuildContext context) {
    // 获取状态栏高度（顶部安全区域）
    final topPadding = MediaQuery.of(context).padding.top;
    // 返回导航栏基础高度 + 状态栏高度
    return kNavBarBaseHeight + topPadding;
  }
  
  // 可以添加其他常量...
}
