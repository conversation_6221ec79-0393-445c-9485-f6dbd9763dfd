import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';

typedef NativeCartListSummary = Pointer<Utf8> Function(Pointer<Utf8>);
typedef DartCartListSummary = Pointer<Utf8> Function(Pointer<Utf8>);
typedef NativeInitRustLogger = Void Function(Int32 level, Int32 platform);
typedef DartInitRustLogger = void Function(int level, int platform);

class KPRustBridge {
  static final KPRustBridge _instance = KPRustBridge._internal();
  late final DynamicLibrary _lib;
  late final DartCartListSummary _cartListSummary;
  late final DartInitRustLogger _initRustLogger;

  factory KPRustBridge() {
    return _instance;
  }

  KPRustBridge._internal() {
    if (Platform.isWindows) {
      _lib = DynamicLibrary.open('cart_calculate.dll');
    } else if (Platform.isIOS || Platform.isMacOS) {
      _lib = DynamicLibrary.process();
    } else if (Platform.isAndroid) {
      _lib = DynamicLibrary.open("libcart_calculate.so");
    } else {
      throw UnsupportedError('Unsupported platform');
    }

    _cartListSummary = _lib
        .lookup<NativeFunction<NativeCartListSummary>>('cart_list_summary')
        .asFunction();

    _initRustLogger = _lib
        .lookup<NativeFunction<NativeInitRustLogger>>('init_rust_logger')
        .asFunction();
  }

  String cartListSummary(String json) {
    final input = json.toNativeUtf8();
    final resultPtr = _cartListSummary(input);
    final result = resultPtr.toDartString();
    calloc.free(input);
    return result;
  }

  void initRustLogger(int level, int platform) {
    _initRustLogger(level, platform);
  }
}
