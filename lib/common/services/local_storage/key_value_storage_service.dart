import 'dart:ffi';
import 'dart:ui';

import 'package:kpos/common/services/local_storage/key_value_storage_base.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../features/device/presentation/choose_device_screen.dart';

part 'key_value_storage_service.g.dart';


class KeyValueStorageService {
  static const _account = '_account';

  static const _languageCodeKey = '_languageCodeKey';

  static const _countryCodeKey = '_countryCodeKey';

  static const _boundStoreInfo = '_boundStoreInfo';

  static const _deviceInfo = '_deviceInfo';

  static const _tableShowType = '_tableShowType';

  static const _tableTimeDisplay = '_tableTimeDisplay';

  static const _tenantId = '_tenantId';

  static const _orderId = '_orderId';

  final _keyValueStorage = KeyValueStorageBase.instance;

  Future<bool> setAccount(String? account) async {
    return await _keyValueStorage.setCommon(_account, account);
  }

  String? getAccount() {
    return _keyValueStorage.getCommon(_account);
  }

  Future<bool> setLocale(Locale locale) {
    String languageCode = locale.languageCode;
    String countryCode = locale.countryCode ?? "";

    return Future.wait([
      _keyValueStorage.setCommon(_languageCodeKey, languageCode),
      _keyValueStorage.setCommon(_countryCodeKey, countryCode)
    ]).then((value) => !value.contains(false));
  }

  Locale? getLocale() {
    String? languageCode = _keyValueStorage.getCommon(_languageCodeKey);
    String? countryCode = _keyValueStorage.getCommon(_countryCodeKey);
    if (languageCode != null) {
      return Locale(languageCode,countryCode);
    }
    return null;
  }

  Future<bool> setBoundStore(String? store) async {
    return await _keyValueStorage.setCommon(_boundStoreInfo, store);
  }

  Future<String?> getBoundStore() async {
    return _keyValueStorage.getCommon(_boundStoreInfo);
  }

  Future<bool> setDeviceInfo(String? deviceInfo) async {
    return await _keyValueStorage.setCommon(_deviceInfo, deviceInfo);
  }

  String? getDeviceInfo() {
    return _keyValueStorage.getCommon(_deviceInfo);
  }

  /// 设置桌台布局显示类型
  Future<bool> setTableShowType(int? type) async {
    return await _keyValueStorage.setCommon(
      _tableShowType,
      type,
    );
  }
  /// 获取桌台布局显示类型
  int getTableShowType() {
    return _keyValueStorage.getCommon(_tableShowType)??1;
  }

  /// 设置桌台时间显示类型
  Future<bool> setTableTimeDisplay(int? type) async {
    return await _keyValueStorage.setCommon(
      _tableTimeDisplay,
      type,
    );
  }

  /// 设置租户id
  Future<bool> setTenantId(int? tenantId) async {
    return await _keyValueStorage.setCommon(_tenantId, tenantId);
  }

  Future<int?> getTenantId() async {
    return _keyValueStorage.getCommon(_tenantId);
  }

  /// 获取桌台时间显示类型
  int getTableTimeDisplay() {
    return _keyValueStorage.getCommon(_tableTimeDisplay)??1;
  }

  /// 设置订单id
  Future<bool> setOrderId(int? orderId) async {
    return await _keyValueStorage.setCommon(_orderId, orderId);
  }

  Future<int?> getOrderId() async {
    return _keyValueStorage.getCommon(_orderId);
  }

  Future<void> resetKeys() async {
    await _keyValueStorage.clearCommon();
    await _keyValueStorage.clearEncrypted();
  }
}

@riverpod
KeyValueStorageService keyValueStorageService(KeyValueStorageServiceRef ref) {
  return KeyValueStorageService();
}