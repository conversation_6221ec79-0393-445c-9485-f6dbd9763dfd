import 'dart:convert';
import 'dart:ffi';

import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:kpos/common/services/local_storage/serializer.dart';
import 'package:shared_preferences/shared_preferences.dart';

class KeyValueStorageBase {
  static SharedPreferences? _sharedPrefs;

  static FlutterSecureStorage? _secureStorage;

  static KeyValueStorageBase? _instance;

  const KeyValueStorageBase._();

  static KeyValueStorageBase get instance =>
      _instance ?? const KeyValueStorageBase._();

  static Future<void> init() async {
    _sharedPrefs ??= await SharedPreferences.getInstance();
    _secureStorage ??= const FlutterSecureStorage();
  }

  Future<bool> setCommon<T>(String key, T? value) {
    if (value == null) {
      return _sharedPrefs!.remove(key);
    }
    switch (T) {
      case const (String):
        return _sharedPrefs!.setString(key, value as String);
      case const (int):
        return _sharedPrefs!.setInt(key, value as int);
      case const (bool):
        return _sharedPrefs!.setBool(key, value as bool);
      case const (double):
        return _sharedPrefs!.setDouble(key, value as double);
      default:
        return _sharedPrefs!.setString(key, value as String);
    }
  }

  T? getCommon<T>(String key) {
    try {
      switch (T) {
        case const (String):
          return _sharedPrefs!.getString(key) as T?;
        case const (int):
          return _sharedPrefs!.getInt(key) as T?;
        case const (bool):
          return _sharedPrefs!.getBool(key) as T?;
        case const (double):
          return _sharedPrefs!.getDouble(key) as T?;
        default:
          return _sharedPrefs!.get(key) as T?;
      }
    } on Exception {
      return null;
    }
  }

  Future<bool> setModel<T>(String key, T? value, Serializer<T> serializer) {
    if (value == null) {
      return _sharedPrefs!.remove(key);
    } else {
      final json = serializer.toJson(value);
      return _sharedPrefs!.setString(key, jsonEncode(json));
    }
  }

  T? getModel<T>(String key, Serializer<T> serializer) {
    try {
      final value = _sharedPrefs!.getString(key);
      return value == null ? null : serializer.fromJson(jsonDecode(value));
    } on Exception {
      return null;
    }
  }

  Future<bool> clearCommon() => _sharedPrefs!.clear();

  Future<bool> setEncrypted(String key, String? value) {
    try {
      if (value == null) {
        _secureStorage!.delete(key: key);
      } else {
        _secureStorage!.write(key: key, value: value);
      }
      return Future.value(true);
    } on PlatformException catch (_) {
      return Future.value(false);
    }
  }

  Future<String?> getEncrypted(String key) {
    try {
      return _secureStorage!.read(key: key);
    } on PlatformException {
      return Future<String?>.value(null);
    }
  }

  Future<bool> clearEncrypted() async {
    try {
      await _secureStorage!.deleteAll();
      return true;
    } on PlatformException catch (_) {
      return false;
    }
  }
}
