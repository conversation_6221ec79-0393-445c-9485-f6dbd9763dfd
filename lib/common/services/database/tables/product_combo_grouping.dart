import 'package:drift/drift.dart';

class ProductComboGrouping extends Table {
  /// 套餐分组记录ID
  IntColumn get comboGroupingId => integer().named('combo_grouping_id')();

  /// 套餐ID
  IntColumn get comboId => integer().named('combo_id')();

  /// 分组名称
  TextColumn get groupingName => text().named('grouping_name').withLength(min: 1, max: 128)();

  /// 分组名称第二语言
  TextColumn get groupingNameSecondLanguage => text().named('grouping_name_second_language').nullable().withLength(min: 0, max: 128)();

  /// 分组类型（1=固定分组，2=可选分组，默认1）
  IntColumn get groupingType => integer().named('grouping_type').withDefault(const Constant(1))();

  /// 分组商品总数上限（最大值为99，不能为空，默认为0）
  IntColumn get groupingCommodityCountMax => integer().named('grouping_commodity_count_max').withDefault(const Constant(0))();

  /// 分组商品总数下限（最小值为0，不能为空，默认为0）
  IntColumn get groupingCommodityCountMin => integer().named('grouping_commodity_count_min').withDefault(const Constant(0))();

  /// 排序权重，默认：1
  IntColumn get sort => integer().named('sort').withDefault(const Constant(1))();

  @override
  Set<Column> get primaryKey => {comboGroupingId};
}
