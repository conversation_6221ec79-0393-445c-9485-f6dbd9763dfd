import 'package:drift/drift.dart';

class StorePreparedReason extends Table {
  /// 预设原因ID
  IntColumn get preparedReasonId => integer().named('prepared_reason_id')();

  /// 预设原因类型（1-折扣原因，2-退货原因，3-备注，4-取消原因，5-服务费取消原因）
  IntColumn get preparedReasonType => integer().named('prepared_reason_type')();

  /// 预设原因名称
  TextColumn get preparedReasonName => text().named('prepared_reason_name').withLength(min: 1, max: 64)();

  /// 排序值
  IntColumn get orderNum => integer().named('order_num')();

  @override
  Set<Column> get primaryKey => {preparedReasonId};
}