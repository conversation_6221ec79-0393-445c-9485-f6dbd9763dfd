import 'package:drift/drift.dart';

/// 商品加料分组表
class ProductAddonsGroup extends Table {
  /// 商品加料分组ID
  IntColumn get commodityAddonsGroupId =>
      integer().named('commodity_addons_group_id')();

  /// 关联商品ID（来自商品主表）
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 商品加料分组名称
  TextColumn get commodityAddonsGroupName =>
      text().named('commodity_addons_group_name').withLength(min: 1, max: 50)();

  /// 点餐时必选数量（默认 0=非必选，必选时1-99）
  IntColumn get minSelection =>
      integer().named('min_selection').withDefault(const Constant(0))();

  /// 点餐时最多可选数量（0=未勾选，1-99）
  IntColumn get maxSelection =>
      integer().named('max_selection').withDefault(const Constant(0))();

  /// 是否允许重复选择：1=允许，0=禁止
  IntColumn get allowDuplicate =>
      integer().named('allow_duplicate').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {commodityAddonsGroupId};
}
