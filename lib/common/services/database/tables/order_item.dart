import 'package:drift/drift.dart';

class OrderItem extends Table {
  /// 主键ID，订单商品唯一标识
  IntColumn get orderItemId => integer().named('order_item_id')();

  /// 关联订单ID，指向订单基础信息表
  IntColumn get orderId => integer().named('order_id')();

  /// 门店ID
  IntColumn get storeId => integer().named('store_id')();

  /// 商品ID，关联商品信息表
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 租户ID，多租户隔离标识
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 商品分类ID，关联分类信息
  IntColumn get commodityCategoryId => integer().named('commodity_category_id')();

  /// 商品分类名称
  TextColumn get commodityCategoryName => text().named('commodity_category_name').withLength(min: 1, max: 384)();

  /// 商品编码
  TextColumn get commodityCode => text().named('commodity_code').withLength(min: 1, max: 32)();

  /// 商品主名称
  TextColumn get commodityName => text().named('commodity_name').withLength(min: 1, max: 255)();

  /// 第二语言商品名称
  TextColumn get commodityNameSec => text().named('commodity_name_sec').nullable().withLength(min: 0, max: 255)();

  /// 商品图片存储路径
  TextColumn get commodityImagePath => text().named('commodity_image_path').nullable()();

  /// 商品类型：1-计量商品, 2-称重商品, 3-套餐, 4-加料, 5-餐盒
  IntColumn get commodityType => integer().named('commodity_type')();

  /// 商品单位（kg-公斤, g-克, lb-磅, kati-司马斤）
  TextColumn get commodityUnit => text().named('commodity_unit').withLength(min: 1, max: 20)();

  /// 商品数量
  RealColumn get quantity => real().named('quantity')();

  /// 商品SKU ID
  IntColumn get commoditySkuId => integer().named('commodity_sku_id')();

  /// 商品SKU编码
  TextColumn get commoditySkuCode => text().named('commodity_sku_code').withLength(min: 1, max: 255)();

  /// 商品SKU名称（单规格商品为空）
  TextColumn get commoditySkuName => text().named('commodity_sku_name').nullable().withLength(min: 0, max: 50)();

  /// 套餐分组ID
  IntColumn get comboGroupingId => integer().named('combo_grouping_id').nullable()();

  /// 商品加料分组ID
  IntColumn get commodityAddonsGroupId => integer().named('commodity_addons_group_id').nullable()();

  /// 商品备注
  TextColumn get remark => text().named('remark').nullable().withLength(min: 0, max: 256)();

  /// 商品价格
  RealColumn get price => real().named('price').nullable()();

  /// 订单分摊优惠金额
  RealColumn get discountPrice => real().named('discount_price').nullable()();

  /// 商品分摊优惠金额
  RealColumn get commodityDiscountPrice => real().named('commodity_discount_price').nullable()();

  /// 商品附加税金额
  RealColumn get taxPrice => real().named('tax_price').nullable()();

  /// 含税金额
  RealColumn get taxIncludePrice => real().named('tax_include_price').nullable()();

  /// 商品实际分摊价格
  RealColumn get sharedPrice => real().named('shared_price').nullable()();

  /// 提成人姓名
  TextColumn get commissionName => text().named('commission_name').nullable().withLength(min: 0, max: 50)();

  /// 提成人ID
  IntColumn get commissionAccountId => integer().named('commission_account_id').nullable()();

  /// 提成金额
  RealColumn get commissionAmount => real().named('commission_amount').nullable()();

  /// 商品是否被退（0-否 1-是）
  IntColumn get returnFlag => integer().named('return_flag').nullable().withDefault(const Constant(0))();

  /// 商品是否有折扣（0-否 1-是）
  IntColumn get discountFlag => integer().named('discount_flag').nullable().withDefault(const Constant(0))();

  /// 商品是否打包（0-否 1-是）
  IntColumn get takeawayFlag => integer().named('takeaway_flag').nullable().withDefault(const Constant(0))();

  /// 退菜原因
  TextColumn get returnReason => text().named('return_reason').nullable().withLength(min: 0, max: 255)();

  /// 父级订单商品ID（无父级为0）
  IntColumn get parentOrderItemId => integer().named('parent_order_item_id')();

  /// 版本号（每次规则修改自动+1）
  IntColumn get version => integer().named('version').nullable().withDefault(const Constant(1))();

  /// 创建记录时区
  TextColumn get createTimezone => text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改记录时区
  TextColumn get modifyTimezone => text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 最后修改时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 最后修改人ID
  IntColumn get modifyAccountId => integer().named('modify_account_id').nullable()();

  /// 处理状态：1-已处理, 0-待处理
  IntColumn get state => integer().named('state').withDefault(const Constant(1))();

  /// 逻辑删除标记：1-已删除, 0-未删除
  IntColumn get deleted => integer().named('deleted').withDefault(const Constant(0))();

  /// 优惠类型，1-打折，2-减价,如果打折是100%就是免费
  IntColumn get kpDiscountType => integer().named('kp_discount_type').nullable()();

  /// 优惠数量
  RealColumn get kpDiscountAmount => real().named('kp_discount_amount').nullable()();

  /// 订单打折原因，本地数据库加的字段
  TextColumn get kpDiscountReason => text().named('kp_discount_reason').nullable().withLength(min: 0, max: 255)();

  /// 是否催菜（0-否 1-是）
  IntColumn get kpUrgeDishFlag => integer().named('kp_urge_dish_flag').nullable()();

  /// 首次催菜时间
  IntColumn get kpUrgeDishTime => integer().named('kp_urge_dish_time').nullable()();

  /// 是否划菜（0-否 1-是）
  IntColumn get kpServedDishFlag => integer().named('kp_served_dish_flag').nullable()();

  /// 划菜时间
  IntColumn get kpServedDishTime => integer().named('kp_served_dish_time').nullable()();

  /// 商品加价价格
  RealColumn get addPrice => real().named('add_price').nullable().withDefault(const Constant(0.0))();

  /// 商品折扣率0-100
  RealColumn get discountRate => real().named('discount_rate').nullable().withDefault(const Constant(100.0))();

  /// 是否在购物车：1是, 0否
  IntColumn get cartIf => integer().named('cart_if').withDefault(const Constant(0))();

  /// 关联会员ID
  IntColumn get memberId => integer().named('member_id').nullable()();

  /// 设备ID
  TextColumn get deviceId => text().named('device_id').nullable().withLength(min: 0, max: 64)();

  /// 套餐本身优惠
  RealColumn get comboDiscountPrice => real().named('combo_discount_price').nullable().withDefault(const Constant(0.0))();

  /// 商品单位数量
  RealColumn get unitQuantity => real().named('unit_quantity').nullable().withDefault(const Constant(1.0))();

  @override
  Set<Column> get primaryKey => {orderItemId};
}
