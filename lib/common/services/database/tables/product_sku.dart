import 'package:drift/drift.dart';

class ProductSku extends Table {
  /// 新增全局自增主键ID
  // IntColumn get id => integer().autoIncrement()();

  /// 商品sku ID
  IntColumn get commoditySkuId => integer().named('commodity_sku_id')();

  /// 关联商品ID（来自商品主表）
  IntColumn get commodityId => integer().named('commodity_id').nullable()();

  /// SKU组合名称（自动生成，如"大杯+红色"）单规格名称为空
  TextColumn get skuName => text().named('sku_name').nullable()();

  /// SKU唯一编码（同一租户下唯一）
  TextColumn get skuCode => text().named('sku_code')();

  /// sku价格
  RealColumn get skuPrice => real().named('sku_price')();

  /// 商品条形码（支持国际标准）
  TextColumn get barcode => text().named('barcode').nullable()();

  /// 关联餐盒商品ID
  IntColumn get packageCommodityId => integer().named('package_commodity_id').nullable()();

  /// 规格/规格分组ID
  IntColumn get specId => integer().named('spec_id').nullable()();

  /// 权重（排序 默认100）
  IntColumn get sort => integer().named('sort').nullable()();

  /// 是否默认推荐 0=否 1=是
  IntColumn get defaultEnable => integer().named('default_enable').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {commoditySkuId};
}
