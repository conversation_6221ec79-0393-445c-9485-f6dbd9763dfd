import 'package:drift/drift.dart';
class ProductPractice extends Table {
  /// 做法ID
  IntColumn get practiceId => integer().named('practice_id')();

  /// 商品做法分组ID
  IntColumn get practiceGroupId => integer().named('practice_group_id')();

  /// 做法/做法分组名称
  TextColumn get practiceName => text().named('practice_name')();

  /// 做法/做法分组名称(第二语言)
  TextColumn get practiceNameSecondaryLanguage => text().named('practice_name_secondary_language').nullable()();

  /// 价格
  RealColumn get price => real().named('price').withDefault(const Constant(0))();

  /// 是否默认推荐 0=否 1=是
  IntColumn get defaultEnable => integer().named('default_enable').withDefault(const Constant(0))();

  /// 权重（排序 默认100）
  IntColumn get sort => integer().named('sort').nullable()();

  @override
  Set<Column> get primaryKey => {practiceId, practiceGroupId};
}
