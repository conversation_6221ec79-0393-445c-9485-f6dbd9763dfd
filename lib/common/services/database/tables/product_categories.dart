import 'package:drift/drift.dart';

class ProductCategories extends Table {
  IntColumn get categoryId => integer().named('category_id')(); // bigint 对应 int64
  TextColumn get categoryName => text().named('category_name').withLength(min: 1, max: 384)(); // varchar(384)
  TextColumn get colorHex => text().named('color_hex').withLength(min: 1, max: 8)(); // varchar(8)

  @override
  Set<Column> get primaryKey => {categoryId};
}
