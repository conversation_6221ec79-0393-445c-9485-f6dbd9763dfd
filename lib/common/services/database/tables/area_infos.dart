import 'package:drift/drift.dart';

//区域信息表
class AreaInfos extends Table {
  /// 区域ID
  IntColumn get areaId => integer().named('area_id')();

  /// 区域名称
  TextColumn get areaName => text().withLength(min: 1, max: 100)();

  /// 所属门店ID
  IntColumn get storeId => integer().named('store_id')();

  /// 权重
  IntColumn get sort => integer().named('sort').withDefault(const Constant(999))();

  IntColumn get state => integer().withDefault(const Constant(1))();

  @override
  Set<Column> get primaryKey => {areaId};
}