import 'package:drift/drift.dart';

class OrderAdditionalFee extends Table {
  /// 订单额外费用ID
  IntColumn get orderAdditionalFeeId => integer().named('order_additional_fee_id')();

  /// 订单ID
  IntColumn get orderId => integer().named('order_id')();

  /// 租户ID
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 额外费用类型（1-服务费 2-小费）
  IntColumn get additionalFeeType => integer().named('additional_fee_type')();

  /// 服务费管理ID
  IntColumn get serviceFeeManagementId => integer().named('service_fee_management_id').nullable()();

  /// 额外费名称
  TextColumn get additionalFeeName => text().named('additional_fee_name').withLength(min: 1, max: 20)();

  /// 额外费收取金额
  RealColumn get additionalFeeAmount => real().named('additional_fee_amount').nullable()();

  /// 创建时区
  TextColumn get createTimezone => text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改时区
  TextColumn get modifyTimezone => text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 更新时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 更新人ID
  IntColumn get modifyAccountId => integer().named('modify_account_id').nullable()();

  /// 状态（1：已处理，0：待处理）
  IntColumn get state => integer().named('state').withDefault(const Constant(1))();

  /// 是否逻辑删除（1：是，0：否）
  IntColumn get deleted => integer().named('deleted').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {orderAdditionalFeeId};
}
