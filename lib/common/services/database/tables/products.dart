import 'package:drift/drift.dart';

class Products extends Table {
  /// 商品ID
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 商品编码（业务唯一标识）
  TextColumn get commodityCode => text().named('commodity_code').withLength(min: 1, max: 32)();

  /// 商品主名称（必填，最大长度255）
  TextColumn get commodityName => text().named('commodity_name').withLength(min: 1, max: 255)();

  /// 第二语言商品名称（如英文名，非必填）
  TextColumn get commodityNameSec => text().nullable().named('commodity_name_sec')();

  /// 叶子节点分类ID（当前分类层级末级）
  IntColumn get leafCategoryId => integer().named('leaf_category_id')();

  /// 商品图片路径（多个用逗号分隔）
  TextColumn get commodityImagePath => text().nullable().named('commodity_image_path')();

  /// 商品详细描述文本
  TextColumn get commodityDesc => text().nullable().named('commodity_desc')();

  /// 商品类型：1=计量商品，2=称重商品，3=套餐，4=加料，5=餐盒
  IntColumn get commodityType => integer().named('commodity_type')();

  /// 规格类型：1=单规格，2=多规格
  IntColumn get specType => integer().nullable().named('spec_type')();

  /// 商品单位（称重商品需用枚举：kg=公斤，g=克，lb=磅，kati=司马斤）
  TextColumn get commodityUnit => text().named('commodity_unit')();

  /// 最低起售数量（≥1的整数）
  IntColumn get minOrderQuantity => integer().withDefault(Constant(1)).named('min_order_quantity')();

  /// 增售步长（≥1的整数）
  IntColumn get incrementQuantity => integer().named('increment_quantity')();

  /// 是否允许手动改价：0=禁止，1=允许
  IntColumn get priceChangeEnable => integer().withDefault(Constant(0)).named('price_change_enable')();

  /// 允许手动折扣：0=不允许，1=允许
  IntColumn get discountEnable => integer().withDefault(Constant(0)).named('discount_enable')();

  /// 是否允许独立销售：0=仅套餐组件，1=可独立销售
  IntColumn get standaloneSaleEnable => integer().withDefault(Constant(1)).named('standalone_sale_enable')();

  /// 售卖时效类型：1=全时段，2=自定义时段
  IntColumn get sellingTimeType => integer().named('selling_time_type')();

  /// 套餐价格或者sku最低价
  RealColumn get price => real().named('price')();

  /// 是否售罄：0=正常状态，1=售罄
  IntColumn get kpSoldOut => integer().named('kp_sold_out').nullable()();

  /// 设置售罄时间
  IntColumn get kpSoldOutTime => integer().named('kp_sold_out_time').nullable()();

  @override
  Set<Column> get primaryKey => {commodityId};
}
