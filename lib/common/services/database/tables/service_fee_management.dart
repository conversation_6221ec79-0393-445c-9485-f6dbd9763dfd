import 'package:drift/drift.dart';

class ServiceFeeManagement extends Table {
  /// 服务费id
  IntColumn get serviceFeeManagementId => integer().named('service_fee_management_id')();

  /// 服务费名称（服务费，小费，加工费，茶位费）
  TextColumn get serviceFeeName => text().named('service_fee_name')();

  /// 收取方式（1：百分比收取，2：固定收取）
  IntColumn get serviceChargeMethod => integer().named('service_charge_method')();

  /// 是否免税（1：是，0：否）
  IntColumn get serviceDutyFree => integer().named('service_duty_free')();

  /// 收取比例(收取方式为百分比收取)
  RealColumn get serviceChargeRate => real().named('service_charge_rate')();

  /// 收取费用(收取方式为固定收取)
  RealColumn get serviceFixedCharge => real().named('service_fixed_charge').nullable()();

  /// 服务费税费（免税不需要填写）
  RealColumn get serviceFeeTax => real().named('service_fee_tax').nullable()();

  @override
  Set<Column> get primaryKey => {serviceFeeManagementId};
}