import 'package:drift/drift.dart';

class OrderMemberInfo extends Table {
  /// 主键ID，会员信息唯一标识
  IntColumn get orderMemberInfoId => integer().named('order_member_info_id')();

  /// 租户ID，标识会员所属租户或品牌
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 关联订单ID，指向订单基础信息表
  IntColumn get orderId => integer().named('order_id')();

  /// 关联会员ID，指向会员基础信息表
  IntColumn get memberId => integer().named('member_id')();

  /// 会员姓名
  TextColumn get memberName =>
      text().named('member_name').nullable().withLength(min: 0, max: 64)();

  /// 会员编号：7位自增数字，品牌维度自增（起始值1000000）
  TextColumn get memberCode =>
      text().named('member_code').nullable().withLength(min: 0, max: 8)();

  /// 会员邮箱
  TextColumn get memberEmail =>
      text().named('member_email').nullable().withLength(min: 0, max: 100)();

  /// 联系电话
  TextColumn get phoneNumber =>
      text().named('phone_number').nullable().withLength(min: 0, max: 20)();

  /// 创建记录时区（如Asia/Shanghai）
  TextColumn get createTimezone =>
      text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改记录时区（如Asia/Shanghai）
  TextColumn get modifyTimezone =>
      text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 最后修改时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 最后修改人ID
  IntColumn get modifyAccountId =>
      integer().named('modify_account_id').nullable()();

  /// 处理状态：1-已处理, 0-待处理
  IntColumn get state =>
      integer().named('state').withDefault(const Constant(1))();

  /// 逻辑删除标记：1-已删除, 0-未删除
  IntColumn get deleted =>
      integer().named('deleted').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {orderMemberInfoId};
}
