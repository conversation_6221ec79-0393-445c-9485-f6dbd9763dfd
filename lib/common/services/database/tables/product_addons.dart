import 'package:drift/drift.dart';

class ProductAddons extends Table {
  /// 商品加料项ID
  IntColumn get addonsId => integer().named('addons_id')();

  /// 商品加料分组ID
  IntColumn get addonsGroupId => integer().named('addons_group_id')();

  /// 商品id
  IntColumn get addonsCommodityId => integer().named('addons_commodity_id')();

  /// 加料名称
  TextColumn get addonsName => text().named('addons_name')();

  /// 价格
  RealColumn get price => real().named('price').withDefault(const Constant(0))();

  /// 商品类型：1=计量商品，2=称重商品，3=套餐，4=加料，5=餐盒
  IntColumn get commodityType => integer().named('commodity_type')();

  /// 是否默认推荐
  IntColumn get defaultEnable => integer().named('default_enable').withDefault(const Constant(0)).nullable()();

  /// 排序, 默认100
  IntColumn get sort => integer().named('sort').withDefault(const Constant(100))();

  @override
  Set<Column> get primaryKey => {addonsId};
}