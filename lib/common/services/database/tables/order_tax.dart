import 'package:drift/drift.dart';

class OrderTax extends Table {
  /// 订单税费ID
  IntColumn get orderTaxId => integer().named('order_tax_id')();

  /// 订单ID
  IntColumn get orderId => integer().named('order_id')();

  /// 税费管理ID
  IntColumn get taxManagementId => integer().named('tax_management_id')();

  /// 租户ID
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 关联ID（订单商品ID / 订单服务费ID / 商品做法ID）
  IntColumn get relationTaxId => integer().named('relation_tax_id')();

  /// 订单税费类型（1-订单商品税 2-订单服务费税 3-商品做法税）
  IntColumn get orderTaxType => integer().named('order_tax_type')();

  /// 税名
  TextColumn get taxName => text().named('tax_name').withLength(min: 1, max: 20)();

  /// 税率
  RealColumn get taxRate => real().named('tax_rate')();

  /// 附加税金额
  RealColumn get taxPrice => real().named('tax_price')();

  /// 形式/展示方式（1:附加税tax exclusive，2:含税tax inclusive）
  IntColumn get taxType => integer().named('tax_type').withDefault(const Constant(1))();

  /// 创建时区
  TextColumn get createTimezone => text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改时区
  TextColumn get modifyTimezone => text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 更新时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 更新人ID
  IntColumn get modifyAccountId => integer().named('modify_account_id').nullable()();

  /// 状态（1：已处理，0：待处理）
  IntColumn get state => integer().named('state').withDefault(const Constant(1))();

  /// 是否逻辑删除（1：是，0：否）
  IntColumn get deleted => integer().named('deleted').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {orderTaxId};
}
