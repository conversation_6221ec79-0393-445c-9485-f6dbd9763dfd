import 'package:drift/drift.dart';
import 'package:kpos/common/services/database/tables/products.dart';
import 'package:kpos/common/services/database/tables/users.dart';
class Carts extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get commodityId => integer().references(Products, #commodityId)();
  IntColumn get userId => integer().references(Users, #employeeId)();
  RealColumn get number => real().nullable()();
  RealColumn get price => real().nullable()();
}