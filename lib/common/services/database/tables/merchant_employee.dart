import 'package:drift/drift.dart';

class MerchantEmployee extends Table {
  /// 员工ID
  IntColumn get employeeId => integer().named('employee_id')();

  /// 角色ID
  IntColumn get roleId => integer().named('role_id')();

  /// 邮箱账号
  TextColumn get email => text().withLength(min: 1, max: 64).named('email')();

  /// 员工名称
  TextColumn get firstName => text()
      .nullable()
      .withLength(min: 0, max: 99)
      .named('first_name')();

  /// 员工姓氏
  TextColumn get lastName => text()
      .nullable()
      .withLength(min: 0, max: 99)
      .named('last_name')();

  /// 员工名称 + 空格 + 员工姓氏
  TextColumn get employeeName => text()
      .nullable()
      .withLength(min: 0, max: 200)
      .named('employee_name')();

  /// 区号
  TextColumn get areaCode => text()
      .nullable()
      .withLength(min: 0, max: 10)
      .named('area_code')();

  /// 手机号码
  TextColumn get phone => text()
      .nullable()
      .withLength(min: 0, max: 20)
      .named('phone')();

  /// 性别：1-男，2-女
  IntColumn get gender => integer()
      .nullable()
      .named('gender')();

  /// 地址
  TextColumn get address => text()
      .nullable()
      .withLength(min: 0, max: 255)
      .named('address')();

  /// PIN码
  TextColumn get pinCode => text()
      .nullable()
      .withLength(min: 0, max: 6)
      .named('pin_code')();

  /// 提成状态：0-否，1-是
  IntColumn get commissionStatus => integer()
      .withDefault(const Constant(0))
      .named('commission_status')();

  /// 提成规则ID
  IntColumn get commissionId => integer()
      .named('commission_id')();

  /// 头像图片路径
  TextColumn get headPortraitPath => text()
      .nullable()
      .named('head_portrait_path')();

  @override
  Set<Column> get primaryKey => {employeeId};
}
