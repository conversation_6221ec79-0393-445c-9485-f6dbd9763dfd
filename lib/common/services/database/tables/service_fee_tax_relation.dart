import 'package:drift/drift.dart';

class ServiceFeeTaxRelation extends Table {
  IntColumn get serviceFeeTaxRelationId => integer().named('service_fee_tax_relation_id')();

  IntColumn get serviceFeeManagementId => integer().named('service_fee_management_id')();

  IntColumn get taxManagementId => integer().named('tax_management_id')();

  @override
  Set<Column> get primaryKey => {serviceFeeTaxRelationId};

}