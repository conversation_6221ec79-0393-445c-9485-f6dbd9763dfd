import 'package:drift/drift.dart';

class TaxManagement extends Table {
  /// 税费id（序号）
  IntColumn get taxManagementId => integer().named('tax_management_id')();

  /// 税名
  TextColumn get taxName => text().named('tax_name')();

  /// 税率
  RealColumn get taxRate => real().named('tax_rate')();

  /// 形式/展示方式（1:附加税tax exclusive，2:含税tax inclusive）
  IntColumn get taxType => integer().named('tax_type')();

  /// 是否对临时菜应用税费（1：是，0：否）
  IntColumn get taxTemporaryDishes => integer().named('tax_temporary_dishes')();

  @override
  Set<Column> get primaryKey => {taxManagementId};
}