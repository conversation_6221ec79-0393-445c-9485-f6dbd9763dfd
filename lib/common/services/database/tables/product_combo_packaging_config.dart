import 'package:drift/drift.dart';

class ProductComboPackagingConfig extends Table {

  /// 套餐包装设置记录ID
  IntColumn get comboPackagingConfigId => integer().named('combo_packaging_config_id')();

  /// 套餐ID
  IntColumn get comboId => integer().named('combo_id')();

  /// 包装设置类型（1-使用套餐内商品关联的包装，2-按整个套餐收取包装费，默认选择1）
  IntColumn get packagingSettingType =>
      integer().named('packaging_setting_type')
          .withDefault(const Constant(1))();

  /// 餐盒ID
  IntColumn get boxId => integer().named('box_id').nullable()();

  @override
  Set<Column> get primaryKey => {comboPackagingConfigId};

}