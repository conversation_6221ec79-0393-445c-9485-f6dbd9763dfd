import 'package:drift/drift.dart';
class ProductSpecGroup extends Table {
  /// 规格分组ID
  IntColumn get commoditySpecGroupId => integer().named('commodity_spec_group_id')();

  /// 关联商品ID（来自商品主表）
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 是否自定义规格（1：是，0：否），默认 0
  IntColumn get customIf => integer()
      .named('custom_if')
      .withDefault(const Constant(0))();

  /// 自定义规格组名称（可为空）
  TextColumn get commoditySpecGroupName => text()
      .named('commodity_spec_group_name')
      .nullable()
      .withLength(min: 0, max: 50)();

  /// 关联标准规格组ID（可为空）
  IntColumn get specGroupId => integer().named('spec_group_id').nullable()();

  @override
  Set<Column> get primaryKey => {commoditySpecGroupId};
}
