import 'package:drift/drift.dart';

class Orders extends Table {
  /// 主键ID，订单基础信息唯一标识
  IntColumn get orderId => integer().named('order_id')();

  /// 租户ID
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 订单号
  TextColumn get orderNumber => text().named('order_number').withLength(min: 1, max: 24)();

  /// 订单来源 （1：收银POS 2:桌台扫码 3：门店码 4：预点餐码 5: 动态码）
  IntColumn get orderSource => integer().named('order_source').withDefault(const Constant(1))();

  /// 就餐方式 （1：堂食，2：自提 3：外带 4：外卖）
  IntColumn get diningMethod => integer().named('dining_method').withDefault(const Constant(1))();

  /// 取餐凭证号（流水号/餐牌号/排队号）
  TextColumn get mealVoucher => text().named('meal_voucher').nullable().withLength(min: 0, max: 16)();

  /// 桌台号
  TextColumn get tableNumber => text().named('table_number').nullable().withLength(min: 0, max: 32)();

  /// 是否多人点餐标识（0-否 1-是）
  IntColumn get multiOrderingFlag => integer().named('multi_ordering_flag').nullable()();

  /// 服务方式（1：自取，2：送餐）
  IntColumn get serviceWay => integer().named('service_way').nullable()();

  /// 用餐人数
  IntColumn get diningNumber => integer().named('dining_number').withDefault(const Constant(1))();

  /// 下单时间
  IntColumn get orderTime => integer().named('order_time').nullable()();

  /// 完成时间
  IntColumn get finishTime => integer().named('finish_time').nullable()();

  /// 取消时间
  IntColumn get cancelTime => integer().named('cancel_time').nullable()();

  /// 退单时间
  IntColumn get refundTime => integer().named('refund_time').nullable()();

  /// 取消原因
  TextColumn get cancelReason => text().named('cancel_reason').nullable().withLength(min: 0, max: 255)();

  /// 退款原因
  TextColumn get refundReason => text().named('refund_reason').nullable().withLength(min: 0, max: 255)();

  /// 期望开始时间
  TextColumn get expectedStartTime => text().named('expected_start_time').nullable().withLength(min: 0, max: 16)();

  /// 期望结束时间
  TextColumn get expectedEndTime => text().named('expected_end_time').nullable().withLength(min: 0, max: 16)();

  /// 外带方式（1 Today Asap, 2 Later）
  IntColumn get takeAway => integer().named('take_away').nullable()();

  /// 取餐状态（0待制作 1制作中 2已完成 3已取消 4待取餐）
  IntColumn get pickUpStatus => integer().named('pick_up_status').nullable()();

  /// 营业日期（如2025-06-03）
  TextColumn get businessDate => text().named('business_date').withLength(min: 1, max: 10)();

  /// 订单状态（1：待下单，2：待确认，3：待支付 4：已确认 5：已完成 6：已取消 7：已退单）
  IntColumn get orderState => integer().named('order_state').withDefault(const Constant(1))();

  /// 订单支付类型（1：先付后食，2：先食后付）
  IntColumn get payType => integer().named('pay_type').withDefault(const Constant(1))();

  /// 整单备注
  TextColumn get remark => text().named('remark').nullable().withLength(min: 0, max: 256)();

  /// 门店ID
  IntColumn get storeId => integer().named('store_id')();

  /// 门店名称
  TextColumn get storeName => text().named('store_name').withLength(min: 1, max: 256)();

  /// 门店地址
  TextColumn get storeAddress => text().named('store_address').nullable().withLength(min: 0, max: 1024)();

  /// 品牌ID
  IntColumn get brandId => integer().named('brand_id')();

  /// 品牌名称
  TextColumn get brandName => text().named('brand_name').withLength(min: 1, max: 64)();

  /// 收银员姓名
  TextColumn get cashierName => text().named('cashier_name').nullable().withLength(min: 0, max: 64)();

  /// 收银员ID
  TextColumn get cashierAccountId => text().named('cashier_account_id').nullable().withLength(min: 0, max: 64)();

  /// 商品总售价
  RealColumn get totalAmount => real().named('total_amount')();

  /// 实付金额
  RealColumn get payAmount => real().named('pay_amount')();

  /// 订单优惠金额
  RealColumn get discountAmount => real().named('discount_amount').nullable()();

  /// 商品优惠金额
  RealColumn get commodityDiscountAmount => real().named('commodity_discount_amount').nullable()();

  /// 商品附加税金额
  RealColumn get commodityTaxAmount => real().named('commodity_tax_amount').nullable()();

  /// 商品含税金额
  RealColumn get commodityTaxIncludeAmount => real().named('commodity_tax_include_amount').nullable()();

  /// 服务费附加税金额
  RealColumn get serviceTaxAmount => real().named('service_tax_amount').nullable()();

  /// 服务费含税金额
  RealColumn get serviceTaxIncludeAmount => real().named('service_tax_include_amount').nullable()();

  /// 服务费金额
  RealColumn get serviceFeeAmount => real().named('service_fee_amount').nullable()();

  /// 小费金额
  RealColumn get tipAmount => real().named('tip_amount').nullable()();

  /// 支付币种
  TextColumn get payCurrency => text().named('pay_currency').withLength(min: 1, max: 16)();

  /// 是否尽早（0-否，1-是）
  IntColumn get earlyIf => integer().named('early_if').withDefault(const Constant(0))();

  /// 是否联台（0-否，1-是）
  IntColumn get mergeTableFlag => integer().named('merge_table_flag').nullable()();

  /// 凭证（类似国内发票）
  TextColumn get voucher => text().named('voucher').nullable().withLength(min: 0, max: 6)();

  /// 联台ID
  IntColumn get mergeTableId => integer().named('merge_table_id').nullable()();

  /// 版本号（每次规则修改自动+1）
  IntColumn get version => integer().named('version').nullable().withDefault(const Constant(1))();

  /// 创建时区
  TextColumn get createTimezone => text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改时区
  TextColumn get modifyTimezone => text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 修改时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 修改人ID
  IntColumn get modifyAccountId => integer().named('modify_account_id').nullable()();

  /// 状态：1-已处理,0-待处理
  IntColumn get state => integer().named('state').withDefault(const Constant(1))();

  /// 是否删除：1-是,0-否
  IntColumn get deleted => integer().named('deleted').withDefault(const Constant(0))();

  /// 是否是存单：1-是,0-否
  IntColumn get isDraftOrder => integer().named('is_draft_order').withDefault(const Constant(0))();

  /// 存单时间
  IntColumn get draftOrderTime => integer().named('draft_order_time').nullable()();

  /// 优惠类型，1-打折，2-减价
  IntColumn get kpDiscountType => integer().named('kp_discount_type').nullable()();

  /// 优惠数量
  RealColumn get kpDiscountAmount => real().named('kp_discount_amount').nullable()();

  /// 订单打折原因，本地数据库加的字段
  TextColumn get kpDiscountReason => text().named('kp_discount_reason').nullable().withLength(min: 0, max: 255)();

  /// 取消服务费原因
  TextColumn get kpCancelServiceFeeReason => text().named('kp_cancel_service_fee_reason').nullable().withLength(min: 0, max: 255)();

  /// 转台之前的桌台号
  TextColumn get kpOriginTableNumber => text().named('kp_origin_table_number').nullable().withLength(min: 0, max: 32)();

  /// 是否拼桌（0-否，1-是）
  IntColumn get kpSharedTableFlag => integer().named('kp_shared_table_flag').nullable()();

  /// 拼桌ID
  IntColumn get kpSharedTableId => integer().named('kp_shared_table_id').nullable()();

  /// 联台组名
  TextColumn get kpMergeTableName => text().named('kp_merge_table_name').nullable().withLength(min: 0, max: 64)();

  /// 评价等级类型（1-poor、2-bad、3-average、4-good、5-excellent）
  IntColumn get reviewType => integer().named('review_type').nullable()();

  /// 订单评价内容
  TextColumn get reviewContent => text().named('review_content').nullable()();

  /// 分账方式（1：按商品付款 2：按人数付款 3：组合付 4：全额付）
  IntColumn get splitMethod => integer().named('split_method').withDefault(const Constant(4))();

  /// 区域名称
  TextColumn get areaName => text().named('area_name').nullable()();

  /// 套餐本身优惠合计
  RealColumn get comboDiscountAmount => real().named('combo_discount_amount').withDefault(const Constant(0.0)).nullable()();

  @override
  Set<Column> get primaryKey => {orderId};
}
