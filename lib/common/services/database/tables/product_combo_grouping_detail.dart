import 'package:drift/drift.dart';

class ProductComboGroupingDetail extends Table {

  /// 套餐分组明细记录ID
  IntColumn get comboGroupingDetailId => integer().named('combo_grouping_detail_id')();

  /// 套餐分组ID
  IntColumn get comboGroupingId => integer().named('combo_grouping_id')();

  /// 商品ID
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 商品skuID
  IntColumn get commoditySkuId => integer().named('commodity_sku_id')();

  /// 套餐ID
  IntColumn get comboId => integer().named('combo_id')();

  /// 加价，不为null，默认为0，保留两位小数
  RealColumn get additionalPrice => real().named('additional_price').withDefault(const Constant(0.00))();

  /// 数量，int类型，不能为空，默认为0
  IntColumn get quantity => integer().named('quantity').withDefault(const Constant(0))();

  /// 是否默认推荐，0=不是默认推荐，1=默认推荐
  IntColumn get defaultRecommendation => integer().named('default_recommendation').withDefault(const Constant(0))();

  /// 排序权重，默认：1
  IntColumn get sort => integer().named('sort').withDefault(const Constant(1))();

  @override
  Set<Column> get primaryKey => {comboGroupingDetailId};
}
