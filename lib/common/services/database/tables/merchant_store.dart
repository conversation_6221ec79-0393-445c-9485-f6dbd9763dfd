import 'package:drift/drift.dart';

class MerchantStore extends Table {
  /// 门店id
  IntColumn get storeId => integer().named('store_id')();

  /// 门店名称
  TextColumn get storeName => text().named('store_name').withLength(min: 1, max: 256)();

  /// 组织架构id
  IntColumn get organizationId => integer().named('organization_id')();

  /// 区域（国家）id
  IntColumn get regionId => integer().named('region_id')();

  /// 门店时区
  TextColumn get storeTimezone => text().named('store_timezone').withLength(min: 1, max: 50)();

  /// 货币类型
  TextColumn get currency => text().named('currency').withLength(min: 1, max: 12)();

  /// 详细地址
  TextColumn get address => text().named('address').withLength(min: 0, max: 1024).nullable()();

  ///  货币符号
  TextColumn get currencySymbol => text().named('currency_symbol').withLength(min: 0, max: 1024).nullable()();

  /// 邮政编码
  TextColumn get postcode => text().named('postcode').withLength(min: 0, max: 12).nullable()();

  /// 区号
  TextColumn get areaCode => text().named('area_code').withLength(min: 0, max: 12).nullable()();

  /// 手机号码
  TextColumn get phone => text().named('phone').withLength(min: 0, max: 16).nullable()();

  /// 品牌id
  IntColumn get brandId => integer().named('brand_id')();

  /// 经营状态 1：在营 0：停业 （默认1）
  IntColumn get businessState => integer().named('business_state').withDefault(const Constant(1))();

  /// 是否手动关闭 1：是 0：否 （默认0）
  IntColumn get manualClose => integer().named('manual_close').withDefault(const Constant(0))();

  /// 门店介绍
  TextColumn get introduction => text().named('introduction').withLength(min: 0, max: 1024).nullable()();

  /// 门店编码
  TextColumn get storeCode => text().withLength(min: 0, max: 8).nullable()();

  @override
  Set<Column> get primaryKey => {storeId};
}