import 'package:drift/drift.dart';

class OrderItemPractice extends Table {
  /// 主键ID，订单商品做法唯一标识
  IntColumn get orderItemPracticeId => integer().named('order_item_practice_id')();

  /// 订单ID
  IntColumn get orderId => integer().named('order_id')();

  /// 关联订单商品ID，指向order_item表
  IntColumn get orderItemId => integer().named('order_item_id')();

  /// 租户ID，多租户隔离标识
  IntColumn get tenantId => integer().named('tenant_id')();

  /// 商品做法分组ID
  IntColumn get commodityPracticeGroupId => integer().named('commodity_practice_group_id')();

  /// 商品做法ID，关联基础做法库
  IntColumn get commodityPracticeId => integer().named('commodity_practice_id')();

  /// 做法名称（如：少辣/去葱/加冰）
  TextColumn get commodityPracticeName => text().named('commodity_practice_name').withLength(min: 1, max: 384)();

  /// 做法数量
  RealColumn get quantity => real().named('quantity')();

  /// 做法附加价格
  RealColumn get price => real().named('price')();

  /// 做法附加税金额
  RealColumn get taxPrice => real().named('tax_price').nullable()();

  /// 做法分摊后附加价格
  RealColumn get sharedPrice => real().named('shared_price').nullable()();

  /// 订单分摊优惠金额
  RealColumn get discountPrice => real().named('discount_price').nullable()();

  /// 含税金额
  RealColumn get taxIncludePrice => real().named('tax_include_price').nullable()();

  /// 商品分摊优惠金额
  RealColumn get commodityDiscountPrice => real().named('commodity_discount_price').nullable()();

  /// 版本号（每次规则修改自动+1）
  IntColumn get version => integer().named('version').nullable().withDefault(const Constant(1))();

  /// 创建记录时区
  TextColumn get createTimezone => text().named('create_timezone').withLength(min: 1, max: 50)();

  /// 修改记录时区
  TextColumn get modifyTimezone => text().named('modify_timezone').withLength(min: 1, max: 50)();

  /// 创建时间
  IntColumn get createTime => integer().named('create_time')();

  /// 创建人ID
  IntColumn get createAccountId => integer().named('create_account_id')();

  /// 最后修改时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  /// 最后修改人ID
  IntColumn get modifyAccountId => integer().named('modify_account_id').nullable()();

  /// 处理状态：1-已处理, 0-待处理
  IntColumn get state => integer().named('state').withDefault(const Constant(1))();

  /// 逻辑删除标记：1-已删除, 0-未删除
  IntColumn get deleted => integer().named('deleted').withDefault(const Constant(0))();

  /// 做法加价价格, 包含套餐内时为0
  RealColumn get addPrice => real().named('add_price').nullable().withDefault(const Constant(0.0))();

  /// 是否在购物车：1是, 0否
  IntColumn get cartIf => integer().named('cart_if').withDefault(const Constant(0))();

  /// 关联会员ID，指向会员基础信息表
  IntColumn get memberId => integer().named('member_id').nullable()();

  /// 设备ID
  TextColumn get deviceId => text().named('device_id').nullable().withLength(min: 0, max: 64)();

  /// 套餐本身优惠分摊（一份做法的分摊优惠）
  RealColumn get comboDiscountPrice => real().named('combo_discount_price').nullable().withDefault(const Constant(0.0))();

  @override
  Set<Column> get primaryKey => {orderItemPracticeId};
}
