import 'package:drift/drift.dart';

class ProductStoreMenuSkuRelations extends Table {

  /// 门店菜单商品sku关联ID
  IntColumn get storeMenuSkuRelationId => integer().named('store_menu_sku_relation_id')();

  /// 门店菜单商品表ID

  IntColumn get storeMenuId => integer().named('store_menu_id')();

  /// skuID
  IntColumn get skuId => integer().named('sku_id')();

  /// 门店菜单商品价格
  RealColumn get storeMenuPrice => real().named('store_menu_price')();


  @override
  Set<Column> get primaryKey => {storeMenuSkuRelationId};
}