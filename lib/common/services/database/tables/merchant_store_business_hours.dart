import 'package:drift/drift.dart';

class MerchantStoreBusinessHours extends Table {
  /// 门店营业时间段id
  IntColumn get storeBusinessHoursId => integer().named('store_business_hours_id')();

  /// 工作日（星期几，1表示星期1）
  IntColumn get weekDay => integer().named('week_day')();

  /// 开始营业时间（例：06:00）
  TextColumn get startTime => text().named('start_time').withLength(min: 1, max: 12)();

  /// 结束营业时间（例：22:00）
  TextColumn get endTime => text().named('end_time').withLength(min: 1, max: 12)();

  /// 是否跨天经营 1:是 0:否 (默认0)
  IntColumn get nextDay => integer().named('next_day').withDefault(const Constant(0))();

  /// 更新时间
  IntColumn get modifyTime => integer().named('modify_time').nullable()();

  @override
  Set<Column> get primaryKey => {storeBusinessHoursId, weekDay};
}