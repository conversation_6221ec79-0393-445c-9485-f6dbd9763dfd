import 'package:drift/drift.dart';

//桌位信息表
class DiningTableInfos extends Table {
  IntColumn get tableInfoId => integer()();
  IntColumn get areaId => integer()();
  TextColumn get tableTitle => text().nullable().withLength(min: 0, max: 20)();
  IntColumn get seatedNumber => integer()();
  IntColumn get tableShape => integer()();
  RealColumn get tableHeight => real().withDefault(const Constant(1))();
  RealColumn get tableWidth => real().withDefault(const Constant(1))();
  RealColumn get nodeX => real().withDefault(const Constant(0))();
  RealColumn get nodeY => real().withDefault(const Constant(0))();
  IntColumn get tableStatus => integer().withDefault(const Constant(0))();
  IntColumn get sort => integer().withDefault(const Constant(999))();
  IntColumn get state => integer().withDefault(const Constant(1))();

  /// 是否是待清台（0-否，1-是）
  IntColumn get kpUncleanedTableFlag => integer().named('kp_uncleaned_table_flag').nullable()();

  @override
  Set<Column> get primaryKey => {tableInfoId};
}