//订单列表信息表
import 'package:drift/drift.dart';

class OrderInfoTable extends Table {
  IntColumn get id => integer().autoIncrement()();

  TextColumn get orderSource => text().nullable()();

  TextColumn get diningType => text().nullable()();

  IntColumn get orderStatus => integer().nullable()();

  TextColumn get pickUpNumber => text().nullable()();

  DateTimeColumn get orderTime => dateTime().nullable()();

  RealColumn get amount => real().nullable()();
}
