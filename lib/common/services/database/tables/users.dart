import 'package:drift/drift.dart';

///员工表
class Users extends Table {
  /// 员工ID
  IntColumn get employeeId => integer().named('employee_id')();

  /// 角色ID
  IntColumn get roleId => integer().named('role_id')();

  /// 邮箱账号
  TextColumn get email => text().named('email')();

  /// 员工名称
  TextColumn get firstName => text().named('first_name').nullable()();

  /// 员工姓氏
  TextColumn get lastName => text().named('last_name').nullable()();

  /// 员工名称 + 空格 + 员工姓氏
  TextColumn get employeeName => text().named('employee_name').nullable()();

  /// 区号
  TextColumn get areaCode => text().named('area_code').nullable()();

  /// 手机号码
  TextColumn get phone => text().named('phone').nullable()();

  /// 性别：1-男，2-女
  IntColumn get gender => integer().named('gender').nullable()();

  /// 地址
  TextColumn get address => text().named('address').nullable()();

  /// PIN码
  TextColumn get pinCode => text().named('pin_code').nullable()();

  /// 提成状态：0-否，1-是
  IntColumn get commissionStatus =>
      integer().named('commission_status').withDefault(const Constant(0))();

  /// 提成规则ID（提成状态为否时，提成规则ID为0）
  IntColumn get commissionId => integer().named('commission_id')();

  /// 头像图片路径
  TextColumn get headPortraitPath =>
      text().named('head_portrait_path').nullable()();

  @override
  Set<Column> get primaryKey => {employeeId}; // 可选，若你以 employee_id 为主键
}