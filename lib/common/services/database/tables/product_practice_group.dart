import 'package:drift/drift.dart';

class ProductPracticeGroup extends Table {
  /// 商品做法分组ID
  IntColumn get commodityPracticeGroupId => integer().named('commodity_practice_group_id')();

  /// 做法分组名称
  TextColumn get name => text().named('name')();

  /// 关联商品ID（来自商品主表）
  IntColumn get commodityId => integer().named('commodity_id')();

  /// 点餐时必选数量（默认 0=非必选，必选时1-99）
  IntColumn get minSelection => integer().named('min_selection').withDefault(const Constant(0))();

  /// 点餐时最多可选数量（0=未勾选，1-99）
  IntColumn get maxSelection => integer().named('max_selection').withDefault(const Constant(0))();

  @override
  Set<Column> get primaryKey => {commodityPracticeGroupId};
}
