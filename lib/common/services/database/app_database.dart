
import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:drift_sqflite/drift_sqflite.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/database/tables/area_infos.dart';
import 'package:kpos/common/services/database/tables/carts.dart';
import 'package:kpos/common/services/database/tables/dining_style.dart';
import 'package:kpos/common/services/database/tables/dining_table_infos.dart';
import 'package:kpos/common/services/database/tables/merchant_employee.dart';
import 'package:kpos/common/services/database/tables/merchant_store.dart';
import 'package:kpos/common/services/database/tables/merchant_store_business_hours.dart';
import 'package:kpos/common/services/database/tables/order.dart';
import 'package:kpos/common/services/database/tables/order_additional_fee.dart';
import 'package:kpos/common/services/database/tables/order_item.dart';
import 'package:kpos/common/services/database/tables/order_item_practice.dart';
import 'package:kpos/common/services/database/tables/order_member_info.dart';
import 'package:kpos/common/services/database/tables/order_serial.dart';
import 'package:kpos/common/services/database/tables/order_tax.dart';
import 'package:kpos/common/services/database/tables/product_addons.dart';
import 'package:kpos/common/services/database/tables/product_addons_group.dart';
import 'package:kpos/common/services/database/tables/product_categories.dart';
import 'package:kpos/common/services/database/tables/product_combo_grouping.dart';
import 'package:kpos/common/services/database/tables/product_combo_grouping_detail.dart';
import 'package:kpos/common/services/database/tables/product_combo_packaging_config.dart';
import 'package:kpos/common/services/database/tables/product_practice.dart';
import 'package:kpos/common/services/database/tables/product_practice_group.dart';
import 'package:kpos/common/services/database/tables/product_sku.dart';
import 'package:kpos/common/services/database/tables/product_spec_group.dart';
import 'package:kpos/common/services/database/tables/product_store_menu.dart';
import 'package:kpos/common/services/database/tables/product_store_menu_sku_relations.dart';
import 'package:kpos/common/services/database/tables/product_tax_relation.dart';
import 'package:kpos/common/services/database/tables/service_fee_management.dart';
import 'package:kpos/common/services/database/tables/service_fee_tax_relation.dart';
import 'package:kpos/common/services/database/tables/store_prepared_reason.dart';
import 'package:kpos/common/services/database/tables/tax_management.dart';
import 'package:kpos/common/services/database/tables/users.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'tables/products.dart';

part 'app_database.g.dart';

@DriftDatabase(tables: [
  Products,
  Carts,
  Users,
  AreaInfos,
  DiningTableInfos,
  ProductCategories,
  ProductPractice,
  ProductPracticeGroup,
  ProductSku,
  ProductSpecGroup,
  ProductAddonsGroup,
  ProductComboGrouping,
  ProductComboGroupingDetail,
  ProductComboPackagingConfig,
  DiningStyle,
  MerchantStore,
  StorePreparedReason,
  ServiceFeeManagement,
  TaxManagement,
  ProductStoreMenu,
  ProductStoreMenuSkuRelations,
  Orders,
  OrderAdditionalFee,
  OrderItem,
  OrderItemPractice,
  OrderTax,
  ProductTaxRelation,
  ProductAddons,
  OrderMemberInfo,
  MerchantEmployee,
  OrderSerial,
  MerchantStoreBusinessHours,
  ServiceFeeTaxRelation
])
class AppDatabase extends _$AppDatabase{
  static AppDatabase? _instance;

  AppDatabase._() : super(_openConnection());

  factory AppDatabase() {
    return _instance ??= AppDatabase._();
  }

  @override
  int get schemaVersion => 3;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
        onCreate: (Migrator m) async {
          await m.createAll();
        },
        onUpgrade: (Migrator m, int from, int to) async {
          if (from < 3) {
          }
        },
        beforeOpen: (details) async {
          await customStatement('PRAGMA foreign_keys = ON');
          if (details.wasCreated) {
            await into(orderSerial).insert(
              const OrderSerialCompanion(
                currentSerial: Value(0),
              ),
            );
      }
    }
    );
  }

  Future<void> insertOrReplaceBatch<T extends Table, D>(
      TableInfo<T, D> table,
      List<Insertable<D>> dataList,
      ) async {
    try {
      await batch((batch) {
        batch.insertAll(
          table,
          dataList,
          mode: InsertMode.insertOrReplace,
        );
      });
      print('${table.actualTableName} 批量插入成功');
    } catch (e, stack) {
      print('${table.actualTableName} 批量插入失败: $e');
      // 可选：记录日志或上报
    }
  }

  // 删除数据库文件
  static Future<void> deleteDatabaseFile() async {
    final dbDir = await getApplicationDocumentsDirectory();
    final dbPath = p.join(dbDir.path, 'korepos.db');
    final file = File(dbPath);
    if (await file.exists()) {
      await file.delete();
      print('🗑️ 数据库文件已删除: $dbPath');
    } else {
      print('⚠️ 数据库文件不存在: $dbPath');
    }
  }

}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbDir = await getApplicationDocumentsDirectory();
    final dbPath = p.join(dbDir.path, 'korepos.db');
    final dbFile = File(dbPath);
    print('数据库路径: $dbPath');
    if (Platform.isAndroid) {
      return SqfliteQueryExecutor(path: dbPath, logStatements: true);
    } else {
      return NativeDatabase(dbFile);
    }
  });
}

@Riverpod(keepAlive: true)
AppDatabase database(Ref ref) {
  return AppDatabase();
}