import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'selection_index.g.dart';

@riverpod
class SelectionIndex extends _$SelectionIndex {
  @override
  List<int> build() {
    return <int>[];
  }

  void toggle(int index) {
    if (state.contains(index)) {
      state = [...state]..remove(index);
    } else {
      state = [...state, index];
    }
  }

  bool isSelected(int index) => state.contains(index);

  void clear() {
    state = <int>[];
  }
}
