import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_request_exception.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'stomp_request_service.g.dart';

class BaseListResponse<T> {
  final int code;
  final String message;
  final List<T> data;

  BaseListResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory BaseListResponse.fromJson(
      Map<String, dynamic> json,
      T Function(Map<String, dynamic>) fromJsonT,
      ) {
    return BaseListResponse<T>(
      code: json['code'] as int,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList(),
    );
  }
}

class StompRequestService {
  final StompService _stompService;
  StompRequestService(this._stompService);

  Future<T> request<T>({
    required String sendDestination,
    required String subscribeDestination,
    required Map<String, dynamic> body,
    required T Function(dynamic json) response,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    if (!_stompService.isConnected) {
      throw StompRequestException(StompStatusCode.notConnected, "Stomp service not connected");
    }
    final completer = Completer<T>();
    Timer? timeoutTimer;
    void cleanUp() {
      timeoutTimer?.cancel();
      _stompService.unsubscribe(destination: subscribeDestination);
    }
    // 设置超时
    timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        cleanUp();
        completer.completeError(
          StompRequestException(StompStatusCode.timeOut, "Stomp Request timed out"),
        );
      }
    });

    _stompService.subscribe(destination: '/user/queue/errors', callback: (frame){
      cleanUp();
    });

    _stompService.subscribe(
      destination: subscribeDestination,
      callback: (frame) {
        try {
          if (completer.isCompleted) return;
          cleanUp();
          if (frame.body == null || frame.body!.isEmpty) {
            throw StompRequestException(StompStatusCode.responseError, "Stomp Response body is empty");
          }
          final decoded = jsonDecode(frame.body!); // dynamic 类型
          final result = response(decoded);
          completer.complete(result);
        } catch (e) {
          if (!completer.isCompleted) {
            completer.completeError(
              e is StompRequestException
                  ? e
                  : StompRequestException(StompStatusCode.responseError, "Response parsing failed: $e"),
            );
          }
        }
      },
    );
    _stompService.send(
      headers: {"lang":"en_US"},
      destination: sendDestination,
      body: jsonEncode(body), // 将Map转换为JSON字符串
    );
    return completer.future;
  }

  Future<List<T>> requestListData<T>({
    required String sendDestination,
    required String subscribeDestination,
    required Map<String, dynamic> body,
    required T Function(dynamic json) itemParser,
    Duration timeout = const Duration(seconds: 30),
    int pageSize = 50,
    bool isPaged = false,
  }) async {
    final allResults = <T>[];
    int currentPage = 1;
    bool hasMoreData = true;

    while (hasMoreData) {
      final requestBody = {
        ...body,
        if (isPaged) ...{
          'page': currentPage.toString(),
          'rows': pageSize.toString(),
        },
        'deviceId': DeviceUtil().terminalSerialNumber,
      };

      final responseJson = await request<Map<String, dynamic>>(
        sendDestination: sendDestination,
        subscribeDestination: subscribeDestination,
        body: requestBody,
        response: (json) {
          if (json is! Map<String, dynamic>) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response format: expected a JSON object, but got ${json.runtimeType}",
            );
          }
          if (!json.containsKey('code')) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response: missing 'code' field",
            );
          }
          if (json['code'] != StompStatusCode.success) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Request failed with code: ${json['code']}, message: ${json['message']}",
            );
          }
          return json;
        },
        timeout: timeout,
      );

      final data = responseJson['data'];

      if (isPaged) {
        if (data is! Map<String, dynamic>) {
          throw StompRequestException(
            StompStatusCode.responseError,
            "Expected paginated data to be a Map, but got ${data.runtimeType}",
          );
        }

        final items = data['data'] as List<dynamic>;
        final totalCount = data['totalCount'] as int;

        final parsedItems = items.map((item) => itemParser(item)).toList();
        allResults.addAll(parsedItems);

        if (allResults.length >= totalCount || items.length < pageSize) {
          hasMoreData = false;
        } else {
          currentPage++;
        }
      } else {
        if (data is! List<dynamic>) {
          throw StompRequestException(
            StompStatusCode.responseError,
            "Expected non-paginated data to be a List, but got ${data.runtimeType}",
          );
        }

        final parsedItems = data.map((item) => itemParser(item)).toList();
        allResults.addAll(parsedItems);
        hasMoreData = false; // 一次性数据，只请求一次
      }
    }

    return allResults;
  }

  Future<Map<String, List<T>>> requestMapListData<T>({
    required String sendDestination,
    required String subscribeDestination,
    required Map<String, dynamic> body,
    required T Function(dynamic json) itemParser,
    Duration timeout = const Duration(seconds: 30),
    int pageSize = 50,
    bool isPaged = false, // 新增参数，默认不分页
  }) async {
    if (!isPaged) {
      final responseJson = await request<Map<String, dynamic>>(
        sendDestination: sendDestination,
        subscribeDestination: subscribeDestination,
        body: {
          ...body,
          'deviceId': DeviceUtil().terminalSerialNumber,
        },
        response: (json) {
          print(json);
          if (json is! Map<String, dynamic>) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response format: expected a JSON object, but got ${json.runtimeType}",
            );
          }
          if (!json.containsKey('code')) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response: missing 'code' field",
            );
          }
          if (json['code'] != StompStatusCode.success) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Request failed with code: ${json['code']}, message: ${json['message']}",
            );
          }
          return json;
        },
        timeout: timeout,
      );

      final data = responseJson['data'];
      if (data is! Map<String, dynamic>) {
        throw StompRequestException(
          StompStatusCode.responseError,
          "Expected 'data' to be a Map, but got ${data.runtimeType}",
        );
      }

      final Map<String, List<T>> result = {};
      data.forEach((key, value) {
        if (value is List) {
          result[key] = value.map(itemParser).toList();
        } else {
          throw StompRequestException(
            StompStatusCode.responseError,
            "Expected list for key '$key', but got ${value.runtimeType}",
          );
        }
      });

      return result;
    } else {
      final Map<String, List<T>> allResults = {};
      // 先请求一次获取所有 keys（可以优化根据实际接口调整）
      final initialResponse = await request<Map<String, dynamic>>(
        sendDestination: sendDestination,
        subscribeDestination: subscribeDestination,
        body: {
          ...body,
          'deviceId': DeviceUtil().terminalSerialNumber,
          'page': '1',
          'rows': pageSize.toString(),
        },
        response: (json) {
          if (json is! Map<String, dynamic>) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response format: expected a JSON object, but got ${json.runtimeType}",
            );
          }
          if (!json.containsKey('code')) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Invalid response: missing 'code' field",
            );
          }
          if (json['code'] != StompStatusCode.success) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Request failed with code: ${json['code']}, message: ${json['message']}",
            );
          }
          return json;
        },
        timeout: timeout,
      );

      final data = initialResponse['data'];
      if (data is! Map<String, dynamic>) {
        throw StompRequestException(
          StompStatusCode.responseError,
          "Expected 'data' to be a Map, but got ${data.runtimeType}",
        );
      }

      for (final key in data.keys) {
        final List<T> items = [];
        int currentPage = 1;
        bool hasMore = true;

        while (hasMore) {
          final requestBody = {
            ...body,
            'deviceId': DeviceUtil().terminalSerialNumber,
            'page': currentPage.toString(),
            'rows': pageSize.toString(),
            'specKey': key, // 具体参数名请按接口调整
          };

          final pageResponse = await request<Map<String, dynamic>>(
            sendDestination: sendDestination,
            subscribeDestination: subscribeDestination,
            body: requestBody,
            response: (json) {
              print(json);
              if (json is! Map<String, dynamic>) {
                throw StompRequestException(
                  StompStatusCode.responseError,
                  "Invalid response format: expected a JSON object, but got ${json.runtimeType}",
                );
              }
              if (!json.containsKey('code')) {
                throw StompRequestException(
                  StompStatusCode.responseError,
                  "Invalid response: missing 'code' field",
                );
              }
              if (json['code'] != StompStatusCode.success) {
                throw StompRequestException(
                  StompStatusCode.responseError,
                  "Request failed with code: ${json['code']}, message: ${json['message']}",
                );
              }
              return json;
            },
            timeout: timeout,
          );

          final pageData = pageResponse['data'];
          if (pageData is! Map<String, dynamic> || !pageData.containsKey(key)) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Expected paginated data Map containing key '$key'",
            );
          }

          final itemsPage = pageData[key];
          if (itemsPage is! List) {
            throw StompRequestException(
              StompStatusCode.responseError,
              "Expected value for key '$key' to be a List, but got ${itemsPage.runtimeType}",
            );
          }

          items.addAll(itemsPage.map(itemParser));

          // 判断分页是否结束，假设接口返回 totalCount 字段，否则用长度判断
          final totalCount = pageData['totalCount'] ?? 0;
          if (totalCount > 0) {
            if (items.length >= totalCount || itemsPage.length < pageSize) {
              hasMore = false;
            } else {
              currentPage++;
            }
          } else {
            if (itemsPage.length < pageSize) {
              hasMore = false;
            } else {
              currentPage++;
            }
          }
        }

        allResults[key] = items;
      }

      return allResults;
    }
  }

  Future<Map<String, dynamic>> requestMapData({
    required String sendDestination,
    required String subscribeDestination,
    required Map<String, dynamic> body,
    Duration timeout = const Duration(seconds: 30),
  }) async {
    final responseJson = await request<Map<String, dynamic>>(
      sendDestination: sendDestination,
      subscribeDestination: subscribeDestination,
      body: {
        ...body,
        'deviceId': DeviceUtil().terminalSerialNumber,
      },
      response: (json) {
        if (json is! Map<String, dynamic>) {
          throw StompRequestException(
            StompStatusCode.responseError,
            "Invalid response format: expected a JSON object, but got ${json.runtimeType}",
          );
        }
        if (json['code'] != StompStatusCode.success) {
          throw StompRequestException(
            StompStatusCode.responseError,
            "Request failed with code: ${json['code']}, message: ${json['message']}",
          );
        }
        return json;
      },
      timeout: timeout,
    );

    final data = responseJson['data'];
    if (data is! Map<String, dynamic>) {
      throw StompRequestException(
        StompStatusCode.responseError,
        "Expected 'data' to be a Map, but got ${data.runtimeType}",
      );
    }

    return data;
  }


}

@riverpod
StompRequestService stompRequestService(Ref ref) {
  return StompRequestService(ref.watch(stompServiceProvider));
}