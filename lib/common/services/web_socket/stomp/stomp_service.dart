import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:stomp_dart_client/stomp_dart_client.dart';

part 'stomp_service.g.dart';

class StompService {
  StompClient? _stompClient;
  Timer? _heartbeatTimer;
  final Map<String,StompUnsubscribe> _subscriptions = {};
  String? _currentUrl;
  String? _heartbeatDestination;
  bool _isConnected = false;

  final _connectStateController = StreamController<bool>.broadcast();
  Stream<bool> get connectionState => _connectStateController.stream;

  bool get isConnected => _isConnected;

  void connect(
      {required String url,
      String? heartbeatDestination,
      Duration heartbeatInterval = const Duration(minutes: 1),
      Map<String, String>? headers}) {
    if (!_isConnected) {
      disconnect();
    }
    
    _currentUrl = url;
    _heartbeatDestination = heartbeatDestination;

    _stompClient = StompClient(
        config: StompConfig(
            url: url,
            onConnect: _onConnectCallback,
            beforeConnect: () async => print('[STOMP] Connecting to $url...'),
            onWebSocketError: (error) {
              print("[STOMP] Websocket Error: $error");
              _updateConnectionState(false);
            },
            onStompError: (frame) {
              print('[STOMP] STOMP Error：${frame.body}');
              _updateConnectionState(false);
            },
            onDisconnect: (frame) => _onDisconnect(),
            reconnectDelay: const Duration(seconds: 5),
            connectionTimeout: const Duration(seconds: 10),
            stompConnectHeaders: headers));
    _stompClient?.activate();
  }
  
  void _onConnectCallback(StompFrame frame) {
    print('[STOMP] Connected successfully');
    _updateConnectionState(true);
    _startHeartbeat(interval: const Duration(minutes: 1));
  }

  void _onDisconnect() {
    print('[STOMP] Disconnected');
    _updateConnectionState(false);
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void _updateConnectionState(bool connected) {
    _isConnected = connected;
    _connectStateController.add(connected);
  }

  void _startHeartbeat({required Duration interval}) {
    _heartbeatTimer?.cancel();

    if (_heartbeatDestination != null) {
      _heartbeatTimer = Timer.periodic(interval, (timer) {
        if (_isConnected) {
          send(
              destination: _heartbeatDestination!,
              body:
                  '{"type":"HeartBeat","timestamp":"${DateTime.now().toIso8601String()}"}');
          print('[STOMP] Heartbeat sent');
        }
      });
    }
  }

  void subscribe({required String destination,required void Function(StompFrame) callback,Map<String,String>? headers}) {
    if (!_isConnected) {
      throw Exception('[STOMP] Not connected when trying to subscribe to $destination');
    }

    unsubscribe(destination: destination);

    final sub = _stompClient!.subscribe(
        destination: destination, callback: callback, headers: headers);
    _subscriptions[destination] = sub;
    // print('[STOMP] Subscribed to $destination');
  }

  void unsubscribe({required String destination}) {
    final sub = _subscriptions[destination];
    if (sub != null) {
      sub();
      _subscriptions.remove(destination);
      // print('[STOMP] Unsubscribed from $destination');
    }
  }

  void send({required String destination, required String body, Map<String,String>? headers}) {
    if (!_isConnected) {
      throw Exception('[STOMP] Not connected when trying to send to $destination');
    }
    _stompClient!.send(destination: destination, body: body, headers: headers);
  }

  void disconnect() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;

    _subscriptions.clear();

    _stompClient?.deactivate();
    _stompClient = null;

    _updateConnectionState(false);
  }

  void reconnect() {
    if (_currentUrl != null) {
      disconnect();
      connect(url: _currentUrl!,heartbeatDestination: _heartbeatDestination);
    }
  }
}

@Riverpod(keepAlive: true)
StompService stompService(Ref ref) {
  final service = StompService();
  ref.onDispose(() => service.disconnect());
  return service;
}