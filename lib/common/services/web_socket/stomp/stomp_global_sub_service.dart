import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_service.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/auth/application/auth_service.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../routing/pages_route.dart';
import '../../../components/kp_toast.dart';
import '../../networking/intranet_service/api_intranet_service.dart';

part 'stomp_global_sub_service.g.dart';


class StompGlobalSubService {
  final StompService _stomp;
  final Ref _ref;

  StompGlobalSubService(this._stomp, this._ref) {
    _initSubscriptions();
  }

  void _initSubscriptions() {
    // 监听连接状态
    _stomp.connectionState.listen((connected) {
      if (connected) {
        _subscribeAll();
      } else {
        _unsubscribeAll();
      }
    });
  }

  void _subscribeAll() {
    _stomp.subscribe(
      destination: '/user/queue/public/message',
      callback: (frame) {
        print(frame.body);
        if (frame.body != null) {
          final Map<String, dynamic> response = jsonDecode(frame.body!);
          final type = response['type'];
          final deviceUuid = response['deviceUuid'];

          if (type == 'changeMainDevice' &&
              deviceUuid == DeviceUtil().terminalSerialNumber) {
            _ref.read(authServiceProvider).logout().then((_) async {
              final context = rootNavigatorKey.currentContext;
              /// TODO 这里后面需要开启
              // await _ref.read(authServiceProvider).clearAuthToStorageService();
              await _ref.read(apiIntranetServiceProvider).stopServer();
              if (context != null && context.mounted) {
                LaunchRoute().go(context);
              }
            }).onError((error, stack) {
              final context = rootNavigatorKey.currentContext;
              // if (context != null && context.mounted) {
              //   String errorMessage =
              //       error.toString().replaceFirst('Exception: ', '');
              //   KPToast.show(content: errorMessage);
              // }
            });
          }
        }
      },
    );
  }

  void _unsubscribeAll() {
    _stomp.unsubscribe(destination: '/user/queue/public/message');
  }
}

@Riverpod(keepAlive: true)
StompGlobalSubService stompGlobalSubService(Ref ref) {
  final stomp = ref.watch(stompServiceProvider);
  final service = StompGlobalSubService(stomp, ref);
  return service;
}
