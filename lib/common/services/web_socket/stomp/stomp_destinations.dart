class StompDestinations {

  /// 登录
  static const String loginSub = "/user/queue/login";
  static const String login = "/app_ws/login";

  /// 获取品牌列表
  static const String brandListSub = "/user/queue/merchant/brand/list";
  static const String brandList = "/app_ws/pos/getAllBrand";

  /// 获取门店数据
  static const String storeListSub = "/user/queue/merchant/store/list";
  static const String storeList = "/app_ws/store/list";

  /// 商品分类列表
  static const String productCategoriesSub = "/user/queue/deviceInit/getCategoryInfoList";
  static const String productCategories = "/app_ws/deviceInit/getCategoryInfoList";

  /// 商品列表
  static const String productsSub = "/user/queue/deviceInit/getCommodityInfoList";
  static const String products = "/app_ws/deviceInit/getCommodityInfoList";

  /// 就餐方式
  static const String diningTypeSub = "/user/queue/deviceInit/getEatModeInfoList";
  static const String diningTypes = "/app_ws/deviceInit/getEatModeInfoList";

  /// 获取门店信息
  static const String storeSub = "/user/queue/deviceInit/getStoreInfo";
  static const String stores = "/app_ws/deviceInit/getStoreInfo";

  /// 获取门店服务费、税费
  static const String taxAndFeeSub = "/user/queue/deviceInit/getTaxAndFee";
  static const String taxAndFees = "/app_ws/deviceInit/getTaxAndFee";

  /// 区域、桌台列表数据
  static const String storeTableSub = "/user/queue/deviceInit/getAreaList";
  static const String storeTables = "/app_ws/deviceInit/getAreaList";

  /// 租户预设原因
  static const String preparedReasonSub = "/user/queue/deviceInit/getPreparedReason";
  static const String preparedReasons = "/app_ws/deviceInit/getPreparedReason";

  /// 门店菜单与SKU关联数据
  static const String menuSkuRelationSub = "/user/queue/deviceInit/getMenuSkuRelation";
  static const String menuSkuRelations = "/app_ws/deviceInit/getMenuSkuRelation";

  /// 商品套餐分组
  static const String productComboGroupingSub = "/user/queue/deviceInit/getComboInfoList";
  static const String productComboGrouping = "/app_ws/deviceInit/getComboInfoList";

  /// 菜品做法列表
  static const String productPracticeSub = "/user/queue/deviceInit/getPracticeInfoList";
  static const String productPractice = "/app_ws/deviceInit/getPracticeInfoList";

  /// 商品规格数据
  static const String specInfoSub = "/user/queue/deviceInit/getSpecInfoList";
  static const String specInfo = "/app_ws/deviceInit/getSpecInfoList";

  /// 商品加料数据
  static const String productAddonsSub = "/user/queue/deviceInit/getAddonsInfoList";
  static const String productAddons = "/app_ws/deviceInit/getAddonsInfoList";

  /// 新增设备信息
  static const String deviceInsertSub = "/user/queue/device/insert";
  static const String deviceInsert = "/app_ws/device/insert";

  /// 查询设备详情信息
  static const String deviceInfoSub = "/user/queue/device/info";
  static const String deviceInfo = "/app_ws/device/info";

  /// 解绑设备信息
  static const String unbindDeviceSub = "/user/queue/device/unbind";
  static const String unbindDevice = "/app_ws/device/unbind";

  /// 获取员工信息
  static const String getUsersSub = "/user/queue/pos/getEmployeePage";
  static const String getUsers = "/app_ws/pos/getEmployeePage";

  /// 退出登录
  static const String logoutSub = "/user/queue/logout";
  static const String logout = "/app_ws/logout";

  /// 员工数据初始化
  static const String getEmployeeSub = "/user/queue/deviceInit/getEmployee";
  static const String getEmployee = "/app_ws/deviceInit/getEmployee";

  /// 税费服务费关联数据
  static const String getTaxRelationSub = "/user/queue/deviceInit/getTaxRelationServiceData";
  static const String getTaxRelation = "/app_ws/deviceInit/getTaxRelationServiceData";

  /// 商品税费关联数据
  static const String getProductRelationTaxSub = "/user/queue/deviceInit/getProductRelationTaxData";
  static const String getProductRelationTax = "/app_ws/deviceInit/getProductRelationTaxData";

}