import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:kpos/features/cart/data/cart_intranet_repository.dart';
import 'package:kpos/features/cart/presentation/cart_controller.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

part 'web_socket_service.g.dart';

enum WSMessageType {cartUpdated,priceUpdated,deleteCart,clientId,heartbeat,unknown}

class WSMessage {
  final WSMessageType type;
  final dynamic payload;

  WSMessage(this.type,this.payload);
}

class WebSocketService {
  final Ref _ref;
  WebSocketChannel? _channel;
  String? _currentUrl;
  bool _isConnecting = false;
  String clientId = '';

  WebSocketService({required ref}) : _ref = ref;

  Future<void> connect() async {
    if (_isConnecting || _currentUrl == _getCurrentUrl()) return;
    _isConnecting = true;
    try {
      final url = _getCurrentUrl();
      await _connect(url);
      _currentUrl = url;
    } catch (e) {
      print(e);
    } finally {
      _isConnecting = false;
    }
  }

  Future<void> _connect(String url) async {
    await _channel?.sink.close();
    try {
      print(Uri.parse(url));
      _channel = WebSocketChannel.connect(Uri.parse(url))
          ..stream.handleError((error) {
            print('WebSocket Error: $error');
            _reconnect();
          });
    } catch (e) {
      print('Connection Failed: $e');
      _reconnect();
    }
  }

  void _reconnect() {
    Future.delayed(const Duration(seconds: 3), () => connect());
  }

  String _getCurrentUrl() {
    final baseUrl = _ref.read(intranetBaseUrlProvider);
    return 'ws://$baseUrl/ws';
  }

  bool get isConnected => _channel?.closeCode == null;

  void send(String message) {
    if (!isConnected) {
      connect();
      return;
    }
    try {
      _channel?.sink.add(message);
    } catch (e) {
      print('Send Message Failed: $e');
      connect();
    }
  }

  Future<void> disconnect() async {
    await _channel?.sink.close();
    _currentUrl = null;
  }

  void sendHeartbeat() {
    final message = jsonEncode({
      'type':'heartbeat_ack',
      'client_id':clientId,
    });
    send(message);
  }

  void dispoase() => disconnect();

  Stream<WSMessage> get messageStream =>
      _channel?.stream.map(_parseMessage) ?? const Stream.empty();

  WSMessage _parseMessage(dynamic data) {
    try {
      final json = jsonDecode(data as String) as Map<String, dynamic>;
      final type = switch (json['type'] as String) {
        'cart_updated' => WSMessageType.cartUpdated,
        'price_updated' => WSMessageType.priceUpdated,
        'delete_cart' => WSMessageType.deleteCart,
        'client_id' => WSMessageType.clientId,
        'heartbeat' => WSMessageType.heartbeat,
        _ => WSMessageType.unknown,
      };
      return WSMessage(type, json['payload']);
    } catch (e) {
      return WSMessage(WSMessageType.unknown, null);
    }
  }
}

@Riverpod(keepAlive: true)
WebSocketService webSocketService(Ref ref) {
  return WebSocketService(ref: ref);
}

final webSocketMessageProvider = StreamProvider<WSMessage>((ref) {
  return ref.watch(webSocketServiceProvider).messageStream;
});

@Riverpod(keepAlive: true)
class WebSocketListener extends _$WebSocketListener {
  @override
  void build() {
    // 监听 WebSocket 消息流
    ref.listen<AsyncValue<WSMessage>>(
      webSocketMessageProvider,
          (_, next) {
        next.whenData((message) {
          _handleMessage(message);
        });
      },
    );
  }

  void _handleMessage(WSMessage message) {
    switch (message.type) {
      case WSMessageType.cartUpdated:
      case WSMessageType.deleteCart:
        ref.invalidate(cartControllerProvider);
        ref.invalidate(productControllerProvider);
        ref.invalidate(cartPriceProvider);
        break;
      case WSMessageType.priceUpdated:
        break;
      case WSMessageType.clientId:
        print("111111111111$message");
        ref.read(webSocketServiceProvider).clientId = message.payload['client_id'];
        break;
      case WSMessageType.heartbeat:
        ref.read(webSocketServiceProvider).sendHeartbeat();
        break;
      case WSMessageType.unknown:
        print('未知消息类型: ${message.payload}');
        break;
    }
  }
}