import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kp_web_socket_client.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'web_socket_server_service.g.dart';

class WebSocketServerService {
  final Ref _ref;

  Map<String, KPWebSocketClient> activeClients = {};

  Map<String, DateTime> lastHeartbeat = {};

  final Duration heartbeatInterval = const Duration(seconds: 30);

  final Duration maxIdleTime = const Duration(seconds: 60);

  WebSocketServerService({required Ref ref}) : _ref = ref;

  void addClient(KPWebSocketClient client) {
    activeClients[client.clientId!] = client;
    lastHeartbeat[client.clientId!] = DateTime.now();
    _startHeartbeat(client.clientId!);
    client.channel.stream.listen((message) {
      _onMessageReceived(client.clientId!, message);
    }, onDone: () {
      deleteClient(client.clientId!);
    });
  }

  void deleteClient(String clientId) {
    activeClients.remove(clientId);
    lastHeartbeat.remove(clientId);
  }

  void _startHeartbeat(String clientId) {
    Timer.periodic(heartbeatInterval, (timer) {
      if (!activeClients.containsKey(clientId)) {
        timer.cancel();
        return;
      }
      if (DateTime.now().difference(lastHeartbeat[clientId]!) > maxIdleTime) {
        deleteClient(clientId);
        activeClients[clientId]?.channel.sink.close();
      } else {
        _sendHeartbeat(clientId);
      }
    });
  }

  void _sendHeartbeat(String clientId) {
    try {
      final message = jsonEncode({'type': 'heartbeat', 'client_id': clientId});
      activeClients[clientId]?.channel.sink.add(message);
    } catch (e) {
      print('Failed to send heartbeat to device $clientId：$e');
    }
  }

  void _onMessageReceived(String clientId, dynamic message) {
    print(message);
    final parsed = jsonDecode(message as String);
    switch (parsed['type']) {
      case 'heartbeat_ack':
        print('设备 $clientId 心跳正常');
        lastHeartbeat[clientId] = DateTime.now();
        break;
      default:
        break;
    }
  }

  void sendAllMessage(String msgType,String sendClientId) {
    final message = jsonEncode({
      'type': msgType,
    });
    print("当前连接的设备${activeClients.keys}");
    print("当前是$sendClientId哪个设备发送的消息");
    List<KPWebSocketClient>? clients = activeClients.values.toList();
    for (final client in clients) {
      if (client.channel.closeCode == null && client.clientId != sendClientId) {
        print("通知设备${client.clientId}发送消息");
        client.channel.sink.add(message);
      }
    }
  }
}

@Riverpod(keepAlive: true)
WebSocketServerService webSocketServerService(Ref ref) {
  return WebSocketServerService(ref: ref);
}
