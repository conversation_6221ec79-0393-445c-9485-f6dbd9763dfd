import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'network_exception.freezed.dart';

@freezed
class NetworkException with _$NetworkException implements Exception {
  const factory NetworkException.formatException({
    required Object exception,
    required StackTrace stacktrace,
    Response? response}) = _FormatException;

  const factory NetworkException.badResponse({
    required DioException exception,
  }) = _ApiException;

  const factory NetworkException.unrecognizedException({
    required Object exception,
  }) = _UnrecognizedException;

  const factory NetworkException.cancelException({
    required DioException exception,
  }) = _CancelException;

  const factory NetworkException.connectTimeoutException({
    required DioException exception,
  }) = _ConnectTimeoutException;

  const factory NetworkException.receiveTimeoutException({
    required DioException exception,
  }) = _ReceiveTimeoutException;

  const factory NetworkException.sendTimeoutException({
    required DioException exception,
  }) = _SendTimeoutException;

  const factory NetworkException.badCertificateException({
    required DioException exception,
  }) = _BadCertificateException;

  /// 连接错误
  /// 对应 [DioExceptionType.connectionError]
  const factory NetworkException.connectionErrorException({
    required DioException exception,
  }) = _ConnectionErrorException;

  /// 连接错误
  /// 对应 [DioExceptionType.connectionError]
  const factory NetworkException.securityErrorException({
    required Object exception,
  }) = _SecurityErrorException;

  /// 未知网络异常 对应 [DioExceptionType.unknown]
  const factory NetworkException.unknownNetworkException({
    required DioException exception,
  }) = UnknownNetworkException;

  const NetworkException._();

  factory NetworkException.formDioException(DioException dioException) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
        return NetworkException.connectTimeoutException(
            exception: dioException);
      case DioExceptionType.sendTimeout:
        return NetworkException.sendTimeoutException(exception: dioException);
      case DioExceptionType.receiveTimeout:
        return NetworkException.receiveTimeoutException(
            exception: dioException);
      case DioExceptionType.badCertificate:
        return NetworkException.badCertificateException(
            exception: dioException);
      case DioExceptionType.badResponse:
        return NetworkException.badResponse(exception: dioException);
      case DioExceptionType.cancel:
        return NetworkException.cancelException(exception: dioException);
      case DioExceptionType.connectionError:
        return NetworkException.connectionErrorException(
            exception: dioException);
      case DioExceptionType.unknown:
        return NetworkException.unknownNetworkException(
            exception: dioException);
    }
  }
}
