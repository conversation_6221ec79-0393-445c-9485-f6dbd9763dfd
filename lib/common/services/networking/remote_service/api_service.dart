import 'package:dio/dio.dart';
import 'package:dio/src/cancel_token.dart';
import 'package:flutter/foundation.dart';
import 'package:kpos/common/services/networking/constants/request_extra_key.dart';
import 'package:kpos/common/services/networking/remote_service/api_endpoint.dart';
import 'package:kpos/common/services/networking/remote_service/api_interface.dart';
import 'package:kpos/common/services/networking/remote_service/dio_service.dart';
import 'package:kpos/common/services/networking/remote_service/network_exception.dart';
import 'package:kpos/common/services/networking/remote_service/typedefs.dart';

import 'base_response.dart';

typedef ApiExceptionHandler = Exception? Function(BaseResponse response);

class ApiService implements ApiInterface {

  static const int _successCode = 200;

  final ApiExceptionHandler? _apiExceptionHandler;

  late final DioService _dioService;

  ApiService(DioService dioService, {ApiExceptionHandler? apiExceptionHandler})
      : _dioService = dioService,
        _apiExceptionHandler = apiExceptionHandler;

  @override
  Future<T> getData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(JSON responseData) converter}) {
    return getBasicData(
      endpoint: endpoint,
      sendTimeout: sendTimeout,
      receiveTimeout: receiveTimeout,
      queryParams: queryParams,
      headers: headers,
      extra: extra,
      cancelToken: cancelToken,
      requiresAuthToken: requiresAuthToken,
      converter: (data) => converter(data as JSON),
    );
  }

  @override
  Future<T> getBasicData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(Object responseData) converter}) {
    return _runCatchingExceptions<T>(
      requestTask: () => _dioService.post(
        path: endpoint.path,
        data: queryParams,
        options: Options(
          headers: headers,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          responseType: ResponseType.json,
          extra: {
            ...?extra,
            ...{RequestExtraKey.requiresAuthToken: requiresAuthToken}
          },
        ),
        cancelToken: cancelToken,
      ),
      converter: converter,
    );
  }

  @override
  Future<List<T>> getListData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(JSON responseDataItem) converter}) {
    return getBasicListData<T>(
      endpoint: endpoint,
      sendTimeout: sendTimeout,
      receiveTimeout: receiveTimeout,
      queryParams: queryParams,
      cancelToken: cancelToken,
      requiresAuthToken: requiresAuthToken,
      converter: (itemData) => converter(itemData as JSON),
    );
  }

  @override
  Future<List<T>> getBasicListData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(Object responseDataItem) converter}) {
    return getBasicData(
        endpoint: endpoint,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        queryParams: queryParams,
        headers: headers,
        extra: extra,
        cancelToken: cancelToken,
        requiresAuthToken: requiresAuthToken,
        converter: (responseData) {
          final List listData = responseData as List;
          return listData.map((e) => converter(e)).toList();
        });
  }

  @override
  Future<ApiPaginationData<T>> getPaginationData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required int page,
        int pageSize = defaultPageSize,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(JSON responsePageItem) converter}) {
    return getBasicPaginationData(
      endpoint: endpoint,
      sendTimeout: sendTimeout,
      receiveTimeout: receiveTimeout,
      page: page,
      pageSize: pageSize,
      queryParams: queryParams,
      headers: headers,
      extra: extra,
      cancelToken: cancelToken,
      requiresAuthToken: requiresAuthToken,
      converter: (data) => converter(data as JSON),
    );
  }

  @override
  Future<ApiPaginationData<T>> getBasicPaginationData<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required int page,
        int pageSize = defaultPageSize,
        JSON? queryParams,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(Object responsePageItem) converter}) {
    final paginationQueryParams = {"page": page, "rows": pageSize};
    return getBasicData(
      endpoint: endpoint,
      sendTimeout: sendTimeout,
      receiveTimeout: receiveTimeout,
      queryParams: {...paginationQueryParams, ...?queryParams},
      headers: headers,
      extra: extra,
      cancelToken: cancelToken,
      requiresAuthToken: requiresAuthToken,
      converter: (data) {
        return ApiPaginationData.fromJson(
            data as JSON, (dataItem) => converter(dataItem as Object));
      },
    );
  }

  @override
  Future<void> postData(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required JSON data,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true}) {
    return postDataThenResult(
        endpoint: endpoint,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        data: data,
        headers: headers,
        extra: extra,
        cancelToken: cancelToken,
        requiresAuthToken: requiresAuthToken,
        converter: (_) {});
  }

  @override
  Future<T> postDataThenResult<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required JSON data,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(Object responseData) converter}) =>
      postObjectDataThenResult(
          endpoint: endpoint,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          data: data,
          headers: headers,
          extra: extra,
          cancelToken: cancelToken,
          requiresAuthToken: requiresAuthToken,
          converter: converter);

  @override
  Future<void> postObjectData(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required Object data,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true}) {
    return postObjectDataThenResult(
        endpoint: endpoint,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        data: data,
        headers: headers,
        extra: extra,
        cancelToken: cancelToken,
        requiresAuthToken: requiresAuthToken,
        converter: (_) {});
  }

  @override
  Future<T> postObjectDataThenResult<T>(
      {required ApiEndpoint endpoint,
        Duration? sendTimeout,
        Duration? receiveTimeout,
        required Object data,
        JSON? headers,
        JSON? extra,
        CancelToken? cancelToken,
        bool requiresAuthToken = true,
        required T Function(Object responseData) converter}) {
    return _runCatchingExceptions<T>(
      requestTask: () => _dioService.post(
        path: endpoint.path,
        data: data,
        options: Options(
          headers: headers,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          extra: {
            ...?extra,
            ...{RequestExtraKey.requiresAuthToken: requiresAuthToken}
          },
        ),
        cancelToken: cancelToken,
      ),
      converter: converter,
    );
  }

  @override
  void cancelRequests({CancelToken? cancelToken}) {
    _dioService.cancelRequests(cancelToken: cancelToken);
  }

  Future<T> _runCatchingExceptions<T>(
      {required Future<Response> Function() requestTask,
        required T Function(Object data) converter}) async {
    Response dioResponse;
    try {
      dioResponse = await requestTask();

      if (dioResponse.extra["retryResponse"] is Response) {
        dioResponse = dioResponse.extra["retryResponse"] as Response;
      }
    } catch (error) {
      if (error is UnknownNetworkException &&
          (error.exception.error is NetworkException)) {
        throw error.exception.error!;
      }
      rethrow;
    }

    final baseResponse =
    BaseResponse.fromJson(dioResponse.data as JSON, (value) => value);
    // code == 10000 时，数据应当能正常序列化
    if (baseResponse.code == _successCode) {
      jsonParser(JSON data) =>
          BaseResponse.fromJson(data, (data) => converter(data as Object));
      final responseModel = await compute(jsonParser, dioResponse.data as JSON);
      return responseModel.data;
    } else {
      throw _exceptionByResponse(
          baseResponse: baseResponse, dioResponse: dioResponse);
    }
  }

  Exception _exceptionByResponse<T>(
      {required BaseResponse baseResponse, required Response dioResponse}) {
    final apiException = _apiExceptionHandler?.call(baseResponse);
    return apiException ??
        NetworkException.unrecognizedException(exception: dioResponse);
  }
}