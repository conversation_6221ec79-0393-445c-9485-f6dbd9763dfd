import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/remote_service/api_interface.dart';
import 'package:kpos/common/services/networking/remote_service/api_service_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'kpos_api_service.g.dart';

const kpayBaseUrl = String.fromEnvironment(
  'kpayBaseUrl',
  defaultValue: 'https://api-kpay.sg.kpay-group.com',
);

@riverpod
ApiInterface kposApiService(Ref ref) {
  return ref.watch(apiServiceProvider(
      baseUrl: kpayBaseUrl));
}


