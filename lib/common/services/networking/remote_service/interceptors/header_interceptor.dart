import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/constants/headers_key.dart';
import 'package:kpos/common/services/web_socket/web_socket_service.dart';

class HeaderInterceptor extends Interceptor {
  final Ref _ref;
  HeaderInterceptor(this._ref) : super();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final defaultHeaders = generateHeaders();
    options.headers = {...defaultHeaders,...options.headers};
    return handler.next(options);
  }

  Map<String, Object?> generateHeaders() {
    final clientId = _ref.read(webSocketServiceProvider).clientId;
    Map<String,Object?> headers = {
      HeadersKey.clientId:clientId
    };
    return headers;
  }

}