import 'package:dio/dio.dart';

class LoggerInterceptor extends LogInterceptor {

  LoggerInterceptor._internal({
    super.request = true,
    super.requestHeader = true,
    super.requestBody = true,
    super.responseHeader = true,
    super.responseBody = true,
    super.error = true,
    super.logPrint,
  });

  static List<Interceptor> get loggerInterceptors {
    return [
      LoggerInterceptor._internal(),
    ];
  }
}