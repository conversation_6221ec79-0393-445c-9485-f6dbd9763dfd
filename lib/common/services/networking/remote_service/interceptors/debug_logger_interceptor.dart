import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 增强调试日志拦截器
/// 专门用于开发阶段详细输出网络请求和响应信息
class DebugLoggerInterceptor extends Interceptor {
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      _printRequestDetails(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      _printResponseDetails(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      _printErrorDetails(err);
    }
    super.onError(err, handler);
  }

  /// 打印请求详情
  void _printRequestDetails(RequestOptions options) {
    print('\n' + '=' * 80);
    print('🚀 【发起网络请求】');
    print('=' * 80);
    
    // 完整请求URL
    final url = options.uri.toString();
    print('📍 完整请求链接: $url');
    
    // 请求方法
    print('🔧 请求方法: ${options.method}');
    
    // 请求头
    if (options.headers.isNotEmpty) {
      print('📋 请求头:');
      options.headers.forEach((key, value) {
        print('   $key: $value');
      });
    }
    
    // 请求参数
    if (options.data != null) {
      print('📦 请求体数据:');
      // print('   ${_formatJson(options.data)}');
      _printLongJson(options.data);
    }
    
    // 查询参数
    if (options.queryParameters.isNotEmpty) {
      print('🔍 查询参数:');
      options.queryParameters.forEach((key, value) {
        print('   $key: $value');
      });
    }
    
    print('=' * 80);
  }

  /// 打印响应详情
  void _printResponseDetails(Response response) {
    print('\n' + '=' * 80);
    print('✅ 【收到网络响应】');
    print('=' * 80);
    
    // 请求URL
    print('📍 响应链接: ${response.requestOptions.uri}');
    
    // 状态码
    print('📊 HTTP状态码: ${response.statusCode}');
    
    // 响应头
    if (response.headers.map.isNotEmpty) {
      print('📋 响应头:');
      response.headers.map.forEach((key, value) {
        print('   $key: ${value.join(', ')}');
      });
    }
    
    // 响应数据
    print('📦 完整响应数据:');
    // print('${_formatJson(response.data)}');
    _printLongJson(response.data);
    
    print('=' * 80);
  }

  /// 分段打印长JSON，避免被截断
void _printLongJson(dynamic data) {
  final jsonString = _formatJson(data);
  const int chunkSize = 900; // 每段800字符，避免被截断
  
  if (jsonString.length <= chunkSize) {
    print(jsonString);
    return;
  }
  
  print('📄 数据较长，分段输出:');
  for (int i = 0; i < jsonString.length; i += chunkSize) {
    int end = (i + chunkSize < jsonString.length) ? i + chunkSize : jsonString.length;
    String chunk = jsonString.substring(i, end);
    print('📄 第${(i ~/ chunkSize) + 1}段: $chunk');
  }
}

  /// 打印错误详情
  void _printErrorDetails(DioException error) {
    print('\n' + '=' * 80);
    print('❌ 【网络请求错误】');
    print('=' * 80);
    
    print('📍 错误链接: ${error.requestOptions.uri}');
    print('🔧 请求方法: ${error.requestOptions.method}');
    print('💥 错误类型: ${error.type}');
    print('📝 错误信息: ${error.message}');
    
    if (error.response != null) {
      print('📊 响应状态码: ${error.response!.statusCode}');
      print('📦 错误响应数据:');
      print('${_formatJson(error.response!.data)}');
    }
    
    print('=' * 80);
  }

  /// 格式化JSON输出
  String _formatJson(dynamic data) {
    if (data == null) return 'null';
    
    try {
      // 如果是字符串，尝试解析为JSON
      if (data is String) {
        // 如果已经是格式化的JSON字符串，直接返回
        return data;
      }
      
      // 如果是Map或List，转换为美化的JSON字符串
      return _prettyPrintJson(data);
    } catch (e) {
      return data.toString();
    }
  }

  /// 美化JSON输出
  String _prettyPrintJson(dynamic data, {int indent = 0}) {
    String indentStr = '  ' * indent;
    
    if (data is Map) {
      if (data.isEmpty) return '{}';
      
      StringBuffer buffer = StringBuffer('{\n');
      List<String> entries = [];
      
      data.forEach((key, value) {
        String valueStr = _prettyPrintJson(value, indent: indent + 1);
        entries.add('${indentStr}  "$key": $valueStr');
      });
      
      buffer.write(entries.join(',\n'));
      buffer.write('\n${indentStr}}');
      return buffer.toString();
    } else if (data is List) {
      if (data.isEmpty) return '[]';
      
      StringBuffer buffer = StringBuffer('[\n');
      List<String> entries = [];
      
      for (var item in data) {
        String itemStr = _prettyPrintJson(item, indent: indent + 1);
        entries.add('${indentStr}  $itemStr');
      }
      
      buffer.write(entries.join(',\n'));
      buffer.write('\n${indentStr}]');
      return buffer.toString();
    } else if (data is String) {
      return '"$data"';
    } else {
      return data.toString();
    }
  }
}