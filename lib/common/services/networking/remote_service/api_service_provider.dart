import 'package:dio/dio.dart';
import 'package:kpos/common/services/networking/remote_service/interceptors/header_interceptor.dart';
import 'package:kpos/common/services/networking/remote_service/interceptors/logger_interceptor.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:kpos/common/services/networking/remote_service/interceptors/debug_logger_interceptor.dart';

import 'api_interface.dart';
import 'api_service.dart';
import 'base_response.dart';
import 'dio_service.dart';

part 'api_service_provider.g.dart';

@Riverpod(keepAlive: true)
ApiInterface apiService(ApiServiceRef ref,
    {required String baseUrl, ApiExceptionHandler? apiExceptionHandler}) {
  final dioService = ref.read(dioServiceProvider(baseUrl: baseUrl));
  return ApiService(dioService, apiExceptionHandler: apiExceptionHandler);
}

final dioTimeoutOptions = BaseOptions(
  connectTimeout: const Duration(seconds: 15),
  sendTimeout: const Duration(seconds: 15),
  receiveTimeout: const Duration(seconds: 30),
);

@riverpod
//ignore: avoid-unused-parameters
Dio dio(DioRef ref, {required String baseUrl}) {
  final dio = Dio(
    dioTimeoutOptions.copyWith(baseUrl: baseUrl),
  );
  return dio;
}

@riverpod
DioService dioService(DioServiceRef ref, {required String baseUrl}) {
  final dio = ref.read(dioProvider(baseUrl: baseUrl));
  return DioService(
    dioClient: dio,
    interceptors: [
      HeaderInterceptor(ref),
      // ...LoggerInterceptor.loggerInterceptors,
      // == 修改开始 == 替换原有的日志拦截器
      DebugLoggerInterceptor(), // 使用增强版调试日志
      // == 修改结束 ==
    ],
  );
}
