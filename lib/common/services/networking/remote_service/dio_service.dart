import 'package:dio/dio.dart';
import 'package:kpos/common/services/networking/remote_service/network_exception.dart';
import 'package:kpos/common/services/networking/remote_service/typedefs.dart';

class DioService {
  late final Dio _dio;
  late final CancelToken _cancelToken;

  DioService({required Dio dioClient, Iterable<Interceptor>? interceptors})
      : _dio = dioClient,
        _cancelToken = CancelToken() {
    if (interceptors != null) _dio.interceptors.addAll(interceptors);
  }

  void cancelRequests({CancelToken? cancelToken}) {
    if (cancelToken == null) {
      _cancelToken.cancel('Canceled');
    } else {
      cancelToken.cancel();
    }
  }

  Future<Response<T>> get<T>(
      {required String path,
      JSON? queryParams,
      Options? options,
      CancelToken? cancelToken}) async {
    return _runCatchingException(() {
      return _dio.get(path,
          queryParameters: queryParams,
          options: options,
          cancelToken: cancelToken ?? _cancelToken);
    });
  }

  Future<Response<T>> post<T>(
      {required String path,
      Object? data,
      Options? options,
      CancelToken? cancelToken}) async {
    return _dio.post(path,
        data: data, options: options, cancelToken: cancelToken ?? _cancelToken);
  }

  Future<T> _runCatchingException<T>(Future<T> Function() task) async {
    try {
      return await task();
    } on DioException catch (dioException) {
      throw NetworkException.formDioException(dioException);
    } catch (error) {
      throw NetworkException.unrecognizedException(exception: error);
    }
  }
}