import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/common/services/networking/remote_service/api_endpoint.dart';
import 'package:kpos/common/services/networking/remote_service/typedefs.dart';

part 'api_interface.freezed.dart';
part 'api_interface.g.dart';
part 'api_pagination_data.dart';

const defaultPageSize = 20;

abstract class ApiInterface {
  const ApiInterface();

  Future<T> getData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(JSON responseData) converter,
  });

  /// 获取基础类型数据,response.data 为 基本类型
  ///
  /// [T] 为 response.data 反序列化后的数据类型，bool,String .. etc.
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有Token 时会在 Header 插入 Token；
  /// 默认为 true
  ///
  /// [converter] response data 的 反序列化器
  Future<T> getBasicData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(Object responseData) converter,
  });

  /// 获取数组数据,response.data 为 [List<JSON>]
  ///
  /// [T] 为 response.data 中的 element 反序列化后的实体类
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有Token 时会在 Header 插入 Token；
  /// 默认为 true
  ///
  /// [converter] response data item 的 反序列化器
  Future<List<T>> getListData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(JSON responseDataItem) converter,
  });

  /// 获取数组数据,response.data 为 [List<Object>]
  ///
  /// [T] 为 response.data 中的 element 反序列化后的基础类型
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有Token 时会在 Header 插入Token；
  /// 默认为 true
  ///
  /// [converter] response data item 的 反序列化器
  Future<List<T>> getBasicListData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(Object responseDataItem) converter,
  });

  /// 获取分页数据,response.data 为 分页数据类型，能反序列化为 [ApiPaginationData]
  ///
  /// [T] 为 [ApiPaginationData.data] List 的 element 反序列化后的实体类型
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入Token；
  /// 默认为 true
  ///
  /// [converter] 单条分页数据 item 的 反序列化器
  Future<ApiPaginationData<T>> getPaginationData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required int page,
    int pageSize = defaultPageSize,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(JSON responsePageItem) converter,
  });

  /// 获取分页数据,response.data 为 分页数据类型，能反序列化为 [ApiPaginationData]
  ///
  /// [T] 为 [ApiPaginationData.data] List 的 element 反序列化后的基础数据类型
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入 Token；
  /// 默认为 true
  ///
  /// [converter] 单条分页数据 item 的 反序列化器
  Future<ApiPaginationData<T>> getBasicPaginationData<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required int page,
    int pageSize = defaultPageSize,
    JSON? queryParams,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(Object responsePageItem) converter,
  });

  /// POST 提交数据
  ///
  /// 大部份提交场景 response.data 都为 [null]
  /// 如需接收&响应返回值请使用 [postDataThenResult]
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入Token；
  /// 默认为 true
  Future<void> postData({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required JSON data,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
  });

  /// POST 提交数据，并响应 response.data 数据
  ///
  /// [T] 为 response.data 反序列化后的数据类型
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入 Token；
  /// 默认为 true
  ///
  /// [converter] response data 的 反序列化器
  Future<T> postDataThenResult<T>({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required JSON data,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(Object responseData) converter,
  });

  /// POST 提交数据；data 类型不做限制
  ///
  /// 大部份提交场景 response.data 都为 [null]
  /// 如需接收&响应返回值请使用 [postDataThenResult]
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入Token；
  /// 默认为 true
  Future<void> postObjectData({
    required ApiEndpoint endpoint,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required Object data,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
  });

  /// POST 提交数据，并响应 response.data 数据；data 类型不做限制
  ///
  /// [T] 为 response.data 反序列化后的数据类型
  ///
  /// [headers] Request Headers
  ///
  /// [extra] An extra map that you can retrieve in [Interceptor]
  ///
  /// [requiresAuthToken] 是否需要 Token
  /// 为 true 时且本地有 Token 时会在 Header 插入 Token；
  /// 默认为 true
  ///
  /// [converter] response data 的 反序列化器
  Future<T> postObjectDataThenResult<T>({
    required ApiEndpoint endpoint,
    required Object data,
    JSON? headers,
    JSON? extra,
    CancelToken? cancelToken,
    bool requiresAuthToken = true,
    required T Function(Object responseData) converter,
  });

  void cancelRequests({CancelToken? cancelToken});
}