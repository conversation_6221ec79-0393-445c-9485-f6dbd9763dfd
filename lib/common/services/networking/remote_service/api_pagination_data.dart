part of 'api_interface.dart';

@Freezed(genericArgumentFactories: true)
class ApiPaginationData<T> with _$ApiPaginationData<T> {
  const factory ApiPaginationData({
    /// 总数量
    required int totalCount,

    /// 分页数据
    required List<T> data,
  }) = _ApiPaginationData;

  factory ApiPaginationData.fromJson(
      Map<String, dynamic> json,
      T Function(Object?) fromJsonT,
      ) =>
      _$ApiPaginationDataFromJson(json, fromJsonT);
}
