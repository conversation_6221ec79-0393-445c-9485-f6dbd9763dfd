import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final networkProvider = StateNotifierProvider<NetworkProvider,ConnectivityResult>(
      (ref) => NetworkProvider(),
);

class NetworkProvider extends StateNotifier<ConnectivityResult> {
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectSubscription;
  
  NetworkProvider() : super(ConnectivityResult.none) {
    _initialize();
  }

  void _initialize() {
    _connectSubscription = _connectivity.onConnectivityChanged.listen((List<ConnectivityResult> results) {
      final ConnectivityResult result = results.isNotEmpty ? results.first : ConnectivityResult.none;
      if (state != result) {
        state = result;
      }
    });
  }

  @override
  dispose() {
    _connectSubscription?.cancel();
    super.dispose();
  }
}