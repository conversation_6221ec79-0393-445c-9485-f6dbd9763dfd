import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/network_provider.dart';

class NetworkObserver extends ConsumerStatefulWidget {

  final Widget child;
  const NetworkObserver(this.child, {super.key});

  @override
  ConsumerState createState() => _NetworkObserverState();
}

class _NetworkObserverState extends ConsumerState<NetworkObserver> {
  bool _dialogShown = false;

  @override
  Widget build(BuildContext context) {
    ref.listen(networkProvider, (previous, next) {
      print(next);
      if (next == ConnectivityResult.none) {
        _showNoNetworkDialog(context);
      }
    });
    return widget.child;
  }

  void _showNoNetworkDialog(BuildContext context) {
    if (_dialogShown) return;
    _dialogShown = true;
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('网络连接丢失'),
            content: const Text('请检测您的网络连接'),
            actions: [
              TextButton(onPressed: (){
                Navigator.of(context).pop();
                _dialogShown = false;
          }, child: const Text('确定')),
            ],
          );
        });
  }
}
