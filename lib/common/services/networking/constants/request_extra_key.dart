class RequestExtraKey {
  /// 标记该请求是否需要携带 AccessToken
  static const requiresAuthToken = "requiresAuthToken";

  /// 原始数据，
  /// 加密前保留，用于重试请求时做重新签名&加密啊
  static const originalData = "originalData";

  /// 加密明文，供调试使用
  static const signPlaintext = "signPlaintext";

  /// 客户端私钥 用于解密服务端返回数据和签名
  static const clientPrivateKey = "clientPrivateKey";

  /// 服务端密钥 用于加密请求数据
  static const servicePublicKey = "servicePublicKey";

  /// 当前请求的触发请求
  ///
  /// 请求不由业务层触发，而是由Token 过期、刷新Token后重试触发，
  /// Token 刷新、刷新Token后重试请求的Extra须携带源请求的 RequestOptions 对象供Alice 做关联标记使用。
  static const triggerRequest = "triggerRequest";
}
