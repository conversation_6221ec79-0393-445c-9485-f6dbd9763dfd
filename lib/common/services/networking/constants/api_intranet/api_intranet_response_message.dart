import 'api_intranet_message_key.dart';
import 'api_intranet_locale_key.dart';

class ResponseMessage {
  static const Map<String, Map<String, String>> messages = {
    MessageKey.invalidParam: {
      LocaleKey.zh: '参数无效',
      LocaleKey.zhHant: '參數無效',
      LocaleKey.en: 'Invalid parameters',
    },
    MessageKey.notFound: {
      LocaleKey.zh: '未找到资源',
      LocaleKey.zhHant: '未找到資源',
      LocaleKey.en: 'Resource not found',
    },
    MessageKey.internalError: {
      LocaleKey.zh: '服务器内部错误',
      LocaleKey.zhHant: '伺服器內部錯誤',
      LocaleKey.en: 'Internal server error',
    },
    MessageKey.operationSuccess: {
      LocaleKey.zh: '操作成功',
      LocaleKey.zhHant: '操作成功',
      LocaleKey.en: 'Operation successful',
    },
    MessageKey.operationFailure: {
      LocaleKey.zh: '操作失败',
      LocaleKey.zhHant: '操作失败',
      LocaleKey.en: 'Operation failure',
    },
  };
}