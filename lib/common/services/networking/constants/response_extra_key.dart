class ResponseExtraKey {
  /// 请求 40001 ，刷新token 报错
  static const tokenRefreshError = "tokenRefreshError";

  static const originalData = "originalData";

  /// 实际响应时间戳，供Alice 使用
  ///
  /// 用于处理因为Response解密、刷新Token重试等耗时操作导致的响应时间统计不准的问题。
  static const actualResponseTime = "actualResponseTime";

  /// 重试操作的Response
  ///
  /// 记录Token刷新成功后重试请求的 Response
  /// 直接覆盖当前 Response.data会导致Alice无法记录原始响应数据，跟踪调试困难，故改为记录在Extra。
  static const retryResponse = "retryResponse";
}
