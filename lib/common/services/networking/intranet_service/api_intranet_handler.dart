import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kp_web_socket_client.dart';
import 'package:kpos/common/services/web_socket/web_socket_server_service.dart';
import 'package:kpos/features/cart/application/cart_server_service.dart';
import 'package:kpos/features/cart/data/cart_endpoint.dart';
import 'package:kpos/features/order/application/order_server_service.dart';
import 'package:kpos/features/order/data/order_endpoint.dart';
import 'package:kpos/features/product/application/product_server_service.dart';
import 'package:kpos/features/product/data/product_endpoint.dart';
import 'package:kpos/features/store/application/store_server_service.dart';
import 'package:kpos/features/table/application/table_server_service.dart';
import 'package:kpos/features/table/data/table_endpoint.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:uuid/uuid.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../../../../features/store/data/store_endpoint.dart';


part 'api_intranet_handler.g.dart';



class ApiIntranetHandler {
  final Ref _ref;

  ApiIntranetHandler({required Ref ref}) : _ref = ref;
  Handler get handler {
    final router = Router();

    router.get('/ws', webSocketHandler((WebSocketChannel channel) {
      final String clientId = const Uuid().v4();
      print("WebSocket 客户端已连接，clientId=$clientId");
      KPWebSocketClient client = KPWebSocketClient(channel);
      client.clientId = clientId;
      _ref.read(webSocketServerServiceProvider).addClient(client);
      final initialMessage = jsonEncode({'type': 'client_id', 'payload': {'client_id':clientId}});
      channel.sink.add(initialMessage);
    }));

    router.post(CartEndpoint.getDiningStyleList.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).getDiningStyleList(request);
    });

    router.post(ProductEndpoint.products.path, (Request request) async {
      return _ref.read(productServerServiceProvider).getProducts(request);
    });

    router.post(TableEndpoint.tableInfos.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).getTableInfos(request);
    });

    router.post(ProductEndpoint.productCategories.path, (Request request) async {
      return _ref.read(productServerServiceProvider).getProductCategories(request);
    });

    router.post(StoreEndpoint.storePreparedReasonList.path, (Request request) async {
      return _ref.read(storeServerServiceProvider).getStorePreparedReasonList(request);
    });

    router.post(ProductEndpoint.productDetail.path, (Request request) async {
      return _ref.read(productServerServiceProvider).getProductDetail(request);
    });

    router.post(ProductEndpoint.productComboDetail.path, (Request request) async {
      return _ref.read(productServerServiceProvider).getProductComboDetail(request);
    });

    router.get(StoreEndpoint.storeServerService.path, (Request request) async {
      return _ref.read(storeServerServiceProvider).getStoreServerService(request);
    });

    router.post(StoreEndpoint.storeTax.path, (Request request) async {
      return _ref.read(storeServerServiceProvider).getTax(request);
    });

    router.post(CartEndpoint.createOrder.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).createOrder(request);
    });

    router.post(CartEndpoint.addCart.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).addCart(request);
    });

    router.post(CartEndpoint.updateCart.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).updateCart(request);
    });

    router.post(CartEndpoint.clearCart.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).clearCart(request);
    });

    router.post(CartEndpoint.getCart.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).getCart(request);
    });

    router.post(CartEndpoint.deleteOrderItem.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).deleteOrderItem(request);
    });

    router.post(CartEndpoint.saveDraft.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).saveDraft(request);
    });

    router.post(CartEndpoint.deleteDraft.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).deleteDraft(request);
    });

    router.post(CartEndpoint.getDrafts.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).getDraftOrders(request);
    });

    router.post(CartEndpoint.setDiningMethod.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setDiningMethod(request);
    });

    router.post(CartEndpoint.setDiningNumber.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setDiningNumber(request);
    });

    router.post(CartEndpoint.setOrderDiscount.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderDiscount(request);
    });

    router.post(CartEndpoint.setOrderRemark.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderRemark(request);
    });

    router.post(CartEndpoint.setOrderMealVoucher.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderMealVoucher(request);
    });

    router.post(CartEndpoint.setOrderItemQuantity.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderItemQuantity(request);
    });

    router.post(StoreEndpoint.storeServiceFeeList.path, (Request request) async {
      return _ref.read(storeServerServiceProvider).getServiceFeeList(request);
    });

    router.post(CartEndpoint.setOrderServiceFee.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderServiceFee(request);
    });

    router.post(CartEndpoint.getOrderServiceFee.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).getOrderServiceFee(request);
    });

    router.post(TableEndpoint.transferTable.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).transferTable(request);
    });

    router.post(TableEndpoint.mergeTable.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).mergeTable(request);
    });

    router.post(TableEndpoint.shareTable.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).shareTable(request);
    });

    router.post(TableEndpoint.getShareTableOrders.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).getShareTableOrders(request);
    });

    router.post(TableEndpoint.cancelTable.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).cancelTable(request);
    });

    router.post(ProductEndpoint.getSoldOutProducts.path, (Request request) async {
      return _ref.read(productServerServiceProvider).getSoldOutProducts(request);
    });

    router.post(ProductEndpoint.setProductSoldOut.path, (Request request) async {
      return _ref.read(productServerServiceProvider).setProductSoldOut(request);
    });

    router.post(OrderEndpoint.deleteOrderItem.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).deleteOrderItem(request);
    });

    router.post(OrderEndpoint.returnOrderItem.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).returnOrderItem(request);
    });

    router.post(OrderEndpoint.urgeOrderItem.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).urgeOrderItem(request);
    });

    router.post(OrderEndpoint.servedOrderItem.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).servedOrderItem(request);
    });

    router.post(OrderEndpoint.cancelOrder.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).cancelOrder(request);
    });

    router.post(CartEndpoint.setOrderItemDiscount.path, (Request request) async {
      return _ref.read(cartServerServiceProvider).setOrderItemDiscount(request);
    });

    router.post(TableEndpoint.cleanTable.path, (Request request) async {
      return _ref.read(tableServerServiceProvider).clearTable(request);
    });

    router.post(OrderEndpoint.submitOrder.path, (Request request) async {
      return _ref.read(orderServerServiceProvider).submitOrder(request);
    });

    return router.call;
  }
}

@riverpod
ApiIntranetHandler apiIntranetHandler(Ref ref) {
  return ApiIntranetHandler(ref: ref);
}