import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/intranet.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_handler.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;

part 'api_intranet_service.g.dart';


class ApiIntranetService {
  final Ref _ref;
  HttpServer? _server;
  ApiIntranetHandler? _apiHandler;

  ApiIntranetService({required Ref ref}) : _ref = ref;

  Future<void> startServer() async {
    if (_server != null) return;
    final ip = DeviceUtil.ip;
    const port = Intranet.serverPort;
    _apiHandler = _ref.watch(apiIntranetHandlerProvider);
    _server = await shelf_io.serve(_apiHandler!.handler, ip, port);
    _server!.autoCompress = true;
    print('服务已启动: http://$ip:$port');
  }

  Future<void> stopServer() async {
    await _server!.close(force: true);
    _server = null;
    _apiHandler = null;
  }

  Middleware handleCors() {
    return (Handler handler) => (Request request) async {
      final response = await handler(request);
      return response.change(headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE',
      });
    };
  }
}

@Riverpod(keepAlive: true)
ApiIntranetService apiIntranetService(Ref ref) {
  return ApiIntranetService(ref: ref);
}