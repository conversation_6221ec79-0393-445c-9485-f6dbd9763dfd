import 'package:kpos/common/services/networking/constants/api_intranet/api_intranet_response_code.dart';
import 'package:shelf/shelf.dart';

class ApiIntranetResponse {
  final int code;
  final String message;
  final dynamic data;

  ApiIntranetResponse({
    required this.code,
    required this.message,
    this.data,
});

  Response toResponse() => Response(code,body: _toString(),headers: {'Content-Type': 'application/json'});

  String _toString() => '{"code":$code, "message": "${_escapeString(message)}", "data": ${data != null ? _encodeData(data) : "null"}}';

  String _escapeString(String input) {
    return input.replaceAll('"', '\\"');
  }

  dynamic _encodeData(dynamic data) {
    if (data is List) {
      return '[${data.map((e) => _encodeData(e)).join(',')}]';
    } else if (data is Map) {
      return '{${data.entries.map((e) => '"${e.key.toString()}":${_encodeData(e.value)}').join(',')}}';
    } else if (data is String) {
      return '"$data"';
    } else if (data == null) {
      return 'null';
    }
    return data.toString();
  }

  static Response success(dynamic data) =>
      ApiIntranetResponse(code: ResponseCode.success, message: "success",data: data).toResponse();

  static Response failure({int code = ResponseCode.failure, required String message}) =>
      ApiIntranetResponse(code: code, message: message).toResponse();

}