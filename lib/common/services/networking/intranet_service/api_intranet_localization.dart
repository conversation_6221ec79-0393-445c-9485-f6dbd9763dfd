import 'dart:ui';
import 'package:kpos/common/services/networking/constants/api_intranet/api_intranet_response_message.dart';
import '../constants/api_intranet/api_intranet_locale_key.dart';

class Localization {
  /// 根据 Locale 返回对应语言 key
  static String _localeKeyFromLocale(Locale locale) {
    if (locale.languageCode == 'zh') {
      if (locale.countryCode == 'TW' || locale.countryCode == 'HK') {
        return LocaleKey.zhHant;
      } else {
        return LocaleKey.zh;
      }
    } else {
      return LocaleKey.en;
    }
  }

  /// 获取错误消息
  static String locale(String key, {Locale? locale}) {
    final map = ResponseMessage.messages[key];
    final languageKey = _localeKeyFromLocale(locale ?? const Locale('zh'));

    if (map == null) {
      return languageKey == LocaleKey.en ? 'Unknown error' : '未知错误';
    }

    return map[languageKey] ?? map[LocaleKey.zh]!;
  }
}