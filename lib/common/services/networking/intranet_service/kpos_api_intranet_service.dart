import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/intranet.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/device_util.dart';
import '../remote_service/api_interface.dart';
import '../remote_service/api_service_provider.dart';

part 'kpos_api_intranet_service.g.dart';


@Riverpod(keepAlive: true)
class IntranetBaseUrl extends _$IntranetBaseUrl {
  @override
  String build() {
    return '${DeviceUtil.ip}:${Intranet.serverPort}';
  } // 初始值

  void updateUrl(String newUrl) {
    state = newUrl;
  }
}

@riverpod
ApiInterface kposApiIntranetService(Ref ref) {
  final baseUrl = ref.watch(intranetBaseUrlProvider);
  return ref.watch(apiServiceProvider(
      baseUrl: 'http://$baseUrl'));
}
