import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'language_settings_service.g.dart';


class LanguageSettingsService {
  late final Ref _ref;

  late Locale currentLocale;

  LanguageSettingsService({required Ref ref}) {
    _ref = ref;
    final keyValueStorageService = ref.read(keyValueStorageServiceProvider);
    final storageLocal = keyValueStorageService.getLocale();
    if (storageLocal != null) {
      currentLocale = storageLocal;
    } else {
      Locale locale = WidgetsBinding.instance.platformDispatcher.locale;
      var defaultLocale = const Locale('zh','SG');
      if (locale.languageCode == 'zh' &&
          (locale.countryCode == 'CN' || locale.countryCode == 'SG')) {
        defaultLocale = const Locale('zh','SG');
      } else if (locale.languageCode == 'zh' &&
          (locale.countryCode == 'TW' || locale.countryCode == 'HK')) {
        defaultLocale = const Locale('zh','HK');
      } else {
        defaultLocale = const Locale('en');
      }
      currentLocale = defaultLocale;
    }
  }
  
  Future<void> setLanguage(Locale locale) async {
    currentLocale = locale;
    _ref.read(currentLocaleProvider.notifier)._updateLocale(locale);
    _ref.read(keyValueStorageServiceProvider).setLocale(locale);
  }

}

@Riverpod(keepAlive: true)
LanguageSettingsService languageSettingsService(Ref ref) {
  return LanguageSettingsService(ref: ref);
}

@riverpod
class CurrentLocale extends _$CurrentLocale {
  @override
  Locale? build() {
    return ref.read(languageSettingsServiceProvider).currentLocale;
  }

  void _updateLocale(Locale locale) {
    state = locale;
  }
}