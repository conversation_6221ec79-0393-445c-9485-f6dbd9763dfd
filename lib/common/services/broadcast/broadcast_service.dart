import 'dart:async';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/intranet.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../utils/device_util.dart';

part 'broadcast_service.g.dart';

class BroadcastService {

  final Ref _ref;

  BroadcastService({required Ref ref}) : _ref = ref;

  void startUdpBroadcast(String mainDeviceName) async {
    final socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 0);
    socket.broadcastEnabled = true;

    // 获取本机IP和子网广播地址
    final ip = DeviceUtil.ip;
    final broadcast = _calculateBroadcast(ip);

    Timer timer = Timer.periodic(const Duration(seconds: 3), (_) {
      final message = 'INTERNAL_SERVER||$ip||${Intranet.serverPort}||$mainDeviceName';
      socket.send(message.codeUnits, broadcast, 8888);
      // print('发送广播到 ${broadcast.address}:8888 message = $message');
    });

    // Future.delayed(const Duration(minutes: 10), () {
    //   timer.cancel();
    //   socket.close();
    // });
  }

  //针对子网掩码是*************
  InternetAddress _calculateBroadcast(String ip) {
    final parts = ip.split('.').map((e) => int.parse(e)).toList();
    return InternetAddress('${parts[0]}.${parts[1]}.${parts[2]}.255');
  }

}

@riverpod
BroadcastService broadcastService (BroadcastServiceRef ref) {
  return BroadcastService(ref:ref);
}

