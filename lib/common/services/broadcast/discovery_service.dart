import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'discovery_service.g.dart';

@riverpod
class DiscoveryService extends _$DiscoveryService {

  RawDatagramSocket? _currentSocket;

  @override
  FutureOr<Map<String,String>> build() async {
    ref.onDispose(() {
      _closeSocket();
    });

    final server = await _discoverServer();
    var host = '';
    var deviceName = '';
    if (server.containsKey("ip") && server.containsKey('port')) {
      host = 'http://${server['ip']}:${server['port']}';
    }
    if (server.containsKey("deviceName")) {
      deviceName = server['deviceName'];
    }
    return {"host":host,"deviceName":deviceName};
  }

  Future<Map<String, dynamic>> _discoverServer() async {
    _closeSocket();
    final completer = Completer<Map<String, dynamic>>();
    final socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 8888);

    _currentSocket = socket;
    socket.listen((event) {
      final packet = socket.receive();
      if (packet != null) {
        final message = String.fromCharCodes(packet.data);
        if (message.startsWith('INTERNAL_SERVER||')) {
          final ip = message.split('||')[1];
          final port = int.parse(message.split('||')[2]);
          ref.read(intranetBaseUrlProvider.notifier).updateUrl('$ip:$port');
          completer.complete({
            'ip': ip,
            'port': ip,
            'deviceName': message.split('||')[3]
          });
          _closeSocket();
        }
      }
    });

    return completer.future.timeout(
      const Duration(seconds: 1),
      onTimeout: () => throw Exception('搜索超时，请检查：1.是否同一Wi-Fi 2.主设备已启动'),
    );
  }

  void _closeSocket() {
    if (_currentSocket != null) {
      _currentSocket!.close();
      _currentSocket = null;
    }
  }

}
