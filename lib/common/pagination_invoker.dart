import 'package:flutter/cupertino.dart';
import 'package:kpos/common/services/networking/remote_service/api_interface.dart';

class PaginationInvoker<T> {
  static const defaultPageSize = 10;

  int get pageIndex => _pageIndex;

  int _pageIndex = 1;

  ApiPaginationData<T> get state => _state;

  ApiPaginationData<T> _state = ApiPaginationData(totalCount: 0, data: []);

  final int pageSize;

  final Future<ApiPaginationData<T>> Function(int pageIndex, int pageSize)
      paginationRequest;

  PaginationInvoker({
    this.pageSize = defaultPageSize,
    required this.paginationRequest,
  }) {
    debugPrint("PaginationInvoker init");
  }

  Future<ApiPaginationData<T>> onRefresh() async {
    final result = await paginationRequest(1,pageSize);
    _pageIndex = 1;
    _state = result;
    return _state;
  }

  Future<ApiPaginationData<T>> onLoad() async {
    final result = await paginationRequest(_pageIndex+1,pageSize);
    _pageIndex++;
    _state = result.copyWith(data: [...state.data,...result.data]);
    return result;
  }
}