import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/pagination_invoker.dart';
import 'package:kpos/common/services/networking/remote_service/api_interface.dart';

mixin PaginationMixin<T> {
  @protected
  late final _paginationInvoker =
      PaginationInvoker(paginationRequest: paginationRequest);

  set state(AsyncValue<ApiPaginationData<T>> newState);

  AsyncValue<ApiPaginationData<T>> get state;

  Future<ApiPaginationData<T>> onBuild() {
    return _paginationInvoker.onRefresh();
  }

  Future<IndicatorResult> onRefresh() async {
    state = await AsyncValue.guard(() => _paginationInvoker.onRefresh());
    if (!state.hasError) {
      return IndicatorResult.success;
    } else {
      return IndicatorResult.fail;
    }
  }

  Future<IndicatorResult> onLoad() async {
    try {
      final newPageData = await _paginationInvoker.onLoad();
      state = AsyncData(_paginationInvoker.state);
      return newPageData.data.length < _paginationInvoker.pageSize
          ? IndicatorResult.noMore
          : IndicatorResult.success;
    } catch (error) {
      return IndicatorResult.fail;
    }
  }

  Future<ApiPaginationData<T>> paginationRequest(int pageIndex, int pageSize);
}
