const emailRegular =
    "^([a-z0-9A-Z][-_|.]*)+[\\w]?@([a-z0-9A-Z_-]+([a-z0-9A-Z_-]+)?\\.)+[a-zA-Z]{2,}\$";
const passwordRegular = "^(?![0-9]+\$)(?![a-zA-Z]+\$)[0-9A-Za-z]{8,30}\$";
const ipv4Regular =
    r"^((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$";

extension FormatCheck on String {
  bool get isCorrectEmail => RegExp(emailRegular).hasMatch(this);

  bool get isValidPassword => RegExp(passwordRegular).hasMatch(this);

  bool get isIpv4 => RegExp(ipv4Regular).hasMatch(this);
}
