import 'package:flutter/material.dart';

extension WidgetExtension on Widget {

  /// 点击
  Widget onTap(VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: this,
    );
  }

  /// 双击
  Widget onDoubleTap(VoidCallback onDoubleTap) {
    return GestureDetector(
      onDoubleTap: onDoubleTap,
      child: this,
    );
  }

  /// 长按
  Widget onLongPress(VoidCallback onLongPress) {
    return GestureDetector(
      onLongPress: onLongPress,
      onLongPressStart: (details) {},
      onLongPressUp: () {},
      onLongPressCancel: () {},
      behavior: HitTestBehavior.opaque,
      child: this,
    );
  }

  /// 同时支持双击和长按
  Widget onDoubleTapOrLongPress(VoidCallback onAction) {
    return GestureDetector(
      onDoubleTap: onAction,
      onLongPress: onAction,
      child: this,
    );
  }
}
