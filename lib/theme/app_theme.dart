class AppTheme {
  static String defaultThemeConfig = '''
  {
    "default": {
        "ref":{
            "brandLightColor": "brandColor1",
            "brandFocusColor": "brandColor2",
            "brandDisabledColor": "brandColor3",
            "brandHoverColor": "brandColor6",
            "brandNormalColor": "brandColor7",
            "brandClickColor": "brandColor8",
            "errorLightColor": "errorColor1",
            "errorFocusColor": "errorColor2",
            "errorDisabledColor": "errorColor3",
            "errorHoverColor": "errorColor5",
            "errorNormalColor": "errorColor6",
            "errorClickColor": "errorColor7",
            "warningLightColor": "warningColor1",
            "warningFocusColor": "warningColor2",
            "warningDisabledColor": "warningColor3",
            "warningHoverColor": "warningColor4",
            "warningNormalColor": "warningColor5",
            "warningClickColor": "warningColor6",
            "successLightColor": "successColor1",
            "successFocusColor": "successColor2",
            "successDisabledColor": "successColor3",
            "successHoverColor": "successColor4",
            "successNormalColor": "successColor5",
            "successClickColor": "successColor6"
            },
        "color": {
            "brandColor1": "#F2F3FF",
            "brandColor2": "#20232B",
            "brandColor3": "#B5C7FF",
            "brandColor4": "#8EABFF",
            "brandColor5": "#618DFF",
            "brandColor6": "#FF0000",
            "brandColor7": "#FF8500",
            "brandColor8": "#FF0000",
            "brandColor9": "#002A7C",
            "brandColor10": "#001A57",
            "errorColor1": "#FFF0ED",
            "errorColor2": "#FFD8D2",
            "errorColor3": "#FFB9B0",
            "errorColor4": "#FF9285",
            "errorColor5": "#F6685D",
            "errorColor6": "#D54941",
            "errorColor7": "#AD352F",
            "errorColor8": "#881F1C",
            "errorColor9": "#68070A",
            "errorColor10": "#490002",
            "warningColor1": "#E2483D",
            "warningColor2": "#FFD9C2",
            "warningColor3": "#FFB98C",
            "warningColor4": "#FA9550",
            "warningColor5": "#E37318",
            "warningColor6": "#BE5A00",
            "warningColor7": "#954500",
            "warningColor8": "#713300",
            "warningColor9": "#532300",
            "warningColor10": "#3B1700",
            "successColor1": "#E3F9E9",
            "successColor2": "#C6F3D7",
            "successColor3": "#92DAB2",
            "successColor4": "#56C08D",
            "successColor5": "#2BA471",
            "successColor6": "#008858",
            "successColor7": "#006C45",
            "successColor8": "#005334",
            "successColor9": "#003B23",
            "successColor10": "#002515",
            "fontGyColor1": "#E6000000",
            "fontGyColor2": "#99000000",
            "fontGyColor3": "#66000000",
            "fontGyColor4": "#42000000",
            "fontWhColor1": "#FFFFFFFF",
            "fontWhColor2": "#8CFFFFFF",
            "fontWhColor3": "#59FFFFFF",
            "fontWhColor4": "#38FFFFFF",
            "whiteColor1": "#FFFFFF",
            "grayColor1": "#F3F3F4",
            "grayColor2": "#6F7686",
            "grayColor3": "#EEEEF0",
            "grayColor4": "#323843",
            "grayColor5": "#A2A6B1",
            "grayColor6": "#DCDDE1",
            "grayColor7": "#F5F6F7",
            "grayColor8": "#777777",
            "grayColor9": "#5E5E5E",
            "grayColor10": "#4B4B4B",
            "grayColor11": "#383838",
            "grayColor12": "#2C2C2C",
            "grayColor13": "#242424",
            "grayColor14": "#181818"
        },
        "font": {
            "fontDisplayLarge": {
                "size": 64,
                "lineHeight": 72,
                "fontWeight": 6
            },
            "fontDisplayMedium": {
                "size": 48,
                "lineHeight": 56,
                "fontWeight": 6
            },
            "fontHeadlineLarge": {
                "size": 36,
                "lineHeight": 44,
                "fontWeight": 6
            },
            "fontHeadlineMedium": {
                "size": 28,
                "lineHeight": 36,
                "fontWeight": 6
            },
            "fontHeadlineSmall": {
                "size": 24,
                "lineHeight": 32,
                "fontWeight": 6
            },
            "fontTitleExtraLarge": {
                "size": 20,
                "lineHeight": 28,
                "fontWeight": 6
            },
            "fontTitleLarge": {
                "size": 18,
                "lineHeight": 26,
                "fontWeight": 6
            },
            "fontTitleMedium": {
                "size": 16,
                "lineHeight": 24,
                "fontWeight": 6
            },
            "fontTitleSmall": {
                "size": 14,
                "lineHeight": 22
            },
            "fontBodyExtraLarge": {
                "size": 18,
                "lineHeight": 26
            },
            "fontBodyLarge": {
                "size": 16,
                "lineHeight": 24
            },
            "fontBodyMedium": {
                "size": 14,
                "lineHeight": 22
            },
            "fontBodySmall": {
                "size": 12,
                "lineHeight": 20
            },
            "fontBodyExtraSmall": {
                "size": 10,
                "lineHeight": 16
            },
            "fontMarkLarge": {
                "size": 16,
                "lineHeight": 24,
                "fontWeight": 6
            },
            "fontMarkMedium": {
                "size": 14,
                "lineHeight": 22,
                "fontWeight": 6
            },
            "fontMarkSmall": {
                "size": 12,
                "lineHeight": 20,
                "fontWeight": 6
            },
            "fontMarkExtraSmall": {
                "size": 10,
                "lineHeight": 16,
                "fontWeight": 6
            },
            "fontLinkLarge": {
                "size": 16,
                "lineHeight": 24
            },
            "fontLinkMedium": {
                "size": 14,
                "lineHeight": 22
            },
            "fontLinkSmall": {
                "size": 12,
                "lineHeight": 20
            }
        },
        "fontFamily": {
            "numberFontFamily": {
				      "fontFamily": "TCloudNumber",
				      "package": "tdesign_flutter"
			      }
        },
        "radius": {
            "radiusSmall": 4,
            "radiusDefault": 8,
            "radiusLarge": 10,
            "radiusExtraLarge": 12,
            "radiusRound": 9999,
            "radiusCircle": 9999
        },
        "shadow": {
            "shadowsBase": [
                {
                    "color": "#0D000000",
                    "blurRadius": 10,
                    "spreadRadius": 1,
                    "offset": {
                        "x": 0,
                        "y": 1
                    }
                },
                {
                    "color": "#14000000",
                    "blurRadius": 5,
                    "spreadRadius": 1,
                    "offset": {
                        "x": 0,
                        "y": 4
                    }
                },
                {
                    "color": "#1F000000",
                    "blurRadius": 4,
                    "spreadRadius": -1,
                    "offset": {
                        "x": 0,
                        "y": 2
                    }
                }
            ],
            "shadowsMiddle": [
                {
                    "color": "#0D000000",
                    "blurRadius": 14,
                    "spreadRadius": 2,
                    "offset": {
                        "x": 0,
                        "y": 3
                    }
                },
                {
                    "color": "#0F000000",
                    "blurRadius": 10,
                    "spreadRadius": 1,
                    "offset": {
                        "x": 0,
                        "y": 8
                    }
                },
                {
                    "color": "#1A000000",
                    "blurRadius": 5,
                    "spreadRadius": -3,
                    "offset": {
                        "x": 0,
                        "y": 0
                    }
                }
            ],
            "shadowsTop": [
                {
                    "color": "#0D000000",
                    "blurRadius": 30,
                    "spreadRadius": 5,
                    "offset": {
                        "x": 0,
                        "y": 6
                    }
                },
                {
                    "color": "#0A000000",
                    "blurRadius": 24,
                    "spreadRadius": 2,
                    "offset": {
                        "x": 0,
                        "y": 16
                    }
                },
                {
                    "color": "#14000000",
                    "blurRadius": 10,
                    "spreadRadius": -5,
                    "offset": {
                        "x": 0,
                        "y": 8
                    }
                }
            ]
        },
        "spacer": {
            "spacer4": 4,
            "spacer8": 8,
            "spacer12": 12,
            "spacer16": 16,
            "spacer24": 24,
            "spacer32": 32,
            "spacer40": 40,
            "spacer48": 48,
            "spacer64": 64,
            "spacer96": 96,
            "spacer160": 160
        }
    }
}

  ''';
}