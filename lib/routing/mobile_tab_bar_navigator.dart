/// 移动端顶部导航栏组件
/// 实现顶部标题、中间区域和右侧按钮组布局

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // 用于kDebugMode判断开发环境
import 'package:flutter_riverpod/flutter_riverpod.dart'; // 状态管理
import 'package:kpos/common/extension/build_context_extension.dart'; // 主题扩展
import 'package:tdesign_flutter/tdesign_flutter.dart'; // TDesign UI组件库
import '../assets/assets.gen.dart'; // 资源生成文件
import '../common/components/button/notification_icon_button.dart'; // 自定义通知按钮组件
import 'pages_route.dart'; // 页面路由定义

/// 顶部导航按钮数据模型
class MobileTopNavButton {
  /// 显示文本
  final String title;
  
  /// 路由路径
  final String routePath;
  
  /// 选中状态
  bool isSelected;
  
  /// 构造函数
  MobileTopNavButton({
    required this.title,
    required this.routePath,
    this.isSelected = false,
  });
}

/// 导航按钮状态管理类
class MobileTopNavButtonsNotifier extends StateNotifier<List<MobileTopNavButton>> {
  /// 初始化为空列表
  MobileTopNavButtonsNotifier() : super([]);
  
  /// 更新按钮列表
  void updateButtons(List<MobileTopNavButton> buttons) {
    state = buttons
        .map((b) => MobileTopNavButton(
              title: b.title,
              routePath: b.routePath,
              isSelected: b.isSelected,
            ))
        .toList();
  }
  
  /// 选择指定索引的按钮
  void selectButton(int index) {
    // 创建一个新的按钮列表，所有按钮都设置为未选中状态
    final updatedButtons = state
        .map((b) => MobileTopNavButton(
              title: b.title,
              routePath: b.routePath,
              isSelected: false,
            ))
        .toList();
    
    // 将指定索引的按钮设置为选中状态（如果索引有效）
    if (index >= 0 && index < updatedButtons.length) {
      updatedButtons[index].isSelected = true;
    }
    
    // 更新状态
    state = updatedButtons;
  }
  
  /// 获取当前选中的按钮索引
  int getSelectedIndex() {
    final index = state.indexWhere((btn) => btn.isSelected);
    return index >= 0 ? index : 0;
  }
}

/// 当前选中的导航按钮索引
final mobileCurrentTopNavIndexProvider = StateProvider<int>((ref) => 0);

/// 导航按钮列表状态提供者
final mobileTopNavButtonsProvider =
    StateNotifierProvider<MobileTopNavButtonsNotifier, List<MobileTopNavButton>>(
        (ref) => MobileTopNavButtonsNotifier());

/// 顶部导航栏组件
class TopNavBar extends ConsumerWidget {
  /// 主标题
  final String mainTitle;

  /// 中间区域内容
  final Widget? middleContent;

  /// 右侧按钮区域
  final Widget? trailingContent;
  
  /// 下拉菜单项列表
  final List<MobileTopNavButton>? dropdownItems;
  
  /// 菜单项点击回调
  final Function(String routePath)? onMenuItemTap;

  /// 构造函数
  const TopNavBar({
    required this.mainTitle,
    this.middleContent,
    this.trailingContent,
    this.dropdownItems,
    this.onMenuItemTap,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取当前选中的菜单项
    // 通过Riverpod监听当前选中的索引值
    final currentIndex = ref.watch(mobileCurrentTopNavIndexProvider);
    
    // 获取菜单项列表，如果未提供则使用空列表
    final menuItems = dropdownItems ?? [];
    
    // 确定当前显示的菜单项文本
    // 如果有有效的菜单项且索引有效，则使用对应菜单项的标题
    // 否则使用主标题作为默认值
    final currentMenuItem = menuItems.isNotEmpty && currentIndex < menuItems.length 
        ? menuItems[currentIndex].title 
        : mainTitle;
    
    // 获取安全区域高度，处理全面屏手机的安全距离
    // MediaQuery.padding.top 对应设备的状态栏高度
    final topPadding = MediaQuery.of(context).padding.top;
    
    return Container(
      // 根据安全区域高度调整导航栏高度
      // 基础高度56像素加上状态栏高度，确保内容不被状态栏遮挡
      height: 56 + topPadding,
      padding: EdgeInsets.only(top: topPadding),
      decoration: BoxDecoration(
        // 使用主题中定义的白色背景
        color: context.theme.whiteColor1,
        // 添加轻微的底部阴影，提供层次感
        boxShadow: const [
          BoxShadow(
            color: Colors.black12, // 半透明黑色阴影
            blurRadius: 2,         // 模糊半径
            offset: Offset(0, 1),  // 向下偏移1像素
          ),
        ],
      ),
      child: Row(
        children: [
          // 1. 左侧标题区域
          // 水平内边距为16像素，确保文本不会贴近屏幕边缘
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerLeft, // 左对齐
            child: Text(
              mainTitle, // 显示主标题
              style: TextStyle(
                color: context.theme.grayColor4, // 使用主题中定义的文本颜色
                fontWeight: FontWeight.bold,    // 粗体
                fontSize: 20                   // 字号20
              ),
            ),
          ),

          // 2. 中间区域 - 下拉菜单或自定义内容
          // 使用Expanded占据所有剩余空间
          Expanded(
            child: middleContent ?? Expanded(child: Container(color: Colors.white,)),
          ),

          // 3. 右侧固定元素(不可滚动)
          // 水平内边距为16像素，确保按钮不会贴近屏幕边缘
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            alignment: Alignment.centerRight, // 右对齐
            // 如果提供了自定义按钮组则使用自定义按钮组，否则使用默认按钮组
            child: trailingContent ?? _buildDefaultTrailingButtons(context),
          )
        ],
      ),
    );
  }
  
  // 中间区域内容方法(当前未使用)
  // Widget _buildDropdownMenu(BuildContext context, WidgetRef ref, String currentItem, List<MobileTopNavButton> items) {
  //   return Center();
  // }

  /// 默认右侧按钮组
  Widget _buildDefaultTrailingButtons(BuildContext context) {
    return Row(
      children: [
        // 1. 搜索按钮
        NotificationIconButton.fromSvg(
          context: context,
          // 使用SVG图标，设置宽高为20像素
          svgIcon: Assets.images.iconSearchMd.svg(width: 20, height: 20),
          // 使用主题中定义的警告色作为提示点颜色
          dotColor: context.theme.warningColor1,
          // 点击事件处理
          onPressed: () {
            // 仅在调试模式下打印日志，避免在生产环境中输出
            if (kDebugMode) {
              print("点击了搜索icon");
            }
            // 这里可以添加搜索功能的实现
          },
        ),
        
        // 2. 语言设置按钮
        NotificationIconButton.fromSvg(
          context: context,
          // 使用SVG图标，设置宽高为20像素
          svgIcon: Assets.images.iconChangeLanguageBlodBlack.svg(width: 20, height: 20),
          // 使用主题中定义的警告色作为提示点颜色
          dotColor: context.theme.warningColor1,
          // 点击事件处理 - 导航到语言设置页面
          onPressed: () {
            LanguageSettingsRoute().push(context);
          },
        ),
        
        // 3. 通知按钮 - 显示通知提示点
        NotificationIconButton.fromSvg(
          context: context,
          // 使用SVG图标，设置宽高为20像素
          svgIcon: Assets.images.iconNotification.svg(width: 20, height: 20),
          // 使用主题中定义的警告色作为提示点颜色
          dotColor: context.theme.warningColor1,
          // 显示通知提示点
          showNotification: true,
          // 点击事件处理
          onPressed: () {
            // 仅在调试模式下打印日志，避免在生产环境中输出
            if (kDebugMode) {
              print("点击了通知按钮icon");
            }
            // 这里可以添加查看通知的功能实现
          },
        ),
      ],
    );
  }
}