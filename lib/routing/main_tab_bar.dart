import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/order/presentation/order_status_tab_bar.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/presentation/widgets/table_area_tab_bar.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import '../routing/main_tab_bar_navigator.dart';
import 'main_tab_bar_navigator_service.dart';

class MainTabBarItem extends NavigationRailDestination {
  final String routerPath;

  MainTabBarItem(
      {required super.icon,
      super.selectedIcon,
      super.padding,
      required super.label,
      required this.routerPath});
}

class MainTabBar extends ConsumerStatefulWidget {
  const MainTabBar(this.child, {super.key});

  final Widget child;

  @override
  ConsumerState createState() => _MainTabBarState();
}

class _MainTabBarState extends ConsumerState<MainTabBar> {
  bool showLeading = true;
  bool showTrailing = true;
  double groupAlignment = -1.0;

  NavigationRailLabelType labelType = NavigationRailLabelType.all;
  List<MainTabBarItem> get tabs => <MainTabBarItem>[
        MainTabBarItem(
            padding: const EdgeInsets.symmetric(vertical: 10),
            icon: Assets.images.iconTableNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textGraySecondary, // 未选中颜色
                  BlendMode.srcIn),
            ),
            selectedIcon: Assets.images.iconTableNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textBrandDefault, // 选中颜色
                  BlendMode.srcIn),
            ),
            label: Text(KPLocale.of(context).table),
            routerPath: '/table'),
        MainTabBarItem(
            padding: const EdgeInsets.symmetric(vertical: 10),
            icon: Assets.images.iconRegisterNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textGraySecondary, // 未选中颜色
                  BlendMode.srcIn),
            ),
            selectedIcon: Assets.images.iconRegisterNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textBrandDefault, // 选中颜色
                  BlendMode.srcIn),
            ),
            label: Text(KPLocale.of(context).register),
            routerPath: '/product'),
        MainTabBarItem(
            padding: const EdgeInsets.symmetric(vertical: 10),
            icon: Assets.images.iconOrdersNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textGraySecondary, // 未选中颜色
                  BlendMode.srcIn),
            ),
            selectedIcon: Assets.images.iconOrdersNavigationMain.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textBrandDefault, // 选中颜色
                  BlendMode.srcIn),
            ),
            label: Text(KPLocale.of(context).orders),
            routerPath: '/order'),
        MainTabBarItem(
            padding: const EdgeInsets.symmetric(vertical: 10),

            /// 针对svg图片设置选中和未选中状态的特别设置
            icon: Assets.images.iconMore.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textGraySecondary, // 未选中颜色
                  BlendMode.srcIn),
            ),
            selectedIcon: Assets.images.iconMore.svg(
              colorFilter: const ColorFilter.mode(
                  KPColors.textBrandDefault, // 选中颜色
                  BlendMode.srcIn),
            ),
            label: Text(KPLocale.of(context).more),
            routerPath: '/more'),
      ];

  int _locationToTabIndex(String location) {
    final index = tabs.indexWhere((t) => location.startsWith(t.routerPath));
    return index < 0 ? 0 : index;
  }

  int get _currentIndex =>
      _locationToTabIndex(GoRouterState.of(context).uri.toString());

  //获取当前主导航标题文本
  String _getLabelText(Widget label) {
    if (label is Text) {
      return label.data ?? '';
    }
    return '';
  }

  void _onItemTapped(BuildContext context, int tabIndex) {
    if (tabIndex != _currentIndex) {
      // 🔥 使用国际化 key 而不是静态文本
      final titleKey = _getTitleKeyByIndex(tabIndex);

      // 更新顶部导航栏标题（使用国际化 key）
      ref.read(navigationTitleProvider.notifier).updateTitleByKey(titleKey);

      // 使用go_router导航到目标路径
      context.go(tabs[tabIndex].routerPath);
    }
  }

  /// 根据索引获取对应的国际化 key
  String _getTitleKeyByIndex(int index) {
    switch (index) {
      case 0:
        return 'table'; // 桌台
      case 1:
        return 'register'; // 商品
      case 2:
        return 'mine'; // 我的
      case 3:
        return 'more'; // 更多
      default:
        return ' ';
    }

    //旧版👇🏻
    // switch (index) {
    //   case 0:
    //     return 'home'; // 首页
    //   case 1:
    //     return 'table'; // 桌台
    //   case 2:
    //     return 'broadcast'; //广播
    //   case 3:
    //     return 'mine'; // 我的
    //   case 4:
    //     return 'more'; // 更多
    //   default:
    //     return ' ';
    // }
  }

  @override
  void initState() {
    super.initState();
    //初始化一些系统配置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final keyValueStorageService = ref.read(keyValueStorageServiceProvider);
      ref.read(tableViewShowTypeProvider.notifier).init(keyValueStorageService);
      ref
          .read(tableTimeDisplayShowProvider.notifier)
          .init(keyValueStorageService);
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentMainTitle = _getLabelText(tabs[_currentIndex].label);

    // 读取状态
    final isNavigationRailVisible = ref.watch(mainTabBarNavigatorProvider);

    return Scaffold(
      body: SafeArea(
          //左侧主导航栏
          child: Row(
        children: [
          // == 修改后的AnimatedContainer
          // == 备选方案 == 完全自定义的导航栏
          AnimatedContainer(
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOut,
            width: isNavigationRailVisible ? 80 : 0,
            child: isNavigationRailVisible
                ? Container(
                    width: 80,
                    color: context.theme.grayColor1,
                    child: Column(
                      children: [
                        // 顶部菜单按钮
                        Container(
                          height: 60,
                          alignment: Alignment.center,
                          child: IconButton(
                            icon: Assets.images.iconMenuBlack
                                .svg(width: 20, height: 14),
                            tooltip: '菜单',
                            onPressed: () {
                              ref
                                  .read(mainTabBarNavigatorProvider.notifier)
                                  .set(false);
                            },
                          ),
                        ),

                        // 导航项列表
                        Expanded(
                          child: ListView.builder(
                            itemCount: tabs.length,
                            itemBuilder: (context, index) {
                              final tab = tabs[index];
                              final isSelected = index == _currentIndex;

                              return Container(
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: isSelected
                                    ? BoxDecoration(
                                        color: context.theme.brandColor7
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      )
                                    : null,
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () => _onItemTapped(context, index),
                                    borderRadius: BorderRadius.circular(12),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          // 图标
                                          IconTheme(
                                            data: IconThemeData(
                                              color: isSelected
                                                  ? KPColors.textBrandDefault
                                                  : KPColors.textGraySecondary,
                                              size: 24,
                                            ),
                                            child: isSelected &&
                                                    tab.selectedIcon != null
                                                ? tab.selectedIcon!
                                                : tab.icon,
                                          ),
                                          const SizedBox(height: 4),
                                          // 标签
                                          if (tab.label is Text)
                                            Text(
                                              (tab.label as Text).data ?? '',
                                              style: isSelected
                                                  ? KPFontStyle.bodyMedium
                                                      .copyWith(
                                                          color: KPColors
                                                              .textBrandDefault)
                                                  : KPFontStyle.bodySmall
                                                      .copyWith(
                                                          color: KPColors
                                                              .textGraySecondary),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),

                        // 底部头像按钮
                        Container(
                          height: 108,
                          padding: const EdgeInsets.fromLTRB(18, 40, 18, 24),
                          child: FloatingActionButton(
                            backgroundColor: context.theme.brandColor7,
                            shape: const CircleBorder(
                                side: BorderSide(
                                    color: Colors.white, width: 0.5)),
                            tooltip: '你点我头像啦~',
                            onPressed: () {
                              print("点击了头像区域");
                            },
                            child: const Text("头像",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16)),
                          ),
                        ),
                      ],
                    ),
                  )
                : null,
          ),

          // == 右侧内容区域保持不变
          Expanded(
              child: Column(
            children: [
              // 使用独立的顶部导航栏组件，传入自定义中间内容
              TopNavBar(
                mainTitle: currentMainTitle,
                middleContent:
                    _buildMiddleContent(tabs[_currentIndex].routerPath),
              ),

              // 主内容区域
              Expanded(
                child: widget.child,
              ),
            ],
          )),
        ],
      )),
    );
  }

  //根据routerPath获取TopNavBar的 middleContent，需要时增加
  Widget? _buildMiddleContent(String path) {
    switch (path) {
      case '/table':
        return const TableAreaTabBarWidget();
      case '/order':
        return const OrderStatusTabBarWidget();
      default:
        return null;
    }
  }
}
