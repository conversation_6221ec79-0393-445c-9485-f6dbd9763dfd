import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/button/notification_icon_button.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/routing/main_tab_bar_navigator_service.dart';
import 'package:kpos/common/services/language_settings_service/language_settings_service.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../assets/assets.gen.dart';
import 'pages_route.dart';

/// 顶部导航按钮
class TopNavButton {
  /// 按钮显示文本
  final String title;

  /// 按钮对应的路由路径
  /// 点击按钮时将导航到此路径
  final String routePath;

  /// 是否有角标
  /// 如果为true，将在按钮右上角显示一个角标
  final bool hasBadge;

  /// 角标文本（通常为数字）
  /// 当hasBadge为true时生效
  final String? badgeText;

  /// 是否为当前选中状态
  /// 选中状态的按钮会有不同的样式显示
  bool isSelected;

  /// 构造函数
  ///
  /// @param title [String] 按钮显示的文本
  /// @param routePath [String] 按钮对应的路由路径
  /// @param hasBadge [bool] 是否显示角标，默认为false
  /// @param badgeText [String?] 角标显示的文本，仅当hasBadge为true时有效
  /// @param isSelected [bool] 是否为选中状态，默认为false
  TopNavButton({
    required this.title,
    required this.routePath,
    this.hasBadge = false,
    this.badgeText,
    this.isSelected = false,
  });
}

/// 顶部导航按钮状态管理类
/// 管理顶部导航按钮列表的状态变化和选中状态
class TopNavButtonsNotifier extends StateNotifier<List<TopNavButton>> {
  TopNavButtonsNotifier() : super([]);

  /// 更新按钮列表
  /// 创建原始按钮列表的深拷贝并设置为新状态
  /// @param buttons [List<TopNavButton>] 原始按钮列表
  void updateButtons(List<TopNavButton> buttons) {
    state = buttons
        .map((b) => TopNavButton(
              title: b.title,
              routePath: b.routePath,
              hasBadge: b.hasBadge,
              badgeText: b.badgeText,
              isSelected: b.isSelected,
            ))
        .toList();
  }

  /// 选择指定索引的按钮
  /// 重置所有按钮的选中状态，然后标记指定索引的按钮为选中
  /// @param index [int] 要选中的按钮索引
  void selectButton(int index) {
    final updatedButtons = state
        .map((b) => TopNavButton(
              title: b.title,
              routePath: b.routePath,
              hasBadge: b.hasBadge,
              badgeText: b.badgeText,
              isSelected: false,
            ))
        .toList();

    // 使用深度复制是为了避免跟踪监听通知复用报错
    if (index >= 0 && index < updatedButtons.length) {
      updatedButtons[index].isSelected = true;
    }

    state = updatedButtons;
  }

  /// 获取当前选中的按钮索引
  /// @return [int] 选中的按钮索引，如果没有选中的按钮则返回 0
  int getSelectedIndex() {
    final index = state.indexWhere((btn) => btn.isSelected);
    return index >= 0 ? index : 0;
  }
}

/// 当前选中的顶部导航按钮索引状态提供者
/// 用于跟踪当前顶部导航栏中哪个按钮处于选中状态
final currentTopNavIndexProvider = StateProvider<int>((ref) => 0);

/// 当前显示的顶部导航按钮列表状态提供者
/// 根据当前选中的主导航项动态更新顶部导航按钮列表
final topNavButtonsProvider =
    StateNotifierProvider<TopNavButtonsNotifier, List<TopNavButton>>(
        (ref) => TopNavButtonsNotifier());

/// 顶部导航栏组件
/// 显示在应用顶部的二级导航栏，包含左侧标题、中间自定义内容和右侧固定按钮
class TopNavBar extends ConsumerWidget {
  /// 当前主导航标题
  final String mainTitle;

  /// 中间区域的自定义内容
  final Widget? middleContent;
  
  /// 右侧按钮区域的自定义内容
  final Widget? trailingContent;

  final ValueNotifier<bool> showSearchNotifier = ValueNotifier(false);

  final TextEditingController _textEditingController = TextEditingController();

  /// 构造函数
  ///
  /// @param mainTitle [String] 当前主导航标题
  /// @param middleContent [Widget?] 中间区域的自定义内容，可以是任何Widget
  /// @param trailingContent [Widget?] 右侧按钮区域的自定义内容，默认为null时使用默认按钮组
  /// @param key [Key?] 组件唯一标识
  TopNavBar({
    required this.mainTitle,
    this.middleContent,
    this.trailingContent,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听当前语言变化，确保组件在语言切换时重建
    ref.watch(currentLocaleProvider);
    
    // 监听导航栏显示状态
    final isNavigationRailVisible = ref.watch(mainTabBarNavigatorProvider);

    // 监听导航栏标题数据
    final navigationTitleData = ref.watch(navigationTitleProvider);

    // 动态获取国际化标题
    final displayTitle = _getDisplayTitle(context, navigationTitleData);

    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: context.theme.whiteColor1,
        // boxShadow: const [
        //   BoxShadow(
        //     color: Colors.black12,
        //     blurRadius: 4,
        //     offset: Offset(0, 2),
        //   ),
        // ],
      ),
      child: Row(
        children: [
          // 1. 左侧固定元素（不可滚动）
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              children: [
                // 当 NavigationRail 隐藏时显示菜单按钮，否则隐藏
                if (!isNavigationRailVisible)
                  IconButton(
                    icon: Assets.images.iconMenuBlack.svg(width: 17, height: 12,),
                    tooltip: '显示',
                    onPressed: () {
                      // 显示NavigationRail
                      ref.read(mainTabBarNavigatorProvider.notifier).set(true);
                    },
                  ),
                // 左侧固定标题, 使用 displayTitle 显示
                Text(
                  displayTitle,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: context.theme.grayColor4,
                  ),
                ),
              ],
            ),
          ),

          // 2. 中间自定义内容区域
          Expanded(
            child: middleContent ?? const SizedBox(),
          ),

          // 3. 右侧固定元素（不可滚动）
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: trailingContent ?? _buildDefaultTrailingButtons(context,ref),
          ),
        ],
      ),
    );
  }

  /// 获取显示标题的核心方法
  /// 支持国际化 key 和静态文本两种模式
  String _getDisplayTitle(BuildContext context, NavigationTitleData? titleData) {
    // 如果没有标题数据，使用默认标题
    if (titleData == null || titleData.isEmpty) {
      return mainTitle;
    }
    
    // 优先使用国际化 key
    if (titleData.i18nKey != null) {
      return _getLocalizedTextByKey(context, titleData.i18nKey!, titleData.params);
    }
    
    // 回退到静态文本
    return titleData.staticText ?? mainTitle;
  }
  
  /// 根据国际化 key 获取本地化文本
  /// 这个方法可以根据需要扩展，支持更多的国际化 key
  String _getLocalizedTextByKey(BuildContext context, String key, Map<String, dynamic>? params) {
    final locale = context.locale;
    
    switch (key) {
      case 'table':
        return locale.table;
      case 'register':
        return locale.home;
      case 'broadcast':
        return locale.broadcast;
      case 'mine':
        return locale.mine;
      case 'more':
        return locale.more;
      case 'cashManagement':
        return locale.cashManagement;
      case 'tableSetting':
        return locale.tableSetting;
      // 可以继续添加更多的 key 映射
      default:
        if (kDebugMode) {
          print('⚠️ 未找到国际化 key: "$key"');
        }
        return key; // 如果找不到对应的国际化文本，返回 key 本身
    }
  }

  /// 创建默认的右侧按钮组
  Widget _buildDefaultTrailingButtons(BuildContext context,WidgetRef ref) {
    return ValueListenableBuilder(
      valueListenable: showSearchNotifier,
      builder: (context,showSearch,_) {
        return Row(
          children: [
            showSearch ? _buildSearchView(context,ref) : NotificationIconButton.fromSvg(
              context: context,
              svgIcon: Assets.images.iconSearchMd.svg(width: 20, height: 20),
              dotColor: context.theme.warningColor1,
              onPressed: () {
                showSearchNotifier.value = true;
              },
            ),
            NotificationIconButton.fromSvg(
              context: context,
              svgIcon: Assets.images.iconChangeLanguageBlodBlack.svg(width: 20, height: 20),
              dotColor: context.theme.warningColor1,
              onPressed: () {
                LanguageSettingsRoute().push(context);
              },
            ),
            NotificationIconButton.fromSvg(
              context: context,
              svgIcon: Assets.images.iconNotification.svg(width: 20, height: 20),
              dotColor: context.theme.warningColor1,
              showNotification: true,
              onPressed: () {
                if (kDebugMode) {
                  print("点击了通知按钮icon");
                }
              },
            ),
          ],
        );
      }
    );
  }

  Widget _buildSearchView(BuildContext context,WidgetRef ref) {
    return Container(
      width: 320,
      height: 48,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.theme.brandNormalColor,
            width: 3,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧搜索icon
          Container(
            width: 48,
            height: 48,
            alignment: Alignment.center,
            child: Assets.images.iconSearchMd.svg(width: 20, height: 20),
          ),

          // 中间输入框
          Expanded(
            child: Center(
              child: TextField(
                controller: _textEditingController,
                textInputAction: TextInputAction.search,
                cursorColor: context.theme.brandNormalColor,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  isCollapsed: true,
                ),
                style: KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
                onSubmitted: (value) {
                    ref.read(mainTabBarSearchProvider.notifier).updateValue(value);
                },
              ),
            ),
          ),

          // 右侧清空icon
          GestureDetector(
            onTap: () {
              _textEditingController.clear();
              showSearchNotifier.value = false;
              ref.read(mainTabBarSearchProvider.notifier).updateValue('');
            },
            child: Container(
              width: 48,
              height: 48,
              alignment: Alignment.center,
              child: Assets.images.iconCloseCircle.svg(width: 20, height: 20),
            ),
          ),
        ],
      ),
    );
  }



}
