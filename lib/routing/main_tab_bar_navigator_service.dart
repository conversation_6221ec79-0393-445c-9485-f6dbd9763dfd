import 'package:riverpod_annotation/riverpod_annotation.dart';

// 先指定生成的文件名称， 再执行自动生成指令
part 'main_tab_bar_navigator_service.g.dart';

/// 导航标题数据类
/// 支持国际化 key 和静态文本两种模式
/// 1. 国际化 key 模式：存储 'home'，运行时翻译为当前语言
/// 2. 静态文本模式：存储 '首页'，直接显示（向后兼容）
class NavigationTitleData {
  /// 国际化 key（优先使用）
  final String? i18nKey;
  
  /// 静态文本（当 i18nKey 为空时使用）
  final String? staticText;
  
  /// 国际化参数
  final Map<String, dynamic>? params;
  
  /// 导航标题数据类 - 核心数据结构
  const NavigationTitleData({
    this.i18nKey, // 国际化 key（如 'home'）
    this.staticText, // 静态文本（如 '首页'）
    this.params,  // 国际化参数
  });
  
  /// 创建基于国际化 key 的标题
  factory NavigationTitleData.fromKey(String key, {Map<String, dynamic>? params}) {
    return NavigationTitleData(i18nKey: key, params: params);
  }
  
  /// 创建基于静态文本的标题
  factory NavigationTitleData.fromText(String text) {
    return NavigationTitleData(staticText: text);
  }
  
  /// 判断是否为空
  bool get isEmpty => i18nKey == null && (staticText == null || staticText!.isEmpty);
  
  /// 判断是否非空
  bool get isNotEmpty => !isEmpty;
}

/// 是否隐藏左侧主导航
@riverpod
class MainTabBarNavigator extends _$MainTabBarNavigator {
  @override
  bool build() => true; // 默认位显示状态

  // 切换导航栏显示状态
  void change() {
    state = !state;
  }
  
  // 设置导航栏显示状态
  void set(bool value) {
    state = value;
  }
}

/// 顶部导航栏标题状态管理
@riverpod
class NavigationTitle extends _$NavigationTitle {
  @override
  NavigationTitleData? build() => null; // 默认为空标题

  /// 使用国际化 key 更新导航栏标题
  void updateTitleByKey(String key, {Map<String, dynamic>? params}) {
    state = NavigationTitleData.fromKey(key, params: params);
  }
  
  /// 使用静态文本更新导航栏标题（向后兼容）
  void updateTitle(String title) {
    state = NavigationTitleData.fromText(title);
  }

  /// 重置导航栏标题
  void resetTitle() {
    state = null;
  }
}

@riverpod
class MainTabBarSearch extends _$MainTabBarSearch {
  @override
  String? build() {
    return null;
  }

  void updateValue(String? value) {
    state = value;
  }
}