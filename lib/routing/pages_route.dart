
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/web_view/kp_web_view_screen.dart';
import 'package:kpos/features/auth/domain/user_item.dart';
import 'package:kpos/features/device/presentation/bind_main_device_confirm_screen.dart';
import 'package:kpos/features/device/presentation/bind_main_device_screen.dart';
import 'package:kpos/features/device/presentation/connect_device_screen.dart';
import 'package:kpos/features/auth/presentation/full_lock_screen.dart';
import 'package:kpos/features/auth/presentation/login_screen.dart';
import 'package:kpos/features/auth/presentation/re_login_password_screen.dart';
import 'package:kpos/features/auth/presentation/re_login_screen.dart';
import 'package:kpos/features/auth/presentation/re_search_users_screen.dart';
import 'package:kpos/features/auth/presentation/user_agreement_screen.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/features/device/presentation/start_connect_screen.dart';
import 'package:kpos/features/language_settings/presentation/language_settings_screen.dart';
import 'package:kpos/features/launch/presentation/launch_screen.dart';
import 'package:kpos/features/store/presentation/select_store_screen.dart';
import 'package:kpos/features/store/presentation/unbind_store_screen.dart';
import 'package:kpos/features/store/presentation/unbind_store_confirm_screen.dart';
import 'package:kpos/routing/route_names.dart';

import '../common/constant/pos_type.dart';

part 'pages_route.g.dart';

@TypedGoRoute<LoginRoute>(path: '/login', name: RouteNames.login)
@immutable
class LoginRoute extends GoRouteData with _$LoginRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: LoginScreen());
  }
}

@TypedGoRoute<LaunchRoute>(path: '/launch', name: RouteNames.launch)
@immutable
class LaunchRoute extends GoRouteData with _$LaunchRoute {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const LaunchScreen();
  }
}

@TypedGoRoute<ConnectDeviceRoute>(path: '/connectDevice', name: RouteNames.connectDevice)
@immutable
class ConnectDeviceRoute extends GoRouteData with _$ConnectDeviceRoute {

  final PosType type;

  const ConnectDeviceRoute({required this.type});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ConnectDeviceScreen(type);
  }
}

@TypedGoRoute<LanguageSettingsRoute>(path: '/languageSettings', name: RouteNames.languageSettings)
@immutable
class LanguageSettingsRoute extends GoRouteData with _$LanguageSettingsRoute {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const LanguageSettingsScreen();
  }
}

@TypedGoRoute<KPWebViewRoute>(path: '/webView', name: RouteNames.webView)
@immutable
class KPWebViewRoute extends GoRouteData with _$KPWebViewRoute {

  final String url;

  const KPWebViewRoute({required this.url});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return KPWebViewScreen(url: url);
  }
}

@TypedGoRoute<UserAgreementRoute>(path: '/userAgreement', name: RouteNames.userAgreement)
@immutable
class UserAgreementRoute extends GoRouteData with _$UserAgreementRoute {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const UserAgreementScreen();
  }
}

@TypedGoRoute<SelectStoreRoute>(path: '/selectStore', name: RouteNames.selectStore)
@immutable
class SelectStoreRoute extends GoRouteData with _$SelectStoreRoute {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SelectStoreScreen();
  }
}

@TypedGoRoute<ChooseDeviceRoute>(path: '/chooseDevice', name: RouteNames.chooseDevice)
@immutable
class ChooseDeviceRoute extends GoRouteData with _$ChooseDeviceRoute {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ChooseDeviceScreen();
  }
}

@TypedGoRoute<StartConnectRoute>(path: '/startConnect', name: RouteNames.startConnect)
@immutable
class StartConnectRoute extends GoRouteData with _$StartConnectRoute {

  final PosType type;

  final int mainDeviceId;

  const StartConnectRoute(this.mainDeviceId, {required this.type});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return StartConnectScreen(type,mainDeviceId);
  }
}

@TypedGoRoute<ReLoginRoute>(path: '/reLogin', name: RouteNames.reLogin)
@immutable
class ReLoginRoute extends GoRouteData with _$ReLoginRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: ReLoginScreen());
  }
}

@TypedGoRoute<ReSearchUsersRoute>(path: '/reSearchUsers', name: RouteNames.reSearchUsers)
@immutable
class ReSearchUsersRoute extends GoRouteData with _$ReSearchUsersRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: ReSearchUsersScreen());
  }
}

@TypedGoRoute<ReLoginPasswordRoute>(path: '/reLoginPassword', name: RouteNames.reLoginPassword)
@immutable
class ReLoginPasswordRoute extends GoRouteData with _$ReLoginPasswordRoute {

  const ReLoginPasswordRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    final user = state.extra as UserItem;
    return NoTransitionPage(
        child: ReLoginPasswordScreen(user));
  }
}

@TypedGoRoute<FullLockScreenRoute>(path: '/fullLockScreen', name: RouteNames.fullLockScreen)
@immutable
class FullLockScreenRoute extends GoRouteData with _$FullLockScreenRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: FullLockScreen());
  }
}

@TypedGoRoute<UnbindStoreRoute>(path: '/unbindStore', name: RouteNames.unbindStore)
@immutable
class UnbindStoreRoute extends GoRouteData with _$UnbindStoreRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: UnbindStoreScreen());
  }
}

@TypedGoRoute<UnbindStoreConfirmRoute>(path: '/unbindStoreConfirm', name: RouteNames.unbindStoreConfirm)
@immutable
class UnbindStoreConfirmRoute extends GoRouteData with _$UnbindStoreConfirmRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: UnbindStoreConfirmScreen());
  }
}

@TypedGoRoute<BindMainDeviceRoute>(path: '/bindMainDevice', name: RouteNames.bindMainDevice)
@immutable
class BindMainDeviceRoute extends GoRouteData with _$BindMainDeviceRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: BindMainDeviceScreen());
  }
}

@TypedGoRoute<BindMainDeviceConfirmRoute>(path: '/bindMainDeviceConfirm', name: RouteNames.bindMainDeviceConfirm)
@immutable
class BindMainDeviceConfirmRoute extends GoRouteData with _$BindMainDeviceConfirmRoute {

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(
        child: BindMainDeviceConfirmScreen());
  }
}
