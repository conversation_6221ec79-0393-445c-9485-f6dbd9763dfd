import 'package:flutter/material.dart'; // 核心UI库
import 'package:flutter_riverpod/flutter_riverpod.dart'; // 状态管理
import 'package:go_router/go_router.dart'; // 路由管理
import 'package:kpos/assets/kp_locale.g.dart'; // 国际化文本
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/routing/mobile_tab_bar_navigator.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart'; // 顶部导航栏组件

class MobileTabBarItem extends BottomNavigationBarItem {

  /// 点击标签时会导航到此路径
  final String routerPath;

  /// 构造函数
  /// @param icon [Icon] 未选中状态的图标
  /// @param activeIcon [Icon] 选中状态的图标
  /// @param label [String?] 标签文本
  /// @param routerPath [String] 对应的路由路径
  MobileTabBarItem(
      {required super.icon,
      required super.activeIcon,
      super.label,
      required this.routerPath});
}

/// 移动端标签栏导航组件
class MobileTabBar extends ConsumerStatefulWidget {

  final Widget child;

  final Widget? topNavBarTrailingButtons;
  
  /// 是否显示顶部导航栏
  /// true表示显示顶部二级导航栏，false表示隐藏
  /// 默认为true
  final bool showTopNavBar;

  /// 构造函数
  /// @param child [Widget] 主内容区域组件
  /// @param topNavBarTrailingButtons [Widget?] 顶部导航栏右侧按钮组
  /// @param showTopNavBar [bool] 是否显示顶部导航栏，默认为true
  /// @param key [Key?] 组件唯一标识
  const MobileTabBar(
    this.child, {
    this.topNavBarTrailingButtons,
    this.showTopNavBar = true,
    super.key
  });

  /// 创建组件状态
  /// 返回一个_MobileTabBarState实例来管理组件状态
  @override
  ConsumerState createState() => _MobileTabBarState();
}

/// 移动端标签栏导航组件状态类
/// 管理底部标签栏和顶部二级导航栏的状态和行为
/// 处理路由导航、标签切换和二级菜单项的显示逻辑
class _MobileTabBarState extends ConsumerState<MobileTabBar> {
  /// 底部标签项列表
  List<MobileTabBarItem> get tabs => <MobileTabBarItem>[
    // 首页标签
    MobileTabBarItem(
        icon: const Icon(Icons.home),         // 未选中时的图标
        activeIcon: const Icon(Icons.home),   // 选中时的图标
        label: KPLocale.of(context).home,     // 标签文本，从国际化资源获取
        routerPath: '/product'),              // 对应的路由路径
        
    // 广播/购物车标签
    MobileTabBarItem(
        icon: const Icon(Icons.broadcast_on_home),
        activeIcon: const Icon(Icons.broadcast_on_home),
        label: KPLocale.of(context).broadcast,
        routerPath: '/cart'),
        
    // 个人中心标签 - 订单页面
    MobileTabBarItem(
        icon: const Icon(Icons.person),
        activeIcon: const Icon(Icons.person),
        label: KPLocale.of(context).mine,
        routerPath: '/order'),
        
    // 另一个个人中心标签
    MobileTabBarItem(
        icon: const Icon(Icons.person),
        activeIcon: const Icon(Icons.person),
        label: KPLocale.of(context).mine,
        routerPath: '/order'),
  ];
  
  /// 根据当前路由路径获取对应的标签索引
  /// @param location [String] 当前路由路径
  /// @return [int] 对应的标签索引，如果没有匹配的标签则返回0（默认首页）
  int _locationToTabIndex(String location) {
    // 查找路由路径前缀匹配的标签项
    final index = tabs.indexWhere((t) => location.startsWith(t.routerPath));
    // 如果没有找到匹配的标签（索引<0），则返回0（默认首页），否则返回找到的索引
    return index < 0 ? 0 : index;
  }
  
  /// 获取当前选中的标签索引
  int get _currentIndex =>
      _locationToTabIndex(GoRouterState.of(context).uri.toString());

  /// 处理底部标签项点击事件
  /// 如果点击的不是当前选中的标签，则导航到对应的路由路径
  /// @param context [BuildContext] 构建上下文
  /// @param tabIndex [int] 被点击的标签索引
  void _onItemTapped(BuildContext context, int tabIndex) {
    // 避免重复导航到当前路由
    if (tabIndex != _currentIndex) {
      // 使用GoRouter导航到对应的路由路径
      context.go(tabs[tabIndex].routerPath);
    }
  }
  
  /// 获取当前主导航标题文本
  /// 根据当前选中的标签索引获取对应的标签文本
  /// 用于顶部导航栏的标题显示
  /// @return [String] 当前选中标签的文本，如果没有则返回空字符串
  String _getCurrentTabTitle() {
    // 确保索引在有效范围内
    if (_currentIndex >= 0 && _currentIndex < tabs.length) {
      // 返回标签文本，如果为null则返回空字符串
      return tabs[_currentIndex].label ?? '';
    }
    // 索引无效时返回空字符串
    return '';
  }
  
  /// 获取当前主导航对应的二级导航菜单项
  /// 根据当前选中的底部标签索引返回对应的顶部二级导航菜单项
  /// 为不同的主标签配置其专属的二级导航项
  /// @return [List<MobileTopNavButton>] 二级导航菜单项列表
  // List<MobileTopNavButton> _getSecondaryMenuItems() {
  //   // 这里根据当前选中的主导航项返回对应的二级导航菜单
  //   // 实际应用中，这些数据可能来自API或其他数据源
  //   switch (_currentIndex) {
  //     case 0: // 首页对应的二级导航项
  //       return [
  //         // 全部产品页面
  //         MobileTopNavButton(title: '全部', routePath: '/product/main'),
  //         // 特色产品页面
  //         MobileTopNavButton(title: '特色产品', routePath: '/product/featured'),
  //         // 新品页面
  //         MobileTopNavButton(title: '新品', routePath: '/product/new'),
  //       ];
  //     case 1: // 购物车/广播对应的二级导航项
  //       return [
  //         // 全部购物车项目
  //         MobileTopNavButton(title: '全部', routePath: '/cart/all'),
  //         // 已保存到购物车的项目
  //         MobileTopNavButton(title: '已保存', routePath: '/cart/saved'),
  //       ];
  //     case 2: // 订单/个人中心对应的二级导航项
  //       return [
  //         // 全部订单页面
  //         MobileTopNavButton(title: '全部订单', routePath: '/order/all'),
  //         // 待处理订单页面
  //         MobileTopNavButton(title: '待处理', routePath: '/order/pending'),
  //         // 已完成订单页面
  //         MobileTopNavButton(title: '已完成', routePath: '/order/completed'),
  //       ];
  //     default: // 其他标签或无效索引时返回空列表
  //       return [];
  //   }
  // }
  
  /// 处理二级导航菜单项点击
  /// @param routePath [String] 被点击的菜单项对应的路由路径
  void _handleMenuItemTap(String routePath) {
    // 使用GoRouter导航到指定的路由路径
    context.go(routePath);
  }

  /// 构建移动端标签栏导航组件的UI
  /// @param context [BuildContext] 构建上下文
  /// @return [Widget] 构建的移动端标签栏导航组件
  @override
  Widget build(BuildContext context) {
    // 获取当前标签的标题文本
    final currentTabTitle = _getCurrentTabTitle();
    // 获取当前标签对应的二级导航菜单项
    // final secondaryMenuItems = _getSecondaryMenuItems();
    
    // 构建整体布局结构
    return Scaffold(
      body: Column(
        children: [
          // 1. 顶部二级导航栏
          // 根据 showTopNavBar 参数决定是否显示顶部导航栏
          if (widget.showTopNavBar)
            TopNavBar(
              mainTitle: currentTabTitle,                     // 主标题文本
              // dropdownItems: secondaryMenuItems,              // 二级导航菜单项
              onMenuItemTap: _handleMenuItemTap,             // 菜单项点击回调
              trailingContent: widget.topNavBarTrailingButtons, // 自定义右侧按钮组
            ),
          
          // 2. 主内容区域
          // 使用Expanded确保主内容区域填充所有可用空间
          Expanded(
            child: widget.child, // 显示传入的主内容组件
          ),
        ],
      ),
      // 3. 底部导航标签栏
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,                 // 当前选中的标签索引
        items: tabs,                                // 所有底部标签项
        onTap: (index) => _onItemTapped(context, index), // 标签点击回调
        // 选中状态颜色 - 橙色
        selectedItemColor: context.theme.brandColor7,
        // 未选中状态颜色 - 浅灰色
        unselectedItemColor: context.theme.grayColor2,
        // 选中状态图标样式
        selectedIconTheme: IconThemeData(
          color: context.theme.brandColor7, // 橙色
          size: 24,                 // 图标大小24像素
        ),
        // 选中状态文本样式
        selectedLabelStyle: TextStyle(
          color: context.theme.brandColor7, // 橙色
          fontSize: 14,            // 文本大小14像素
        ),
        // 未选中状态图标样式
        unselectedIconTheme: IconThemeData(
          color: context.theme.grayColor2, // 浅灰色
          size: 24,                 // 图标大小24像素
        ),
        // 未选中状态文本样式
        unselectedLabelStyle: TextStyle(
          color: context.theme.grayColor2, // 浅灰色
          fontSize: 14,            // 文本大小14像素
        ),
      ),
    );

  }
}
