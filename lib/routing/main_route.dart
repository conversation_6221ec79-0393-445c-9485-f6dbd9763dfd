import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/cart/presentation/cart_screen.dart';
import 'package:kpos/features/more/presentation/cash_management_screen.dart';
import 'package:kpos/features/more/presentation/other_setting_screen.dart';
import 'package:kpos/features/more/presentation/table_setting_screen.dart';
import 'package:kpos/features/order/presentation/order_screen.dart';
import 'package:kpos/features/product/presentation/product_screen.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:kpos/routing/main_tab_bar.dart';
import 'package:kpos/routing/mobile_tab_bar.dart';

import '../features/more/presentation/more_screen.dart';
import '../features/order/presentation/order_list_test_screen.dart';

part 'main_route.g.dart';

@TypedShellRoute<MainRoute>(
  routes: <TypedRoute<RouteData>> [
    TypedGoRoute<ProductRoute>(path: '/product'),
    TypedGoRoute<TableRoute>(path: '/table'),
    TypedGoRoute<CartRoute>(path: '/cart'),
    TypedGoRoute<OrderRoute>(path: '/order'),
    TypedGoRoute<MoreRoute>(path: '/more', routes: [
      TypedGoRoute<TableSettingRoute>(path: '/table_setting'),
      TypedGoRoute<CashManagementRoute>(path: '/cash_management'),
      TypedGoRoute<OtherSettingScreenRoute>(path: '/other_setting'),
    ]),
  ],
)

class MainRoute extends ShellRouteData {
  static final GlobalKey<NavigatorState> $navigatorKey = shellNavigatorKey;

  @override
  Widget builder(BuildContext context, GoRouterState state, Widget navigator) {
    debugPrint("ShellRouteData Screen Name = ${state.name}");
    return DeviceUtil.isMobile() ? MobileTabBar(navigator) : MainTabBar(navigator);
  }
}

@immutable
class ProductRoute extends GoRouteData with _$ProductRoute {
  const ProductRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    final extra = state.extra as Map?;
    final table = extra?['table'] as TableItem?;
    final tableGroup = extra?['group'] as TableGroupItem?;
    return NoTransitionPage(child: ProductScreen(table: table,tableGroup: tableGroup));
  }

  @override
  FutureOr<bool> onExit(BuildContext context, GoRouterState state) async{
    KPSlidePopup.dismissFromTarget();
    return super.onExit(context, state);
  }
}

@immutable
class CartRoute extends GoRouteData with _$CartRoute {
  const CartRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return NoTransitionPage(child: CartScreen());
  }
}

@immutable
class OrderRoute extends GoRouteData with _$OrderRoute {
  const OrderRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: OrderScreen());
  }
}

@immutable
class TableRoute extends GoRouteData with _$TableRoute {
  const TableRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: TableScreen());
  }

  @override
  FutureOr<bool> onExit(BuildContext context, GoRouterState state) async{
    final ref = ProviderScope.containerOf(context, listen: false);
    final isOpening = ref.read(sharingTableOpeningProvider);
    if(isOpening){
      //正在开台，弹窗提示
      TableItem? table = ref.read(selectedTableProvider);
      final result = await KPDialog.showAlertDialog(
        context: context,
        title: context.locale.orderNotOpenedTip(table?.tableTitle??''),
        content: context.locale.willCancelOrderTip,
        leftBtnTitle: context.locale.tableExit,
        rightBtnTitle: context.locale.tableOpenTable,
        leftAction: (){
          ref.read(sharingTableOpeningProvider.notifier).set(false);
        },
        rightAction: (){
          Navigator.of(context).pop(true);
        }
      );
      return result == false;
    }else{
      return super.onExit(context, state);
    }
  }
}

@immutable
class MoreRoute extends GoRouteData with _$MoreRoute {
  const MoreRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: MoreScreen());
  }
}

@immutable
class TableSettingRoute extends GoRouteData with _$TableSettingRoute {
  const TableSettingRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: TableSettingScreen());
  }
}

@immutable
class CashManagementRoute extends GoRouteData with _$CashManagementRoute {
  const CashManagementRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: CashManagementScreen());
  }
}

@immutable
class OtherSettingScreenRoute extends GoRouteData with _$OtherSettingScreenRoute {
  const OtherSettingScreenRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: OtherSettingScreen());
  }
}
