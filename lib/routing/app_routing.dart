
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'main_route.dart' as main;
import 'pages_route.dart' as pages;

part 'app_routing.g.dart';


final GlobalKey<NavigatorState> rootNavigatorKey = GlobalKey<NavigatorState>();

final GlobalKey<NavigatorState> shellNavigatorKey = GlobalKey<NavigatorState>();

@riverpod
GoRouter goRouter(GoRouterRef ref) {
  return appRouter;
}

@visibleForTesting
final appRouter = GoRouter(
  initialLocation: "/launch",
  navigatorKey: rootNavigatorKey,
  routes: [
    ...pages.$appRoutes,
    ...main.$appRoutes,
  ],
  errorBuilder: (
      BuildContext context,
      GoRouterState state,
      ) {
    return Scaffold(
      body: Center(
        child: Text(state.error.toString()),
      ),
    );
  },
  // extraCodec: MyExtraCodec(),
);