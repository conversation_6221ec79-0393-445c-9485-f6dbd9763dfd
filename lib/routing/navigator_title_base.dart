// lib/common/base/titled_page_base.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/routing/main_tab_bar_navigator_service.dart';

/// 带标题管理的消费者状态组件基类
/// 继承该类可以自动管理页面标题的更新和重置
abstract class TitledConsumerStatefulWidget extends ConsumerStatefulWidget {
  const TitledConsumerStatefulWidget({Key? key}) : super(key: key);
}

/// 带标题管理的消费者状态基类
/// 继承该类可以自动管理页面标题的更新和重置
abstract class TitledConsumerState<T extends TitledConsumerStatefulWidget> extends ConsumerState<T> {
  /// 获取页面标题（向后兼容）
  /// 子类可以实现该方法返回当前页面的标题
  String? getPageTitle(BuildContext context) => null;
  
  /// 新增：获取页面标题的国际化 key（推荐使用）
  /// 子类可以实现该方法返回当前页面的国际化 key
  String? getPageTitleKey() => null;
  
  /// 新增：获取国际化参数
  /// 子类可以实现该方法返回国际化所需的参数
  Map<String, dynamic>? getPageTitleParams() => null;
  
  @override
  void initState() {
    super.initState();
    // 在页面初始化后更新标题
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updatePageTitle();
    });
  }
  
  /// 新增：更新页面标题的统一方法
  void _updatePageTitle() {
    final titleKey = getPageTitleKey();
    
    if (titleKey != null) {
      // 优先使用国际化 key
      final params = getPageTitleParams();
      ref.read(navigationTitleProvider.notifier).updateTitleByKey(titleKey, params: params);
    } else {
      // 回退到静态文本（向后兼容）
      final title = getPageTitle(context);
      if (title != null) {
        ref.read(navigationTitleProvider.notifier).updateTitle(title);
      }
    }
  }

  // @override
  // void dispose() {
  //   // 在页面销毁时重置标题
  //   // 在 super.dispose() 之前获取并使用notifier使用
  //   final Notifier = ref.read(navigationTitleProvider.notifier);
  //   Notifier.resetTitle();
    
  //   super.dispose();
  // }
}