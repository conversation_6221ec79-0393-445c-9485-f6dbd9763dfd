import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_base.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/common/utils/platform_window_util.dart';
import 'package:kpos/kpos_app.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (PlatformUtil.isWindows || PlatformUtil.isMacOS) {
    PlatformWindowUtil.init();
  }
  if (Platform.isAndroid) {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }
  await DeviceUtil.init();
  await KeyValueStorageBase.init();
  // runApp(const ProviderScope(observers: [RiverpodLogger()], child: KPosApp()));
  runApp(const ProviderScope(child: KPosApp()));
}