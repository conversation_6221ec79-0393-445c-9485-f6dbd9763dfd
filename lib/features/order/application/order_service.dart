import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../domain/order_info.dart';
import '../presentation/order_list_test_screen.dart';

part 'order_service.g.dart';

class OrderService {
  final Ref _ref;

  OrderService({required Ref ref}) : _ref = ref;
  TabController? tabController;

  OrderStatusInfo? orderStatusInfo;

  final orderStatusInfoList = [
    OrderStatusInfo(orderStatusName: "全部", orderStatus: 0),
    OrderStatusInfo(orderStatusName: "待接单", orderStatus: 1),
    OrderStatusInfo(orderStatusName: "待结账", orderStatus: 2),
    OrderStatusInfo(orderStatusName: "已完成", orderStatus: 3),
    OrderStatusInfo(orderStatusName: "已取消", orderStatus: 4),
    OrderStatusInfo(orderStatusName: "已退单", orderStatus: 5),
  ];

  void itemTabEvent(BuildContext context, OrderStatusInfo orderStatusInfo) {
    _ref
        .read(selectedOrderStatusInfoProvider.notifier)
        .state = orderStatusInfo.orderStatus;
  }
}

@riverpod
OrderService orderService(Ref ref) {
  return OrderService(ref: ref);
}

