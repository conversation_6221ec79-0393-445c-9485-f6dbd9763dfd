import 'dart:convert';
import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/order/data/order_local_repository.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import '../../../common/services/networking/intranet_service/api_intranet_exception.dart';
import '../../../common/services/networking/intranet_service/api_intranet_localization.dart';
import '../../../common/services/networking/intranet_service/api_intranet_response.dart';

part 'order_server_service.g.dart';

class OrderServerService {
  final Ref _ref;

  OrderServerService({required Ref ref}) : _ref = ref;

  Locale get currentLocale => _ref.read(languageSettingsServiceProvider).currentLocale;

  Future<Response> deleteOrderItem(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.deleteOrderItem(data["orderItemId"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> returnOrderItem(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["status"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.returnOrderItem(data["status"],data["orderItemId"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> urgeOrderItem(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["status"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.urgeOrderItem(data["status"],data["orderItemId"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> servedOrderItem(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["status"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.servedOrderItem(data["status"],data["orderItemId"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> cancelOrder(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["reason"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.cancelOrder(data["orderId"],data["reason"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> submitOrder(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final localRes = OrderLocalRepository(_ref.watch(databaseProvider));
      await localRes.submitOrder(data["orderId"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

}

@riverpod
OrderServerService orderServerService(Ref ref) {
  return OrderServerService(ref: ref);
}