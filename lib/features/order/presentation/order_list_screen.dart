import 'package:flutter/material.dart';
import 'package:kpos/common/constant/app_sizes.dart';

class OrderListScreen extends StatelessWidget {
  OrderListScreen({super.key});

  final titles = [
    'Real crab california roll',
    'Real crab california roll',
    'Real crab california roll',
    'Real crab california roll',
    'Real crab california roll',
  ];

  final priceMap = [
    {'title': 'Subtotal', 'price': 45.00},
    {'title': 'Discount', 'price': 5.50},
    {'title': 'Tax（5%）', 'price': 4.50},
    {'title': 'Service', 'price': 2.50},
    {'title': 'Total', 'price': 49.50},
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildTopView(context),
          gapH20,
          Expanded(child: _buildListView(context)),
          _buildBottomView(),
        ],
      ),
    );
  }

  Widget _buildTopView(BuildContext context) {
    return SizedBox(
      width: 380,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('New Sale',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
              Text('Actions',
                  style: TextStyle(
                      color: Color(0xFFFF7D00),
                      fontSize: 14,
                      fontWeight: FontWeight.w700)),
            ],
          ),
          gapH12,
          Row(
            children: [
              Container(
                alignment: Alignment.center,
                padding: EdgeInsets.only(left: 10, right: 10),
                height: 40,
                decoration: BoxDecoration(
                    border: Border.all(color: Color(0xFFDCDDE1), width: 1)),
                child: Text(
                  'Dining method',
                  style: TextStyle(fontSize: 15),
                ),
              ),
              gapW16,
              Icon(Icons.person_2)
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildListView(BuildContext context) {
    return SizedBox(
      width: 380,
      child: ListView.builder(
          itemCount: titles.length,
          itemBuilder: (context, index) {
            return _buildItemView(context, titles[index]);
          }),
    );
  }

  Widget _buildItemView(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              children: [
                Container(
                  color: Color(0xFFF0F0F0),
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(left: 10, right: 10),
                  height: 22,
                  child: Text(
                    'X1',
                    style: TextStyle(fontSize: 14, color: Color(0xFF323843)),
                  ),
                ),
                gapW8,
                Flexible(
                  child: Text(
                    'Shrimp dumpling miso soup11111111111',
                    style: TextStyle(fontSize: 15, fontWeight: FontWeight.w700),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                )
              ],
            ),
          ),
          Text('S\$30.00', style: TextStyle(fontSize: 15)),
        ],
      ),
    );
  }

  Widget _buildBottomView() {
    return SizedBox(
      width: 380,
      height: 200,
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
                itemCount: priceMap.length,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final data = priceMap[index];
                  return Padding(
                    padding: EdgeInsets.only(bottom: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('${data['title']}',
                            style: _buildBottomStyle(
                                index == priceMap.length - 1)),
                        Text('S\$${data['price']}',
                            style: _buildBottomStyle(
                                index == priceMap.length - 1)),
                      ],
                    ),
                  );
                }),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      color: const Color(0xFFF3F3F4)),
                  child: const Text(
                    'Send',
                    style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w700,
                        fontSize: 16),
                  ),
                ),
              ),
              gapW12,
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      color: const Color(0xFF20232B)),
                  child: const Text(
                    'Pay',
                    style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  TextStyle _buildBottomStyle(bool isBold) {
    if (isBold) {
      return const TextStyle(
          fontSize: 20, fontWeight: FontWeight.w700, color: Color(0xFF323643));
    }
    return const TextStyle(fontSize: 14, color: Color(0xFF6F7686));
  }
}
