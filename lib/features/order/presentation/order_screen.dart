import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_handler.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_service.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/auth/application/auth_service.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../common/components/index.dart';
import '../../../common/services/networking/intranet_service/api_intranet_service.dart';
import '../../../common/services/rust_bridge/kp_rust_bridge.dart';

class OrderScreen extends ConsumerStatefulWidget {
  const OrderScreen({super.key});

  @override
  ConsumerState createState() => _OrderScreenState();
}

class _OrderScreenState extends ConsumerState<OrderScreen> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TDButton(
            width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
            height: 48,
            text: "锁屏开关",
            textStyle: TextStyle(color: Colors.white),
            style: TDButtonStyle(backgroundColor: Colors.black),
            onTap: () {
              FullLockScreenRoute().push(context);
            },
          ),
          SizedBox(height: 20,)
          ,
          TDButton(
            width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
            height: 48,
            text: "退出登录(临时)",
            textStyle: TextStyle(color: Colors.white),
            style: TDButtonStyle(backgroundColor: Colors.black),
            onTap: () {
              KPDialog.showExitConfirmDialog(
                  context: context,
                  title: context.locale.loginOut,
                  confirmBtnText: context.locale.confirm,
                  cancelBtnText: context.locale.cancel,
                  confirmAction: () => _onLogout(),
                  cancelAction: () {
                    // 用户点击取消按钮后的操作
                  });
            },
          ),
          SizedBox(height: 20,)
          ,
          TDButton(
            width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
            height: 48,
            text: "清除本地数据",
            textStyle: TextStyle(color: Colors.white),
            style: TDButtonStyle(backgroundColor: Colors.black),
            onTap: _clearData,
          ),
          SizedBox(height: 20,)
          ,
          TDButton(
            width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
            height: 48,
            text: "STOMP连接",
            textStyle: TextStyle(color: Colors.white),
            style: TDButtonStyle(backgroundColor: Colors.black),
            onTap: () {
              connectStomp();
            },
          ),
          SizedBox(height: 20,),
          TDButton(
            width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
            height: 48,
            text: "获取算价结果",
            textStyle: TextStyle(color: Colors.white),
            style: TDButtonStyle(backgroundColor: Colors.black),
            onTap: () {
              calculatePrice();
            },
          ),
        ],
      ),
    );
  }

  void calculatePrice() {
    final json = '''  
  {
  "tenant_id": 12345,
  "order_id": 67890,
  "service_fee_data": [
    {
      "service_charge_method": 1,
      "service_charge_rate": 10,
      "service_fixed_charge": 0,
      "service_fee_management_id": 1,
      "service_fee_name": "服务费",
      "service_duty_free": 0,
      "tax_management_settings": [
        {
          "service_fee_management_id": 1,
          "tax_management_id": 101,
          "tax_name": "增值税",
          "tax_rate": 6,
          "tax_type": 1
        }
      ]
    }
  ],
  "order_items": [
    {
      "tenant_id": 12345,
      "order_item_id": 1001,
      "order_id": 67890,
      "commodity_id": 501,
      "commodity_type": 1,
	  "discount_rate": 10,
      "parent_order_item_id": 0,
      "discount_flag": 1,
      "takeaway_flag": 0,
      "quantity": 2,
	  "commodity_price": "150.25",
      "packaging_setting_type": 0,
      "discount_type": 1,
      "tax_list": [
        {
          "tax_management_id": 101,
          "tax_rate": 6,
          "tax_type": 1,
          "tax_name": "增值税"
        }
      ],
      "order_item_practices": [
        {
          "order_item_practice_id": 2001,
          "order_item_id": 1001,
          "quantity": 1,
          "price": 5
        }
      ]
    }
  ]
}
  ''';
    final result = KPRustBridge().cartListSummary(json);
    KPDialog.showConfirmDialog(context: context,content: result);
    writeLogToFile(result);
    if (Platform.isWindows) {
      final result = KPRustBridge().cartListSummary(json);
      print(result);
      KPDialog.showConfirmDialog(context: context,content: result);
    }
  }

  Future<void> writeLogToFile(String content) async {
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/debug_log.txt');
    await file.writeAsString(content);
    print('Log written to ${file.path}');
  }

  Future<void> _clearData() async {
    final ctx = context;
    await ref.read(authServiceProvider).clearAuthToStorageService();
    if (!ctx.mounted) return;
    LaunchRoute().go(ctx);
  }

  void _onLogout() {
    final ctx = context;
    ref.read(authServiceProvider).logout().then((value) async {
      await ref.read(apiIntranetServiceProvider).startServer();
      if (!ctx.mounted) return;
      LaunchRoute().go(ctx);
    }).onError((error, stack) {
      String errorMessage = error.toString();
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.replaceAll('Exception: ', '');
      }
      if (!context.mounted) return;
      KPToast.show(content: errorMessage);
    });
  }


  void connectStomp() {
    KpInputDialog.show(
        context: context,
        title: context.locale.enterIPAddress,
        label: context.locale.ipAddress,
        hintText: context.locale.getIPAddressTip,
        confirmButtonText: context.locale.confirm,
        cancelButtonText: context.locale.cancel,
        keyboardType: TextInputType.number,
        type: InputDialogType.ipAddress, //连续输入3位
        //确认
        onConfirm: (inputText) {
          if (inputText != null && inputText.isNotEmpty) {
            final stomp = ref.read(stompServiceProvider);
            stomp.connect(url: 'ws://$inputText:8080/ws/websocket?deviceUuid=${DeviceUtil().terminalSerialNumber}');
          }
        },
        // 取消
        onCancel: () {

        }
    );
  }
}
