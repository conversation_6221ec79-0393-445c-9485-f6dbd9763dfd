import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../../assets/assets.gen.dart';
import '../../../../common/components/kp_toast.dart';

class OrderDetailsInfo {
  final String orderNumber;
  final String orderTime;
  final String orderSource;
  final String diningType;
  final String pickUpNumber;
  final String orderAmount;
  final String paymentStatus;
  final String orderStatus;
  final String orderNote;
  final String operatorName;
  final String operatorNumber;
  final String businessDate;
  final int dinerNUmber;

  const OrderDetailsInfo({
    required this.orderNumber,
    required this.orderTime,
    required this.orderSource,
    required this.diningType,
    required this.pickUpNumber,
    required this.orderAmount,
    required this.paymentStatus,
    required this.orderStatus,
    required this.orderNote,
    required this.operatorName,
    required this.operatorNumber,
    required this.businessDate,
    required this.dinerNUmber,
  });
}

class OrderSlidePopup extends ConsumerStatefulWidget {
  final OrderDetailsInfo orderDetailsInfo;

  const OrderSlidePopup({super.key, required this.orderDetailsInfo});

  @override
  ConsumerState createState() => _OrderSlidePopupState();
}

class _OrderSlidePopupState extends ConsumerState<OrderSlidePopup> {
  bool isExpandedActivityLog = false;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: double.maxFinite,
        color: Colors.white,
        padding: const EdgeInsets.all(20),
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  mainWidget(widget.orderDetailsInfo),
                  const SizedBox(height: 20),
                  _buildBasicInformation(widget.orderDetailsInfo),
                  const SizedBox(height: 20),
                  Text("Order Details",
                      style: KPFontStyle.headingLarge.copyWith(
                        fontWeight: FontWeight.w700,
                        color: KPColors.textGrayPrimary,
                      )),
                  _buildListView(context),
                  _buildPrice(context),
                  _buildDiscount(context),
                 _buildPaymentDetailsView(context),
                  _buildMergeTableOrdersView(context),
                  _buildPaymentDetailsByProductView(context),
                  _buildCustomerInformation(widget.orderDetailsInfo),
                  _buildActivityLog(),
                  // _buildActivityLog(context),
                  const SizedBox(height: 100),
                ],
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomView(),
            ),
          ],
        ));
  }

  Widget mainWidget(OrderDetailsInfo orderDetailsInfo) {
    late Color color;
    switch (orderDetailsInfo.orderStatus) {
      case "待下单":
        color = KPColors.textYellowDefault;
        break;
      case "待接单":
        color = KPColors.textBlueDefault;
        break;
      case "待支付":
        color = KPColors.textBrandDefault;
        break;
      case "已完成":
        color = KPColors.textGreenDefault;
        break;
      case "已取消":
        color = KPColors.textGrayTertiary;
        break;
      case "已退单":
        color = KPColors.textRedDefault;
        break;
      case "待取餐":
        color = KPColors.chartCategorical7Default;
        break;
      default:
        color = KPColors.textGrayPrimary;
        break;
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      "${orderDetailsInfo.orderNumber} (${orderDetailsInfo.orderSource})",
                      style: KPFontStyle.headingLarge.copyWith(
                          fontWeight: FontWeight.w700,
                          color: KPColors.textGrayPrimary),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      child: Assets.images.iconCopy
                          .svg(width: 20, height: 20),
                      onTap: () {
                        Clipboard.setData(
                            ClipboardData(text: orderDetailsInfo.orderNumber));
                        KPToast.show(content: "已复制订单号");
                      },
                    ),
                  ],
                )),
            Text(
              orderDetailsInfo.orderStatus,
              style: KPFontStyle.headingMedium
                  .copyWith(fontWeight: FontWeight.w700, color: color),
            ),
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Assets.images.iconDish.svg(width: 16, height: 16),
            const SizedBox(
              width: 4,
            ),
            Text(
              "${orderDetailsInfo.diningType} - ${orderDetailsInfo.pickUpNumber}",
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            ),
            // const SizedBox(
            //   width: 8,
            // ),
            // SizedBox(
            //   width: 1,
            //   height: 16,
            //   child: Container(
            //     color: KPColors.borderGrayLightBase,
            //   ),
            // ),
            // const SizedBox(
            //   width: 8,
            // ),
            // Assets.images.iconUser.svg(width: 16, height: 16),
            // const SizedBox(
            //   width: 5,
            // ),
            // Text(
            //   "${orderDetailsInfo.dinerNUmber}",
            //   style: KPFontStyle.bodyMedium.copyWith(
            //       fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            // ),
            // const SizedBox(
            //   width: 8,
            // ),
            // SizedBox(
            //   width: 1,
            //   height: 16,
            //   child: Container(
            //     color: KPColors.borderGrayLightBase,
            //   ),
            // ),
            // const SizedBox(
            //   width: 8,
            // ),
            // Text(
            //   "${orderDetailsInfo.operatorName}(${orderDetailsInfo.operatorNumber})",
            //   style: KPFontStyle.bodyMedium.copyWith(
            //       fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            // ),
          ],
        ),
        // const SizedBox(
        //   height: 20,
        // ),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     Text(
        //       "Business date",
        //       style: KPFontStyle.bodyMedium.copyWith(
        //           fontWeight: FontWeight.w400,
        //           color: KPColors.textGraySecondary),
        //     ),
        //     const SizedBox(
        //       width: 4,
        //     ),
        //     Text(
        //       orderDetailsInfo.businessDate,
        //       style: KPFontStyle.bodyMedium.copyWith(
        //           fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
        //     ),
        //   ],
        // ),
        // const SizedBox(
        //   height: 16,
        // ),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     Text(
        //       "Order time",
        //       style: KPFontStyle.bodyMedium.copyWith(
        //           fontWeight: FontWeight.w400,
        //           color: KPColors.textGraySecondary),
        //     ),
        //     const SizedBox(
        //       width: 4,
        //     ),
        //     Text(
        //       orderDetailsInfo.orderTime,
        //       style: KPFontStyle.bodyMedium.copyWith(
        //           fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
        //     ),
        //   ],
        // ),
        const SizedBox(height: 20),
        SizedBox(
          height: 1,
          child: Container(
            color: KPColors.borderGrayLightBase,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInformation(OrderDetailsInfo orderDetailsInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Basic information",
          style: KPFontStyle.headingLarge.copyWith(
              fontWeight: FontWeight.w700, color: KPColors.textGrayPrimary),
        ),
        const SizedBox(
          height: 16,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Cashier",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGraySecondary,
                  ),
                ),
                Text(
                  "Clyde Hung",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Guests",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGraySecondary,
                    ),
                  ),
                  Text(
                    "4",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGrayPrimary,
                    ),
                  )
                ],
              )),
        ]),
        const SizedBox(
          height: 16,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Reservation time",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGraySecondary,
                  ),
                ),
                Text(
                  "------",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Business date",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGraySecondary,
                    ),
                  ),
                  Text(
                    "2025-05-22",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGrayPrimary,
                    ),
                  )
                ],
              )),
        ]),
        const SizedBox(
          height: 16,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Order time",
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGraySecondary),
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              orderDetailsInfo.orderTime,
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 1,
          child: Container(
            color: KPColors.borderGrayLightBase,
          ),
        )
      ],
    );
  }

  Widget _buildCustomerInformation(OrderDetailsInfo orderDetailsInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Text(
          "Customer information",
          style: KPFontStyle.headingLarge.copyWith(
              fontWeight: FontWeight.w700, color: KPColors.textGrayPrimary),
        ),
        const SizedBox(
          height: 16,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Name",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGraySecondary,
                  ),
                ),
                Text(
                  "Clyde Wong",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Membership number",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGraySecondary,
                    ),
                  ),
                  Text(
                    "00000001",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGrayPrimary,
                    ),
                  )
                ],
              )),
        ]),
        const SizedBox(
          height: 16,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.start, children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Email",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGraySecondary,
                  ),
                ),
                Text(
                  "<EMAIL>",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )
              ],
            ),
          ),
          Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Phone number",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGraySecondary,
                    ),
                  ),
                  Text(
                    "+65 6123 4567",
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGrayPrimary,
                    ),
                  )
                ],
              )),
        ]),
        const SizedBox(
          height: 16,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Order time",
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGraySecondary),
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              orderDetailsInfo.orderTime,
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 1,
          child: Container(
            color: KPColors.borderGrayLightBase,
          ),
        )
      ],
    );
  }

  Widget _buildListView(BuildContext context) {
    return ListView.builder(
        shrinkWrap: true,
        itemCount: 4,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return _buildItemView(context, index);
        });
  }

  Widget _buildItemView(BuildContext context, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            constraints: const BoxConstraints(minWidth: 32),
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            child: Text(
              "x1",
              style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400, color: KPColors.textGrayPrimary),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.network(
                    'https://img1.baidu.com/it/u=1361177696,203903602&fm=253&app=138&f=JPEG?w=800&h=1455',
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Padding(
                                  padding: const EdgeInsets.only(right: 10),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Flexible(flex: 1,child: Text(
                                        "Shrimp dumpling miso ",
                                        style:
                                        KPFontStyle.headingSmall.copyWith(
                                          fontWeight: FontWeight.w700,
                                          color: KPColors.textGrayPrimary,
                                          decoration: index == 2
                                              ? TextDecoration.lineThrough
                                              : TextDecoration.none,
                                        ),
                                      ),),
                                      const SizedBox(width: 8),
                                      Visibility(
                                          visible: index == 1 || index == 2 || index == 3,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: KPColors.fillRedLightest,
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 1, horizontal: 8),
                                            child: Text("-20% off",
                                                style: KPFontStyle.bodySmall
                                                    .copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: KPColors
                                                            .textRedDefault)),
                                          )),
                                      const SizedBox(width: 8),
                                      Visibility(
                                          visible: index == 2 || index == 3,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  KPColors.fillGrayLightLight,
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 1, horizontal: 8),
                                            child: Text("Return",
                                                style: KPFontStyle.bodySmall
                                                    .copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: KPColors
                                                            .textGrayPrimary)),
                                          )),
                                      const SizedBox(width: 8),
                                      Visibility(
                                          visible: index == 3,
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: KPColors.fillBlueLightest,
                                              borderRadius:
                                                  BorderRadius.circular(4),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 1, horizontal: 8),
                                            child: Text("Takeaway",
                                                style: KPFontStyle.bodySmall
                                                    .copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: KPColors
                                                            .textBlueDefault)),
                                          )),
                                    ],
                                  )),
                            ),
                            Visibility(
                                visible: index == 1,
                                child: Text(
                                  "S\$6.25",
                                  style: KPFontStyle.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: KPColors.textGrayTertiary,
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                )),
                            Visibility(
                                visible: index == 1,
                                child: const SizedBox(width: 8)),
                            Text(
                              "S\$18.98",
                              style: KPFontStyle.bodyMedium.copyWith(
                                fontWeight: FontWeight.w400,
                                color: KPColors.textGrayPrimary,
                              ),
                            )
                          ],
                        ),
                        const SizedBox(height: 2),
                        Visibility(
                            visible: index == 0,
                            child: Text(
                              "Add spice",
                              style: KPFontStyle.bodySmall.copyWith(
                                fontWeight: FontWeight.w400,
                                color: KPColors.textGraySecondary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            )),
                        const SizedBox(height: 2),
                        Visibility(
                          visible: index == 1,
                          child: Text(
                            "Discount reason: Flash promotion",
                            style: KPFontStyle.bodySmall.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Visibility(
                          visible: index == 2,
                          child: Text(
                            "Return reason: Wrong item ordered",
                            style: KPFontStyle.bodySmall.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Visibility(
                          visible: index == 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Chicken burger",
                                style: KPFontStyle.headingXSmall.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: KPColors.textGrayPrimary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                "Add spice",
                                style: KPFontStyle.bodySmall.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: KPColors.textGraySecondary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                "French fries",
                                style: KPFontStyle.headingXSmall.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: KPColors.textGrayPrimary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                "Add tomato sauce",
                                style: KPFontStyle.bodySmall.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: KPColors.textGraySecondary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                "Cola",
                                style: KPFontStyle.headingXSmall.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: KPColors.textGrayPrimary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                "Add ice",
                                style: KPFontStyle.bodySmall.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: KPColors.textGraySecondary,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                      flex: 1,
                                      child: Text(
                                        "x1 Packing box",
                                        style:
                                            KPFontStyle.headingXSmall.copyWith(
                                          fontWeight: FontWeight.w700,
                                          color: KPColors.textGrayPrimary,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      )),
                                  Text(
                                    "S\$0.5",
                                    style: KPFontStyle.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w400,
                                      color: KPColors.textGrayPrimary,
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        const SizedBox(height: 2),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                "Commission: Clyde Hung",
                style: KPFontStyle.bodySmall.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGraySecondary,
                    decoration: index == 2
                        ? TextDecoration.lineThrough
                        : TextDecoration.none),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          )),
        ],
      ),
    );
  }

  Widget _buildPrice(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Notes",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )),
            Text(
              "The boss’s good friend.",
              style: KPFontStyle.bodyMedium.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayPrimary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Service",
                  style: KPFontStyle.bodyMedium.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayPrimary,
                  ),
                )),
            Text(
              "S\$15.00",
              style: KPFontStyle.bodyMedium.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayPrimary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Subtotal",
                  style: KPFontStyle.headingXSmall.copyWith(
                    fontWeight: FontWeight.w700,
                    color: KPColors.textGrayPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "S\$82.00",
              style: KPFontStyle.bodyMedium.copyWith(
                fontWeight: FontWeight.w700,
                color: KPColors.textGrayPrimary,
              ),
            )
          ],
        ),
        const SizedBox(height: 20),
        SizedBox(
          height: 1,
          child: Container(
            color: KPColors.borderGrayLightBase,
          ),
        ),
      ],
    );
  }

  Widget _buildDiscount(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Discount",
                  style: KPFontStyle.headingXSmall.copyWith(
                    fontWeight: FontWeight.w700,
                    color: KPColors.textGrayPrimary,
                  )
                )),
            Text(
              "-S\$82.00",
              style: KPFontStyle.bodyMedium.copyWith(
                fontWeight: FontWeight.w700,
                color: KPColors.textRedDefault,
              ),
            )
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Product discount: Get a S\$15 discount on purchases over S\$50",
                  style: KPFontStyle.bodySmall.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayTertiary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "-S\$15.00",
              style: KPFontStyle.bodySmall.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayTertiary,
              ),
            )
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Dish discount: Flash promotion",
                  style: KPFontStyle.bodySmall.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayTertiary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "-S\$15.00",
              style: KPFontStyle.bodySmall.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayTertiary,
              ),
            )
          ],
        ),
        const SizedBox(height: 16),

        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                    "Tax",
                    style: KPFontStyle.headingXSmall.copyWith(
                      fontWeight: FontWeight.w700,
                      color: KPColors.textGrayPrimary,
                    )
                )),
            Text(
              "-S\$8.00",
              style: KPFontStyle.bodyMedium.copyWith(
                fontWeight: FontWeight.w700,
                color: KPColors.textRedDefault,
              ),
            )
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "State tax (5%)",
                  style: KPFontStyle.bodySmall.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayTertiary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "-S\$4.00",
              style: KPFontStyle.bodySmall.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayTertiary,
              ),
            )
          ],
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "City tax (5%)",
                  style: KPFontStyle.bodySmall.copyWith(
                    fontWeight: FontWeight.w400,
                    color: KPColors.textGrayTertiary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "-S\$1.00",
              style: KPFontStyle.bodySmall.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayTertiary,
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                flex: 1,
                child: Text(
                  "Total",
                  style: KPFontStyle.headingMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: KPColors.textGrayPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),
            Text(
              "S\$82.00",
              style: KPFontStyle.headingMedium.copyWith(
                fontWeight: FontWeight.w700,
                color: KPColors.textGrayPrimary,
              ),
            )
          ],
        ),
        const SizedBox(height: 20),
        SizedBox(
          height: 1,
          child: Container(
            color: KPColors.borderGrayLightBase,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentDetailsView(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const SizedBox(height: 20),
      Text("Payment details",
          style: KPFontStyle.headingLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: KPColors.textGrayPrimary,
          )),
      const SizedBox(height: 16),
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              flex: 1,
              child: Text(
                "KPay - Credit card (1234567890)",
                style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGrayPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
          Text(
            "S\$49.00",
            style: KPFontStyle.bodyMedium.copyWith(
              fontWeight: FontWeight.w400,
              color: KPColors.textGrayPrimary,
            ),
          )
        ],
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              flex: 1,
              child: Text(
                "Bank card (**********)",
                style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGrayPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
          Text(
            "S\$0.49",
            style: KPFontStyle.bodyMedium.copyWith(
              fontWeight: FontWeight.w400,
              color: KPColors.textGrayPrimary,
            ),
          )
        ],
      ),
      const SizedBox(height: 16),
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              flex: 1,
              child: Text(
                "Total",
                style: KPFontStyle.headingMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: KPColors.textGrayPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                "S\$49.99",
                style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: KPColors.textGrayPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                "Includes S\$5.00 tip",
                style: KPFontStyle.bodySmall.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGrayTertiary,
                ),
              )
            ],
          )
        ],
      ),
      const SizedBox(height: 16),
      SizedBox(
        height: 1,
        child: Container(
          color: KPColors.borderGrayLightBase,
        ),
      ),
    ]);
  }

  Widget _buildMergeTableOrdersView(BuildContext context) {
    final List<String> tableNumbers = [
      "123456789876543212",
      "458464684846464684",
      "416546" "498644"
    ];
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const SizedBox(height: KPSpacing.xl),
      Text("Merge tables order (${tableNumbers.length})",
          style: KPFontStyle.headingLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: KPColors.textGrayPrimary,
          )),
      const SizedBox(height: KPSpacing.xl),
      ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: tableNumbers.length,
          itemBuilder: (context, index) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text(tableNumbers[index],
                      style: KPFontStyle.bodyMedium.copyWith(
                        color: KPColors.textGrayPrimary,
                        fontWeight: FontWeight.w400,
                      )),
                  const SizedBox(width: 8),
                  Assets.images.iconCopy.svg(width: 20, height: 20),
                ]),
                const SizedBox(height: KPSpacing.xl),
              ],
            );
          }),
      const SizedBox(height: KPSpacing.xl),
      SizedBox(
        height: 1,
        child: Container(
          color: KPColors.borderGrayLightBase,
        ),
      ),
    ]);
  }

  Widget _buildPaymentDetailsByProductView(BuildContext context) {
    final List<PaymentLog> paymentList = [
      PaymentLog(
          tableNumber: "Settle 1",
          paymentMethod: [
            PaymentMethod(
                paymentPay: "KPay", creditCardNumber: "15156", amount: 49.00),
            PaymentMethod(
                paymentPay: "Cash", creditCardNumber: "", amount: 149.00),
          ],
          totalAmount: 49.00,
          tip: 1.0),
      PaymentLog(
          tableNumber: "Settle 2",
          paymentMethod: [
            PaymentMethod(
                paymentPay: "WeiXin", creditCardNumber: "", amount: 249.00),
            PaymentMethod(
                paymentPay: "ZhiFuBao", creditCardNumber: "", amount: 349.00),
          ],
          totalAmount: 59.00,
          tip: 10.0),
      PaymentLog(
          tableNumber: "Settle 3",
          paymentMethod: [
            PaymentMethod(
                paymentPay: "JingDong", creditCardNumber: "", amount: 449.00),
            PaymentMethod(
                paymentPay: "MeiTuan", creditCardNumber: "", amount: 549.00),
          ],
          totalAmount: 69.00,
          tip: 20.0),
    ];
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const SizedBox(height: 20),
      Text("Payment details - Checkout by item",
          style: KPFontStyle.headingLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: KPColors.textGrayPrimary,
          )),
      const SizedBox(height: 16),
      ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: paymentList.length,
          itemBuilder: (context, index) {
            final payment = paymentList[index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  payment.tableNumber,
                  style: KPFontStyle.headingSmall.copyWith(
                    fontWeight: FontWeight.w700,
                    color: KPColors.textGrayPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: KPSpacing.s),
                ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: payment.paymentMethod.length,
                    itemBuilder: (context, index) {
                      final paymentMethod = payment.paymentMethod[index];
                      String paymentInfo = paymentMethod.paymentPay;
                      if (paymentMethod.creditCardNumber.isNotEmpty) {
                        paymentInfo =
                            "${paymentMethod.paymentPay} -(${paymentMethod.creditCardNumber})";
                      }

                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Text(
                                    paymentInfo,
                                    style: KPFontStyle.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w400,
                                      color: KPColors.textGrayPrimary,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  )),
                              Text(
                                "S\$${paymentMethod.amount}",
                                style: KPFontStyle.bodyMedium.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: KPColors.textGrayPrimary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: KPSpacing.s),
                        ],
                      );
                    }),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                        flex: 1,
                        child: Text(
                          "Total",
                          style: KPFontStyle.headingXSmall.copyWith(
                            fontWeight: FontWeight.w700,
                            color: KPColors.textGrayPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "S\$${payment.totalAmount}",
                          style: KPFontStyle.bodyMedium.copyWith(
                            fontWeight: FontWeight.w700,
                            color: KPColors.textGrayPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Visibility(
                          visible: payment.tip != 0.0,
                          child: Text(
                            "Includes S\$${payment.totalAmount} tip",
                            style: KPFontStyle.bodySmall.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGrayTertiary,
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
                const SizedBox(height: 16),
              ],
            );
          }),
      const SizedBox(height: 16),
      Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
              flex: 1,
              child: Text(
                "Total",
                style: KPFontStyle.headingMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: KPColors.textGrayPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                "S\$49.99",
                style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: KPColors.textGrayPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                "Includes S\$5.00 tip",
                style: KPFontStyle.bodySmall.copyWith(
                  fontWeight: FontWeight.w400,
                  color: KPColors.textGrayTertiary,
                ),
              )
            ],
          )
        ],
      ),
      const SizedBox(height: 16),
      SizedBox(
        height: 1,
        child: Container(
          color: KPColors.borderGrayLightBase,
        ),
      ),
    ]);
  }

  Widget _buildActivityLog() {
    final activityLogList = [
      ActivityLog(
          status: "Add 4 items",
          time: "2025-05-22 00:20:00",
          descriptionList: [
            "Shrimp dumpling miso soup (large)",
            "Mango Pudding (small)",
            "Durian pizza (large)",
            "Shrimp dumpling miso soup (large)",
            "Mango Pudding (small)",
            "Durian pizza (large)",
          ],
          operator: "Clyde Hung",
          machine: "A POS",
          machineNumber: "1234567890"),
      ActivityLog(
          status: "Create order",
          time: "2025-05-24 01:20:00",
          descriptionList: [],
          operator: "Clyde Hung",
          machine: "A POS",
          machineNumber: "9*245455681234567890"),
      ActivityLog(
          status: "Open a table",
          time: "2025-05-29 01:20:00",
          descriptionList: ["A01"],
          operator: "Clyde Hung",
          machine: "A POS",
          machineNumber: "21219*245455681234567890")
    ];

    return SingleChildScrollView(
      child: ExpansionPanelList(
        elevation: 0,
        expansionCallback: (int index, bool isExpanded) {
          setState(() {
            isExpandedActivityLog = isExpanded;
          });
        },
        children: [
          ExpansionPanel(
              backgroundColor: Colors.white,
              headerBuilder: (BuildContext context, bool isExpanded) {
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text("Activity log",
                      style: KPFontStyle.headingLarge.copyWith(
                        fontWeight: FontWeight.w700,
                        color: KPColors.textGrayPrimary,
                      )),
                );
              },
              body: Column(children: [
                ...activityLogList.asMap().entries.map((activityLogMap) {
                  final activityLog = activityLogMap.value;
                  final index = activityLogMap.key;
                  final isLastStep = index == activityLogList.length - 1;
                  return IntrinsicHeight(
                      child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 8,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const SizedBox(height: 8),
                            Visibility(
                                visible: index == 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: KPColors.fillGrayDarDarkest,
                                  ),
                                )),
                            Visibility(
                                visible: index != 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: KPColors.fillGrayLightDarkest,
                                        width: 1,
                                      )),
                                )),
                            const SizedBox(height: KPSpacing.m),
                            if (!isLastStep)
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    width: 1,
                                    padding:
                                        const EdgeInsets.only(top: KPSpacing.m),
                                    color: KPColors.fillGrayLightDarkest,
                                  ))
                          ],
                        ),
                      ),
                      const SizedBox(width: KPSpacing.xl),
                      // 右侧信息
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            activityLog.status,
                            style: KPFontStyle.headingSmall.copyWith(
                              fontWeight: index == 0
                                  ? FontWeight.w700
                                  : FontWeight.w400,
                              color: index == 0
                                  ? KPColors.textGrayPrimary
                                  : KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          ...activityLog.descriptionList
                              .map((description) => Text(description,
                                  style: KPFontStyle.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: KPColors.textGrayPrimary,
                                  )))
                        ],
                      )),
                      const SizedBox(width: 4),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            activityLog.time,
                            style: KPFontStyle.bodyMedium.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            "${activityLog.machine}(${activityLog.machineNumber})",
                            style: KPFontStyle.bodyMedium.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            activityLog.operator,
                            style: KPFontStyle.bodyMedium.copyWith(
                              fontWeight: FontWeight.w400,
                              color: KPColors.textGraySecondary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 16),
                        ],
                      )
                    ],
                  ));
                }),
              ]),
              isExpanded: isExpandedActivityLog),
        ],
      ),
    );
  }

  Widget _buildBottomView() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: context.theme.grayColor1,
            width: 1.0,
          ),
        ),
      ),
      padding: const EdgeInsets.all(KPSpacing.xl),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Expanded(
            flex: 1,
            child: TDButton(
              height: 48,
              style:
                  TDButtonStyle(backgroundColor: KPColors.fillGrayLightLight),
              textStyle: KPFontStyle.headingSmall.copyWith(
                  fontWeight: FontWeight.w700, color: KPColors.textGrayPrimary),
              text: "Reject order",
              onTap: () {},
            ),
          ),
          const SizedBox(width: KPSpacing.l),
          Expanded(
            flex: 1,
            child: TDButton(
              height: 48,
              style: TDButtonStyle(backgroundColor: Colors.black),
              textStyle: const TextStyle(color: Colors.white),
              text: "Accept order",
              onTap: () {},
            ),
          )
        ],
      ),
    );
  }
}

class ActivityLog {
  final String status;
  final String time;
  final List<String> descriptionList;
  final String operator;
  final String machine;
  final String machineNumber;

  ActivityLog({
    required this.status,
    required this.time,
    required this.descriptionList,
    required this.operator,
    required this.machine,
    required this.machineNumber,
  });
}

class PaymentLog {
  final String tableNumber;
  final List<PaymentMethod> paymentMethod;
  final double totalAmount;
  final double tip;

  PaymentLog({
    required this.tableNumber,
    required this.paymentMethod,
    required this.totalAmount,
    required this.tip,
  });
}

class PaymentMethod {
  final double amount;
  final String paymentPay;
  final String creditCardNumber;

  PaymentMethod({
    required this.amount,
    required this.paymentPay,
    required this.creditCardNumber,
  });
}
