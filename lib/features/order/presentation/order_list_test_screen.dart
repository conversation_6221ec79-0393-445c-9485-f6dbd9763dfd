import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/order/application/order_service.dart';
import 'package:kpos/features/order/presentation/items/order_silde_popup.dart';
import 'package:kpos/features/order/presentation/order_status_all_list.dart';
import 'package:kpos/features/order/presentation/order_status_cancelled_list.dart';
import 'package:kpos/features/order/presentation/order_status_completed_list.dart';
import 'package:kpos/features/order/presentation/order_status_pending_payment_list.dart';
import 'package:kpos/features/order/presentation/order_status_refunded_list.dart';

import '../../../routing/app_routing.dart';
import '../domain/order_info.dart';
import 'order_status_waiting_for_order_list.dart';

class OrderListTestScreen extends ConsumerStatefulWidget {
  const OrderListTestScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _OrderListTestScreenState();
}

class _OrderListTestScreenState extends ConsumerState<OrderListTestScreen>
    with SingleTickerProviderStateMixin {
  // 获取go_router实例
  late final GoRouter _goRouter;

  @override
  void initState() {
    _goRouter = ref.read(goRouterProvider);
    // 添加路由监听
    _goRouter.routerDelegate.addListener(_onRouteChanged);
    super.initState();
  }

  @override
  void dispose() {
    // 移除路由监听
    _goRouter.routerDelegate.removeListener(_onRouteChanged);
    super.dispose();
  }

  void _onRouteChanged() {
    // 当路由变化时，检查当前是否显示弹窗，并关闭
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  Widget buildExtendedTabBarView(
      {required Widget contentWidget}) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child:
              contentWidget
        ),
        Container(
          width: 1,
          height: double.maxFinite,
          color: KPColors.borderGrayLightBase,
        ),
        const Expanded(
            flex: 1,
            child:
            OrderSlidePopup(
                orderDetailsInfo: OrderDetailsInfo(
                    orderNumber: "#467147914810940194-1",
                    orderTime: "2025/01/01 00:00:00 ~ 2025/01/01 15:00:00",
                    orderSource: "QR code",
                    diningType: "Dine-in",
                    pickUpNumber: "A16",
                    orderAmount: "50",
                    paymentStatus: "待支付",
                    orderStatus: "待支付",
                    orderNote: "orderNote",
                    operatorName: "Clyde Hung",
                    operatorNumber: "!21414141",
                    businessDate: "2025/01/01",
                    dinerNUmber: 5))
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final orderStatusInfoList =
        ref.watch(orderServiceProvider.select((s) => s.orderStatusInfoList));
    final controller =
        ref.watch(orderServiceProvider.select((s) => s.tabController));

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          Container(
            width: double.maxFinite,
            height: 1,
            color: const Color(0xFFEEEEF0),
          ),
          Expanded(
            child: ExtendedTabBarView(
                controller: controller,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusAllList(
                        orderStatus: 0,
                      )),
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusWaitingForOrderList(
                        orderStatus: 1,
                      )),
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusPendingPaymentList(
                        orderStatus: 2,
                      )),
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusCompletedList(
                        orderStatus: 3,
                      )),
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusCancelledList(
                        orderStatus: 4,
                      )),
                  buildExtendedTabBarView(
                      contentWidget: const OrderStatusRefundedList(
                        orderStatus: 5,
                      )),
                ]),
          ),
        ],
      ),
    );
  }
}

final selectedOrderStatusInfoProvider = StateProvider<int?>((ref) {
  return null;
});