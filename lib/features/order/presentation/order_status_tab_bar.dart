import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/order/application/order_service.dart';
import 'package:kpos/features/order/presentation/order_list_test_screen.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class OrderStatusTabBarWidget extends ConsumerStatefulWidget {
  const OrderStatusTabBarWidget({super.key});

  @override
  ConsumerState<OrderStatusTabBarWidget> createState() =>
      _OrderStatusTabBarWidgetState();
}

class _OrderStatusTabBarWidgetState
    extends ConsumerState<OrderStatusTabBarWidget>
    with TickerProviderStateMixin {
  int curIndex = 0;

  @override
  void initState() {
    super.initState();
    _initTabController();
  }

  void _initTabController() {
    final service = ref.read(orderServiceProvider);
    final orderStatusInfoList = ref.read(orderServiceProvider).orderStatusInfoList;
    service.tabController?.dispose();
    service.tabController = TabController(
      length: orderStatusInfoList.length,
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    final orderStatusInfoList = ref.watch(
        orderServiceProvider.select((s) => s.orderStatusInfoList)
    );
    final controller = ref.watch(
        orderServiceProvider.select((s) => s.tabController)
    );
    if (controller == null) {
      return const SizedBox();
    }
    return Container(
      margin: const EdgeInsets.only(left: 16),
      child: ExtendedTabBar(
        controller: controller,
        isScrollable: true,
        labelPadding: const EdgeInsets.only(left: 0, right: 32),
        indicatorColor: context.theme.brandNormalColor,
        indicatorSize: TabBarIndicatorSize.label,
        splashBorderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        indicatorWeight: 3,
        dividerColor: Colors.transparent,
        labelColor: context.theme.brandNormalColor,
        labelStyle: TextStyle(
            color: context.theme.brandNormalColor,
            fontSize: 14,
            fontWeight: FontWeight.w500),
        unselectedLabelColor: Colors.black,
        unselectedLabelStyle: const TextStyle(
            color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500),
        scrollDirection: Axis.horizontal,
        onTap: (index) {
          if(curIndex==index)return;
          curIndex = index;
          final service = ref.read(orderServiceProvider);
          service.orderStatusInfo = orderStatusInfoList[index];
          ref.read(selectedOrderStatusInfoProvider.notifier).state = orderStatusInfoList[index].orderStatus;
        },
        tabs: orderStatusInfoList.map((orderStatusInfo) => ExtendedTab(
          height: 48,
          text: orderStatusInfo.orderStatusName,
        )).toList(),
      ),
    );
  }
}
