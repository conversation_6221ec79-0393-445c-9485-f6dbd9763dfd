import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/kp_async_value_widget.dart';
import '../../../common/components/kp_drop_down_menu.dart';
import '../../../common/components/kp_pagination.dart';
import '../../../common/components/popup/kp_slide_popup.dart';
// import '../../more/domain/KPDatePicker.dart';
import '../../more/presentation/widgets/KPDatePicker.dart';
import '../domain/order_info.dart';
import 'order_controller.dart';
import 'order_list_test_screen.dart';

/// 订单列表管理页面
abstract class BaseOrderStatusList extends ConsumerStatefulWidget {
  final int orderStatus;

  const BaseOrderStatusList({super.key, required this.orderStatus});

  @override
  ConsumerState createState() => _BaseOrderStatusList();
}

class _BaseOrderStatusList extends ConsumerState<BaseOrderStatusList>
    with AutomaticKeepAliveClientMixin {
  /// 是否正在加载数据
  bool _isLoading = false;

  /// 是否有错误
  bool _hasError = false;

  /// 错误信息
  String _errorMessage = '';

  /// 下拉刷新控制器
  final EasyRefreshController _refreshController = EasyRefreshController();

  /// 总记录数
  int _totalRecords = 0;

  /// 当前选中的日期范围显示文本
  String _selectedDateRange = 'mm/dd/yyyy — mm/dd/yyyy';

  /// 当前选中的就餐方式筛选条件
  String _selectedDiningType = '';

  String _selectedDiningTypeId = '';

  /// 当前选中的就餐来源筛选条件
  String _selectedOrderSource = '';

  String _selectedOrderSourceId = '';

  // 分页控制 - 每页显示的记录数量
  /// 默认每页显示10条记录
  int _rowsPerPage = 10;

  /// 分页控制 - 当前页码
  int _currentPage = 1;

  /// 分页控制 - 总页数
  /// 根据数据量和每页显示数量计算得出
  int _totalPages = 13;

  /// 单号/取餐码输入框的控制器
  /// 用于管理单号/取餐码
  final TextEditingController _orderNumberOrPickUpNumberController =
      TextEditingController();

  int _selectedIndex = 0;

  final FocusNode _orderNumberOrPickNumberFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _orderNumberOrPickUpNumberController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  List<Order> _list = [];

  /// 初始化数据
  void _initializeData() {
    // 生成初始数据
    _list = List.generate(
      8,
      (index) => Order.random(id: 'init_record_$index'),
    );
    _totalRecords = 100; // 假设总共有100条记录
    _totalPages = (_totalRecords / _rowsPerPage).ceil();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(KPSpacing.xxl), // 添加内边距使内容不贴近屏幕边缘
          child: Container(
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start, // 左对齐
              children: [
                // 顶部筛选区域 - 包含订单号或者取餐码输入框、日期、就餐类型和订单来源方式筛选
                Visibility(
                    visible: _list.isNotEmpty, child: _buildFilterArea()),
                Expanded(
                  child: _buildDataTable(),
                ),
                // 底部分页控制区域
              ],
            ),
          ),
        ),
      ),
    );
  }


  /// 构建页面顶部的筛选区域
  Widget _buildFilterArea() {
    return Row(children: [
      //订单编号或者取餐码输入框
      Expanded(
          child: SizedBox(
        height: 56,
        child: TextField(
          focusNode: _orderNumberOrPickNumberFocusNode,
          controller: _orderNumberOrPickUpNumberController,
          buildCounter: (
            BuildContext context, {
            required int currentLength,
            required bool isFocused,
            int? maxLength,
          }) =>
              null,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.search),
            border:
                _getOutlineInputBorder(color: KPColors.borderGrayLightDarkest),
            focusedBorder:
                _getOutlineInputBorder(color: KPColors.borderBrandDefault),
            contentPadding: const EdgeInsets.fromLTRB(8, 16, 16, 16),
            hintText: "订单号或者取餐码",
            hintStyle: KPFontStyle.headingXSmall.copyWith(
              color: KPColors.textGraySecondary,
            ),
          ),
        ),
      )),
      const SizedBox(width: 40),
      Padding(
          padding: const EdgeInsets.all(KPSpacing.xl),
          child: Stack(
            children: <Widget>[
              Assets.images.iconFilter
                  .svg(
                width: 24,
                height: 24,
              )
                  .onTap(() {
                KPSlidePopup.showSlidePopup(
                  context: context,
                  slideTransitionFrom: SlideTransitionFrom.right,
                  contentWidget: _buildFilterActionView(),
                );
              }),
              Visibility(visible: _selectedDiningType.isNotEmpty && _selectedOrderSource.isNotEmpty && _selectedDateRange.isNotEmpty,child: Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 10,
                  height: 10,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),),
            ],
          ))
    ]);
  }

  Widget _buildFilterActionView() {
    return Container(
        width: 560,
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxl),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitleView(),
            const SizedBox(height: KPSpacing.xxl),
            // 日期范围选择器部分
            SizedBox(
              width: double.infinity,
              child: _buildDateRangePicker(),
            ),
            const SizedBox(height: KPSpacing.xl),
            SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 使用新的KPTextFieldDropDown组件
                  KPTextFieldDropDown(
                    items: [
                      DropdownItem(id: "all", name: "全部"),
                      DropdownItem(id: "define-in", name: "堂食"),
                      DropdownItem(id: "taking-out", name: "外带"),
                      DropdownItem(id: "packing", name: "自提"),
                      DropdownItem(id: "waimai", name: "外卖"),
                    ],
                    value: DropdownItem(
                      id: _selectedDiningTypeId,
                      name: _selectedDiningType,
                    ),
                    labelText: '就餐类型',
                    hintText: '请选择类型',
                    onChanged: (item) {
                      if (item != null) {
                        setState(() {
                          _selectedDiningType = item.name;
                          _selectedDiningTypeId = item.id;
                        });
                      }
                    },
                    menuMaxHeight: 300,
                    menuWidth: 200,
                    itemTextStyle: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF323843),
                      fontWeight: FontWeight.w500,
                    ),
                    dropdownColor: Colors.grey[50],
                  ),
                ],
              ),
            ),
            const SizedBox(height: KPSpacing.xl),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 使用新的KPTextFieldDropDown组件
                  KPTextFieldDropDown(
                    items: [
                      DropdownItem(id: "all", name: "全部"),
                      DropdownItem(id: "pos", name: "收银POS"),
                      DropdownItem(id: "qr", name: "QR点餐"),
                    ],
                    labelText: '订单来源',
                    hintText: '请选择订单来源',
                    onChanged: (item) {
                      if (item != null) {
                        setState(() {
                          _selectedOrderSource = item.name;
                          _selectedOrderSourceId = item.id;
                        });
                      }
                    },
                    value: DropdownItem(
                      id: _selectedOrderSourceId,
                      name: _selectedOrderSource,
                    ),
                    menuMaxHeight: 300,
                    menuWidth: 200,
                    itemTextStyle: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF323843),
                      fontWeight: FontWeight.w500,
                    ),
                    dropdownColor: Colors.grey[50],
                  ),
                ],
              ),
            ),
            const SizedBox(height: KPSpacing.xl),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SizedBox(
                  width: 85,
                  height: 48, // 固定高度以保持一致的外观
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _selectedIndex = -1;
                        _selectedOrderSource = "";
                        _selectedDiningType = "";
                        _selectedDateRange = "";
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KPColors.borderGrayLightLighter, // 按钮背景色
                      foregroundColor: KPColors.textGrayPrimary, // 按钮文字颜色
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4), // 圆角按钮
                      ),
                    ),
                    child: Text(
                      "重置",
                      style: KPFontStyle.headingSmall.copyWith(
                        fontWeight: FontWeight.w700,
                        color: KPColors.textGrayPrimary,
                      ),
                    ), // 按钮文字
                  ),
                ),
                const SizedBox(width: KPSpacing.xl),
                SizedBox(
                  width: 85,
                  height: 48, // 固定高度以保持一致的外观
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _selectedIndex = -1;
                        Navigator.of(context).pop(); // 点击关闭按钮时关闭当前界面
                        List<Order> newList = _list;
                        if (_selectedDiningType != "全部") {
                          newList = newList
                              .where((order) =>
                                  order.diningType == _selectedDiningType)
                              .toList();
                        }
                        if (_selectedOrderSource != "全部") {
                          newList = newList
                              .where((order) =>
                                  order.orderSource == order.orderSource)
                              .toList();
                        }
                        _list = newList;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black87, // 按钮背景色
                      foregroundColor: Colors.white, // 按钮文字颜色
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4), // 圆角按钮
                      ),
                    ),
                    child: Text(
                      "确定",
                      style: KPFontStyle.headingSmall.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                    ), // 按钮文字
                  ),
                ),
              ],
            )
          ],
        ));
  }

  Widget _buildTitleView() {
    return Padding(
      padding: const EdgeInsets.only(top: 4, left: 24, bottom: 4, right: 12),
      // 设置内边距
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子组件分散对齐（两端对齐）
        children: [
          Expanded(
              child: Text(
            "Filter",
            style: KPFontStyle.headingLarge.copyWith(
              fontWeight: FontWeight.w700,
              color: KPColors.textGrayPrimary,
            ),

            maxLines: 1,

            overflow: TextOverflow.ellipsis, // 文本溢出显示省略号
          )),
          GestureDetector(
              child: Assets.images.iconClose.svg(width: 24, height: 24),
              // 关闭图标，使用SVG格式
              onTap: () {
                Navigator.of(context).pop(); // 点击关闭按钮时关闭当前界面
              })
        ],
      ),
    );
  }

  OutlineInputBorder _getOutlineInputBorder(
      {required Color color, double borderWith = 1, double borderRadius = 4}) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: color, width: borderWith),
      borderRadius: BorderRadius.circular(borderRadius),
    );
  }

  Widget _buildDateRangePicker() {
    return KPDateRangePicker(
        labelText: '日期范围',
        hintText: '请选择日期范围',
        // 选中边框的颜色
        selectedBorderColor: context.theme.brandColor7,
        // 未选中边框的颜色
        unselectedBorderColor: const Color(0xFFDCDDE1),
        suffixIcon: Assets.images.iconDatePicker.svg(width: 24, height: 24),
        onConfirm: (startDate, endDate) {
          print("1日期:${startDate} - ${endDate}");

          try {
            setState(() {
              print("2日期:${startDate} - ${endDate}");

              if (startDate != null && endDate != null) {
                final formatter = DateFormat('MM/dd/yyyy');
                _selectedDateRange =
                    '${formatter.format(startDate)} — ${formatter.format(endDate)}';
                print("日期3:${_selectedDateRange}");
              } else {
                print("警告：收到空的日期值");
                _selectedDateRange = '未选择日期范围';
              }
            });
          } catch (e) {
            print("处理日期时出错: $e");
          }
        });
  }

  /// 构建订单列表记录数据表格
  Widget _buildDataTable() {
    // return KPAsyncValueWidget(
    //   asyncValueProvider: orderControllerProvider,
    //     dataBuilder: (context, ref, data) {
    //       print("mark----_buildDataTable-2222222222222222222");
    //       final list = data;
    //       print("mark-----list = ${list.length}");
    //       return Text("data");
    //
    // });
    // 如果正在加载中显示加载指示器
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(context.theme.brandColor7),
            ),
            const SizedBox(height: 16),
            const Text(
              '正在加载数据...',
              style: TextStyle(color: Colors.black54, fontSize: 14),
            ),
          ],
        ),
      );
    }

    // 如果有错误显示错误信息
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.black87, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _simulatePullToRefresh,
              style: ElevatedButton.styleFrom(
                backgroundColor: context.theme.brandColor7,
                foregroundColor: Colors.white,
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 如果数据为空显示空状态
    if (_list.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.iconEmptyData.svg(width: 32, height: 32),
            const SizedBox(height: KPSpacing.xl),
            Text(
              '暂无数据',
              style: KPFontStyle.bodyLarge.copyWith(
                fontWeight: FontWeight.w400,
                color: KPColors.textGrayPrimary,
              ),
            ),
          ],
        ),
      );
    }

    // 使用EasyRefresh包装数据表格
    return EasyRefresh(
      controller: _refreshController,
      onRefresh: () async {
        // 实现下拉刷新逐辑
        await _simulatePullToRefresh();
        _refreshController.finishRefresh();
        _refreshController.resetFooter(); // 重置底部加载状态
        return IndicatorResult.success;
      },
      onLoad: () async {
        // 如果已经是最后一页，则不加载更多
        if (_currentPage >= _totalPages) {
          _refreshController.finishLoad(IndicatorResult.noMore);
          return IndicatorResult.noMore;
        }

        // 实现上拉加载更多逐辑
        await _simulateLoadMore();
        final result = _currentPage >= _totalPages
            ? IndicatorResult.noMore
            : IndicatorResult.success;
        _refreshController.finishLoad(result, true);
        return result;
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 表格数据行
            ListView.separated(
              separatorBuilder: (context, index) => const SizedBox(height: 16),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _list.length,
              itemBuilder: (context, index) {
                final itemBorderColor = _selectedIndex == index
                    ? KPColors.borderBrandDefault
                    : KPColors.borderGrayLightBase;
                final record = _list[index];

                // 格式化日期时间
                final formattedDate =
                    DateFormat('yyyy-MM-dd HH:mm').format(record.orderTime);

                return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIndex = index;
                      });
                      ref.read(selectedOrderStatusInfoProvider.notifier).state =
                          widget.orderStatus;
                    },
                    child: Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        color: KPColors.fillGrayLightLightest,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: itemBorderColor),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Text(
                                    record.orderNumber,
                                    style: KPFontStyle.headingSmall.copyWith(
                                        fontWeight: FontWeight.w700,
                                        color: KPColors.textGrayPrimary),
                                  ),
                                ),
                                paymentStatusWidget(record.orderStatus)
                              ]),
                          Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(
                                          height: 12,
                                        ),
                                        Text(record.orderSource,
                                            style: KPFontStyle.bodySmall
                                                .copyWith(
                                                    fontWeight: FontWeight.w400,
                                                    color: KPColors
                                                        .textGraySecondary)),
                                        const SizedBox(
                                          height: 4,
                                        ),
                                        Text(formattedDate,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            textAlign: TextAlign.left),
                                      ]),
                                ),
                                Expanded(
                                    flex: 1,
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'S${record.orderAmount.toStringAsFixed(2)}',
                                            style: KPFontStyle.headingSmall
                                                .copyWith(
                                                    fontWeight: FontWeight.w700,
                                                    color: KPColors
                                                        .textGrayPrimary),
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Assets.images.iconAlertCircle
                                                  .svg(width: 16, height: 16),
                                              const SizedBox(
                                                width: 4,
                                              ),
                                              Text(
                                                "${record.diningType} - ${record.pickUpNumber}",
                                                style: KPFontStyle.bodySmall
                                                    .copyWith(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: KPColors
                                                            .textGraySecondary),
                                              ),
                                            ],
                                          )
                                        ])),
                              ]),
                        ],
                      ),
                    ));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget paymentStatusWidget(String paymentStatus) {
    late Color textColor;
    late Color backgroundColor;
    switch (paymentStatus) {
      case "待下单":
        textColor = KPColors.textYellowDefault;
        backgroundColor = KPColors.fillYellowLighter;
        break;
      case "待接单":
        textColor = KPColors.textBlueDefault;
        backgroundColor = KPColors.fillBlueLighter;
        break;
      case "待支付":
        textColor = KPColors.textBrandDefault;
        backgroundColor = KPColors.fillBrandLightest;
        break;
      case "已完成":
        textColor = KPColors.textGreenDefault;
        backgroundColor = KPColors.fillGreenLighter;
        break;
      case "已取消":
        textColor = KPColors.textGrayTertiary;
        backgroundColor = KPColors.fillGrayLightLighter;
        break;
      case "已退单":
        textColor = KPColors.textRedDefault;
        backgroundColor = KPColors.fillRedLightest;
        break;
      case "待取餐":
        textColor = KPColors.chartCategorical7Default;
        backgroundColor = KPColors.fillTealLighter;
        break;
      default:
        textColor = KPColors.textYellowDefault;
        backgroundColor = KPColors.fillYellowLighter;
        break;
    }
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 8),
      child: Text(paymentStatus,
          style: KPFontStyle.bodySmall
              .copyWith(fontWeight: FontWeight.w400, color: textColor)),
    );
  }

  /// 模拟下拉刷新操作
  Future<void> _simulatePullToRefresh() async {
    setState(() {
      _selectedIndex = 0;
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 生成新的随机记录
    final newLists = List.generate(
      _rowsPerPage,
      (index) => Order.random(
          id: 'record_refresh_${DateTime.now().millisecondsSinceEpoch}_$index'),
    );

    setState(() {
      // 替换现有记录
      _list = newLists;
      _isLoading = false;
      _currentPage = 1;

      // 显示成功消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('刷新成功，获取到${newLists.length}条新记录'),
          backgroundColor: Colors.blue,
        ),
      );
    });
  }

  /// 模拟上拉加载更多操作
  Future<void> _simulateLoadMore() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 生成新的随机记录
    final additionalRecords = List.generate(
      _rowsPerPage,
      (index) => Order.random(
          id: 'record_more_${DateTime.now().millisecondsSinceEpoch}_$index'),
    );

    setState(() {
      // 添加新记录到现有列表末尾
      _list.addAll(additionalRecords);
      _isLoading = false;
      _currentPage++;

      // 显示成功消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载成功，获取到${additionalRecords.length}条新记录'),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  /// 模拟网络错误
  void _simulateNetworkError() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    // 模拟网络延迟
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = '网络请求失败，请检查网络连接后重试';

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('网络请求失败'),
            backgroundColor: Colors.red,
          ),
        );
      });
    });
  }

  /// 模拟空数据
  void _simulateEmptyData() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    // 模拟网络延迟
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _list.clear();
        _isLoading = false;
        _totalRecords = 0;
        _totalPages = 1;
        _currentPage = 1;

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('没有找到符合条件的记录'),
            backgroundColor: Colors.blue,
          ),
        );
      });
    });
  }
}
