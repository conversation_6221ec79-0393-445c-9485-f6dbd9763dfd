import 'package:kpos/features/order/data/order_intranet_repository.dart';
import 'package:kpos/features/order/domain/order_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'order_controller.g.dart';

@riverpod
class OrderController extends _$OrderController {
  @override
  Future<List<OrderItem>> build() async {
    return fetch();
  }

  Future<List<OrderItem>> fetch()  {
    return ref.read(orderIntranetRepositoryProvider).getOrderList();
  }
}
