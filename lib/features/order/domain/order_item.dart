import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_item.freezed.dart';

part 'order_item.g.dart';

@freezed
class OrderItem with _$OrderItem {
  const factory OrderItem({
    required int id,
    required String diningType,
    required String orderTime,
    required String orderSource,
    required int orderStatus,
    required double amount,
    required String pickUpNumber,
  }) = _OrderItem;

  factory OrderItem.fromJson(Map<String, dynamic> json) =>
      _$OrderItemFromJson(json);
}
