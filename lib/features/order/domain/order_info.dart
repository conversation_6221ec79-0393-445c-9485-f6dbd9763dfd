import 'dart:math';

class OrderStatusInfo {
  String orderStatusName;
  int orderStatus;

  OrderStatusInfo({required this.orderStatusName, required this.orderStatus});
}

class Order {
  final String orderNumber;
  final String orderSource;
  final String diningType;
  final String orderStatus;
  final DateTime orderTime;
  final double orderAmount;
  final String pickUpNumber;

  const Order({
    required this.orderNumber,
    required this.orderSource,
    required this.diningType,
    required this.orderStatus,
    required this.orderTime,
    required this.orderAmount,
    required this.pickUpNumber,
  });

  static Order random({required String id}) {
    final random = Random(id.hashCode);

    // 订单来源
    final orderSources = ["收银POS", "QR点餐"];
    final orderSource = orderSources[random.nextInt(orderSources.length)];

    // 就餐方式
    final diningTypes = ["堂食", "外带", "自提","外卖"];
    final diningType = diningTypes[random.nextInt(diningTypes.length)];

    // 订单状态
    final orderStatuses = ["待下单","待接单","待支付" ,"已完成", "已取消","已退单", "待取餐"];

    // 生成随机金额（10到1000之间，保留两位小数）
    final amount = (random.nextInt(99000) + 1000) / 100;

    // 生成随机订单号（8位数字）
    final orderNumber = random.nextInt(99999999).toString().padLeft(8, '0');

    final orderNumberOrPickUpNumber =
    random.nextInt(999).toString().padLeft(3, '0');

    // 生成随机日期（过去30天内）
    final now = DateTime.now();
    final daysAgo = random.nextInt(30);
    final dateTime = now.subtract(Duration(
      days: daysAgo,
      hours: random.nextInt(24),
      minutes: random.nextInt(60),
    ));

    return Order(
        orderNumber: orderNumber,
        orderSource: orderSource,
        diningType: diningType,
        orderStatus: orderStatuses[random.nextInt(orderStatuses.length)],
        orderTime: dateTime,
        orderAmount: amount,
        pickUpNumber: orderNumberOrPickUpNumber);
  }
}
