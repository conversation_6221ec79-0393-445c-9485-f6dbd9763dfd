abstract class OrderLocalRepositoryInterface {
  Future<List<Map<String, dynamic>>> getDiningStyles();

  /// 删除菜品
  Future<void> deleteOrderItem(int orderItemId);

  /// 退菜
  Future<void> returnOrderItem(int status,int orderItemId);

  /// 催菜
  Future<void> urgeOrderItem(int status,int orderItemId);

  /// 划菜
  Future<void> servedOrderItem(int status,int orderItemId);

  /// 取消订单
  Future<void> cancelOrder(int orderId,String reason);

  /// 提交订单
  Future<void> submitOrder(int orderId);
}