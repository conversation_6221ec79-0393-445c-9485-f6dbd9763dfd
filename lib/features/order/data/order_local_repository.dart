import 'package:drift/drift.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_exception.dart';
import 'package:kpos/common/utils/snowflake_id_generator.dart';
import 'package:kpos/features/order/data/order_local_repository_interface.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';

class OrderLocalRepository implements OrderLocalRepositoryInterface {

  final AppDatabase db;
  OrderLocalRepository(this.db);
  @override
  Future<List<Map<String, dynamic>>> getDiningStyles() async {
    try {
      final list = await db.customSelect(
          'SELECT * FROM dining_style;'
      ).get();

      final result = list.map((row) => {
        'type': row.read<int>('sale_type'),
        'typeName': row.read<String>('sale_type_name'),
      }).toList();

      return result;
    } catch (e, st) {
      print('❌ getDiningStyles 查询失败: $e\n$st');
      return []; // 或者根据业务返回 null / 抛出自定义异常
    }
  }

  @override
  Future<void> deleteOrderItem(int orderItemId) async {
    final orderItem = await db.customSelect(
      '''
    SELECT *
    FROM order_item
    WHERE order_item_id = ? AND deleted = 0
    ''',
      variables: [Variable.withInt(orderItemId)],
    ).getSingleOrNull();

    if (orderItem == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }
    // 执行更新
    await (db.update(db.orderItem)
      ..where((tbl) => tbl.orderItemId.equals(orderItemId)))
        .write(
      const OrderItemCompanion(
        deleted: Value(1),
      ),
    );
  }

  @override
  Future<void> returnOrderItem(int status,int orderItemId) async {
    final orderItem = await db.customSelect(
      '''
    SELECT *
    FROM order_item
    WHERE order_item_id = ? AND deleted = 0
    ''',
      variables: [Variable.withInt(orderItemId)],
    ).getSingleOrNull();

    if (orderItem == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }
    // 执行更新
    await (db.update(db.orderItem)
      ..where((tbl) => tbl.orderItemId.equals(orderItemId)))
        .write(
      OrderItemCompanion(
        returnFlag: status == 1 ? const Value(1) : const Value(0),
      ),
    );
  }

  @override
  Future<void> urgeOrderItem(int status,int orderItemId) async {
    final orderItem = await db.customSelect(
      '''
    SELECT *
    FROM order_item
    WHERE order_item_id = ? AND deleted = 0
    ''',
      variables: [Variable.withInt(orderItemId)],
    ).getSingleOrNull();

    if (orderItem == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }
    // 执行更新
    await (db.update(db.orderItem)
      ..where((tbl) => tbl.orderItemId.equals(orderItemId)))
        .write(
      OrderItemCompanion(
        kpUrgeDishFlag: status == 1 ? const Value(1) : const Value(0),
        kpUrgeDishTime: status == 1 ? Value(DateTime.now().millisecondsSinceEpoch) : const Value(null),
      ),
    );
  }

  @override
  Future<void> servedOrderItem(int status,int orderItemId) async {
    await db.transaction(() async {
      final orderItem = await db.customSelect(
        '''
      SELECT *
      FROM order_item
      WHERE order_item_id = ? AND deleted = 0
      ''',
        variables: [Variable.withInt(orderItemId)],
      ).getSingleOrNull();

      if (orderItem == null) {
        throw ApiIntranetException(MessageKey.invalidParam);
      }

      final type = orderItem.read<int>('commodity_type');
      final now = DateTime.now().millisecondsSinceEpoch;

      // 更新主项
      await (db.update(db.orderItem)
        ..where((tbl) => tbl.orderItemId.equals(orderItemId)))
          .write(
        OrderItemCompanion(
          kpServedDishFlag: status == 1 ? const Value(1) : const Value(0),
          kpServedDishTime: status == 1 ? Value(now) : const Value(null),
        ),
      );

      // 如果是套餐主项，则更新其子项
      if (type == 3) {
        await (db.update(db.orderItem)
          ..where((tbl) => tbl.parentOrderItemId.equals(orderItemId)))
            .write(
          OrderItemCompanion(
            kpServedDishFlag: status == 1 ?  const Value(1) : const Value(0),
            kpServedDishTime: status == 1 ? Value(now) : const Value(null),
          ),
        );
      }
    });
  }

  @override
  Future<void> cancelOrder(int orderId,String reason) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ? AND deleted = 0
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (order == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        state: const Value(6),
        cancelReason: Value(reason),
        cancelTime: Value(DateTime.now().millisecondsSinceEpoch)
      ),
    );
  }

  @override
  Future<void> submitOrder(int orderId) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ? AND deleted = 0
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (order == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        /// TODO还需要增加更新时间和更新人
          state: const Value(3),
      ),
    );
  }

}