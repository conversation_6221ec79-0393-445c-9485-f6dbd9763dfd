import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/order/data/order_endpoint.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import '../../../common/services/networking/remote_service/api_interface.dart';
import '../domain/order_item.dart';

part 'order_intranet_repository.g.dart';

class OrderIntranetRepository {
  final ApiInterface _apiService;

  OrderIntranetRepository({required ApiInterface apiService})
      : _apiService = apiService;

  Future<List<OrderItem>> getOrderList() async {
    // return await _apiService.getListData(
    //     endpoint: OrderEndpoint.orders, converter: OrderItem.fromJson);
    return [];
  }
  
  /// 划菜
  Future<void> servedOrderItem({
    required int orderItemId,
    required int status, // 1=划菜, 2=取消划菜
  }) async {
    await _apiService.postData(
      endpoint: OrderEndpoint.servedOrderItem,
      data: {
        "orderItemId": orderItemId,
        "status": status,
      }
    );
  }
  
  /// 催菜
  Future<void> urgeOrderItem({
    required int orderItemId,
    required int status, // 1=催菜, 2=取消催菜
  }) async {
    await _apiService.postData(
      endpoint: OrderEndpoint.urgeOrderItem,
      data: {
        "orderItemId": orderItemId,
        "status": status,
      }
    );
  }
  
  /// 退菜
  Future<void> returnOrderItem({
    required int orderItemId,
    required int status, // 1=退菜, 2=取消退菜
    String? reason, // 退菜原因
  }) async {
    final Map<String, dynamic> data = {
      "orderItemId": orderItemId,
      "status": status,
    };

    if(reason!=null){
      data["reason"] = reason;
    }
    
    await _apiService.postData(
      endpoint: OrderEndpoint.returnOrderItem,
      data: data
    );
  }
}

@riverpod
OrderIntranetRepository orderIntranetRepository(Ref ref) {
  return OrderIntranetRepository(
      apiService: ref.watch(kposApiIntranetServiceProvider));
}
