import '../../../common/services/networking/remote_service/api_endpoint.dart';

enum OrderEndpoint implements ApiEndpoint {

  //就餐方式
  diningStyle("/dining/style"),

  //删除菜品
  deleteOrderItem("/delete/order/item"),

  //退菜
  returnOrderItem("/return/order/item"),

  // 催菜
  urgeOrderItem("/urge/order/item"),

  // 划菜
  servedOrderItem("/served/order/item"),

  cancelOrder("/cancel/order"),

  // 提交订单
  submitOrder("/submit/order");

  const OrderEndpoint(this.path);

  @override
  final String path;

}