
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../data/product_intranet_repository.dart';
import '../../presentation/product_controller.dart';
import '../../presentation/product_pagination_controller.dart';
import '../domain/sold_out_detail_product.dart';


part 'sold_out_products_controller.g.dart';

/// 关联商品控制器
/// 用于获取设置售罄时受影响的商品列表
@riverpod
class SoldOutProductsController extends _$SoldOutProductsController {
  @override
  Future<List<SoldOutDetailProduct>> build(int productId) async {
    final repository = ref.read(productIntranetRepositoryProvider);
    return await repository.getSoldOutProducts(productId);
  }
  
  /// 刷新关联商品数据
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}


/// 售罄状态管理控制器
/// 用于处理商品售罄状态的设置操作
@riverpod
class SoldOutStatusController extends _$SoldOutStatusController {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);
  
  /// 设置商品售罄状态
  /// [productId] 商品ID
  /// [isSoldOut] true=设置为售罄，false=恢复正常
  Future<void> setSoldOutStatus({
    required int productId,
    required bool isSoldOut,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final repository = ref.read(productIntranetRepositoryProvider);
      await repository.setProductSoldOut(
        type: isSoldOut ? 1 : 0,
        productId: productId,
      );
      
      state = const AsyncValue.data(null);
      
      // 成功后刷新相关数据
      ref.invalidate(productControllerProvider);
      ref.read(productPaginationProvider.notifier).resetToFirstPage();
      
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
  
  /// 重置状态
  void reset() {
    state = const AsyncValue.data(null);
  }
}