import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/domain/product_item.dart';

import '../../../../common/components/index.dart';
import '../../presentation/product_controller.dart';
import '../../presentation/product_pagination_controller.dart';
import '../domain/sold_out_detail_product.dart';
import 'sold_out_products_controller.dart';

/// 商品售罄确认视图
/// 
/// 支持两种模式：
/// 1. 有关联商品模式：适用于单品，显示受影响的关联商品列表
/// 2. 无关联商品模式：适用于套餐，显示中间图标
class SoldOutConfirmView extends ConsumerStatefulWidget {
  /// 当前操作的商品
  final ProductItem product;
  
  /// 受影响的关联商品列表（可选，为null或空表示无关联商品模式）
  final List<SoldOutDetailProduct>? affectedProducts;
  
  /// 确认回调
  final VoidCallback? onConfirm;
  
  /// 取消回调
  final VoidCallback? onCancel;

  /// 强制显示模式（可选）：
  /// - null: 根据affectedProducts自动判断
  /// - true: 强制显示有关联商品模式
  /// - false: 强制显示无关联商品模式
  final bool? forceShowMode;

  /// 是否为恢复可用模式（新增）
  final bool isMarkAvailableMode;

  const SoldOutConfirmView({
    super.key,
    required this.product,
    this.affectedProducts,
    this.onConfirm,
    this.onCancel,
    this.forceShowMode,
    this.isMarkAvailableMode = false,
  });

  @override
  ConsumerState createState() => _ProductSoldOutConfirmViewState();
}

/// `SoldOutConfirmView` 的状态管理类
class _ProductSoldOutConfirmViewState extends ConsumerState<SoldOutConfirmView> {
  
  ProductItem get product => widget.product;
  List<SoldOutDetailProduct>? get affectedProducts => widget.affectedProducts;

  /// 显示成功消息
  void _showSuccessMessage() {
    // 使用项目的Toast组件显示成功消息
    // 这里可以根据项目实际的Toast组件进行调整
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.isMarkAvailableMode ? '商品已恢复正常' : '商品已设置为售罄'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('操作失败: $message'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  /// 判断是否有关联商品
  bool get hasAffectedProducts {
    // 如果设置了强制模式，使用强制模式
    if (widget.forceShowMode != null) {
      return widget.forceShowMode!;
    }
    // 否则根据数据判断
    return affectedProducts != null && affectedProducts!.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {

    // 在build方法中添加监听
    ref.listen<AsyncValue<void>>(
      soldOutStatusControllerProvider,
      (previous, next) {
        next.when(
          data: (_) {
            // 成功时显示提示并关闭弹窗
            if (mounted) {
              // _showSuccessMessage();
              KPSlidePopup.dismissFromTarget();
              widget.onConfirm?.call();
            }
          },
          error: (error, stackTrace) {
            // 错误时显示错误提示
            if (mounted) {
              _showErrorMessage(error.toString());
            }
          },
          loading: () {
            // 加载状态已在按钮中处理
          },
        );
      },
    );
    
    return Container(
      width: 560,
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          _buildTitleBar(),
          
          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 提示文本
                    _buildHeaderContentText(),
                    
                    // 根据模式显示不同内容
                    if (hasAffectedProducts) ...[
                      const SizedBox(height: KPSpacing.l),
                      _buildAffectedProductsSection(),
                    ],
                    
                    const SizedBox(height: 24),
                    
                    // 底部说明文本
                    _buildFooterContentText(),
                    //无商品的图标
                     if (!hasAffectedProducts) ...[
                      const SizedBox(height: 120),
                      _buildSoldOutIcon(),
                    ]
                  ],
                ),
              ),
            ),
          ),
          
          // 底部按钮
          Container(color: KPColors.borderGrayLightBase, height: 1,),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            child: _buildBottomButtons(),
          ),
        ],
      ),
    );
  }

  /// 刷新产品列表
void _refreshProductList() {
  try {
    // 1. 刷新当前分类的产品列表数据
    ref.invalidate(productControllerProvider);
    
    // 2. 回到第一页
    ref.read(productPaginationProvider.notifier).resetToFirstPage();
    
    print('✅ 产品列表已刷新并回到第一页');
    
  } catch (e) {
    print('❌ 刷新产品列表失败: $e');
  }
}
  /// 构建标题栏
  Widget _buildTitleBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl, vertical: KPSpacing.s),
      child: Row(
        children: [
          // 标题文本
          Expanded(
            child: Text(
              context.locale.confirmSoldOut,
              style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          
          // 关闭按钮
          GestureDetector(
            onTap: _handleCancel,
            child: const Icon(
              Icons.close,
              size: 24,
              color: KPColors.textGrayPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建顶部描述文本
  Widget _buildHeaderContentText() {
    return Container(
      color: Colors.white,
      child: Text(
              hasAffectedProducts 
                  ? 'After confirming, This dish will be unavailable. Set meals requiring it will also be unavailable.'
                  : 'After confirming, This dish will be unavailable. Set meals requiring it will also be unavailable.',
              style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary,
            ),
          ),
    );
  }

  /// 构建底部描述文本
  Widget _buildFooterContentText() {
    return Text(
      'The sold-out status will automatically return to the available status on the next business day.',
       style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary,)
    );
  }

  /// 构建售罄图标（无关联商品模式）
  Widget _buildSoldOutIcon() {
    return Center(
      child: Assets.images.iconSoldOnDetail.svg(width: 120, height: 120,),
    );
  }

  /// 构建受影响商品区域(商品列表)
  Widget _buildAffectedProductsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Affected required set meals:',
          style: KPFontStyle.headingXSmall.copyWith(color: KPColors.textGrayPrimary,),
        ),
        const SizedBox(height: KPSpacing.l),
        ..._buildAffectedProductsList(),
      ],
    );
  }

  /// 构建受影响的商品列表
  List<Widget> _buildAffectedProductsList() {
    if (affectedProducts == null || affectedProducts!.isEmpty) {
      return [];
    }

    return affectedProducts!.map((product) {
      return _buildAffectedProductItem(product);
    }).toList();
  }

  /// 构建单个受影响商品项
  Widget _buildAffectedProductItem(SoldOutDetailProduct product) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 商品图片
          _buildProductImage(product),
          const SizedBox(width: 12),
          
          // 商品信息
          Expanded(
            child: _buildProductInfo(product),
          ),
        ],
      ),
    );
  }

  /// 构建商品图片
  Widget _buildProductImage(SoldOutDetailProduct product) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 68,
        height: 68,
        child: product.commodityImagePath.isNotEmpty
            ? CachedNetworkImage(
                    imageUrl: product.commodityImagePath,  // 修改：使用 commodityImagePath
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) => Assets.images.iconTest6
                        .image(
                            width: double.infinity,
                            height: double.infinity,
                            fit: BoxFit.cover),
                  )
            : _buildPlaceholderImage(),
      ),
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage() {
    return Container(
      width: 48,
      height: 48,
      color: const Color(0xFFF2F2F7),
      // child: Center(
      //   child: Assets.images.iconTest6.image(
      //     width: 24,
      //     height: 24,
      //   ),
      // ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo(SoldOutDetailProduct product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              product.commodityName,
              style: KPFontStyle.headingSmall.copyWith(
                color: KPColors.textGrayPrimary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis, //溢出: 省略号模式：在末尾显示"..."
            ),
            const SizedBox(height: KPSpacing.s),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: const Color(0xFFFFF7E6),
              ),
              child: Text(
                context.locale.combo,
                style: KPFontStyle.bodySmall.copyWith(
                  color: KPColors.textBrandDefault,
                ),
              ),
            ),
          ],
        ),
        _buildProductPrice(product),
      ],
    );
  }

  /// 构建商品价格
  Widget _buildProductPrice(SoldOutDetailProduct product) {
    return Text(
      '${product.currencyUnit}${product.price.toStringAsFixed(2)}',
      style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary,),
    );
  }

  

  /// 构建底部按钮
  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 取消按钮
        Container(
          child: _buildCustomButton(
            text: context.locale.cancel,
            backgroundColor: KPColors.fillGrayLightLighter,
            textColor: KPColors.textGrayPrimary,
            onTap: _handleCancel,
          ),
        ),
        const SizedBox(width: 16),
        
        // 确认按钮
        // Container(
        //   child: _buildCustomButton(
        //     text: context.locale.confirm,
        //     backgroundColor: KPColors.fillRedNormal,
        //     textColor: KPColors.textGrayInverse,
        //     onTap: _handleConfirm,
        //   ),
        // ),
      // 确认按钮 - 使用Consumer监听状态
      Consumer(
        builder: (context, ref, child) {
          final soldOutState = ref.watch(soldOutStatusControllerProvider);
          
          return Container(
            child: _buildCustomButton(
              text: soldOutState.isLoading 
                  ? '处理中...' 
                  : context.locale.confirm,
              backgroundColor: soldOutState.isLoading 
                  ? KPColors.borderBrandDefault 
                  : KPColors.fillRedNormal,
              textColor: KPColors.textGrayInverse,
              onTap: soldOutState.isLoading ? null : _handleConfirm,
            ),
          );
        },
      ),
      ],
    );
  }

  /// 构建自定义按钮
  Widget _buildCustomButton({
  required String text,
  required Color backgroundColor,
  required Color textColor,
  required VoidCallback? onTap, // 改为可空类型
}) {
  return GestureDetector(
    onTap: onTap, // 当onTap为null时，按钮将被禁用
    child: Container(
      height: 48,
      width: 104,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          text,
          style: KPFontStyle.headingSmall.copyWith(color: textColor),
        ),
      ),
    ),
  );
}

  /// 处理取消操作
  void _handleCancel() {
    if (widget.onCancel != null) {
      widget.onCancel!();
    }
    KPSlidePopup.dismissFromTarget();
  }

  /// 处理确认操作
  void _handleConfirm() {
    // if (widget.onConfirm != null) {
    //   widget.onConfirm!();
    // }
    // KPSlidePopup.dismissFromTarget();

    final controller = ref.read(soldOutStatusControllerProvider.notifier);
    controller.setSoldOutStatus(
    productId: product.commodityId,
    isSoldOut: !widget.isMarkAvailableMode, // 根据模式决定是售罄还是恢复
  );
  
  }
}