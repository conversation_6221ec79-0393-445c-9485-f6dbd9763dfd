import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/domain/product_item.dart';

import '../../../../common/components/index.dart';
import '../domain/sold_out_detail_product.dart';
import 'sold_out_products_controller.dart';

/// 取消售罄确认视图
class SoldOutCancelConfirmView extends ConsumerStatefulWidget {
  /// 当前操作的商品
  final ProductItem product;
  
  /// 受影响的关联商品列表
  final List<SoldOutDetailProduct>? affectedProducts;
  
  /// 确认回调
  final VoidCallback? onConfirm;
  
  /// 取消回调
  final VoidCallback? onCancel;

  /// 是否需要把onCancel一次性取消所有的弹窗,比如是👆🏻的情况,连续打开2次
  final bool isCancelAllPopup;

  /// 刷新关联商品数据的回调
  final VoidCallback? onRefreshAffectedProducts;

  /// 是否禁用自动请求关联商品数据（默认为false，即默认会自动请求）
  final bool disableAutoFetch;

  /// 单独为一行商品显示二次取消售罄的view,显示对话框回调
  final Function(ProductItem item, List<SoldOutDetailProduct> affectedProducts, {bool isCancelAllPopup, bool disableAutoFetch})? onShowConfirmDialog;

  const SoldOutCancelConfirmView({
    super.key,
    required this.product,
    this.affectedProducts,
    this.onConfirm,
    this.onCancel,
    this.onShowConfirmDialog,
    this.isCancelAllPopup = false,
    this.onRefreshAffectedProducts,
    this.disableAutoFetch = false,
  });

  @override
  ConsumerState createState() => _SoldOutCancelConfirmViewState();
}

class _SoldOutCancelConfirmViewState extends ConsumerState<SoldOutCancelConfirmView> {
  
  ProductItem get product => widget.product;
  List<SoldOutDetailProduct>? get affectedProducts => widget.affectedProducts;

  @override
  Widget build(BuildContext context) {
   // 根据 disableAutoFetch 属性决定是否监听数据变化
  if (widget.disableAutoFetch) {
    // 禁用自动请求，直接使用传入的数据
    return _buildContent(widget.affectedProducts);
  } else {
    // 启用自动请求，监听关联商品数据的变化
    final affectedProductsAsync = ref.watch(
      soldOutProductsControllerProvider(widget.product.commodityId)
    );

    return affectedProductsAsync.when(
      loading: () => _buildContent(widget.affectedProducts), // 加载中显示原数据
      data: (newAffectedProducts) => _buildContent(newAffectedProducts), // 使用新数据
      error: (error, stack) => _buildContent(widget.affectedProducts), // 出错时显示原数据
    );
  }
  }
 
Widget _buildContent(List<SoldOutDetailProduct>? affectedProducts) {
  return  Container(
      width: 560,
      color: Colors.white,
      // padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(color: KPColors.borderGrayLightBase, height: 1),
          // 标题栏
          _buildTitleBar(),
          
          // 内容区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // const SizedBox(height: 16),
                
                
                
                const SizedBox(height: 24),
                
                // 确认文本
                // _buildConfirmHintView(),
                // 内容区域
        Expanded(
          child: affectedProducts != null && affectedProducts.isNotEmpty 
              ? _buildProductsListView() // 有关联商品：显示商品列表
              : _buildNoProductsView(),   // 无关联商品：显示当前UI
        ),
              ],
            ),
          ),
          
          // 底部按钮
          Container(color: KPColors.borderGrayLightBase, height: 1),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: _buildBottomButtons(),
          ),
        ],
      ),
    );
  
}

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Confirm cancel sold-out status',
              style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          GestureDetector(
            onTap: _handleCancel,
            child: const Icon(
              Icons.close,
              size: 24,
              color: KPColors.textGrayPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息提示卡片
  Widget _buildInfoCard() {
    return Container(
      height: 48,
      margin: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 13),
      decoration: BoxDecoration(
        color: KPColors.fillBlueLightest, 
        borderRadius: BorderRadius.circular(6),
        // border: Border.all(color: const Color(0xFF1890FF), width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 信息图标
          Container(
            child: Assets.images.iconFoCircleOutlined.svg(
              width: 22,
              height: 22,
            ) ,
          ),
          const SizedBox(width: 8),
          
          // 提示文本
          Expanded(
            child: Text(
              'The required dishes in the set meals are available for sale.',
              style: KPFontStyle.bodyMedium.copyWith(
                color: KPColors.textGrayPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }


 // 👇🏻👇🏻 有商品 👇🏻👇🏻
  /// 构建有关联商品时的UI（按设计图样式）
Widget _buildProductsListView() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // 顶部警告信息卡片
      _buildWarningInfoCard(),
      
      const SizedBox(height: 24),
      
      // 提示文本
      _buildProductsHintText(),
      
      const SizedBox(height: 16),
      
      // 商品列表
      Expanded(
        child: _buildAffectedProductsList(),
      ),
    ],
  );
}

/// 构建关联商品列表
Widget _buildAffectedProductsList() {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
    child: ListView.builder(
      itemCount: affectedProducts!.length,
      itemBuilder: (context, index) {
        final product = affectedProducts![index];
        return _buildProductItem(product);
      },
    ),
  );
}

/// 构建单个商品项（按设计图样式）
Widget _buildProductItem(SoldOutDetailProduct product) {
  return Container(
    height: 72,
    margin: const EdgeInsets.only(bottom: 12),
    child: Row(
      children: [
        // 商品图片
        Container(
          width: 68,
          height: 68,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: KPColors.fillGrayLightLighter,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: product.commodityImagePath.isNotEmpty
                ? CachedNetworkImage(
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  imageUrl: product.commodityImagePath,
                  errorWidget: (context, url, error) => Assets.images.iconTest6
                                .image(
                                    width: double.infinity,
                                    height: double.infinity,
                                    fit: BoxFit.cover),
                  )
                : Assets.images.iconTest6
                                .image(
                                    width: double.infinity,
                                    height: double.infinity,
                                    fit: BoxFit.cover),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // 商品信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 商品名称
              Text(
                product.commodityName,
                style: KPFontStyle.headingSmall.copyWith(
                  color: KPColors.textGrayPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ), 
              
              // const SizedBox(height: 4),
              _buildScales(product),
              
              // 商品类型标签和价格
              _buildPrice(product),
            ],
          ),
        ),
        
        // Mark available 开关
        _buildMarkAvailable (product),
      ],
    ),
  );
}

Widget _buildScales(SoldOutDetailProduct product){
    if (product.commodityType == 2) {
      return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4), color: Color(0xE6E9F2FF)),
      child: const Text(
        "Scales",
        style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: Color(0xFF0C66E4)),
      ),
    );
    } else {
      return const SizedBox.shrink();
    }    
  }

  Widget _buildPrice(SoldOutDetailProduct product){
    return Row(
                children: [
                  Text( 
                    '${product.currencyUnit}${product.price.toStringAsFixed(2)}',
                    style: KPFontStyle.bodyMedium.copyWith(
                      color: KPColors.textGraySecondary,
                    ),
                  ),

                  if(product.actives.isEmpty) ...[
                    const SizedBox(width: 4),
                    Assets.images.iconProductItemBottomRomotion.svg(width: 16, height: 16)
                  ]
                  
                ],
              );
  }

  Widget _buildMarkAvailable (SoldOutDetailProduct product) {
    return GestureDetector(
      child: Container(
        width: 128,
        height: 48,
        decoration: BoxDecoration(
          color: KPColors.fillGrayLightLighter,
          borderRadius: BorderRadius.circular(KPRadius.l)
        ),
        child: Center(
          child: Text(
            'Mark available',
            style: KPFontStyle.headingXSmall.copyWith(
              color: KPColors.textGrayPrimary,
            ),
          ),
        ),
      ),
      onTap: () {
        // 修改product的数据,强行执行无关联商品但是要打开二次取消售罄的view
        ProductItem item = product.toProductItem(); //拼装数据
        List<SoldOutDetailProduct> tempAffectedProducts = []; //这里需要空数据

        // 添加弹窗关闭监听回调
        KPSlidePopup.addDismissCallback(() {
          // 第二次弹窗关闭后，重新请求关联商品数据
          print('🔄 第二次弹窗已关闭，开始刷新关联商品数据');
          _refreshAffectedProducts();
        });
        //写死 关闭自动请求
        widget.onShowConfirmDialog?.call(item, tempAffectedProducts, isCancelAllPopup: widget.isCancelAllPopup, disableAutoFetch: true);
      },
    );
  }

  /// 刷新关联商品数据
void _refreshAffectedProducts() {
  print('🔄 开始刷新关联商品数据');
  widget.onRefreshAffectedProducts?.call();
}

// 👆🏻👆🏻 有商品 👆🏻👆🏻

/// 构建无关联商品时的UI（当前样式）
Widget _buildNoProductsView() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // 提示信息卡片
      _buildInfoCard(),
      
      const SizedBox(height: 24),
      
      // 中心确认区域
      _buildConfirmHintView(),
    ],
  );
}

/// 构建警告信息卡片（有关联商品时使用）
Widget _buildWarningInfoCard() {
  return Container(
    height: 48,
    margin: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 13),
    decoration: BoxDecoration(
      color: const Color(0xFFFFF7E6), // 警告橙色背景
      borderRadius: BorderRadius.circular(6),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 警告图标
        Container(
          width: 22,
          height: 22,
          decoration: const BoxDecoration(
            color: Color(0xFFFF8C00), // 橙色圆形背景
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.priority_high,
            color: Colors.white,
            size: 16,
          ),
        ),
        const SizedBox(width: 8),
        
        // 警告文本
        Expanded(
          child: Text(
            'The required dishes in the set meals are still not available for sale.',
            style: KPFontStyle.bodyMedium.copyWith(
              color: KPColors.textGrayPrimary,
            ),
          ),
        ),
      ],
    ),
  );
}

/// 构建商品列表提示文本
Widget _buildProductsHintText() {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
    child: Text(
      'Please cancel the sold-out status of the following dishes first:',
      style: KPFontStyle.bodyMedium.copyWith(
        color: KPColors.textGrayPrimary,
      ),
    ),
  );
}




  /// 构建中心提示区域(图标+文字)
  Widget _buildConfirmHintView() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.images.iconBatchAction.svg(width: 120, height: 120,),
            // Container(width: 120, height: 120, color: Colors.red,),
            const SizedBox(height: 16),
            _buildConfirmHintText(),
          ],
        ),
      ));
  }

  Widget _buildConfirmHintText() {
    return RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
         style: KPFontStyle.bodyLarge.copyWith(
                color: KPColors.textGrayPrimary
                ),
          children: [//预防后期要给商品名搞富文本
            const TextSpan(text: 'Confirm make combo '),
            TextSpan(
              text: '"${product.commodityName}"',
              style: KPFontStyle.bodyLarge.copyWith(
                color: KPColors.textGrayPrimary
                ),
            ),
            const TextSpan(text: '\navailable again ?'),
          ],
        ),
      );
  }

  /// 构建中间图标
  Widget _buildCenterIcon() {
    return Center(
      child: Assets.images.iconSoldOnDetail.svg(
        width: 120, 
        height: 120,
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 取消按钮
        _buildCustomButton(
          text: 'Cancel',
          backgroundColor: KPColors.fillGrayLightLighter,
          textColor: KPColors.textGrayPrimary,
          onTap: _handleCancel,
          width: 93, // 和设计图保持一致的宽度
        ),
        const SizedBox(width: 12),
        
        // 确认按钮
        Consumer(
          builder: (context, ref, child) {
            final soldOutState = ref.watch(soldOutStatusControllerProvider);
            
            return _buildCustomButton(
              text: soldOutState.isLoading 
                  ? 'Processing...' 
                  : 'Mark available',
              backgroundColor: soldOutState.isLoading 
                  ? KPColors.borderBrandDefault 
                  : KPColors.textGrayPrimary,
              textColor: Colors.white,
              onTap: soldOutState.isLoading ? null : _handleConfirm,
              width: 156, // Mark available按钮稍宽一些
            );
          },
        ),
      ],
    );
  }

  /// 构建自定义按钮
  Widget _buildCustomButton({
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required VoidCallback? onTap,
    required double width,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 40, // 和设计图保持一致的高度
        width: width,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6), // 稍小的圆角
          border: onTap == null 
            ? Border.all(color: KPColors.borderGrayLightBase, width: 1)
            : null,
        ),
        child: Center(
          child: Text(
            text,
            style: KPFontStyle.headingSmall.copyWith(
              color: textColor,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理取消操作
  void _handleCancel() {
    print('🔍 _handleCancel called');
  print('🔍 isCancelAllPopup: ${widget.isCancelAllPopup}');
  
  if (widget.isCancelAllPopup == false) {
    // 单个弹窗：先调用onCancel，再关闭弹窗
    widget.onCancel?.call();
    // onCancel里面已经调用了KPSlidePopup.dismissFromTarget()，所以这里不需要再调用
  } else {
    // 多个弹窗：直接关闭所有，不调用onCancel避免冲突
    KPSlidePopup.dismissAll();
  }
}

  /// 处理确认操作
  void _handleConfirm() async {
    final controller = ref.read(soldOutStatusControllerProvider.notifier);

    try {
      // 直接等待操作完成
      await controller.setSoldOutStatus(
        productId: product.commodityId,
        isSoldOut: false, // 恢复为可用状态
      );

      // 操作成功，关闭弹窗
      if (mounted) {
        if (widget.isCancelAllPopup) {
          KPSlidePopup.dismissAll();
        } else {
          KPSlidePopup.dismissFromTarget();
        }
      }
    } catch (e) {}
  }
}