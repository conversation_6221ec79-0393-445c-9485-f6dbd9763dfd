import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/product_item.dart';

part 'sold_out_detail_product.freezed.dart';
part 'sold_out_detail_product.g.dart';

/// 售罄详情商品数据模型
/// 
/// 用于售罄确认页面显示受影响的套餐商品信息
@freezed
class SoldOutDetailProduct with _$SoldOutDetailProduct {
  const factory SoldOutDetailProduct({
    /// 商品ID
    required int commodityId,
    /// 商品名称
    required String commodityName,
    /// 商品图片路径
    @Default('') String commodityImagePath,
    /// 商品类型：1=计量商品，2=称重商品，3=套餐，4=加料，5=餐盒
    @Default(1) int commodityType,
    /// 价格（用于显示）
    @Default(0.0) double price,
    /// 货币单位
    @Default('S\$') String currencyUnit,
    /// 有优惠活动
    @Default([]) List<String> actives,
  }) = _SoldOutDetailProduct;

  factory SoldOutDetailProduct.fromJson(Map<String, dynamic> json) =>
      _$SoldOutDetailProductFromJson(json);
}

extension SoldOutDetailProductExtension on SoldOutDetailProduct {
  /// 转换为ProductItem（用于兼容现有UI组件）
  ProductItem toProductItem() {
    return ProductItem(
      commodityId: commodityId,
      commodityName: commodityName,
      commodityType: commodityType,
      price: price,
      currencyUnit: currencyUnit,
      commodityImagePath: commodityImagePath,
      priceChangeEnable: 0,
      standaloneSaleEnable: 1,
      specType: 1,
      categoryId: 1,
      discountEnable: 1,
    );
  }
}