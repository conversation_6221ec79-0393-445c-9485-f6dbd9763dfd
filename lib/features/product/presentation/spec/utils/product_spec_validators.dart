import 'package:kpos/features/product/domain/product_spec_group.dart';

/// 商品规格验证工具类
/// 用于验证商品规格选择是否符合要求
class ProductSpecValidators {
  /// 自动生成验证器函数
  /// 
  /// 根据规格组信息自动生成相应的验证函数
  /// [group] 规格组信息
  /// 
  /// 返回一个验证函数，接收选中的规格项复合键列表，并返回错误信息（如有）
  /// == 修改开始 == 支持复合键格式 List<String>
  static String? Function(List<String>?) autoValidator(ProductSpecGroup group) {
    return (List<String>? selectedIds) {
      // 如果规格组是必选的，但用户没有选择任何规格项，则返回错误提示
      if (group.isRequired && (selectedIds == null || selectedIds.isEmpty)) {
        return '请选择${group.name}';
      }
      return null; // 验证通过
    };
  }
  // == 修改结束 ==
}