import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/domain/product_spec_selection_data.dart';

import '../../domain/product_combo.dart';
import '../../domain/product_combo_selection_state.dart';
import '../../domain/product_detail.dart' as product_detail;
import '../../domain/product_item.dart';
import '../../domain/product_combo_modifier_config.dart';
import '../../domain/product_spec_group.dart';
import '../../application/product_combo_modifier_config_service.dart';
import 'product_select_spec_view.dart';
import 'product_spec_selection_controller.dart';

/// Tab类型枚举
enum TabType { modifier, moreAction }

/// Modifier显示模式枚举
enum ModifierDisplayMode {
  unified, // 统一口味模式 (Varied Flavors)
  individual // 独立配置模式 (Modifier 1, 2, ...)
}

/// 商品规格修饰器视图 - 用于在侧边栏中显示商品的详细修饰选项
class ProductSelectSpecModifierView extends ConsumerStatefulWidget {
  /// 当前选中的套餐商品选项
  final ProductComboChoice? currentChoice;

  /// 套餐分组ID
  final String? groupId;

  /// 套餐对象（新增：用于访问套餐选择控制器）
  final ProductCombo? combo;

  /// 商品的choiceKey（新增：用于状态管理）
  final String? choiceKey;

  /// 备注选项列表
  final List<product_detail.ReasonOption> reasonOptions;

  /// 商品数量
  final int quantity;

  /// 关闭弹窗的回调
  final VoidCallback? onClose;

  /// FormBuilder值更新回调（新增）
  final Function(String choiceKey, Map<String, dynamic> formData)? onFormUpdate;

  /// 初始FormBuilder值（新增）
  final Map<String, dynamic>? initialFormValues;

  const ProductSelectSpecModifierView({
    Key? key,
    this.currentChoice,
    this.groupId,
    this.combo,
    this.choiceKey,
    this.reasonOptions = const [],
    required this.quantity,
    this.onClose,
    this.onFormUpdate,
    this.initialFormValues,
  }) : super(key: key);

  @override
  ConsumerState<ProductSelectSpecModifierView> createState() =>
      _ProductSelectSpecModifierViewState();
}

class _ProductSelectSpecModifierViewState extends ConsumerState<ProductSelectSpecModifierView> with TickerProviderStateMixin {
  late TabController _tabController;
  late List<ProductComboChoice> _modifierChoices;
  int _currentModifierIndex = 0;

  // FormBuilder相关状态
  late Map<String, GlobalKey<FormBuilderState>> _modifierFormKeys;
  late Map<String, Map<String, dynamic>> _modifierFormValues;

  // Modifier显示模式相关状态
  ModifierDisplayMode _displayMode = ModifierDisplayMode.unified;
  bool _hasSwitchedToIndividual = false;

  // 从 choiceKey 解析出新格式所需的参数
  Map<String, dynamic>? _parsedChoiceKey;
  

  @override
  void initState() {
    super.initState();

    // 解析 choiceKey 获取新格式参数
    if (widget.choiceKey != null) {
      try {
        _parsedChoiceKey = ProductComboSelectionStateExtension.parseComboChoiceKey(widget.choiceKey!);
      } catch (e) {
        // debugPrint('🚨 解析 choiceKey 失败: ${widget.choiceKey}');
      }
    }
    
    _initializeModifierChoices();
    _initializeFormKeys();
    _tabController = TabController(length: 2, vsync: this);
  }

  /// 初始化Modifier选项数据
  void _initializeModifierChoices() {
    _modifierChoices = _generateModifierChoicesFromCombo();
    
    // 初始化Modifier配置到服务中
    if (_parsedChoiceKey != null && _modifierChoices.isNotEmpty) {
      final groupId = _parsedChoiceKey!['groupId'] as String;
      final commodityId = _parsedChoiceKey!['commodityId'] as int;
      final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
      final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
      
      ref.read(productComboModifierConfigServiceProvider.notifier)
          .initializeModifiersForProduct(
            groupId,
            commodityId,
            groupIndex,
            choiceIndex,
            _modifierChoices,
          );
    }
  }

  /// 初始化FormBuilder键和值
  void _initializeFormKeys() {
    _modifierFormKeys = {};
    _modifierFormValues = {};

    if (_parsedChoiceKey == null) return;

    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';

    // 统一模式的键
    final unifiedKey = '${baseKey}_unified_modifier';
    _modifierFormKeys[unifiedKey] = GlobalKey<FormBuilderState>();
    _modifierFormValues[unifiedKey] = widget.initialFormValues ?? {};

    // 独立模式的键
    for (int i = 0; i < _modifierChoices.length; i++) {
      final modifierKey = '${baseKey}_modifier_$i';
      _modifierFormKeys[modifierKey] = GlobalKey<FormBuilderState>();
      _modifierFormValues[modifierKey] = widget.initialFormValues ?? {};
    }
  }

  /// 切换到独立模式的方法(统一模式(数量大于1) -> 独立模式)
  void _switchToIndividualMode() {
    if (_hasSwitchedToIndividual || _parsedChoiceKey == null || widget.quantity <= 1) return;

    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
    
    // 1. 获取统一配置
    final unifiedConfig = modifierConfigs['${baseKey}_unified_modifier'];

    // 2. 将统一配置复制到各个独立配置中
        // == 修改开始 == 🔧 为所有独立Modifier立即配置状态
    // 2. 将统一配置复制到各个独立配置中
    if (unifiedConfig != null && unifiedConfig.isConfigured) {
     //debugPrint('🔧 检测到统一模式配置，开始为所有独立Modifier配置状态');
      
      for (int i = 0; i < _modifierChoices.length; i++) {
        final individualModifierId = 'modifier_$i';

        // A. 复制配置数据到 ModifierConfigService
        ref
            .read(productComboModifierConfigServiceProvider.notifier)
            .updateModifierConfigForProduct(
              groupId,
              commodityId,
              groupIndex,
              choiceIndex,
              individualModifierId,
              Map<String, dynamic>.from(unifiedConfig.formData), // 深拷贝避免引用问题
              isConfigured: true,
            );

        // B. 立即为每一个独立Modifier恢复状态到对应的ProductSpecSelectionController
        try {
          final specGroups = _generateSpecGroupsFromCombo(individualModifierId);
          if (specGroups.isNotEmpty) {
            final instanceId = '${baseKey}_$individualModifierId';
            ref
                .read(productSpecSelectionControllerProvider(instanceId).notifier)
                .restoreFromSavedData(Map<String, dynamic>.from(unifiedConfig.formData), specGroups);
            
           //debugPrint('🔧 ✅ 成功为 $individualModifierId 配置状态');
          }
        } catch (e) {
         //debugPrint('🚨 为 $individualModifierId 配置状态失败: $e');
          
          // 回退到默认选中逻辑
          try {
            final specGroups = _generateSpecGroupsFromCombo(individualModifierId);
            if (specGroups.isNotEmpty) {
              final instanceId = '${baseKey}_$individualModifierId';
              ref
                  .read(productSpecSelectionControllerProvider(instanceId).notifier)
                  .initializeDefaultSelections(specGroups);
              
             //debugPrint('🔧 ✅ 成功为 $individualModifierId 初始化默认选中项');
            }
          } catch (fallbackError) {
           //debugPrint('🚨 为 $individualModifierId 初始化默认选中项也失败: $fallbackError');
          }
        }
      }

      // // 不需要提示
      // // 显示成功提示,
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('已切换到独立配置模式，统一配置已应用到所有商品'),
      //     backgroundColor: KPColors.iconGreenDefault,
      //     duration: const Duration(seconds: 2),
      //   ),
      // );
    } else {
     //debugPrint('🔧 未检测到统一模式配置，为所有独立Modifier初始化默认配置');
      
      // 没有统一配置时，为所有独立Modifier初始化默认选中
      for (int i = 0; i < _modifierChoices.length; i++) {
        final individualModifierId = 'modifier_$i';
        
        try {
          final specGroups = _generateSpecGroupsFromCombo(individualModifierId);
          if (specGroups.isNotEmpty) {
            final instanceId = '${baseKey}_$individualModifierId';
            ref
                .read(productSpecSelectionControllerProvider(instanceId).notifier)
                .initializeDefaultSelections(specGroups);
            
           //debugPrint('🔧 ✅ 成功为 $individualModifierId 初始化默认配置');
          }
        } catch (e) {
         //debugPrint('🚨 为 $individualModifierId 初始化默认配置失败: $e');
        }
      }

      // // 不需要提示
      // // 显示切换提示
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('已切换到独立配置模式，请分别配置各个商品'),
      //     backgroundColor: KPColors.borderBrandDefault,
      //     duration: const Duration(seconds: 2),
      //   ),
      // );
    }
    // == 修改结束 ==

    // 3. 更新状态
    setState(() {
      _displayMode = ModifierDisplayMode.individual;
      _hasSwitchedToIndividual = true;
      _currentModifierIndex = 0; // 切换后选中第一个
    });

    // 使用多层回调确保状态完全恢复后再刷新UI
     // 使用多层回调确保状态完全恢复后再刷新UI
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // 第一次刷新：确保Modifier按钮正确显示
          setState(() {});
          
          // 第二次延迟刷新：确保ProductSelectSpecView重建
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {});
             //debugPrint('🔧 强制刷新UI以确保状态正确显示');
              
              // 第三次微延迟：给UI充分时间完成重建
              Future.delayed(const Duration(milliseconds: 10), () {
                if (mounted) {
                  setState(() {});
                 //debugPrint('🔧 最终UI刷新完成');
                }
              });
            }
          });
        }
      });
  }

  /// 构建Modifier Tab的完整内容
  Widget _buildModifierTabContent() {
    return Column(
      children: [
        // Modifier按钮选择行
        _buildModifierButtonRow(),

        // 当前选中Modifier的配置区域
        Expanded(
          child: _buildCurrentModifierConfig(),
        ),
      ],
    );
  }

  /// 构建Modifier按钮选择行
  Widget _buildModifierButtonRow() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(vertical: 20),
      width: double.infinity,
      child: Align(
        alignment: Alignment.centerLeft,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 16), // 左边距

              // == 新增开始 == 根据显示模式渲染不同的按钮
              if (_displayMode == ModifierDisplayMode.unified) ...[
                _buildVariedFlavorsButton(),
              ] else ...[
                ..._buildIndividualModifierButtons(),
              ],
              // == 新增结束 ==
            ],
          ),
        ),
      ),
    );
  }

  // 构建统一模式 按钮 (Varied Flavors) - 点击直接切换
  Widget _buildVariedFlavorsButton() {
  return GestureDetector(
    onTap: _switchToIndividualMode,
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: KPColors.borderGrayLightDark,
          width: 1.2,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.images.iconVariedFlavors.svg(),
          const SizedBox(width: 8),
          Text(
            'Varied Flavors',
            style: KPFontStyle.bodyLarge.copyWith(
              color: KPColors.textGrayPrimary,
            ),
          ),
        ],
      ),
    ),
  );
}

  /// 构建独立模式 单个Modifier按钮
  Widget _buildModifierButton({
    required String title,
    required bool isSelected,
    required bool isConfigured,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? KPColors.fillBrandLightest : Colors.white,
          border: Border.all(
            color: isSelected
                ? KPColors.borderBrandDefault
                : KPColors.fillGrayLightLight,
            width: 1.2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          title,
          style: KPFontStyle.bodyLarge.copyWith(
            color: isSelected
                ? KPColors.textBrandDefault
                : KPColors.textGrayPrimary,
          ),
        ),
      ),
    );
  }

  // 构建独立配置按钮列表
  List<Widget> _buildIndividualModifierButtons() {
    return _modifierChoices.asMap().entries.map((entry) {
      final index = entry.key;
      final modifierChoice = entry.value;
      final title = 'Modifier ${index + 1}';
      final isSelected = index == _currentModifierIndex;

      return Padding(
        padding: const EdgeInsets.only(right: 8),
        child: _buildModifierButton(
          title: title,
          isSelected: isSelected,
          isConfigured: false, // 不再使用此参数
          onTap: () {
            // 切换到独立模式
            _switchToModifier(index);
          },
        ),
      );
    }).toList();
  }
  
  /// 切换到指定的Modifier，包含状态保存和恢复逻辑
  void _switchToModifier(int targetIndex) {
     if (_parsedChoiceKey == null || targetIndex == _currentModifierIndex) return;

    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';

     // 步骤1：保存当前Modifier的状态
    if (_displayMode == ModifierDisplayMode.individual) {
      final currentInstanceId = '${baseKey}_modifier_$_currentModifierIndex';
      _saveCurrentModifierState(currentInstanceId, _currentModifierIndex);
    }

    // 步骤2：切换到目标Modifier
    setState(() {
      _currentModifierIndex = targetIndex;
    });

    // 步骤3：恢复目标Modifier的状态（延迟执行确保UI已更新）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final targetInstanceId = '${baseKey}_modifier_$targetIndex';
      _restoreModifierState(targetInstanceId, targetIndex);
    });

   //debugPrint('🔧 切换Modifier: $_currentModifierIndex → $targetIndex');
  }

  /// 保存当前Modifier的选择状态
  void _saveCurrentModifierState(String instanceId, int modifierIndex) {
    try {
      // 从ProductSpecSelectionController获取当前状态
      final selectionState = ref.read(productSpecSelectionControllerProvider(instanceId));
      
      if (selectionState.selectedItemIds.isNotEmpty || 
          selectionState.itemQuantities.isNotEmpty ||
          selectionState.selectedRemarkTags.isNotEmpty ||
          selectionState.customRemark.isNotEmpty) {
        
        final configData = selectionState.toMap();
        configData['additionalPrice'] = selectionState.additionalPrice;
        
        final hasSelections = selectionState.selectedItemIds.isNotEmpty ||
            selectionState.itemQuantities.values.any((qty) => qty > 0) ||
            selectionState.selectedRemarkTags.isNotEmpty ||
            selectionState.customRemark.trim().isNotEmpty;

        final isConfigured = hasSelections && selectionState.isValid;
        configData['isConfigured'] = isConfigured;

        // 保存到ModifierConfigService
        final parsedKey = _parsedChoiceKey!;
        final localModifierId = 'modifier_$modifierIndex';
        
        ref
            .read(productComboModifierConfigServiceProvider.notifier)
            .updateModifierConfigForProduct(
              parsedKey['groupId'] as String,
              parsedKey['commodityId'] as int,
              parsedKey['groupIndex'] as int,
              parsedKey['choiceIndex'] as int,
              localModifierId,
              configData,
              isConfigured: isConfigured,
            );

       //debugPrint('🔧 ✅ 保存Modifier $modifierIndex 状态成功');
      } else {
       //debugPrint('🔧 ⚠️ Modifier $modifierIndex 无状态需要保存');
      }
    } catch (e) {
     //debugPrint('🚨 保存Modifier $modifierIndex 状态失败: $e');
    }
  }

  /// 恢复目标Modifier的选择状态
  void _restoreModifierState(String instanceId, int modifierIndex) {
    try {
      final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
      final parsedKey = _parsedChoiceKey!;
      final baseKey = '${parsedKey['commodityId']}_${parsedKey['groupId']}_${parsedKey['groupIndex']}_${parsedKey['choiceIndex']}';
      final configKey = '${baseKey}_modifier_$modifierIndex';
      final config = modifierConfigs[configKey];

      if (config != null && config.isConfigured && config.formData.isNotEmpty) {
        // 从保存的配置中恢复状态
        final specGroups = _generateSpecGroupsFromCombo('modifier_$modifierIndex');
        if (specGroups.isNotEmpty) {
          ref
              .read(productSpecSelectionControllerProvider(instanceId).notifier)
              .restoreFromSavedData(config.formData, specGroups);
          
         //debugPrint('🔧 ✅ 恢复Modifier $modifierIndex 状态成功');
        }
      } else {
        // 没有保存的状态，初始化默认选中
        final specGroups = _generateSpecGroupsFromCombo('modifier_$modifierIndex');
        if (specGroups.isNotEmpty) {
          ref
              .read(productSpecSelectionControllerProvider(instanceId).notifier)
              .initializeDefaultSelections(specGroups);
          
         //debugPrint('🔧 ℹ️ Modifier $modifierIndex 无保存状态，使用默认选中');
        }
      }
    } catch (e) {
     //debugPrint('🚨 恢复Modifier $modifierIndex 状态失败: $e');
    }
  }
  
  /// 构建当前选中Modifier的配置内容
  Widget _buildCurrentModifierConfig() {
    if (widget.currentChoice == null || 
        widget.currentChoice!.specGroups.isEmpty ||
        _parsedChoiceKey == null) {
      return Container(
        child: Center(
          child: Text(
            '当前商品无可配置的Modifier选项',
            style: KPFontStyle.bodyMedium.copyWith(
              color: KPColors.textGraySecondary,
            ),
          ),
        ),
      );
    }

    // 使用新格式生成 instanceId
    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
    
    final String currentInstanceId;
    if (_displayMode == ModifierDisplayMode.unified) {
      currentInstanceId = '${baseKey}_unified_modifier';
    } else {
      currentInstanceId = '${baseKey}_modifier_$_currentModifierIndex';
    }

    return ProductSelectSpecView(
      // 添加动态key强制重建组件
      key: ValueKey('${currentInstanceId}_${_displayMode.toString()}_$_currentModifierIndex'),
      instanceId: currentInstanceId,
      specGroups: _generateSpecGroupsFromCombo(currentInstanceId),
      reasonOptions: widget.reasonOptions,
      initialFormValues: _modifierFormValues[currentInstanceId],
      onSelectionChanged: (selectionData) {
        final configData = selectionData.toMap();
        configData['additionalPrice'] = selectionData.additionalPrice;

        final hasSelections = selectionData.selectedItemIds.isNotEmpty ||
            selectionData.itemQuantities.values.any((qty) => qty > 0) ||
            selectionData.selectedRemarkTags.isNotEmpty ||
            selectionData.customRemark.trim().isNotEmpty;

        final isConfigured = hasSelections && selectionData.isValid;
        configData['isConfigured'] = isConfigured;

        // 使用新格式更新配置
        final localModifierId = _displayMode == ModifierDisplayMode.unified 
            ? 'unified_modifier' 
            : 'modifier_$_currentModifierIndex';
            
        ref
            .read(productComboModifierConfigServiceProvider.notifier)
            .updateModifierConfigForProduct(
              groupId,
              commodityId,
              groupIndex,
              choiceIndex,
              localModifierId,
              configData,
            );

        _handleFormBuilderChange(currentInstanceId);
      },
      onSubmit: (selectionData) {
       //debugPrint('🔧 提交规格选择: ${selectionData.selectedItemIds}');
      },
    );
  }

  /// 生成Modifier选项数据（根据套餐中的实际选中数量）
  List<ProductComboChoice> _generateModifierChoicesFromCombo() {
    // 如果没有传入currentChoice，返回空列表
    if (widget.currentChoice == null) {
      return [];
    }

    final currentChoice = widget.currentChoice!;
    final quantity = widget.quantity;

    if (quantity <= 0) {
      return [];
    }

    if (currentChoice.specGroups.isNotEmpty) {
      return _convertSpecGroupsToModifierChoices(
          currentChoice.specGroups, quantity);
    }

    return _generateModifierChoicesByQuantity(currentChoice, quantity);
  }

  /// 根据套餐选中数量生成对应个数的Modifier选项
  List<ProductComboChoice> _generateModifierChoicesByQuantity(
      ProductComboChoice currentChoice, int quantity) {
    List<ProductComboChoice> modifierChoices = [];
    final product = currentChoice.product;

    if (_parsedChoiceKey == null) return modifierChoices;
    
    //  使用新的ID生成规则和命名策略
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final originalCommodityId = product.commodityId;

    for (int i = 0; i < quantity; i++) {
      // 生成新的commodityId: ${原始commodityId}_${groupIndex}_${choiceIndex}_${modifierIndex}
      final modifierCommodityId = '${originalCommodityId}_${groupIndex}_${choiceIndex}_$i';
      
      // 使用 "Modifier 1, Modifier 2" 命名策略
      final modifierName = 'Modifier ${i + 1}';

      final modifierProduct = ProductItem(
        commodityId: int.tryParse(modifierCommodityId) ?? originalCommodityId + i, // 如果转换失败则使用备用方案
        commodityName: modifierName,
        commodityType: product.commodityType,
        commodityImagePath: product.commodityImagePath,
        priceChangeEnable: product.priceChangeEnable,
        standaloneSaleEnable: product.standaloneSaleEnable,
        specType: product.specType,
        price: product.price,
        imageUrl: product.imageUrl,
        categoryId: product.categoryId ?? 1,
        discountEnable: product.discountEnable ?? 1,
      );

      final modifierChoice = ProductComboChoice(
        product: modifierProduct,
        additionalPrice: currentChoice.additionalPrice,
        defaultEnable: i == 0 ? 1.0 : 0.0,
        priceDifference: currentChoice.priceDifference,
        specGroups: _generateDefaultSpecGroupsForProduct(modifierProduct),
      );
      
      modifierChoices.add(modifierChoice);
    }

    return modifierChoices;
  }

  /// 将规格组转换为Modifier选项
  List<ProductComboChoice> _convertSpecGroupsToModifierChoices(
      List<ProductSpecGroup> specGroups, int quantity) {
    List<ProductComboChoice> modifierChoices = [];

    //  使用正确的ID生成规则和深拷贝策略
    if (_parsedChoiceKey == null) return modifierChoices;
    
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final originalProduct = widget.currentChoice!.product;
    final originalCommodityId = originalProduct.commodityId;

    for (int i = 0; i < quantity; i++) {
      // 生成新的commodityId: ${原始commodityId}_${groupIndex}_${choiceIndex}_${modifierIndex}
      final modifierCommodityId = '${originalCommodityId}_${groupIndex}_${choiceIndex}_$i';
      
      // 使用 "Modifier 1, Modifier 2" 命名策略
      final modifierName = 'Modifier ${i + 1}';

      // 创建独立的Product对象
      final modifierProduct = ProductItem(
        commodityId: int.tryParse(modifierCommodityId) ?? originalCommodityId + i, // 如果转换失败则使用备用方案
        commodityName: modifierName,
        commodityType: originalProduct.commodityType,
        commodityImagePath: originalProduct.commodityImagePath,
        priceChangeEnable: originalProduct.priceChangeEnable,
        standaloneSaleEnable: originalProduct.standaloneSaleEnable,
        specType: originalProduct.specType,
        price: originalProduct.price,
        imageUrl: originalProduct.imageUrl,
        categoryId: originalProduct.categoryId ?? 1,
        discountEnable: originalProduct.discountEnable ?? 1,
      );

      // 为每个modifier创建独立的specGroups副本
      final independentSpecGroups = specGroups.map((specGroup) {
        return ProductSpecGroup(
          id: specGroup.id,
          name: specGroup.name,
          isRequired: specGroup.isRequired,
          isMultiSelect: specGroup.isMultiSelect,
          upperLimit: specGroup.upperLimit,
          lowerLimit: specGroup.lowerLimit,
          selectedItemIds: List<int>.from(specGroup.selectedItemIds),
          items: specGroup.items.map((item) {
            return SpecItem(
              id: item.id,
              name: item.name,
              isAdditional: item.isAdditional,
              additionalPrice: item.additionalPrice,
              measuredValue: item.measuredValue,
              isSelected: item.isSelected,
              isMeasured: item.isMeasured,
              skuId: item.skuId,
            );
          }).toList(),
          groupType: specGroup.groupType,
        );
      }).toList();

      final modifierChoice = ProductComboChoice(
        product: modifierProduct,
        additionalPrice: widget.currentChoice!.additionalPrice,
        defaultEnable: i == 0 ? 1.0 : 0.0,
        priceDifference: widget.currentChoice!.priceDifference,
        specGroups: independentSpecGroups,
      );
      
      modifierChoices.add(modifierChoice);
    }

    return modifierChoices;
  }

  /// 为商品生成默认规格组 - 修复版本
  List<ProductSpecGroup> _generateDefaultSpecGroupsForProduct(
      ProductItem product) {
    return []; // 返回空列表，强制使用套餐自身定义的数据
  }

  /// 生成规格组数据,⭐️:单规格specType==1,不展示规格组
  List<ProductSpecGroup> _generateSpecGroupsFromCombo(String modifierId) {
    if (widget.currentChoice?.specGroups.isEmpty ?? true) {
      return [];
    }

    final currentChoice = widget.currentChoice!;

    if (currentChoice.specGroups.isNotEmpty) {
      if (currentChoice.product.specType == 1) {
        final filteredSpecGroups = currentChoice.specGroups.where((group) {
          if (group.id == 0 &&
              (group.name.isEmpty || group.name.trim().isEmpty)) {
            return false;
          }
          return true;
        }).toList();

        return filteredSpecGroups;
      }

      return currentChoice.specGroups;
    }

    return widget.currentChoice!.specGroups;
  }

  /// 处理FormBuilder值变化
  void _handleFormBuilderChange(String modifierKey) {
    final formKey = _modifierFormKeys[modifierKey];
    if (formKey?.currentState != null) {
      final currentValues = formKey!.currentState!.value;
      _modifierFormValues[modifierKey] =
          Map<String, dynamic>.from(currentValues);

     //debugPrint('📝 FormBuilder值发生变化: $modifierKey -> ${currentValues.keys.toList()}');

      // 回调给父组件更新套餐状态
      if (widget.onFormUpdate != null && widget.choiceKey != null) {
        widget.onFormUpdate!(widget.choiceKey!, currentValues);
       //debugPrint('📝 已同步到套餐状态: ${widget.choiceKey}');
      }
    }
  }

  @override
  void dispose() {
    _saveCurrentStateBeforeDispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      color: Colors.white,
      width: 600, // 固定宽度，适配侧边栏
      height: MediaQuery.of(context).size.height, // 全屏高度
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部标题栏
          _buildTitleHeader(),

          // Tab标签栏 (Modifier / More Action)
          _buildTabBar(),

          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // 第一个Tab：Modifier功能
                _buildModifierTabContent(),
                // 第二个Tab：More Action功能
                _buildMoreActionView(),
              ],
            ),
          ),

          _buildBottomConfirmView(context),
        ],
      ),
    );
  }

  /// 构建顶部标题栏
  Widget _buildTitleHeader() {
    // 获取商品名称和数量
    final productName = widget.currentChoice?.product.commodityName;
    final quantity = widget.quantity;

    return Container(
      padding: const EdgeInsets.fromLTRB(4, 4, 12, 4),
      height: 40,
      color: Colors.white,
      child: Row(
        children: [
          // 返回按钮
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              padding: const EdgeInsets.fromLTRB(0, 8, 8, 8),
              child: const Icon(
                Icons.arrow_back,
                size: 24,
                color: KPColors.iconGrayPrimary,
              ),
            ),
          ),
          const SizedBox(width: 4),
          // 商品标题
          Expanded(
            child: Text(
              '$productName (×$quantity)',
              style: KPFontStyle.headingLarge.copyWith(
                color: KPColors.textGrayPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // 关闭按钮
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.close,
                size: 24,
                color: KPColors.iconGrayPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Tab标签栏
  Widget _buildTabBar() {
    return Container(
      child: ExtendedTabBar(
        isScrollable: true,
        controller: _tabController,
        dividerColor: Colors.transparent, // 新增：隐藏底部分割线
        indicatorColor: KPColors.borderBrandDefault,
        indicatorWeight: 3,
        labelColor: KPColors.borderBrandDefault,
        unselectedLabelColor: KPColors.textGrayPrimary,
        labelStyle: KPFontStyle.headingSmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: KPFontStyle.headingSmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          ExtendedTab(
            height: 48,
            text: 'Modifier',
          ),
          ExtendedTab(
            height: 48,
            text: 'More action',
          ),
        ],
      ),
    );
  }

  /// 构建More Action视图
  Widget _buildMoreActionView() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.settings,
              size: 48,
              color: KPColors.iconGraySecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'More Action',
              style: KPFontStyle.headingMedium.copyWith(
                color: KPColors.textGrayPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '更多操作功能待实现',
              style: KPFontStyle.bodyMedium.copyWith(
                color: KPColors.textGraySecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部确认视图
  Widget _buildBottomConfirmView(BuildContext context) {
    // 监听所有Modifier的配置状态
    final modifierConfigs =
        ref.watch(productComboModifierConfigServiceProvider);

    // 使用正确的方式检查配置状态
    final isAllConfigured = _checkAllModifiersConfigured(modifierConfigs);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: KPColors.borderGrayDarkLightest,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 价格显示区域
          Expanded(
            child: _buildPriceDisplay(modifierConfigs),
          ),
          const SizedBox(width: 16),
          // 确认按钮
          SizedBox(
            width: 120,
            height: 48,
            child: ElevatedButton(
              onPressed: isAllConfigured ? () => _handleConfirm(context) : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: isAllConfigured
                        ? const Color(0xFF20232B)
                        : Colors.grey[400],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                '确认',
                style: KPFontStyle.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格显示
  Widget _buildPriceDisplay(Map<String, ModifierConfigData> allConfigs) {
    final priceInfo = _calculateDetailedPrice(allConfigs);
    final basePrice = priceInfo['basePrice'] ?? 0.0;
    final modifierPrice = priceInfo['modifierPrice'] ?? 0.0;
    final totalPrice = priceInfo['totalPrice'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
         // 基础价格
        if (basePrice > 0)
          Text(
            '基础价格: \$${basePrice.toStringAsFixed(2)}',
            style: KPFontStyle.bodySmall.copyWith(
              color: KPColors.textGraySecondary,
            ),
          ),
        
        // Modifier价格
        if (modifierPrice > 0)
          Text(
            'Modifier: +\$${modifierPrice.toStringAsFixed(2)}',
            style: KPFontStyle.bodySmall.copyWith(
              color: KPColors.textBrandDefault,
            ),
          ),
        
        // 总价格
        Text(
          '总计: \$${totalPrice.toStringAsFixed(2)}',
          style: KPFontStyle.bodyMedium.copyWith(
            color: KPColors.textGrayPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 检查所有Modifier是否已配置
  bool _checkAllModifiersConfigured(Map<String, ModifierConfigData> allConfigs) {
    if (_parsedChoiceKey == null) return false;

    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';

    if (_displayMode == ModifierDisplayMode.unified) {
      final unifiedConfig = allConfigs['${baseKey}_unified_modifier'];
      return unifiedConfig?.isConfigured == true;
    } else {
      // 检查所有独立配置
      for (int i = 0; i < _modifierChoices.length; i++) {
        final config = allConfigs['${baseKey}_modifier_$i'];
        if (config?.isConfigured != true) {
          return false;
        }
      }
      return true;
    }
  }

  /// 处理确认按钮点击
  void _handleConfirm(BuildContext context) {
    if (_parsedChoiceKey == null) return;

    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';

    // 收集所有配置数据
    Map<String, dynamic> allFormValues = {};

    if (_displayMode == ModifierDisplayMode.unified) {
      final unifiedConfig = modifierConfigs['${baseKey}_unified_modifier'];
      if (unifiedConfig != null) {
        allFormValues.addAll(unifiedConfig.formData);
      }
    } else {
      for (int i = 0; i < _modifierChoices.length; i++) {
        final config = modifierConfigs['${baseKey}_modifier_$i'];
        if (config != null) {
          allFormValues['modifier_$i'] = config.formData;
        }
      }
    }

    // 保存到套餐状态，标记为用户自定义
    if (widget.onFormUpdate != null && widget.choiceKey != null) {
      // 添加标记表示这是用户自定义的值
      allFormValues['_userModified'] = true;
      allFormValues['_timestamp'] = DateTime.now().millisecondsSinceEpoch;

      widget.onFormUpdate!(widget.choiceKey!, allFormValues);
     //debugPrint('📝 确认保存用户选择: ${widget.choiceKey} -> ${allFormValues.keys.toList()}');
    }

    // 关闭当前视图
    widget.onClose?.call();
  }

  /// 计算详细的价格信息 - 支持两种模式
  /// 返回包含基础价格、Modifier价格和总价的Map
 Map<String, double> _calculateDetailedPrice(Map<String, ModifierConfigData> allConfigs) {
    if (widget.currentChoice == null || _parsedChoiceKey == null) {
      return {'basePrice': 0.0, 'modifierPrice': 0.0, 'totalPrice': 0.0};
    }
    
    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
    
    // 🔧 只考虑当前商品的配置
    final productConfigs = <String, ModifierConfigData>{};
    for (final entry in allConfigs.entries) {
      if (entry.key.startsWith('${baseKey}_')) {
        final localKey = entry.key.substring('${baseKey}_'.length);
        productConfigs[localKey] = entry.value;
      }
    }

    // 1. 计算基础价格
    double basePrice = _calculateSingleSpecBasePrice() * widget.quantity;

    // 2. 计算Modifier附加价格
    double modifierPrice = 0.0;

    if (_displayMode == ModifierDisplayMode.unified) {
      final unifiedConfig = productConfigs['unified_modifier'];
      if (unifiedConfig != null && unifiedConfig.isConfigured) {
        final additionalPrice = unifiedConfig.formData['additionalPrice'] as double? ?? 0.0;
        modifierPrice = additionalPrice * widget.quantity;
      }
    } else {
      for (int i = 0; i < _modifierChoices.length; i++) {
        final config = productConfigs['modifier_$i'];
        if (config != null && config.isConfigured) {
          final additionalPrice = config.formData['additionalPrice'] as double? ?? 0.0;
          modifierPrice += additionalPrice;
        }
      }
    }

    final totalPrice = basePrice + modifierPrice;

    return {
      'basePrice': basePrice,
      'modifierPrice': modifierPrice,
      'totalPrice': totalPrice,
    };
  }

  /// 计算单规格商品的基础价格
  double _calculateSingleSpecBasePrice() {
    if (widget.currentChoice == null) return 0.0;

    final currentChoice = widget.currentChoice!;

    if (currentChoice.product.specType == 1 ) {//单规格,正常是只有一个规格项
      final specGroups = currentChoice.specGroups
          .where((group) => group.groupType == ProductSpecGroupType.spec)
          .toList();
          if (specGroups.isNotEmpty) {
      final firstSpecGroup = specGroups.first;
        if (firstSpecGroup.items.isNotEmpty) {
          final basePrice = firstSpecGroup.items.first.additionalPrice;
         //debugPrint('🔧 找到单规格基础价格（从spec规格组）: $basePrice');
          return basePrice;
        }
      }
    }
    
    // 这里应该根据实际的价格计算逻辑来实现
    // 暂时返回商品的基础价格
    return widget.currentChoice!.product.price?.toDouble() ?? 0.0;
  }

  /// 页面销毁前保存当前状态
void _saveCurrentStateBeforeDispose() {
  if (_parsedChoiceKey == null) return;
  
  try {
    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    final groupId = _parsedChoiceKey!['groupId'] as String;
    final commodityId = _parsedChoiceKey!['commodityId'] as int;
    final groupIndex = _parsedChoiceKey!['groupIndex'] as int;
    final choiceIndex = _parsedChoiceKey!['choiceIndex'] as int;
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
    
    // 收集当前所有表单数据
    final Map<String, dynamic> allFormValues = {};
    
    if (_displayMode == ModifierDisplayMode.unified) {
      final unifiedConfig = modifierConfigs['${baseKey}_unified_modifier'];
      if (unifiedConfig != null) {
        allFormValues.addAll(unifiedConfig.formData);
      }
    } else {
      for (int i = 0; i < _modifierChoices.length; i++) {
        final config = modifierConfigs['${baseKey}_modifier_$i'];
        if (config != null) {
          allFormValues['modifier_$i'] = config.formData;
        }
      }
    }
    
    // 保存到套餐状态
    if (widget.onFormUpdate != null && widget.choiceKey != null && allFormValues.isNotEmpty) {
      allFormValues['_autoSaved'] = true;
      allFormValues['_timestamp'] = DateTime.now().millisecondsSinceEpoch;
      
      widget.onFormUpdate!(widget.choiceKey!, allFormValues);
     //debugPrint('🔧 页面关闭时自动保存状态: ${widget.choiceKey}');
    }
  } catch (e) {
   //debugPrint('🚨 自动保存状态失败: $e');
  }
}

}