/// 产品规格屏幕模块
/// 
/// 本文件实现了产品规格选择的界面，包含多个标签页用于产品配置：
/// 1. 修改和选项标签页 - 用于调整产品规格
/// 2. 特殊价格标签页 - 用于设置产品特殊价格
/// 3. 更多操作标签页 - 用于其他产品操作
/// 4. 套餐标签页 - 用于产品套餐配置
///
/// 该屏幕使用TabController管理多个标签页之间的切换

import 'package:extended_tabs/extended_tabs.dart'; // 导入扩展标签组件，提供更多标签功能
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // 导入状态管理库
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart'; // 导入上下文扩展，提供主题和本地化功能
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/product/presentation/providers/editing_cart_item_provider.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_combo_view.dart';
import 'package:kpos/features/product/presentation/spec/product_select_spec_view.dart'; // 导入规格选择视图/ 导入套餐屏幕// 导入特殊价格屏幕
import 'package:kpos/features/product/presentation/spec/product_spec_selection_controller.dart';
import 'package:kpos/features/product/presentation/spec/product_special_price_view.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart'; 
import 'package:kpos/features/product/application/product_spec_service.dart';
import '../../application/product_combo_modifier_config_service.dart';
import '../../domain/product_item.dart';
import '../../domain/product_spec_page_data.dart';
import '../../domain/product_spec_selection_data.dart';
import '../more_actions/product_more_actions_view.dart';
import '../widgets/more_activities_popover.dart';
import '../widgets/product_item_tags.dart';
import './product_final_price_controller.dart';
import '../../domain/product_spec_validation_service.dart' as validation;
import '../../../../assets/assets.gen.dart';
import '../../domain/product_spec_group.dart'; 
import '../../domain/product_combo.dart';
import 'product_combo_selection_controller.dart';
import 'product_special_price_controller.dart';

// /// 按钮验证状态枚举,先去掉校验按钮的逻辑
// enum ButtonValidationState {
//   valid,    // 有效：黑色，可点击
//   invalid,  // 无效：灰色，但仍可点击（提示用户）
// }

/// 详细验证结果（UI层使用）
class DetailedValidationResult {
  final bool isValid;
  final List<String> errorMessages;
  
  const DetailedValidationResult({
    required this.isValid,
    required this.errorMessages,
  });
  
  /// 创建有效结果
  factory DetailedValidationResult.valid() {
    return const DetailedValidationResult(
      isValid: true,
      errorMessages: [],
    );
  }
  
  /// 创建无效结果
  factory DetailedValidationResult.invalid(List<String> errors) {
    return DetailedValidationResult(
      isValid: false,
      errorMessages: errors,
    );
  }
  
  /// 获取格式化的错误信息
  String get formattedErrorMessage {
    if (errorMessages.isEmpty) return '';
    return errorMessages.join('\n');
  }
}

/// 当前活跃标签页的Provider
/// 用于让价格控制器知道当前显示的是哪个标签页
final currentActiveTabProvider = StateProvider<int>((ref) => 0);

/// 套餐数据的Provider  
/// 用于在价格控制器中访问套餐信息
final currentComboProvider = StateProvider<ProductCombo?>((ref) => null);

/// 产品规格屏幕组件
/// 
/// 一个消费者有状态组件，使用Riverpod进行状态管理
/// 用于展示和编辑产品的规格信息
class ProductSpecScreen extends ConsumerStatefulWidget {
  // final ProductItem currentProduct; // 接收 Product 对象
  final ProductSpecPageData pageData;
  final int initialTabIndex;
  final bool isEditMode; // 是否为编辑模式


  const ProductSpecScreen({
    super.key,
    required this.pageData,
    this.initialTabIndex = 0,
    this.isEditMode = false, // 默认为添加模式

  });

  @override
  ConsumerState createState() => _ProductSpecScreenState();
}

/// 产品规格屏幕状态类
///
/// 实现产品规格屏幕的具体功能和状态管理
/// 混入SingleTickerProviderStateMixin以支持动画效果，主要用于TabController
class _ProductSpecScreenState extends ConsumerState<ProductSpecScreen> with SingleTickerProviderStateMixin {

  /// 标签控制器，用于管理多个标签页
  late final TabController tabController;
    /// 表单全局键，用于表单验证和提交
  final _formKey = GlobalKey<FormBuilderState>();

  void initState() {
    super.initState(); // 最佳实践：应首先调用 super.initState()
    
    final bool isComboProduct = widget.pageData.product.commodityType == 3;
    final tabCount = 3; // 统一使用3个标签
    
    // 创建TabController
    tabController = TabController(
      length: tabCount, 
      vsync: this, 
      initialIndex: _getInitialTabIndex(isComboProduct)
    );

    // 添加标签页监听器
    tabController.addListener(() {
      if (mounted) {
        ref.read(currentActiveTabProvider.notifier).state = tabController.index;
      }
    });
    
    // 🔧 如果是套餐，进入页面时重置为默认状态
  if (widget.pageData.combo != null) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final combo = widget.pageData.combo!;
      final controller = ref.read(productComboSelectionControllerProvider(combo).notifier);
      controller.resetState(); // 重置为默认状态
     //debugPrint('🔧 套餐页面初始化：已重置为默认状态');
    });
  }
  }

  // 获取初始标签索引
  int _getInitialTabIndex(bool isComboProduct) {
    if (isComboProduct) {
      return 0; // 套餐商品默认显示Combo标签
    } else {
      return 0; // 单品商品默认显示Modify & Options标签
    }
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }
  
  /// 构建整体界面
  ///
  /// 返回一个固定宽度为600的白色容器，内部包含三部分：
  /// 1. 顶部标题视图
  /// 2. 中间标签页视图（占用扩展空间）
  /// 3. 底部操作视图
  @override
  Widget build(BuildContext context) {
   // 订阅价格控制器状态变化
    final _ = ref.watch(productFinalPriceControllerProvider(widget.pageData.product));

    // 设置套餐数据和当前标签页（PostFrameCallback）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(currentComboProvider.notifier).state = widget.pageData.combo;
      ref.read(currentActiveTabProvider.notifier).state = tabController.index;
    });

    return Container(
      color: Colors.white, // 设置背景色为白色
      width: 600, // 固定宽度为600像素
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
        children: [
          _buildTitleView(widget.pageData.product, context), // 构建顶部标题视图
          Expanded(child: _buildTabView(widget.pageData.product)), // 构建中间标签页视图，占用所有可用空间
          _buildBottomView(widget.pageData.product) // 构建底部操作视图
        ],
      ),
    );
  }

  /// 构建顶部标题视图
  ///
  /// 包含产品名称和关闭按钮
  /// 产品名称会在过长时显示省略号
  Widget _buildTitleView(ProductItem currentProduct, BuildContext tContext) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, left: 24, bottom: 4, right: 12), // 设置内边距
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子组件分散对齐（两端对齐）
        children: [
          ProductTitleText(currentProduct: currentProduct),
          const SizedBox(width: 4,),
          Expanded(
              // child: ProductItemTags(product: currentProduct, style: ProductTagType.comboAndActivity)
              child: _buildTopTabBarTags(currentProduct),
            ),
          // _buildTopTabBarTags(currentProduct),
          GestureDetector(
              child: Assets.images.iconClose.svg(width: 24, height: 24), // 关闭图标，使用SVG格式
              onTap: () {
                KPSlidePopup.dismissFromTarget(); // 点击关闭按钮时关闭当前界面
              })
        ],
      ),
    );
  }

  /// 处理标签点击事件
  void _handleTagTap(ProductItem product, BuildContext tContext) {
    // 根据商品类型执行不同的操作
    switch(product.commodityType) {
      case 1: // 计量商品
        // 处理计量商品标签点击
        _showTagInfo('计量商品标签被点击');
        break;
      case 2: // 称重商品
        // 处理称重商品标签点击
        _showTagInfo('称重商品标签被点击');
        break;
      case 3: // 套餐
        // 处理套餐标签点击
        _showMoreActive('我是要展示的', tContext);
        break;
      default:
        // 默认处理
        _showTagInfo('商品标签被点击');
        break;
    }
  }

  /// 显示标签信息(写死)
  void _showTagInfo(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示标签信息
  void _showMoreActive(String message, BuildContext atContext) {
    KPPopover.showPopover(
      context: atContext,
      width: 200, // == 修改：调整宽度 ==
      height: 50, // == 修改：调整高度，因为只有两行内容 ==
      contentWidget: _buildPopoverList(atContext, ref),
      placement: TDPopoverPlacement.bottomLeft, // == 修改：改为底部弹出 ==
      showArrow: true, // == 修改：确保箭头显示 ==
    arrowSize: 8, // == 新增：明确设置箭头大小 ==
    theme: TDPopoverTheme.dark, // == 新增：使用深色主题，这样箭头颜色会匹配 ==
    overlayColor: Colors.transparent,
    closeOnClickOutside: true,
    // offset: 8, // == 新增：增加偏移量，给箭头留出空间 ==
    );
  }

 /// 构建标题气泡展示的内容,构建弹出层列表内容  
  Widget _buildPopoverList(BuildContext context, WidgetRef ref) {
  return Container(
    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    // == 移除：去掉自定义decoration，让TDPopover的dark主题处理背景和箭头颜色 ==
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildPopoverItem('Spend \$50, save \$5'),
        const SizedBox(height: 8),
        _buildPopoverItem('5 for 1'),
      ],
    ),
  );
}

 /// 构建弹出层单个项目 - 简洁的圆点+文字样式
Widget _buildPopoverItem(String title) {
  return InkWell(
    onTap: () {
      Navigator.of(context).pop(); // 关闭弹出层
      _handleActivityTap(title);
    },
    borderRadius: BorderRadius.circular(4),
    child: Container(
      child: Row(
        children: [
          // == 新增开始 ==
          // 白色圆点
          Container(
            width: 4,
            height: 4,
            margin: const EdgeInsets.only(right: 12),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
          ),
          // 活动文字
          Expanded(
            child: Text(
              title,
              style: KPFontStyle.bodySmall.copyWith(color: KPColors.textGrayInverse),
            ),
          ),
          // == 新增结束 ==
        ],
      ),
    ),
  );
}
  /// 处理活动点击事件
  void _handleActivityTap(String activityName) {
    _showTagInfo('选择了活动: $activityName');
  }

  /// 构建顶部标签栏
  Widget _buildTopTabBarTags(ProductItem item) {
  List<Widget> tagsWidget = [];

  if (item.commodityType != 4 && item.commodityType != 5) {
    switch(item.commodityType) {
      case 3: // 套餐商品 - 使用新的组件
        tagsWidget = [
          // Combo标签（不可点击）
          _buildComboTypeUIWidget("combo"),
          const SizedBox(width: 4),
          // 活动名标签（不可点击）
          _buildActiveName("活动名"),
          const SizedBox(width: 4),
          // == 修改：使用新的MoreActivitiesTag组件 ==
          MoreActivitiesTag(
            count: 2,
            activities: '满减优惠,折扣活动,买一送一,新用户专享', // 你的活动字符串
            onActivityTap: _handleActivityTap,
            width: 200,
            placement: TDPopoverPlacement.bottom,
          ),
        ];
        break;
      default:
        // 其他商品类型保持原有逻辑
        tagsWidget = [
          LayoutBuilder(builder: (tagContext, constraints) {
            return ProductItemTags(product: item, style: ProductTagType.activityOnly)
                .onTap(() {
                  _handleTagTap(item, tagContext);
                });
          })
        ];
        break;
    }
  }

  return Wrap(
    spacing: 0,
    runSpacing: 6,
    children: tagsWidget,
  );
}

 /// 保留这些UI构建方法
Widget _buildComboTypeUIWidget(String name) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      color: const Color(0xE5FFF7E6),
    ),
    child: Text(
      name,
      style: KPFontStyle.bodyXSmall.copyWith(color: KPColors.fillBrandNormal),
    ),
  );
}

Widget _buildActiveName(String name) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      border: Border.all(color: KPColors.borderRedDefault, width: 1.0),
      color: const Color(0xE5FFF7E6).withAlpha(150),
    ),
    child: Text(
      name,
      style: KPFontStyle.bodyXSmall.copyWith(color: KPColors.borderRedDefault),
    ),
  );
}

  /// 根据商品类型构建标签列表
  List<ExtendedTab> _buildTabsForProductType(bool isComboProduct) {
    if (isComboProduct) {
      // 套餐商品：Combo | Special Price | More Actions
      return const [
        ExtendedTab(
          height: 48,
          text: 'Combo',
        ),
        ExtendedTab(
          height: 48,
          text: 'Special Price',
        ),
        ExtendedTab(
          height: 48,
          text: 'More Actions',
        ),
      ];
    } else {
      // 单品商品：Modify & Options | Special Price | More Actions
      return const [
        ExtendedTab(
          height: 48,
          text: 'Modify & Options',
        ),
        ExtendedTab(
          height: 48,
          text: 'Special Price',
        ),
        ExtendedTab(
          height: 48,
          text: 'More Actions',
        ),
      ];
    }
  }

  /// 根据商品类型构建标签内容视图列表
  List<Widget> _buildTabViewsForProductType(ProductItem currentProduct, bool isComboProduct) {
    // 直接从 Provider 获取当前编辑的购物车商品
    final editingCartItem = ref.watch(editingCartItemProvider);

    if (isComboProduct) {
      // 套餐商品：Combo | Special Price | More Actions
      return [
        ProductSpecComboView(
          combo: widget.pageData.combo!,
        ),

        ProductSpecialPriceView(currentProduct: currentProduct),

        ProductMoreActionsView(product: currentProduct, cartItem: editingCartItem,),
      ];
    } else {
      // 🔧 单品商品：过滤单规格商品的规格组显示
      // 对于 specType == 1 的单规格商品，过滤掉规格组（只保留做法组和加料组）
      final filteredSpecGroups = currentProduct.specType == 1 
          ? widget.pageData.specGroups.where((group) => 
              group.groupType != ProductSpecGroupType.spec).toList()
          : widget.pageData.specGroups;
      
     //debugPrint('🔧 规格组过滤 - 商品: ${currentProduct.commodityName}, specType: ${currentProduct.specType}');
     //debugPrint('🔧 原始规格组数量: ${widget.pageData.specGroups.length}, 过滤后: ${filteredSpecGroups.length}');
      
      return [
        ProductSelectSpecView(
          specGroups: filteredSpecGroups,
          reasonOptions: widget.pageData.reasonOptions,
          onSelectionChanged: (selectionData) {
           //debugPrint('规格选择已变化: ${selectionData.selectedItemIds}');
          },
          onSubmit: (selectionData) {
           //debugPrint('提交规格选择: ${selectionData.selectedItemIds}');
          },
        ),
        
        ProductSpecialPriceView(currentProduct: currentProduct),

        ProductMoreActionsView(product: currentProduct, cartItem: editingCartItem,),
      ];
    }
  }

  /// 构建标签页视图
  ///
  /// 包含一个标签栏和对应的标签内容视图
  /// 根据商品类型动态调整标签：单品显示"Modify & Options"，套餐显示"Combo"
  Widget _buildTabView(ProductItem currentProduct) {
    // 🔧 根据商品类型确定标签配置
    final bool isComboProduct = currentProduct.commodityType == 3;

    return Padding(
      padding: const EdgeInsets.only(left: 24, right: 24), // 设置左右内边距
      child: Column(
        children: [
          // 标签栏部分，左对齐
          Align(
            alignment: Alignment.centerLeft, // 左对齐
            child: ExtendedTabBar(
              isScrollable: true, // 支持水平滚动
              labelPadding: const EdgeInsets.only(left: 0, right: 32), // 标签右侧间距32
              indicatorColor: context.theme.brandNormalColor, // 选中指示器颜色使用主题品牌颜色
              indicatorSize: TabBarIndicatorSize.label, // 指示器宽度与标签文本同宽
              splashBorderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ), // 点击效果的圆角
              indicatorWeight: 3, // 指示器高度为3像素
              dividerColor: Colors.transparent, // 分隔线透明
              labelColor: context.theme.brandNormalColor, // 选中标签颜色为品牌颜色
              labelStyle: TextStyle(color: context.theme.brandNormalColor, fontSize: 14, fontWeight: FontWeight.w500), // 选中标签文字样式
              unselectedLabelColor: Colors.black, // 未选中标签颜色为黑色
              unselectedLabelStyle: const TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500), // 未选中标签文字样式
              scrollDirection: Axis.horizontal, // 水平滚动方向
              // 🔧 关键修改：根据商品类型动态生成标签
              tabs: _buildTabsForProductType(isComboProduct),
              controller: tabController, // 使用前面初始化的标签控制器
            ),
          ),
          const SizedBox(height: 20), // 标签栏与内容之间的间距
          // 标签内容视图部分
          Expanded(
            child: ExtendedTabBarView(
              controller: tabController, // 使用相同的标签控制器
              scrollDirection: Axis.horizontal, // 水平滑动方向
              // 🔧 关键修改：根据商品类型动态生成标签内容
              children: _buildTabViewsForProductType(currentProduct, isComboProduct),
            ),
          )
        ],
      ),
    );
  }

  /// 构建底部操作视图
  ///
  /// 包含产品价格显示、数量调节器和确认按钮
  /// 顶部有一条灰色分隔线
  Widget _buildBottomView(ProductItem currentProduct) {
    final finalPriceState = ref.watch(productFinalPriceControllerProvider(currentProduct));
    /// 是否称重商品
    final isWeight = currentProduct.commodityType == 2 ? true : false;

    return FormBuilder(
      key: _formKey,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24), // 水平内边距24
        height: 73, // 固定高度73像素
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              color: context.theme.grayColor1, // 顶部边框使用主题灰色
              width: 1.0, // 边框宽度1像素
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子组件分散对齐（两端对齐）
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            // 左侧价格显示
            Text(
              isWeight ? 
              "${currentProduct.commodityUnit}${finalPriceState.formattedTotalPrice}/${currentProduct.currencyUnit}" : 
              "${currentProduct.currencyUnit}${finalPriceState.formattedTotalPrice}", // 价格文本
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700), // 字体大小16，粗体700
            ),
            // 右侧操作区
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 数量调节器
              isWeight ? _buildWeightTextField(currentProduct) : _buildStepper(currentProduct),            
              const SizedBox(width: 16), // 间距16像素 // 间距16像素
                // 确认按钮
                _buildConfirmButton(currentProduct, finalPriceState),
              ],
            )
          ],
        ),
      ),
    );
  }

/// 构建确认按钮
/// 🔧 关键修复：监听套餐选择状态和Modifier配置状态的变化
Widget _buildConfirmButton(ProductItem currentProduct, ProductFinalPriceState finalPriceState) {
  // 根据编辑模式确定按钮文字
  final buttonText = widget.isEditMode ? '更新' : context.locale.confirm;
  
      // 套餐
      if (currentProduct.commodityType == 3) {
          return SizedBox(
          width: 104,
          height: 48,
          child: ElevatedButton(
            onPressed: () => _handleConfirmSubmitWithTabAwareness(currentProduct, finalPriceState), // 🔧 验证失败时禁用按钮
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16,
              ),
              minimumSize: const Size(104, 48),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(buttonText),
          ),
        );

      } else {
        //单品
        return SizedBox(
          width: 104,
          height: 48,
          child: ElevatedButton(
            onPressed: () => _handleConfirmSubmitWithTabAwareness(currentProduct, finalPriceState), // 🔧 验证失败时禁用按钮
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
              padding: const EdgeInsets.symmetric(
                vertical: 12,
                horizontal: 16,
              ),
              minimumSize: const Size(104, 48),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(buttonText),
          ),
        );
      }
}

/// 根据当前标签页智能选择提交逻辑
/// 这是修复套餐验证错误调用规格提交的核心方法
Future<void> _handleConfirmSubmitWithTabAwareness(
  ProductItem currentProduct, 
  ProductFinalPriceState finalPriceState
) async {
  final currentTab = ref.read(currentActiveTabProvider);
  final bool isComboProduct = currentProduct.commodityType == 3;
  
 //debugPrint('🔧 确认按钮点击 - 当前标签页: $currentTab, 是否套餐: $isComboProduct');
  
  if (isComboProduct) {
        await _handleComboSubmitWithValidation();
  } else {
    // 单品商品的处理逻辑
        // await _handleSpecConfirmSubmitWithValidation(currentProduct, finalPriceState);
        await _handleSpecSubmitWithValidation();
  }
}

/// 处理规格提交（使用智能提交）
Future<void> _handleSpecSubmitWithValidation() async {
  try {
    final currentProduct = widget.pageData.product;
    final finalPriceState = ref.read(productFinalPriceControllerProvider(currentProduct));
    final currentCustomNotes = ref.read(customNotesProvider);

    // 获取用户当前的选择状态
    final specSelectionState = ref.read(productSpecSelectionControllerProvider('product_spec'));
    
    // 将用户选择状态同步到规格组数据中
    final updatedSpecGroups = _syncUserSelectionToSpecGroups(widget.pageData.specGroups, specSelectionState);

    final modeText = widget.isEditMode ? '更新' : '添加';
   //debugPrint('🔧 规格验证通过，开始智能${modeText}');
    
    // 使用智能提交方法
    await ref.read(productSpecServiceProvider('product_spec')).submitSpecToCartApiSmart(
      product: currentProduct,
      specGroups: updatedSpecGroups,
      quantity: finalPriceState.amount,
      remark: _getProductRemark(),
      context: context,
      customNotes: currentCustomNotes,
      isEditMode: widget.isEditMode,
    );
    
    // 关闭页面
    if (mounted) {
      KPSlidePopup.dismissFromTarget();
    }
    
  } catch (e) {
    final modeText = widget.isEditMode ? '更新' : '添加';
   //debugPrint('🔧 规格${modeText}失败: $e');
    _showSpecErrorMessage('规格提交失败: ${e.toString()}');
  }
}

/// 将用户选择状态同步到规格组数据中
List<ProductSpecGroup> _syncUserSelectionToSpecGroups(
  List<ProductSpecGroup> originalSpecGroups, 
  ProductSpecSelectionData selectionState
) {
  return originalSpecGroups.map((group) {
    final groupIndex = originalSpecGroups.indexOf(group);// 获取规格组在列表中的索引
    
    final updatedItems = group.items.map((item) {
      final itemIndex = group.items.indexOf(item);// 获取规格项在组内的索引

      // 生成四元组复合键：groupId_itemId_groupIndex_itemIndex
      final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
        group.id, item.id, groupIndex, itemIndex);
      
      // 检查该项是否被用户选中（通过复合键匹配）
      final isSelected = selectionState.selectedItemIds.contains(itemKey);
      
      // 返回更新了选择状态的规格项
      return item.copyWith(isSelected: isSelected);
    }).toList();
    
    return group.copyWith(items: updatedItems);
  }).toList();
}

/// 处理套餐提交
/// 专门处理套餐页面的提交逻辑
/// 处理套餐提交（使用智能提交）
Future<void> _handleComboSubmitWithValidation() async {
  try {
    final combo = _getCurrentCombo();
    if (combo == null) {
      _showSpecErrorMessage('套餐数据不存在，无法提交');
      return;
    }
    
    final comboQuantity = _getCurrentComboQuantity();
    final currentCustomNotes = ref.read(customNotesProvider);

    final modeText = widget.isEditMode ? '更新' : '添加';
   //debugPrint('🔧 套餐验证通过，开始智能${modeText}');
    
    // 使用智能提交方法
    await ref.read(productSpecServiceProvider('product_spec')).submitComboToCartApiSmart(
      product: widget.pageData.product,
      combo: combo,
      quantity: comboQuantity,
      remark: _getComboRemark(),
      context: context,
      customNotes: currentCustomNotes,
      isEditMode: widget.isEditMode,
    );
    
    // 关闭页面
    if (mounted) {
      KPSlidePopup.dismissFromTarget();
    }
    
  } catch (e) {
    final modeText = widget.isEditMode ? '更新' : '添加';
   //debugPrint('🔧 套餐${modeText}失败: $e');
    _showSpecErrorMessage('套餐提交失败: ${e.toString()}');
  }
}

/// 获取套餐备注 - 参考单商品的备注处理方式
String? _getComboRemark() {
  try {
    final List<String> allRemarks = [];
    
    //debugPrint('🔧 开始收集套餐备注信息...');
    
    // 1. 🆕 从Special Price页面的Custom notes获取
    final customNotes = ref.read(customNotesProvider);
    if (customNotes.trim().isNotEmpty) {
      allRemarks.add(customNotes.trim());
      //debugPrint('🔧 从Special Price页面添加Custom notes: "$customNotes"');
    }
    
    // 2. 从所有Modifier配置中收集备注（参考单商品的RemarkData构建）
    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    //debugPrint('🔧 Modifier配置数量: ${modifierConfigs.length}');
    
    for (final entry in modifierConfigs.entries) {
      final configData = entry.value;
      
      if (configData.isConfigured && configData.formData.isNotEmpty) {
        // 🆕 模仿单商品的RemarkData构建逻辑
        final selectedRemarkTags = configData.formData['selectedRemarkTags'] as List? ?? [];
        final customRemark = configData.formData['customRemark'] as String? ?? '';
        
        //debugPrint('🔧 配置${entry.key}的备注数据:');
        //debugPrint('🔧   备注标签: $selectedRemarkTags');
        //debugPrint('🔧   自定义备注: "$customRemark"');
        
        // 🆕 按照单商品的方式处理：先添加标签，再添加自定义备注
        if (selectedRemarkTags.isNotEmpty) {
          final tags = selectedRemarkTags.cast<String>();
          allRemarks.addAll(tags);
          //debugPrint('🔧 从${entry.key}添加备注标签: $tags');
        }
        
        if (customRemark.trim().isNotEmpty) {
          allRemarks.add(customRemark.trim());
          //debugPrint('🔧 从${entry.key}添加自定义备注: "$customRemark"');
        }
      }
    }
    
    // 3. 🆕 从主规格选择控制器获取备注（如果有的话）
    try {
      final specSelectionState = ref.read(productSpecSelectionControllerProvider('product_spec'));
      
      // 模仿单商品的RemarkData构建
      if (specSelectionState.selectedRemarkTags.isNotEmpty) {
        allRemarks.addAll(specSelectionState.selectedRemarkTags);
        //debugPrint('🔧 从主控制器添加备注标签: ${specSelectionState.selectedRemarkTags}');
      }
      
      if (specSelectionState.customRemark.trim().isNotEmpty) {
        allRemarks.add(specSelectionState.customRemark.trim());
        //debugPrint('🔧 从主控制器添加自定义备注: "${specSelectionState.customRemark}"');
      }
    } catch (e) {
      //debugPrint('🔧 获取主控制器备注失败: $e');
    }
    
    // 4. 🆕 去重并按照单商品的方式，用逗号连接所有备注
    final uniqueRemarks = allRemarks.toSet().toList(); // 去重
    final finalRemark = uniqueRemarks.isNotEmpty ? uniqueRemarks.join(',') : null;
    
    //debugPrint('🔧 ====== 套餐备注收集完成 ======');
    //debugPrint('🔧 收集到的备注列表: $allRemarks');
    //debugPrint('🔧 去重后备注列表: $uniqueRemarks');
    //debugPrint('🔧 最终备注（逗号分隔）: $finalRemark');
    //debugPrint('🔧 ===============================');
    
    return finalRemark;
    
  } catch (e) {
    //debugPrint('🔧 获取套餐备注失败: $e');
    return null;
  }
}

/// 可以从表单中获取，或默认为1
int _getCurrentComboQuantity() {
  // 如果你的界面有套餐数量选择器，在这里获取
  // 暂时返回1作为默认值
  return 1;
}

/// 获取当前套餐数据
ProductCombo? _getCurrentCombo() {
  return widget.pageData.combo;
}

/// 获取商品备注
String? _getProductRemark() {
  // 如果您的UI有备注输入功能，在这里获取
  // 否则返回null
  return null;
}

/// 显示规格处理错误消息
void _showSpecErrorMessage(String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
      duration: const Duration(seconds: 3),
    ),
  );
}

  Widget _buildStepper(ProductItem currentProduct) {
    final finalPriceState = ref.watch(productFinalPriceControllerProvider(currentProduct));
    return SizedBox(
      width: 120, // 控制宽度
      height: 30,
      child: KPFormBuilderStepper(
        name: 'product_quantity', // 表单字段名
        initialValue: finalPriceState.amount.toInt(), // 初始值
        min: 1, // 最小值为1
        max: 15, // 最大值
        step: 1, // 步长
        // 表单装饰
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
          alignLabelWithHint: true,
        ),
        // 数量变化回调
        onStepperChanged: (value) {
          // 更新Riverpod状态
          ref.read(productFinalPriceControllerProvider(widget.pageData.product).notifier)
            .setAmount(value);
        },
        // 数字键盘事件
        showNumberKeyboardEvent: (value) => _showQuantityKeyboard(context, "product_quantity", value),
      ),
    );
  }

  /// 称重
  Widget _buildWeightTextField(ProductItem currentProduct) {
    final finalPriceState = ref.watch(productFinalPriceControllerProvider(currentProduct));
    return Container(
      color: Colors.white,
      width: 120,
      height: 48,
      child: FormBuilderTextField(
        name: "product_quantity_weight",
        initialValue: finalPriceState.amount.toString(), // 初始值
        readOnly: true, // 设为只读,防止系统键盘弹出
        textAlign: TextAlign.center,
        style: KPFontStyle.bodyXSmall.copyWith(color: KPColors.textGrayPrimary),
        decoration: InputDecoration(
          labelText: "重量(${currentProduct.commodityUnit})",          
          // 统一边框样式
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: KPColors.borderGrayDarkDarkest),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: KPColors.borderGrayDarkDarkest),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: KPColors.borderBrandPressed),
          ),
          // suffixIcon: const Icon(Icons.keyboard_arrow_down, size: 18),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12.0),
        ),
        validator: (value) {
        if (value == null || value.isEmpty) {
          return '请输入数量';
        }
        final number = int.tryParse(value);
        if (number == null) {
          return '请输入有效数字';
        }
        if (number < 1) {
          return '最小数量为1';
        }
        if (number > 15) {
          return '最大数量为15';
        }
        return null;
      },
      // 在 onTap 中调用键盘
      onTap: () {
        final field = _formKey.currentState?.fields['product_quantity_weight'];
        final currentValue = num.tryParse(field?.value.toString() ?? "") ?? 1.0;
        
        _showQuantityKeyboard(context, "product_quantity_weight", currentValue, );
      },
        onChanged: (val) {
         debugPrint("重量:$val");

          if (val != null) {
            final weight = double.tryParse(val) ?? 1.0;
            ref.read(productFinalPriceControllerProvider(currentProduct).notifier).setAmount(weight);
          }
        },
        ),
    );
  }

  /// 显示数量输入键盘(通用版本)
void _showQuantityKeyboard(BuildContext context, String fieldName , num currentValue) {
  final bool isWeight = fieldName == "product_quantity_weight";
  final String title = isWeight ? "请输入重量(kg)" : "请输入数量";

  showDialog(
    
    context: context,
    builder: (BuildContext dialogContext) {
      return Dialog(
        backgroundColor: Colors.white,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                KPNumberKeyboard(
                  onConfirmPressed: (newValue) {
                    // 1.安全检查
                    if (newValue == null) {
                      Navigator.of(dialogContext).pop();
                      return;
                    }

                    // 2.获取对应的表单字段
                    final field = _formKey.currentState?.fields[isWeight ? "product_quantity_weight" : "product_quantity"];
                    if (field == null) {
                      Navigator.of(dialogContext).pop();
                      return;
                    }                   

                    // 3.更新
                    if (isWeight) {
                      // 如果是重量,更新为字符串的小数
                      field.didChange(newValue.toString());
                      
                    } else {
                      // 如果是数量, 更新为整数 ,并同步riverpod
                      final intValue = newValue.toInt();
                      field.didChange(intValue);
                      // ref.read(productFinalPriceControllerProvider(widget.pageData.product).notifier)
                      //   .setAmount(intValue);
                    }
                    ref.read(productFinalPriceControllerProvider(widget.pageData.product).notifier)
                        .setAmount(newValue);
                    
                    // 4.关闭弹窗
                    Navigator.of(dialogContext).pop();
                  },
                  buttonWidth: 101,
                  buttonHeight: 54,
                  title: title,                
                  // isDecimal: isWeight,// 允许小数输入
                  minValue: 0.0,
                  maxValue: 999999.0,
                  showDot: true,
                  value: currentValue.toDouble(),
                  onCancelPressed: () {
                    // Navigator.of(dialogContext).pop();
                  },
                ),
              ],
            ),
          ),
        ),
      );
    }
  );
}

}

// 逻辑块拆分成独立的 ConsumerWidget,只 watch 它们自己真正需要的数据。
class ProductTitleText extends ConsumerWidget {
  final ProductItem currentProduct;
  const ProductTitleText({super.key, required this.currentProduct});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productName = ref.watch(
      productFinalPriceControllerProvider(currentProduct).select((state) => state.product.commodityName)
    );
    return Text(
      productName,
      style: KPFontStyle.headingLarge
                        .copyWith(color: KPColors.borderGrayDarkDarkest),
    );// TODO: implement build
  }
  
}

