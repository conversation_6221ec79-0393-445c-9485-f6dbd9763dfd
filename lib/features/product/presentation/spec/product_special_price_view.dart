import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/product/presentation/spec/product_final_price_controller.dart';
import '../../../../common/components/index.dart';
import '../../../../common/components/kp_async_value_widget.dart';
import '../../../cart/domain/discount_item.dart';
import '../../../cart/presentation/cart_action/index.dart';
import '../../../store/application/store_prepared_reason_cache.dart';
import '../../application/product_discount_service.dart';
import 'product_special_price_controller.dart';

import '../../../../assets/assets.gen.dart';
import '../../../../common/components/kp_number_keyboard.dart';
import '../../domain/product_item.dart';
import '../../domain/product_discount_option.dart';

class ProductSpecialPriceView extends ConsumerStatefulWidget {
  /// 当前正在操作的产品对象
  final ProductItem currentProduct;

  const ProductSpecialPriceView({super.key, required this.currentProduct});

  @override
  ConsumerState createState() => _ProductSpecialPriceViewState();
}

/// `ProductSpecialPriceView` 的状态管理类
class _ProductSpecialPriceViewState
    extends ConsumerState<ProductSpecialPriceView> {
  /// 表单的全局 Key，用于访问、验证和重置表单状态。
  final _discountFormKey = GlobalKey<FormBuilderState>();

  /// 备注输入框的焦点节点，可以用于控制输入框的焦点。
  final FocusNode _focusNode = FocusNode();

  /// 折扣原因备注的文本编辑器控制器。
  final TextEditingController _reasonEditController = TextEditingController();

  // 🎯 混合方案：局部变量作为可靠数据源
  ProductDiscountOption? _localSelectedDiscount;
  List<String> _localSelectedReasons = []; //  多选原因列表

  ProductItem get product => widget.currentProduct;

  /// 销毁资源，防止内存泄漏。
  @override
  void dispose() {
    _focusNode.dispose();
    _reasonEditController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // 🎯 初始化时同步Provider状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncFromProvider();
    });
  }

  /// 🔄 从Provider同步状态到局部变量
  void _syncFromProvider() {
    final providerDiscount = ref.read(selectedDiscountOptionProvider);
    final providerReasons =
        ref.read(selectedDiscountReasonsProvider); //  多选原因

    if (_localSelectedDiscount != providerDiscount) {
      setState(() {
        _localSelectedDiscount = providerDiscount;
      });
    }

    //  同步多选原因
    if (_localSelectedReasons.toString() != providerReasons.toString()) {
      setState(() {
        _localSelectedReasons = [...providerReasons];
      });
    }
  }

  /// 🎯 统一的折扣选择更新方法
  void _updateDiscountSelection(ProductDiscountOption? option) {
    //debugPrint('🔍 选择的折扣: $option');

    // 1. 更新局部变量（立即生效）
    setState(() {
      _localSelectedDiscount = option;
    });

    // 2. 更新Provider（全局共享）
    ref.read(selectedDiscountOptionProvider.notifier).update(option);

    // 3. 验证状态同步
    //debugPrint('🔍 折扣选择更新完成');
  }

  ///  统一的多选原因更新方法
  void _updateMultipleReasonsSelection(List<String> reasons) {
    //debugPrint('🔍 更新多选原因: $reasons');

    // 1. 更新局部变量
    setState(() {
      _localSelectedReasons = [...reasons];
    });

    // 2. 更新Provider
    ref.read(selectedDiscountReasonsProvider.notifier).update(reasons);
  }

  /// 🔧 统一的状态清除方法
  void _clearAllStates() {
    // 1. 清空局部变量
    setState(() {
      _localSelectedDiscount = null;
      _localSelectedReasons = []; //  清空多选原因
    });

    // 2. 清空表单字段
    _reasonEditController.clear();

    // 3. 重置所有Provider状态
    ref.read(selectedDiscountOptionProvider.notifier).update(null);
    ref.read(selectedDiscountReasonsProvider.notifier).clear(); //  清空多选原因
    ref.read(customDiscountProvider.notifier).update(null);
    ref.read(realSalesPriceProvider.notifier).clear();
    ref.read(customNotesProvider.notifier).clear(); //  清空自定义备注
  }

  /// 构建视图的根组件。
  @override
  Widget build(BuildContext context) {
    // 🎯 监听Provider变化，同步到局部变量
    ref.listen(selectedDiscountOptionProvider, (previous, next) {
      if (_localSelectedDiscount != next) {
        setState(() {
          _localSelectedDiscount = next;
        });
      }
    });

    //  监听多选原因变化
    ref.listen(selectedDiscountReasonsProvider, (previous, next) {
      if (_localSelectedReasons.toString() != next.toString()) {
        setState(() {
          _localSelectedReasons = [...next];
        });
      }
    });

    // 使用 GestureDetector 可以在点击空白区域时取消输入框的焦点。
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      // 使用 FormBuilder 包裹整个视图，以便统一管理所有表单字段。
      child: FormBuilder(
        key: _discountFormKey,
        child: Column(
          children: [
            _buildSalesView(product),
            const SizedBox(height: 24),
            _buildDiscountTtileView(),
            const SizedBox(height: 8),
            _buildDiscountView(),
            const SizedBox(height: 24),
            _buildReasonView(),
          ],
        ),
      ),
    );
  }

  /// 构建售价显示区域
  Widget _buildSalesView(ProductItem product) {
    return GestureDetector(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sales',
              style: KPFontStyle.headingMedium
                  .copyWith(color: KPColors.textGrayPrimary),
            ),
            Row(
              children: [
                TotalPriceText(currentProduct: widget.currentProduct),
                const SizedBox(width: 12),
                Assets.images.iconEdit.svg(width: 20, height: 20)
              ],
            )
          ],
        ),
      ),
      onTap: () {
        _showPriceInputDialog(context);
      },
    );
  }

  ///  显示价格输入对话框（修改真实售价）
  void _showPriceInputDialog(BuildContext context) {
    final priceState =
        ref.read(productFinalPriceControllerProvider(widget.currentProduct));
    final currentPrice = priceState.actualSalesPrice;

    final title = '修改售价 (${product.currencyUnit})';

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.white,
          child: Container(
            width: 335,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: KPNumberKeyboard(
                buttonWidth: 101,
                buttonHeight: 54,
                showDot: true,
                title: title,
                hintText: 'Enter New Price',
                value: currentPrice, //  显示当前价格作为初始值
                onConfirmPressed: (newValue) {
                  if (newValue != null) {
                    //  更新真实售价状态
                    ref
                        .read(productFinalPriceControllerProvider(
                                widget.currentProduct)
                            .notifier)
                        .setRealSalesPrice(newValue.toDouble());
                  }
                  Navigator.of(dialogContext).pop();
                },
                onCancelPressed: () {
                  // Navigator.of(dialogContext).pop();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  /// 显示自定义折扣输入对话框
  void _showNumberInputDialog(Function(num) onConfirm, BuildContext context) {
    final title = '${context.locale.customDiscount}(${product.currencyUnit})';
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          backgroundColor: Colors.white,
          child: Container(
            width: 335,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: KPNumberKeyboard(
                buttonWidth: 101,
                buttonHeight: 54,
                showDot: true,
                title: title,
                hintText: 'Enter Custom Price',
                onConfirmPressed: (newValue) {
                  if (newValue != null) {
                    // 🔄 Custom折扣：直接设置为真实售价
                    // ref.read(productFinalPriceControllerProvider(widget.currentProduct).notifier)
                    //     .setRealSalesPrice(newValue.toDouble());
                    onConfirm(newValue);
                  }
                  Navigator.of(dialogContext).pop();
                },
                onCancelPressed: () {
                  // Navigator.of(dialogContext).pop();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  ///  显示自定义折扣输入对话框（复用Cart组件）
  void _showCustomDiscountDialog(
      Function(num) onConfirm, BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return const CartCustomDiscount();
      },
    ).then((value) {
      if (value != null) {
        final discountItem = value as DiscountItem;

        //  将DiscountItem转换为我们需要的格式
        if (discountItem.type == 1) {
          // 百分比折扣：返回百分比值
          onConfirm(discountItem.discount);
        } else {
          // 固定金额折扣：返回负数表示金额减免
          onConfirm(-discountItem.discount);
        }
      }
    });
  }

  ///  获取自定义折扣显示文本（复用Cart逻辑）
  String _getCustomDiscountText(num? customValue) {
    if (customValue == null) return '';

    if (customValue < 0) {
      // 固定金额折扣
      return ' (-S\$${(-customValue).toStringAsFixed(2)})';
    } else {
      // 百分比折扣
      return ' (${customValue.toStringAsFixed(0)}%)';
    }
  }

  /// 构建折扣区域的标题行，包含标题、提示和"清除"按钮。
  Widget _buildDiscountTtileView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Text(
            context.locale.discount,
            style: KPFontStyle.headingXSmall
                .copyWith(color: KPColors.textGrayPrimary),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              context.locale.noDiscountForAddOns,
              style: KPFontStyle.bodySmall
                  .copyWith(color: KPColors.textGraySecondary),
            ),
          ),
          // "清除"按钮，使用 onTap 扩展方法添加点击事件。
          Text(
            context.locale.clear,
            style: const TextStyle(
              fontSize: 14,
              color: KPColors.textBrandDefault,
              fontWeight: FontWeight.w700,
            ),
          ).onTap(() {
            // 🔧 使用统一的状态清除方法
            _clearAllStates();
          }),
        ],
      ),
    );
  }

  /// 🎯 构建折扣选项视图，使用混合方案
  Widget _buildDiscountView() {
    final discountOptions = ref.watch(discountOptionsProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Wrap(
        spacing: 8, // 水平间距
        runSpacing: 8, // 垂直间距
        children: discountOptions.map((option) {
          // 🎯 使用局部变量判断选中状态（最可靠）
          final isSelected = _localSelectedDiscount == option;

          Widget labelWidget;
          // 对"Custom"选项进行特殊处理
          if (option.isCustom) {
            // 使用 Consumer 仅重绘自定义折扣的文本部分
            labelWidget = Consumer(
              builder: (context, ref, child) {
                final customValue = ref.watch(customDiscountProvider);
                String text = option.discount; // 默认显示 "Custom"
                if (customValue != null) {
                  text =
                      'Custom${_getCustomDiscountText(customValue)}'; //  使用新的文本格式
                }
                return Text(
                  text,
                  style: KPFontStyle.headingXSmall.copyWith(
                    color: isSelected
                        ? KPColors.textBrandDefault
                        : KPColors.textGrayPrimary,
                  ),
                );
              },
            );
          } else {
            // 普通折扣选项的文本
            labelWidget = Text(
              option.discount, // 使用 option.discount
              style: KPFontStyle.headingXSmall.copyWith(
                color: isSelected
                    ? KPColors.textBrandDefault
                    : KPColors.textGrayPrimary,
              ),
            );
          }

          return Theme(
            data: Theme.of(context).copyWith(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: ChoiceChip(
              pressElevation: 0,
              label: labelWidget,
              selected: isSelected,
              onSelected: (selected) {
                //debugPrint('🔍 === 折扣选择交互开始 ===');
                //debugPrint('🔍 selected: $selected, option: $option');

                if (selected) {
                  if (option.isCustom) {
                    // _showNumberInputDialog((newValue) {
                    //   //debugPrint('🔍 设置自定义折扣选项: $option, 值: $newValue');
                    //   ref.read(customDiscountProvider.notifier).update(newValue);
                    //   _updateDiscountSelection(option);
                    // }, context);

                    //  使用复用的自定义折扣对话框
                    _showCustomDiscountDialog((newValue) {
                      ref
                          .read(customDiscountProvider.notifier)
                          .update(newValue);
                      _updateDiscountSelection(option);
                    }, context);
                  } else {
                    //debugPrint('🔍 设置普通折扣选项: $option');
                    ref.read(customDiscountProvider.notifier).update(null);
                    _updateDiscountSelection(option);
                  }
                } else {
                  //debugPrint('🔍 取消选择折扣');
                  ref.read(customDiscountProvider.notifier).update(null);
                  _updateDiscountSelection(null);
                }
                //debugPrint('🔍 === 折扣选择交互结束 ===');
              },
              backgroundColor: const Color(0xFFF4F5F7),
              selectedColor: KPColors.fillBrandLightest,
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: isSelected
                      ? KPColors.borderBrandDefault
                      : Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              showCheckmark: false,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 🔄 构建原因输入区域 - 参考CartActionOrderNotes的缓存优先模式
  Widget _buildReasonView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.locale.reason,
            style: KPFontStyle.headingXSmall
                .copyWith(color: KPColors.textGrayPrimary),
          ),
          const SizedBox(height: 8),
          _buildReasonTagsWithCache(), // 🔄 使用缓存优先的原因标签
          const SizedBox(height: 8),
          _buildCustomNotesTextField(), //  重构的自定义备注输入框
        ],
      ),
    );
  }

  /// 🔄 使用缓存优先的原因标签构建器 - 参考CartActionOrderNotes
  Widget _buildReasonTagsWithCache() {
    // 先尝试从缓存获取数据
    final cache = ref.read(storePreparedReasonCacheProvider);
    final discountReasons = cache.getDiscountReasons();

    if (discountReasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: productDiscountReasonsProvider,
        dataBuilder: (context, ref, data) {
          return _buildReasonTagsWidget(
              data.map((e) => e.preparedReasonName).toList());
        },
      );
    }

    // 如果缓存中有数据，直接使用（无网络请求）
    return _buildReasonTagsWidget(
        discountReasons.map((e) => e.preparedReasonName).toList());
  }

  /// 🎯 构建原因标签组件 - 完全按照CartActionOrderNotes的多选实现
  Widget _buildReasonTagsWidget(List<String> reasonNames) {
    return SingleChildScrollView(
      // 🎯 添加滚动支持，和CartActionOrderNotes一致
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormBuilder(
            child: FormBuilderField<List<String>>(
              name: 'discount_reasons', // 🔧 多选字段名
              initialValue: _localSelectedReasons, // 🔧 使用局部状态作为初始值
              builder: (FormFieldState<List<String>> field) {
                return Wrap(
                  spacing: 8,
                  runSpacing: 4, // 🎯 和CartActionOrderNotes保持一致的间距
                  children: reasonNames.map((reasonName) {
                    // 🎯 完全按照CartActionOrderNotes的逻辑判断选中状态
                    final isSelected =
                        field.value?.contains(reasonName) ?? false;

                    return ChoiceChip(
                      label: Text(
                        reasonName,
                        style: KPFontStyle.headingXSmall.copyWith(
                          color: isSelected
                              ? KPColors.textBrandDefault
                              : KPColors.textGrayPrimary,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        //debugPrint('🔍 === 多选原因交互开始 ===');
                        //debugPrint('🔍 selected: $selected, reason: $reasonName');

                        // 🔧 完全按照CartActionOrderNotes的多选逻辑
                        final currentValue =
                            List<String>.from(field.value ?? []);
                        if (selected) {
                          currentValue.add(reasonName);
                        } else {
                          currentValue.remove(reasonName);
                        }

                        // 🔧 更新FormBuilder字段
                        field.didChange(currentValue);

                        // 🔧 更新多选状态 - 使用正确的方法名
                        _updateMultipleReasonsSelection(currentValue);

                        //debugPrint('🔍 当前选中原因: $currentValue');
                        //debugPrint('🔍 === 多选原因交互结束 ===');
                      },
                      backgroundColor:
                          Colors.white, // 🎨 和CartActionOrderNotes一致
                      selectedColor: KPColors.fillBrandLightest,
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          color: isSelected
                              ? KPColors.borderBrandDefault
                              : KPColors
                                  .borderGrayLightDark, // 🎨 和CartActionOrderNotes一致
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      showCheckmark: false,
                    );
                  }).toList(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  ///  构建自定义备注输入框 - 参考CartActionOrderNotes的TextField实现
  Widget _buildCustomNotesTextField() {
    final currentNotes = ref.watch(customNotesProvider); // 🔧 监听状态变化
  
   // debugPrint('🔧 构建自定义备注输入框 - 当前状态: "$currentNotes"');

    return TextField(
      focusNode: _focusNode,
      controller: _reasonEditController,
      maxLength: 64, // 🎯 参考CartActionOrderNotes的字符限制
      maxLengthEnforcement: MaxLengthEnforcement.enforced,
      // 🎨 不显示字符计数器 - 参考CartActionOrderNotes
      buildCounter: (
        BuildContext context, {
        required int currentLength,
        required bool isFocused,
        int? maxLength,
      }) =>
          null,
      decoration: InputDecoration(
        // 🎨 参考CartActionOrderNotes的边框样式
        border: OutlineInputBorder(
          borderSide: const BorderSide(
              color: KPColors.borderGrayLightDarkest, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide:
              const BorderSide(color: KPColors.borderBrandDefault, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintText: context.locale.customNotes,
        hintStyle: KPFontStyle.headingXSmall.copyWith(
          color: KPColors.textGraySecondary,
        ),
      ),
      // 🛡️ 不允许输入表情 - 参考CartActionOrderNotes的输入限制
      inputFormatters: [NoEmojiInputFormatter()],
      onChanged: (value) {
       // debugPrint('🔧 Special Price 输入框变化: "$value"');
        //  保存 Custom notes 内容
        ref.read(customNotesProvider.notifier).update(value);
        // // 🔧 验证更新后的状态
        final updatedValue = ref.read(customNotesProvider);
       // debugPrint('🔧 保存Custom notes: "$updatedValue"');
      },
    );
  }
}

/// 总价文本组件
///
/// 通过 Riverpod 订阅 `productFinalPriceControllerProvider` 来动态显示最终价格。
class TotalPriceText extends ConsumerWidget {
  final ProductItem currentProduct;
  const TotalPriceText({super.key, required this.currentProduct});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final priceState =
        ref.watch(productFinalPriceControllerProvider(currentProduct));

    return Text(
      priceState.formattedActualSalesPrice, // ✅ 显示真实售价
      style: KPFontStyle.amountMedium.copyWith(
        color: KPColors.textGrayPrimary,
        fontWeight: FontWeight.w600,
      ),
    );
  }
}
