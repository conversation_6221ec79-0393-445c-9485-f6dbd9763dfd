import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/domain/product_spec_selection_data.dart';

part 'product_spec_selection_controller.g.dart';

/// 商品规格选择状态管理器
/// 负责管理用户的规格选择状态，计算价格和验证表单
/// [instanceId] 实例ID，用于区分不同的使用场景
@riverpod
class ProductSpecSelectionController extends _$ProductSpecSelectionController {
  @override
  ProductSpecSelectionData build(String instanceId) {
    // 根据instanceId初始化不同的状态实例
    return const ProductSpecSelectionData();
  }

  /// 初始化默认选中项
  /// 根据规格组中的默认选中项自动设置初始状态
  void initializeDefaultSelections(List<ProductSpecGroup> allGroups) {
   //debugPrint('🔧 开始初始化默认选中项');

    // 打印每个规格组的详细信息
     for (int i = 0; i < allGroups.length; i++) {
       final group = allGroups[i];
      //debugPrint('🔧 规格组[$i]: ${group.name}, 必选: ${group.isRequired}, 项数: ${group.items.length}');
       
       for (int j = 0; j < group.items.length; j++) {
         final item = group.items[j];
        //debugPrint('🔧   规格项[$j]: ${item.name}, 默认选中: ${item.isSelected}, 价格: ${item.additionalPrice}');
       }
     }
    
    final List<String> selectedItemIds = [];
    final Map<String, int> itemQuantities = {};
    
    for (int groupIndex = 0; groupIndex < allGroups.length; groupIndex++) {
      final group = allGroups[groupIndex];
     //debugPrint('🔧 处理规格组: ${group.name} (必选: ${group.isRequired})');
      
      for (int itemIndex = 0; itemIndex < group.items.length; itemIndex++) {
        final item = group.items[itemIndex];
        
        // 🔧 关键逻辑：检查是否默认选中
        if (item.isSelected) {
          final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
            group.id, item.id, groupIndex, itemIndex);
          
          selectedItemIds.add(itemKey);
          itemQuantities[itemKey] = 1;
          
         //debugPrint('🔧 自动选中默认项: ${item.name} (${group.name})');
        }
      }
    }
    
    if (selectedItemIds.isNotEmpty) {
     //debugPrint('🔧 应用默认选中项: ${selectedItemIds.length} 个');
      
      // 更新状态
      _updateState(
        selectedItemIds: selectedItemIds,
        itemQuantities: itemQuantities,
        allGroups: allGroups,
      );
    } else {
     //debugPrint('🔧 没有默认选中项');
    }
  }

/// 从保存的数据中恢复选择状态
void restoreFromSavedData(Map<String, dynamic> savedData, List<ProductSpecGroup> specGroups) {
 //debugPrint('🔧 开始从保存数据恢复选择状态');
 //debugPrint('🔧 保存数据字段: ${savedData.keys.toList()}');
  
  // == 新增开始 == 🔧 完全使用新格式，移除兼容性处理
  // 直接从新格式中恢复数据
  final savedSelectedIds = savedData['selectedItemIds'] as List<dynamic>? ?? [];
  final selectedItemIds = savedSelectedIds.cast<String>().toList();
  
  final savedQuantities = savedData['itemQuantities'] as Map<String, dynamic>? ?? {};
  final itemQuantities = savedQuantities.map((key, value) => MapEntry(key, value as int));
  
  // 如果数据为空，回退到默认选中逻辑
  if (selectedItemIds.isEmpty) {
   //debugPrint('🔧 ⚠️ 保存数据为空，回退到默认选中逻辑');
    initializeDefaultSelections(specGroups);
    return;
  }
  // == 新增结束 ==
  
  // 恢复备注标签
  final savedRemarkTags = savedData['selectedRemarkTags'] as List<dynamic>? ?? [];
  final selectedRemarkTags = savedRemarkTags.cast<String>().toList();
  
  // 恢复自定义备注
  final customRemark = savedData['customRemark'] as String? ?? '';
  
 //debugPrint('🔧 恢复选中项: $selectedItemIds');
 //debugPrint('🔧 恢复数量: $itemQuantities');
  
  // 更新状态
  state = state.copyWith(
    selectedItemIds: selectedItemIds,
    itemQuantities: itemQuantities,
    selectedRemarkTags: selectedRemarkTags,
    customRemark: customRemark,
  );
  
  // 重新计算验证状态和价格
  _updateState(
    selectedItemIds: selectedItemIds,
    itemQuantities: itemQuantities,
    allGroups: specGroups,
  );
  
 //debugPrint('🔧 数据恢复完成');
}

  /// 单选模式：选择某个规格项（同组内其他项自动取消选择）
  void selectSingleItem({
    required int groupId,
    required int itemId,
    required int groupIndex,
    required int itemIndex,
    required List<ProductSpecGroup> allGroups,
  }) {
    final currentState = state;
    final newSelectedIds = List<String>.from(currentState.selectedItemIds);
    final newQuantities = Map<String, int>.from(currentState.itemQuantities);
    
    // 生成当前项的复合键
    final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
      groupId, itemId, groupIndex, itemIndex);
    
    // 找到目标组并清除该组的所有选择（通过groupIndex匹配）
    newSelectedIds.removeWhere((key) {
      final parsed = ProductSpecSelectionDataExtension.parseItemKey(key);
      return parsed['groupIndex'] == groupIndex;
    });
    newQuantities.removeWhere((key, value) {
      final parsed = ProductSpecSelectionDataExtension.parseItemKey(key);
      return parsed['groupIndex'] == groupIndex;
    });
    
    // 选择新的项目
    newSelectedIds.add(itemKey);
    newQuantities[itemKey] = 1;
    
    // 更新状态
    _updateState(
      selectedItemIds: newSelectedIds,
      itemQuantities: newQuantities,
      allGroups: allGroups,
    );
  }

  /// 多选模式：切换某个规格项的选择状态
  void toggleMultipleItem({
    required int itemId,
    required int groupIndex,
    required int itemIndex,
    required List<ProductSpecGroup> allGroups,
  }) {
    final currentState = state;
    final newSelectedIds = List<String>.from(currentState.selectedItemIds);
    final newQuantities = Map<String, int>.from(currentState.itemQuantities);
    
    // 找到该规格项所属的规格组
    final targetGroup = allGroups[groupIndex];
    final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
      targetGroup.id, itemId, groupIndex, itemIndex);
    
    if (newSelectedIds.contains(itemKey)) {
      // 取消选择
      newSelectedIds.remove(itemKey);
      newQuantities.remove(itemKey);
    } else {
      if (targetGroup.upperLimit > 0) {
        // 计算当前组的总选择数量
        int currentGroupTotal = 0;
        for (final key in newQuantities.keys) {
          final parsed = ProductSpecSelectionDataExtension.parseItemKey(key);
          if (parsed['groupIndex'] == groupIndex) {
            currentGroupTotal += newQuantities[key] ?? 0;
          }
        }
        
        // 检查是否会超过上限
        if (currentGroupTotal >= targetGroup.upperLimit) {
         //debugPrint('🚫 ${targetGroup.name}最多只能选择${targetGroup.upperLimit}个，当前已选${currentGroupTotal}个');
          return;
        }
      }

      // 添加选择
      newSelectedIds.add(itemKey);
      newQuantities[itemKey] = 1;
    }
    
    // 更新状态
    _updateState(
      selectedItemIds: newSelectedIds,
      itemQuantities: newQuantities,
      allGroups: allGroups,
    );
  }

  /// 步进器模式：设置某个规格项的数量
  void setItemQuantity({
    required int itemId,
    required int groupIndex,
    required int itemIndex,
    required int quantity,
    required List<ProductSpecGroup> allGroups,
  }) {
    final currentState = state;
    final newSelectedIds = List<String>.from(currentState.selectedItemIds);
    final newQuantities = Map<String, int>.from(currentState.itemQuantities);
    
    // 找到该规格项所属的规格组
    final targetGroup = allGroups[groupIndex];
    final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
      targetGroup.id, itemId, groupIndex, itemIndex);
    
    if (quantity <= 0) {
      // 数量为0时移除选择
      newSelectedIds.remove(itemKey);
      newQuantities.remove(itemKey);
    } else {
      // 数量>0时添加到选择中
      if (!newSelectedIds.contains(itemKey)) {
        newSelectedIds.add(itemKey);
      }
      newQuantities[itemKey] = quantity;
    }
    
    // 更新状态
    _updateState(
      selectedItemIds: newSelectedIds,
      itemQuantities: newQuantities,
      allGroups: allGroups,
    );
  }

  /// 更新选择的备注固定标签列表
  void updateSelectedRemarks(List<String> tags) {
    state = state.copyWith(selectedRemarkTags: tags);
  }

  void updateCustomRemark(String text) {
    state = state.copyWith(customRemark: text);
  }

  /// 清空所有选择
  void clearAllSelections() {
    state = const ProductSpecSelectionData();
  }

  /// 统一的状态更新方法
  /// == 修改开始 == 更新参数类型以支持复合键
  void _updateState({
    required List<String> selectedItemIds,
    Map<String, int>? itemQuantities,
    required List<ProductSpecGroup> allGroups,
  }) {
    final newQuantities = itemQuantities ?? state.itemQuantities;
    
    // 计算额外价格
    final additionalPrice = _calculateAdditionalPrice(
      selectedItemIds,
      newQuantities,
      allGroups,
    );
    
    // 验证必选组
    final groupValidation = _validateGroups(selectedItemIds, allGroups);
    final isValid = _isFormValid(groupValidation);
    
    // 更新状态
    state = ProductSpecSelectionData(
      selectedItemIds: selectedItemIds,
      itemQuantities: newQuantities,
      groupValidation: groupValidation,
      isValid: isValid,
      additionalPrice: additionalPrice,
      selectedRemarkTags: state.selectedRemarkTags,
      customRemark: state.customRemark,
    );
  }
  
  /// 计算额外价格
  double _calculateAdditionalPrice(
    List<String> selectedItemIds,
    Map<String, int> itemQuantities,
    List<ProductSpecGroup> allGroups,
  ) {
    double totalPrice = 0.0;
    
    for (final itemKey in selectedItemIds) {
      final parsed = ProductSpecSelectionDataExtension.parseItemKey(itemKey);
      final groupIndex = parsed['groupIndex']!;
      final itemId = parsed['itemId']!;
      
      if (groupIndex < allGroups.length) {
        final group = allGroups[groupIndex];
        final item = group.items.firstWhere(
          (item) => item.id == itemId,
          orElse: () => throw StateError('Item not found'),
        );
        
        final quantity = itemQuantities[itemKey] ?? 1;
        totalPrice += item.additionalPrice * quantity;
      }
    }
    
    return totalPrice;
  }

  /// 验证所有规格组的选择状态
  Map<int, bool> _validateGroups(
    List<String> selectedItemIds,
    List<ProductSpecGroup> allGroups,
  ) {
    final Map<int, bool> validation = {};
    
    for (int groupIndex = 0; groupIndex < allGroups.length; groupIndex++) {
      final group = allGroups[groupIndex];
      final totalQuantity = _calculateGroupTotalQuantity(group, groupIndex);
      final meetsLowerLimit = totalQuantity >= group.lowerLimit;
      final meetsUpperLimit = group.upperLimit <= 0 || totalQuantity <= group.upperLimit;
      validation[group.id] = meetsLowerLimit && meetsUpperLimit;
    }
    
    return validation;
  }

  /// 计算指定规格组的总数量
  int _calculateGroupTotalQuantity(ProductSpecGroup group, int groupIndex) {
    int totalQuantity = 0;
    final currentQuantities = state.itemQuantities;
    
    for (final key in currentQuantities.keys) {
      final parsed = ProductSpecSelectionDataExtension.parseItemKey(key);
      if (parsed['groupIndex'] == groupIndex) {
        totalQuantity += currentQuantities[key] ?? 0;
      }
    }
    
    return totalQuantity;
  }

  /// 判断整体表单是否有效
  bool _isFormValid(Map<int, bool> groupValidation) {
    return groupValidation.values.every((isValid) => isValid);
  }

  /// 获取指定规格项的当前状态
  SpecItemSelectionState getItemState(int itemId) {
    final currentState = state;
    
    // 遍历所有选中的复合键，查找匹配的itemId
    bool isSelected = false;
    int quantity = 0;
    
    for (final itemKey in currentState.selectedItemIds) {
      final parsed = ProductSpecSelectionDataExtension.parseItemKey(itemKey);
      if (parsed['itemId'] == itemId) {
        isSelected = true;
        quantity = currentState.itemQuantities[itemKey] ?? 1;
        break; // 找到匹配项就退出
      }
    }
    
    // 如果没有在选中列表中找到，再检查数量映射
    if (!isSelected) {
      for (final entry in currentState.itemQuantities.entries) {
        final parsed = ProductSpecSelectionDataExtension.parseItemKey(entry.key);
        if (parsed['itemId'] == itemId && entry.value > 0) {
          isSelected = true;
          quantity = entry.value;
          break;
        }
      }
    }
    
    return SpecItemSelectionState(
      isSelected: isSelected,
      quantity: quantity,
      enabled: true,
    );
  }

  /// 获取指定规格组的验证状态
  bool isGroupValid(int groupId) {
    return state.groupValidation[groupId] ?? true;
  }

  /// 从数据恢复选择状态
/// 作用：用于编辑模式下恢复用户之前的规格选择,提供一个简单的方法来批量恢复规格选择状态，避免逐个调用选择方法。
void restoreFromData(ProductSpecSelectionData data) {
  //debugPrint('🔄 恢复规格选择状态');
  //debugPrint('🔄 选中项数量: ${data.selectedItemIds.length}');
  //debugPrint('🔄 数量设置: ${data.itemQuantities.length}');
  state = data;
}

}
