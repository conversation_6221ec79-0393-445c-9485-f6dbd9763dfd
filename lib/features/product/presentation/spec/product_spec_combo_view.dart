import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/widget_extension.dart';

import '../../../../common/components/kp_selection_indicator.dart';
import '../../application/product_combo_modifier_config_service.dart';
import '../../domain/product_combo.dart';
import '../../application/product_combo_calculation_service.dart';
import '../../application/product_combo_validation_service.dart';
import '../../domain/product_combo_selection_state.dart';
import '../../domain/product_spec_group.dart';
import '../../domain/product_spec_validation_service.dart';
import 'product_combo_selection_controller.dart';
import 'product_select_spec_modifier_view.dart';

class ProductSpecComboView extends ConsumerStatefulWidget {
  /// 要展示的套餐数据模型，包含了所有分组和选项信息。
  final ProductCombo combo;

  /// 套餐确认提交回调
  final Function(Map<String, dynamic> comboData)? onComboSubmit;

  /// 关闭回调
  final VoidCallback? onClose;

  const ProductSpecComboView({
    super.key,
    required this.combo,
    this.onComboSubmit,
    this.onClose,
  });

  @override
  ConsumerState<ProductSpecComboView> createState() =>
      _ProductSpecComboViewState();
}

class _ProductSpecComboViewState extends ConsumerState<ProductSpecComboView> {
  final Map<String, GlobalKey> _modifierKeys = {};

  @override
  void initState() {
    super.initState();

    // // 延迟预初始化，避免与Controller冲突
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   // 延迟更长时间，确保Controller的初始化先完成
    //   Future.delayed(const Duration(milliseconds: 100), () {
    //     _preInitializeModifierConfigs();
    //   });
    // });
  }

  /// 预初始化Modifier配置（不选中商品）
  /// 根据API返回的defaultEnable字段预生成默认配置
  // void _preInitializeModifierConfigs() {
  //  //debugPrint('🔧 === 开始预初始化Modifier配置 ===');
  //   // 获取Modifier服务
  //   final modifierService =
  //       ref.read(productComboModifierConfigServiceProvider.notifier);
  //   final existingConfigs = ref.read(productComboModifierConfigServiceProvider);

  //   // 检查是否已有配置，避免覆盖Controller的初始化
  //   if (existingConfigs.isNotEmpty) {
  //    //debugPrint('🔧 检测到已有Modifier配置，跳过预初始化');
  //    //debugPrint('🔧 现有配置数量: ${existingConfigs.length}');

  //     final hasConfiguredDefaults =
  //         existingConfigs.values.any((config) => config.isConfigured);
  //     if (hasConfiguredDefaults) {
  //      //debugPrint('🔧 检测到已配置的默认选中商品，价格监听应该正常工作');
  //       return; // 直接返回，不再执行后续逻辑
  //     }
  //   } 

  //   // 收集所有需要预配置的商品
  //   final List<ProductComboChoice> allChoices = [];

  //   for (int groupIndex = 0;
  //       groupIndex < widget.combo.groups.length;
  //       groupIndex++) {
  //     final group = widget.combo.groups[groupIndex];
  //    //debugPrint('🔧 处理套餐分组: ${group.name}');

  //     for (int choiceIndex = 0;
  //         choiceIndex < group.items.length;
  //         choiceIndex++) {
  //       final choice = group.items[choiceIndex];

  //       // 🔧 为所有商品预生成配置（不仅仅是默认选中的）
  //       // 这样用户选择任何商品时都能立即获得预配置
  //       if (choice.specGroups.isNotEmpty) {
  //        //debugPrint('🔧 发现需要配置的商品: ${choice.product.commodityName}');
  //         allChoices.add(choice);
  //       }
  //     }
  //   }

  //   if (allChoices.isNotEmpty) {
  //     // 使用forceReset: false，避免覆盖已有配置
  //     final maxNeededModifiers = allChoices.length;
  //     final List<ProductComboChoice> modifierChoices = [];

  //     for (int i = 0; i < maxNeededModifiers; i++) {
  //       final choice = allChoices[i];
  //       final modifierChoice = ProductComboChoice(
  //         product: choice.product.copyWith(
  //           commodityId: choice.product.commodityId + i,
  //           commodityName: '${choice.product.commodityName} - 配置${i}',
  //         ),
  //         defaultEnable: choice.defaultEnable,
  //         specGroups: choice.specGroups,
  //       );
  //       modifierChoices.add(modifierChoice);
  //     }

  //     // 为每个商品生成默认配置
  //     // 需要为每个商品单独初始化，而不是批量初始化
  //     for (int i = 0; i < allChoices.length; i++) {
  //       final choice = allChoices[i];
  //       final commodityId = choice.product.commodityId;
        
  //       // 找到该商品对应的分组信息
  //       String? groupId;
  //       int? groupIndex;
  //       int? choiceIndex;
        
  //       // 遍历套餐分组找到该商品的位置信息
  //       for (int gIndex = 0; gIndex < widget.combo.groups.length; gIndex++) {
  //         final group = widget.combo.groups[gIndex];
  //         for (int cIndex = 0; cIndex < group.items.length; cIndex++) {
  //           final item = group.items[cIndex];
  //           if (item.product.commodityId == commodityId) {
  //             groupId = group.id;
  //             groupIndex = gIndex;
  //             choiceIndex = cIndex;
  //             break;
  //           }
  //         }
  //         if (groupId != null) break;
  //       }

  //       if (groupId != null && groupIndex != null && choiceIndex != null) {
  //        //debugPrint('🔧 初始化商品${i}的Modifier配置结构: ${choice.product.commodityName}');
  //         modifierService.initializeModifiersForProduct(
  //           groupId,
  //           commodityId,
  //           groupIndex,
  //           choiceIndex,
  //           [choice], // 每个商品单独初始化
  //           forceReset: false,
  //         );
  //       }
  //     }
  //   }

  //  //debugPrint('🔧 === Modifier配置预初始化完成 ===');
  // }

//   ///  为商品预生成Modifier配置
//   void _preGenerateModifierConfigForChoice(
//   ProductComboChoice choice,
//   ProductComboModifierConfigService modifierService,
//   String modifierId,
// ) {
//   if (choice.specGroups.isEmpty) return;

//  //debugPrint('🔧 为商品预生成Modifier配置: ${choice.product.commodityName}');
//  //debugPrint('🔧 配置ID: $modifierId');

//   // 生成预配置的默认表单数据
//   final defaultFormData = _generateDefaultFormDataForChoice(choice);

//   if (defaultFormData.isNotEmpty) {
//     // 🔧 修复：使用新的方法名，需要提取商品ID和本地modifierId
//     final commodityId = choice.product.commodityId;
    
//     // 从全局modifierId中提取本地ID（去除商品前缀）
//     String localModifierId = modifierId;
//     if (modifierId.startsWith('${commodityId}_')) {
//       localModifierId = modifierId.substring('${commodityId}_'.length);
//     }

//     // 🔧 修复：需要找到商品的位置信息
//     String? groupId;
//     int? groupIndex;
//     int? choiceIndex;
    
//     // 遍历套餐分组找到该商品的位置信息
//     for (int gIndex = 0; gIndex < widget.combo.groups.length; gIndex++) {
//       final group = widget.combo.groups[gIndex];
//       for (int cIndex = 0; cIndex < group.items.length; cIndex++) {
//         final item = group.items[cIndex];
//         if (item.product.commodityId == commodityId) {
//           groupId = group.id;
//           groupIndex = gIndex;
//           choiceIndex = cIndex;
//           break;
//         }
//       }
//       if (groupId != null) break;
//     }

//     if (groupId != null && groupIndex != null && choiceIndex != null) {
//       modifierService.updateModifierConfigForProduct(
//         groupId,
//         commodityId,
//         groupIndex,
//         choiceIndex,
//         localModifierId,
//         defaultFormData,
//         isConfigured: true,
//       );
//     } else {
//      //debugPrint('🔧 ❌ 找不到商品位置信息，跳过配置: ${choice.product.commodityName}');
//     }

//    //debugPrint('🔧 已为商品预生成默认配置: ${choice.product.commodityName}');
//    //debugPrint('🔧 配置ID: $modifierId');
//    //debugPrint('🔧 预配置数据: ${defaultFormData.keys.toList()}');
//    //debugPrint('🔧 默认选中项数量: ${(defaultFormData['selectedItemIds'] as List).length}');
//   }
// }

/// 生成默认的表单数据
Map<String, dynamic> _generateDefaultFormDataForChoice(ProductComboChoice choice) {
  if (choice.specGroups.isEmpty) return {};
  
  final Map<String, dynamic> formData = {};
  final List<String> selectedItemIds = [];
  final Map<String, int> itemQuantities = {};
  double totalAdditionalPrice = 0.0;
  
  // 处理规格组的默认选择
  for (int groupIndex = 0; groupIndex < choice.specGroups.length; groupIndex++) {
    final specGroup = choice.specGroups[groupIndex];
    if (specGroup.items.isEmpty) continue;
    
    // 根据规格组类型处理默认选择
    if (!specGroup.isMultiSelect) { // 单选
      final defaultItem = specGroup.items.firstWhere(
        (item) => item.isSelected == true,
        orElse: () => specGroup.items.first,
      );
      
      // == 新增开始 == 🔧 使用统一的新格式
      final itemIndex = specGroup.items.indexOf(defaultItem);
      final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
        specGroup.id, defaultItem.id, groupIndex, itemIndex);
      
      selectedItemIds.add(itemKey);
      itemQuantities[itemKey] = 1;
      totalAdditionalPrice += defaultItem.additionalPrice;
      // == 新增结束 ==
      
    } else if (specGroup.isMultiSelect) { // 多选
      final defaultItems = specGroup.items.where((item) => item.isSelected == true).toList();
      if (defaultItems.isNotEmpty) {
        // == 新增开始 == 🔧 使用统一的新格式
        for (final item in defaultItems) {
          final itemIndex = specGroup.items.indexOf(item);
          final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
            specGroup.id, item.id, groupIndex, itemIndex);
          
          selectedItemIds.add(itemKey);
          itemQuantities[itemKey] = 1;
          totalAdditionalPrice += item.additionalPrice;
        }
        // == 新增结束 ==
      }
    }
  }
  
  // == 新增开始 == 🔧 统一使用新格式字段
  formData['selectedItemIds'] = selectedItemIds;
  formData['itemQuantities'] = itemQuantities;
  formData['additionalPrice'] = totalAdditionalPrice;
  formData['selectedRemarkTags'] = <String>[];
  formData['customRemark'] = '';
  formData['isValid'] = true;
  formData['groupValidation'] = <String, bool>{};
  formData['isConfigured'] = selectedItemIds.isNotEmpty;
  // == 新增结束 ==
  
  return formData;
}
  /// 计算附加价格
  double _calculateAdditionalPrice(
    List<Map<String, dynamic>> practiceConfigList,
    List<Map<String, dynamic>> addonsConfigList,
    ProductComboChoice choice,
  ) {
    double totalPrice = 0.0;

    // 计算做法价格
    for (final practiceConfig in practiceConfigList) {
      final groupId = practiceConfig['commodityPracticeGroupId'] as int;
      final itemId = practiceConfig['practiceItemId'] as int;

      for (final group in choice.specGroups) {
        if (group.id == groupId &&
            group.groupType == ProductSpecGroupType.practice) {
          for (final item in group.items) {
            if (item.id == itemId) {
              totalPrice += item.additionalPrice;
              break;
            }
          }
          break;
        }
      }
    }

    // 计算加料价格
    for (final addonConfig in addonsConfigList) {
      final groupId = addonConfig['commodityAddonsGroupId'] as int;
      final itemId = addonConfig['addonsCommodityId'] as int;
      final quantity = addonConfig['unitQuantity'] as int;

      for (final group in choice.specGroups) {
        if (group.id == groupId &&
            group.groupType == ProductSpecGroupType.addons) {
          for (final item in group.items) {
            if (item.id == itemId) {
              totalPrice += item.additionalPrice * quantity;
              break;
            }
          }
          break;
        }
      }
    }

    return totalPrice;
  }

  @override
  Widget build(BuildContext context) {
    // 监听套餐选择状态
    final state =
        ref.watch(productComboSelectionControllerProvider(widget.combo));
    final notifier = ref
        .read(productComboSelectionControllerProvider(widget.combo).notifier);
    // 监听价格计算
    final totalPrice = ref.watch(comboTotalPriceProvider(widget.combo));
    final priceBreakdown = ref.watch(comboPriceBreakdownProvider(widget.combo));
    // 监听价格计算
    final validationResult = ref.watch(comboValidationResultProvider(widget.combo));

    // 构建UI
    return Container(
      color: Colors.white,
      child: CustomScrollView(
        slivers: [
          //  关键修改：传递组索引到构建方法
          ...widget.combo.groups.asMap().entries.expand((groupEntry) {
            final groupIndex = groupEntry.key;
            final group = groupEntry.value;

            return [
              // 为分组创建一个Sliver头部。
              SliverToBoxAdapter(
                child: _buildHeader(context, group, state),
              ),

              // 为该分组的每个商品选项创建一个Sliver列表项。
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, choiceIndex) {
                    final choice = group.items[choiceIndex];

                    //  生成组合键
                    final choiceKey = ProductComboSelectionStateExtension
                        .generateComboChoiceKey(
                      groupId: group.id,
                      commodityId: choice.product.commodityId,
                      groupIndex: groupIndex,
                      choiceIndex: choiceIndex,
                    );

                    //  使用组合键获取数量
                    final quantity = state.quantities[choiceKey] ?? 0;
                    final dynamicMaxValue = state.dynamicMaxValues[choiceKey] ??
                        group.maxSelections;

                    // 为每个 "Modifier" 按钮创建并关联一个唯一的 GlobalKey
                    final key =
                        _modifierKeys.putIfAbsent(choiceKey, () => GlobalKey());

                    //  传递组合键和索引信息
                    return _buildItemRow(
                      context,
                      group,
                      choice,
                      state,
                      notifier,
                      key,
                      groupIndex,
                      choiceIndex,
                      choiceKey,
                      quantity,
                      dynamicMaxValue,
                    );
                  },
                  childCount: group.items.length,
                ),
              ),
            ];
          }),

          // 添加底部间距，避免被底部操作栏遮挡
          const SliverToBoxAdapter(
            child: SizedBox(height: 120), // 为底部操作栏预留空间
          ),
        ],
      ),
    );
  }

  /// 构建列表项的行视图 - 支持组合键
  Widget _buildItemRow(
    BuildContext context,
    ProductComboGroup group,
    ProductComboChoice choice,
    ProductComboSelectionState state,
    ProductComboSelectionController notifier,
    GlobalKey key,
    int groupIndex,
    int choiceIndex,
    String choiceKey,
    int quantity,
    int dynamicMaxValue,
  ) {
    final bool isSelected = quantity > 0;

    return InkWell(
      onTap: group.selectionMode == SelectionMode.single
          ? () => notifier.selectSingleChoice(
              group.id, choice, groupIndex, choiceIndex)
          : null,
      child: Container(
        color:
            isSelected ? KPColors.iconBrandDefault.withAlpha(20) : Colors.white,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 11.0),
          child: Row(
            children: [
              _buildUrlImage(choice.product.imageUrl ?? ''),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(choice.product.commodityName,
                        style: KPFontStyle.headingSmall
                            .copyWith(color: KPColors.textGrayPrimary)),
                    if (choice.product.price! > 0) ...[
                      Text(
                          choice.priceDifference > 0
                              ? "+S\$${choice.priceDifference + choice.product.price!}"
                              : "+S\$${choice.product.price}",
                          style: KPFontStyle.bodyMedium
                              .copyWith(color: KPColors.textGraySecondary)),
                    ],
                    Row(
                      children: [
                        if (choice.specGroups.isNotEmpty)
                          Text('Modifier >',
                                  style: KPFontStyle.bodyMedium.copyWith(
                                      color: KPColors.textBrandDefault))
                              .onTap(() {
                            //  修改：如果商品未选中，先自动选中
                            int? realQuantity = state.quantities[choiceKey];

                            if (realQuantity == null || realQuantity == 0) {
                              // 🎯 关键修复：根据分组的选择模式使用正确的方法
                              if (group.selectionMode == SelectionMode.single) {
                                // 单选模式：使用 selectSingleChoice 方法
                                notifier.selectSingleChoice(
                                    group.id, choice, groupIndex, choiceIndex);
                              } else {
                                // 多选模式：使用 updateQuantity 方法
                                notifier.updateQuantity(
                                    group.id, choice, 1, groupIndex, choiceIndex);
                              }
                              realQuantity = 1;
                              
                              // 使用 addPostFrameCallback 确保 UI 完全更新后再执行弹窗
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                _showModifierPopup(context, choice, group,
                                    choiceKey, realQuantity!);
                              });
                            } else {
                              // 如果已选中，直接显示弹窗
                              _showModifierPopup(context, choice, group,
                                  choiceKey, realQuantity);
                            }
                          }),
                        const Spacer(),
                        _buildQuantityControl(
                          context,
                          group,
                          choice,
                          quantity,
                          dynamicMaxValue,
                          notifier,
                          state,
                          groupIndex,
                          choiceIndex,
                          choiceKey,
                        )
                      ],
                    )
                  ],
                ),
              ),
              const SizedBox(width: 12),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示 Modifier 弹窗的辅助方法
  void _showModifierPopup(
    BuildContext context,
    ProductComboChoice choice,
    ProductComboGroup group,
    String choiceKey,
    int quantity,
  ) {
    final notifier = ref
        .read(productComboSelectionControllerProvider(widget.combo).notifier);

    KPSlidePopup.showFromTarget(
      context: context,
      targetWidth: 360,
      allowMultipleLayers: true, //  关键参数：启用多层弹窗支持
      fullOverlap: true,
      contentWidget: ProductSelectSpecModifierView(
        currentChoice: choice,
        groupId: group.id,
        combo: widget.combo, // 新增：传递套餐对象
        choiceKey: choiceKey, // 新增：传递choiceKey
        reasonOptions: widget.combo.reasonOptions,
        quantity: quantity,
        // 新增：传递FormBuilder初始值
        initialFormValues: notifier.getInitialFormValues(choiceKey, choice),
        // 新增：FormBuilder值更新回调
        onFormUpdate: (key, values) {
          notifier.updateFormBuilderValues(key, values);
        },
        onClose: () => KPSlidePopup.dismissFromTarget(),
      ),
    );
  }

  /// 构建分组头部
  Widget _buildHeader(BuildContext context, ProductComboGroup group,
      ProductComboSelectionState state) {
    return Container(
      color: KPColors.fillGrayLightLightest,
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              group.name,
              style: KPFontStyle.bodyMedium
                  .copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          KPSelectionIndicator(
            currentCount: _getCurrentSelectionCount(group, state),
            lowerLimit: group.minSelections,
            upperLimit: group.maxSelections,
          ),
        ],
      ),
    );
  }

  /// 构建数量控制器 - 支持组合键
  Widget _buildQuantityControl(
    BuildContext context,
    ProductComboGroup group,
    ProductComboChoice choice,
    int quantity,
    int max,
    ProductComboSelectionController notifier,
    ProductComboSelectionState state,
    int groupIndex,
    int choiceIndex,
    String choiceKey,
  ) {
    if (group.selectionMode == SelectionMode.single) {
      return Radio<String>(
        value: choiceKey, //  使用组合键作为value
        groupValue: _getSingleSelectedChoiceKey(group.id, state, groupIndex),
        onChanged: (v) => notifier.selectSingleChoice(
            group.id, choice, groupIndex, choiceIndex),
        fillColor: WidgetStateProperty.resolveWith<Color>((states) =>
            states.contains(WidgetState.selected)
                ? KPColors.borderBrandDefault
                : KPColors.iconGrayTertiary),
      );
    }

    final dynamicMax = state.dynamicMaxValues[choiceKey] ?? max;

    return KPStepper(
      key: ValueKey('${choiceKey}_${dynamicMax}_${quantity}'), //  使用组合键作为key
      value: quantity,
      max: dynamicMax, //  使用动态计算的最大值
      onChanged: (newValue) {
       //debugPrint(' 步进器值变化: ${choice.product.commodityName} -> $newValue (max: $dynamicMax)');
        notifier.updateQuantity(
            group.id, choice, newValue, groupIndex, choiceIndex);
      },
    );
  }

  /// 获取单选分组中当前选中的商品组合键
  String? _getSingleSelectedChoiceKey(
      String groupId, ProductComboSelectionState state, int groupIndex) {
    final group = widget.combo.groups[groupIndex];

    // 在该分组的所有选项中找到数量大于0的项
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      final choiceKey =
          ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: groupId,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );

      if ((state.quantities[choiceKey] ?? 0) > 0) {
        return choiceKey;
      }
    }

    return null; // 没有选中任何项
  }

  /// 获取当前分组的选择数量 - 支持组合键
  int _getCurrentSelectionCount(
      ProductComboGroup group, ProductComboSelectionState state) {
    int totalCount = 0;
    final groupIndex = widget.combo.groups.indexOf(group);

    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      final choiceKey =
          ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: group.id,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      totalCount += state.quantities[choiceKey] ?? 0;
    }
    return totalCount;
  }

  /// 构建网络图片
  Widget _buildUrlImage(String url) {
    return SizedBox(
      width: 64,
      height: 64,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: CachedNetworkImage(
          imageUrl: url,
          fit: BoxFit.cover,
          placeholder: (context, url) =>
              const Center(child: CupertinoActivityIndicator()),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey[200],
            child: const Icon(Icons.image_not_supported_outlined,
                color: Colors.grey),
          ),
        ),
      ),
    );
  }


}
