import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';

import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/domain/product_spec_selection_data.dart';
import 'package:kpos/features/product/presentation/spec/components/product_spec_enhanced_item.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_selection_controller.dart';
import 'package:kpos/features/product/presentation/spec/utils/product_spec_validators.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:kpos/features/product/application/product_spec_service.dart';
import 'package:kpos/features/product/domain/product_detail.dart' as pd;

import '../../../../common/components/kp_remark.dart';
import '../../../../common/components/kp_selection_indicator.dart';
import '../../application/product_combo_modifier_config_service.dart';

/// 商品规格选择视图
/// 使用 flutter_form_builder 实现的规格选择界面
class ProductSelectSpecView extends ConsumerStatefulWidget {
  /// 实例ID，用于多实例状态管理
  final String? instanceId;

  /// 商品的规格组列表
  final List<ProductSpecGroup> specGroups;

  /// 商品的原因选项列表
  final List<pd.ReasonOption> reasonOptions;

  /// FormBuilder初始值（新增）
  final Map<String, dynamic>? initialFormValues;

  /// 是否为编辑模式
  final bool isEditMode;


  /// 表单提交回调
  final Function(ProductSpecSelectionData selectionData)? onSubmit;

  /// 选择变化回调（实时回调）
  final Function(ProductSpecSelectionData selectionData)? onSelectionChanged;

  const ProductSelectSpecView({
    super.key,
    this.instanceId,
    required this.specGroups,
    this.reasonOptions = const [],
    this.initialFormValues, // 新增
    this.isEditMode = false, // 默认为添加模式 

    this.onSubmit,
    this.onSelectionChanged,
  });

  @override
  ConsumerState<ProductSelectSpecView> createState() =>
      _ProductSelectSpecViewState();
}

class _ProductSelectSpecViewState extends ConsumerState<ProductSelectSpecView> {
  /// 表单全局键，用于表单验证和提交
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();
  late String _instanceId;
  final _remarkKey = GlobalKey<FormBuilderState>();

  //  初始化状态控制
  bool _hasInitialized = false;

  // 用户编辑状态标记
  bool _isUserEditingRemark = false;

  // 备注
  late final TextEditingController _remarkTextController;
  late final FocusNode _remarkFocusNode;
  List<String> _predefinedProductRemarks = [];

  /// 网格布局配置
  static const int _columnCount = 3;
  static const double _rowGap = 8.0;
  static const double _columnGap = 8.0;

  /// 获取当前实例ID，如果未传入则使用默认的单商品规格实例
  String _getInstanceId() => widget.instanceId ?? 'product_spec';

  @override
  void initState() {    
    super.initState();
    _instanceId = widget.instanceId ?? 'default_${DateTime.now().millisecondsSinceEpoch}';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _attemptDataRestore();
    });

    _remarkTextController = TextEditingController();
    _remarkFocusNode = FocusNode();

    _predefinedProductRemarks = widget.reasonOptions
      .map((option) => option.name)
      .toList();

    WidgetsBinding.instance.addPostFrameCallback((_) {
    //  如果是编辑模式，设置备注的初始值
    _initializeRemarkData();
  });

    // 监听自定义备注文本变化，并更新到 Riverpod
    _remarkTextController.addListener(() {
      // 标记用户正在编辑
      if (!_isUserEditingRemark) {
        _isUserEditingRemark = true;
      }

      // 同步到Provider
      ref
          .read(productSpecSelectionControllerProvider(_getInstanceId()).notifier)
          .updateCustomRemark(_remarkTextController.text);
    });
  }

  // 重置备注输入的方法（供外部调用）
  void resetRemarkInput() {
    _isUserEditingRemark = false; // 解除保护
    _remarkTextController.clear(); // 清空输入
  }

 // 尝试从保存的配置中恢复数据
void _attemptDataRestore() {
  final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);

  // 如果有instanceId，尝试从Modifier配置中恢复数据
  if (widget.instanceId != null) {
    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    final configData = modifierConfigs[widget.instanceId];
    
    if (configData != null && configData.isConfigured && configData.formData.isNotEmpty) {
     //debugPrint(' 发现已保存的配置，开始恢复: ${widget.instanceId}');
      
      try {
        controller.restoreFromSavedData(configData.formData, widget.specGroups);

        // 恢复自定义备注文本到TextController
        _restoreCustomRemarkText(configData.formData);
        
        // 强制重建表单以反映恢复的状态
        if (mounted) {
          setState(() {});
        }
        
       //debugPrint(' ✅ 数据恢复成功');
        return;  // == 成功恢复后直接返回 ==
      } catch (e) {
       //debugPrint(' ❌ 数据恢复失败: $e，回退到默认选中');
      }
    } else {
     //debugPrint(' 没有保存的配置或配置无效');
    }
  } else {
   //debugPrint(' 没有instanceId');
  }
  
  // 没有保存的配置时，或恢复失败时，初始化默认选中项
 //debugPrint(' 执行默认选中逻辑');
  controller.initializeDefaultSelections(widget.specGroups);
}


// 初始化备注数据（用于编辑模式的备注回显）
void _initializeRemarkData() {
  // 如果有初始表单值，从中提取备注信息
  if (widget.initialFormValues != null) {
    final initialRemarkTags = widget.initialFormValues!['initialRemarkTags'] as List<String>? ?? [];
    final initialCustomRemark = widget.initialFormValues!['initialCustomRemark'] as String? ?? '';

    // 设置自定义备注的初始值
    if (initialCustomRemark.isNotEmpty) {
      _remarkTextController.text = initialCustomRemark;
      debugPrint(' 设置自定义备注初始值: "$initialCustomRemark"');
    }
    
    // 设置预设标签的初始值
    if (initialRemarkTags.isNotEmpty) {
      ref
          .read(productSpecSelectionControllerProvider(_getInstanceId()).notifier)
          .updateSelectedRemarks(initialRemarkTags);
      debugPrint(' 设置预设标签初始值: $initialRemarkTags');
    }
  }
}

  // 恢复自定义备注文本的方法,用于套餐配置恢复
  void _restoreCustomRemarkText(Map<String, dynamic> savedFormData) {
    final String customRemark = savedFormData['customRemark']?.toString() ?? '';
    if (customRemark.isNotEmpty) {
      _remarkTextController.text = customRemark;
      ////debugPrint(' 恢复自定义备注文本: $customRemark');
    }
  }

  @override
  void dispose() {
    _remarkTextController.dispose();
    _remarkFocusNode.dispose();
    super.dispose();
  }

  /// 获取当前规格选择摘要（便捷方法）
  String getCurrentSpecSummary() {
    return ref.read(productSpecServiceProvider(_getInstanceId())).getSpecSelectionSummary(
      specGroups: widget.specGroups,
    );
  }

  /// 获取当前规格附加价格（便捷方法）
  double getCurrentSpecAdditionalPrice() {
    return ref.read(productSpecServiceProvider(_getInstanceId())).calculateSpecAdditionalPrice(
      specGroups: widget.specGroups,
    );
  }

  /// 检查是否可以提交（便捷方法）
  bool canSubmitSpec() {
    final specSelectionState = ref.read(productSpecSelectionControllerProvider(_getInstanceId()));
    return specSelectionState.isValid;
  }

  @override
Widget build(BuildContext context) {
  // 统一使用动态实例ID
 //debugPrint(' ProductSelectSpecView build - Instance ID: ${_getInstanceId()}');
  
  final selectionState = ref.watch(productSpecSelectionControllerProvider(_getInstanceId()));
    
    // 🔧 新增：只有在用户没有编辑时，才从状态同步到文本控制器
    if (!_isUserEditingRemark && 
        _remarkTextController.text != selectionState.customRemark) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _remarkTextController.text = selectionState.customRemark;
        }
      });
    }

  // 实时回调选择变化
  ref.listen<ProductSpecSelectionData>(
    // 使用动态实例ID
    productSpecSelectionControllerProvider(_getInstanceId()),
    (previous, next) {
      // 更新调试信息
     //debugPrint(' 状态监听变化 - Instance ID: ${_getInstanceId()}');
     //debugPrint(' previous: ${previous?.selectedItemIds}');
     //debugPrint(' next: ${next.selectedItemIds}');

      // 实时回调选择变化
      widget.onSelectionChanged?.call(next);
    },
  );

  // // 监听规格选择状态变化
  // //使用动态实例ID
  // final selectionState = ref.watch(productSpecSelectionControllerProvider(_getInstanceId()));

  // 更新调试信息
 //debugPrint(' 当前监听的状态 - Instance ID: ${_getInstanceId()}, selectedIds: ${selectionState.selectedItemIds}');

  // 准备FormBuilder的初始值
    // final Map<String, dynamic> formInitialValues = {
    //   "custom_product_remark": selectionState.customRemark,
    // };

    final Map<String, dynamic> formInitialValues = {};
    
    // 合并传入的初始值
    if (widget.initialFormValues != null) {
      final filteredInitialValues = Map<String, dynamic>.from(widget.initialFormValues!);
      filteredInitialValues.remove('custom_product_remark'); // 🔧 移除备注字段的初始值
      formInitialValues.addAll(filteredInitialValues);
    }


  

    return FormBuilder(
      key: _formKey,
      initialValue: formInitialValues,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 构建所有规格组
            ...widget.specGroups.map((group) => _buildSpecGroup(group)),
            
            // 备注
            _buildRemarkSectionWidget(),

            // 显示当前选择摘要
            if (selectionState.selectedItemIds.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildSelectionSummary(selectionState),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建单个规格组
  Widget _buildSpecGroup(ProductSpecGroup group) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 规格组标题
          _buildGroupTitle(group),
          const SizedBox(height: 8),
          // items
          _buildGroupFormField(group),
        ],
      ),
    );
  }

  /// 构建规格组标题
  Widget _buildGroupTitle(ProductSpecGroup group) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RichText(
            text: TextSpan(
              text: group.name,
              style: KPFontStyle.headingXSmall
                  .copyWith(color: KPColors.textGrayPrimary),
              children: [
                // 必选标记
                if (group.isRequired)
                  TextSpan(
                    text: ' *',
                    style: KPFontStyle.bodyMedium
                        .copyWith(color: KPColors.textRedDefault),
                  ),
                const WidgetSpan(child: SizedBox(width: KPSpacing.xs)),
                // 多选提示
                _buildMultiSelectHint(group),
              ],
            ),
          ),
          _buildSelectionIndicator(group),
        ],
      ),
    );
  }

  /// 获取当前规格组的选择数量
int _getCurrentSelectionCount(ProductSpecGroup group, ProductSpecSelectionData selectionState) {
  int totalCount = 0;
  final groupIndex = widget.specGroups.indexOf(group);
  
  for (int itemIndex = 0; itemIndex < group.items.length; itemIndex++) {
    final item = group.items[itemIndex];
    final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
      group.id, item.id, groupIndex, itemIndex);
    totalCount += selectionState.itemQuantities[itemKey] ?? 0;
  }
  return totalCount;
}

/// 检查规格项是否选中
bool _isItemSelected(ProductSpecGroup group, SpecItem item, ProductSpecSelectionData selectionState) {
  final groupIndex = widget.specGroups.indexOf(group);
  final itemIndex = group.items.indexOf(item);
  final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
    group.id, item.id, groupIndex, itemIndex);
  return selectionState.selectedItemIds.contains(itemKey);
}

  /// 构建选择数量状态指示器
  Widget _buildSelectionIndicator(ProductSpecGroup group) {
    final selectionState = ref.watch(productSpecSelectionControllerProvider(_getInstanceId()));
    final currentCount = _getCurrentSelectionCount(group, selectionState);

    return KPSelectionIndicator(
      currentCount: currentCount,
      lowerLimit: group.lowerLimit,
      upperLimit: group.upperLimit,
      autoHide: false,
    );
  }

  /// 构建多选提示
  TextSpan _buildMultiSelectHint(ProductSpecGroup group) {
    // String hint = '';
    // if (group.lowerLimit == 1 && group.upperLimit == 1) {
    //   hint = '';
    // } else if (group.lowerLimit == 1 && group.upperLimit == 0) {
    //   hint = '必选1个,可多选';
    // } else if (group.lowerLimit == 1 && group.upperLimit > 0) {
    //   hint = '可选1-${group.upperLimit}个';
    // } else if (group.lowerLimit > 1 && group.upperLimit == 0) {
    //   hint = '必选${group.lowerLimit}个,可多选';
    // } else if (group.lowerLimit > 1 && group.upperLimit > 0) {
    //   hint = '必选${group.lowerLimit}个-${group.upperLimit}个';
    // }

    final minStr = group.lowerLimit == 0 ? "" : "必选:${group.lowerLimit},";

    return TextSpan(
      text: "$minStr 最多可选:${group.upperLimit}",
      style: KPFontStyle.bodySmall.copyWith(color: KPColors.textGraySecondary),
    );
  }

  /// 构建规格组表单字段
  Widget _buildGroupFormField(ProductSpecGroup group) {
    return FormBuilderField<List<String>>(
      name: 'spec_group_${group.id}',
      validator: ProductSpecValidators.autoValidator(group),
      builder: (FormFieldState<List<String>> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSpecItemsGrid(group, field),
            if (field.hasError) ...[
              const SizedBox(height: 4),
              Text(
                field.errorText!,
                style: TextStyle(
                  color: context.theme.errorColor1,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  /// 构建规格项网格布局
  Widget _buildSpecItemsGrid(ProductSpecGroup group, FormFieldState<List<String>> field) {
    final items = group.items;
    final rowCount = (items.length / _columnCount).ceil();

    final columnSizes = List.generate(_columnCount, (_) => const FixedTrackSize(178));
    final rowSizes = List.generate(rowCount, (_) => auto);

    return LayoutGrid(
      rowSizes: rowSizes,
      columnSizes: columnSizes,
      rowGap: _rowGap,
      columnGap: _columnGap,
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;

        return _buildSpecItemWidget(
          group: group,
          item: item,
          field: field,
        ).withGridPlacement(
          columnStart: index % _columnCount,
          rowStart: index ~/ _columnCount,
        );
      }).toList(),
    );
  }

  /// 具体代码：构建单个规格项组件
  Widget _buildSpecItemWidget({
    required ProductSpecGroup group,
    required SpecItem item,
    required FormFieldState<List<String>> field,
  }) {
    final selectionState = ref.watch(productSpecSelectionControllerProvider(_getInstanceId()));
    
    // 计算组序号和项序号
    final groupIndex = widget.specGroups.indexOf(group);
    final itemIndex = group.items.indexOf(item);
    
    // 生成复合键
    final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
      group.id, item.id, groupIndex, itemIndex);
    
    // 从状态中获取正确的选择和数量信息
    final isSelected = selectionState.selectedItemIds.contains(itemKey);
    num quantity = selectionState.itemQuantities[itemKey] ?? 0;

    // 步进器类型且选中时，数量最小为1
    if (item.isMeasured && item.isSelected && quantity == 0) {
      quantity = 1;
    }
    
    // 创建规格项选择状态
    final itemState = SpecItemSelectionState(
      isSelected: isSelected,
      quantity: quantity,
    );

    return ProductSpecEnhancedItem(
      specItem: item,
      displayMode: item.displayMode,
      selectionState: itemState,
      formFieldName: 'spec_group_${group.id}_${item.id}',
      allGroups: widget.specGroups,
      instanceId: _getInstanceId(),
      groupIndex: groupIndex,
      itemIndex: itemIndex,
      onTap: () => _handleItemTap(group, item, field),
      onQuantityChanged: (quantity) => _handleQuantityChanged(group, item, quantity, field),
    );
  }

  /// 处理规格项点击事件
  // == 修改开始 == 更新控制器调用方式，传入组序号和项序号
void _handleItemTap(ProductSpecGroup group, SpecItem item, FormFieldState<List<String>> field) {
  final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);

  // 计算组序号和项序号
  final groupIndex = widget.specGroups.indexOf(group);
  final itemIndex = group.items.indexOf(item);

  if (group.isMultiSelect) {
    controller.toggleMultipleItem(
      itemId: item.id, 
      groupIndex: groupIndex,
      itemIndex: itemIndex,
      allGroups: widget.specGroups
    );
  } else {
    controller.selectSingleItem(
      groupId: group.id, 
      itemId: item.id,
      groupIndex: groupIndex,
      itemIndex: itemIndex,
      allGroups: widget.specGroups
    );
  }

  _updateFieldValue(field);
}

  /// 处理数量变化事件
 void _handleQuantityChanged(ProductSpecGroup group, SpecItem item, int quantity, FormFieldState<List<String>> field) {
  final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);
  
  // 计算组序号和项序号
  final groupIndex = widget.specGroups.indexOf(group);
  final itemIndex = group.items.indexOf(item);
  
  controller.setItemQuantity(
    itemId: item.id, 
    groupIndex: groupIndex,
    itemIndex: itemIndex,
    quantity: quantity, 
    allGroups: widget.specGroups
  );
  _updateFieldValue(field);
}

  /// 更新表单字段值
  void _updateFieldValue(FormFieldState<List<String>> field) {
    final selectionState = ref.read(productSpecSelectionControllerProvider(_getInstanceId()));
    field.didChange(selectionState.selectedItemIds);
  }

  /// 构建选择摘要
  Widget _buildSelectionSummary(ProductSpecSelectionData selectionState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.theme.grayColor1,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: context.theme.grayColor3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '开发测试自用-已选择规格:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: context.theme.fontGyColor1,
            ),
          ),
          const SizedBox(height: 8),
          ..._buildSelectedSpecItemsList(selectionState),
          if (selectionState.selectedRemarkTags.isNotEmpty || selectionState.customRemark.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildRemarksSummary(selectionState),
          ],
          if (selectionState.additionalPrice > 0) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '附加费用',
                  style: TextStyle(
                    fontSize: 13,
                    color: context.theme.fontGyColor2,
                  ),
                ),
                Text(
                  '+¥${selectionState.additionalPrice.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: context.theme.warningColor1,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建已选规格项列表
  List<Widget> _buildSelectedSpecItemsList(ProductSpecSelectionData selectionState) {
    // == 添加调试开始 ==
  ////debugPrint(' ========== 开发测试模块状态检查 ==========');
  ////debugPrint(' 当前Instance ID: ${_getInstanceId()}');
  ////debugPrint(' selectionState.selectedItemIds: ${selectionState.selectedItemIds}');
  ////debugPrint(' selectionState.itemQuantities: ${selectionState.itemQuantities}');
  ////debugPrint(' specGroups数量: ${widget.specGroups.length}');
  
  for (int groupIndex = 0; groupIndex < widget.specGroups.length; groupIndex++) {
    final group = widget.specGroups[groupIndex];
    ////debugPrint(' 规格组: ${group.name} (ID: ${group.id})');
    for (int itemIndex = 0; itemIndex < group.items.length; itemIndex++) {
      final item = group.items[itemIndex];
      final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
        group.id, item.id, groupIndex, itemIndex);
      final isSelected = selectionState.selectedItemIds.contains(itemKey);
      final quantity = selectionState.itemQuantities[itemKey] ?? 0;
      ////debugPrint('   规格项: ${item.name} (ID: ${item.id}) - 选中: $isSelected, 数量: $quantity, 复合键: $itemKey');
    }
  }
  ////debugPrint(' ===================================');
  // == 添加调试结束 ==

    if (selectionState.selectedItemIds.isEmpty) return [];
    
    final List<Widget> specItemWidgets = [];
    
    for (final group in widget.specGroups) {
      final groupIndex = widget.specGroups.indexOf(group);
      final selectedItems = <SpecItem>[];
      
      // == 修改开始 == 使用复合键匹配选中的规格项
      for (int itemIndex = 0; itemIndex < group.items.length; itemIndex++) {
        final item = group.items[itemIndex];
        final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
          group.id, item.id, groupIndex, itemIndex);
        if (selectionState.selectedItemIds.contains(itemKey)) {
          selectedItems.add(item);
        }
      }
      // == 修改结束 ==
      
      if (selectedItems.isEmpty) continue;
      
      specItemWidgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 60,
                child: Text(
                  '${group.name}:',
                  style: TextStyle(
                    fontSize: 13,
                    color: context.theme.fontGyColor2,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  _formatSelectedItems(selectedItems, selectionState),
                  style: TextStyle(
                    fontSize: 13,
                    color: context.theme.fontGyColor1,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return specItemWidgets;
  }
  
  /// 格式化已选规格项文本
  /// == 修改开始 == 支持复合键格式的数量获取
  String _formatSelectedItems(List<SpecItem> selectedItems, ProductSpecSelectionData selectionState) {
    final List<String> itemTexts = [];
    
    for (final item in selectedItems) {
      // 查找匹配的复合键来获取数量
      int quantity = 1;
      for (final entry in selectionState.itemQuantities.entries) {
        final parsed = ProductSpecSelectionDataExtension.parseItemKey(entry.key);
        if (parsed['itemId'] == item.id) {
          quantity = entry.value;
          break;
        }
      }
      
      if (quantity > 1) {
        itemTexts.add('${item.name} x $quantity');
      } else {
        itemTexts.add(item.name);
      }
      
      if (item.additionalPrice > 0) {
        final lastIndex = itemTexts.length - 1;
        itemTexts[lastIndex] = '${itemTexts[lastIndex]} (+¥${item.additionalPrice.toStringAsFixed(2)})';
      }
    }
    
    return itemTexts.join('、');
  }
  // == 修改结束 ==

  Widget _buildRemarkSectionWidget() {
    // 监听 Riverpod 状态变化，获取当前选中的备注标签
    final selectionState = ref.watch(productSpecSelectionControllerProvider(_getInstanceId()));
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: KPRemark(
        remarkFormKey: _remarkKey,
        title: KPLocale.of(context).customNotes,
        hintText: KPLocale.of(context).customNotes,
        remarkList: _predefinedProductRemarks,
        isShowAsterisk: false,
        formBuilderFieldName: "custom_product_remark",
        controller: _remarkTextController,
        focusNode: _remarkFocusNode,
        isNeedDecorationLabel: true,
        // 传入初始选中的备注标签
        initialSelectedTags: selectionState.selectedRemarkTags,
        onRemarkChanged: (List<String> selectedTags) {
          ref
              .read(productSpecSelectionControllerProvider(_getInstanceId()).notifier)
              .updateSelectedRemarks(selectedTags);
        },
      ),
    );
  }
  
  /// 构建备注摘要
  Widget _buildRemarksSummary(ProductSpecSelectionData selectionState) {
    final List<String> remarkTexts = [];
    
    if (selectionState.selectedRemarkTags.isNotEmpty) {
      remarkTexts.add(selectionState.selectedRemarkTags.join(','));
    }
    
    if (selectionState.customRemark.isNotEmpty) {
      remarkTexts.add(selectionState.customRemark);
    }
    
    final String remarkText = remarkTexts.join(',');
    
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60,
          child: Text(
            '备注:',
            style: TextStyle(
              fontSize: 13,
              color: context.theme.fontGyColor2,
            ),
          ),
        ),
        Expanded(
          child: Text(
            remarkText,
            style: TextStyle(
              fontSize: 13,
              color: context.theme.fontGyColor1,
            ),
          ),
        ),
      ],
    );
  }

}