import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/domain/product_spec_selection_data.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_selection_controller.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

/// 商品规格增强项组件
/// 支持三种不同的显示模式，统一使用FormBuilder步进器
/// 
/// 设计理念：
/// - 纯文字模式：基础属性选择（口味、辣度等）
/// - 文字+价格模式：有额外费用的选项
/// - 文字+步进器模式：可调节数量的附加项目，统一使用FormBuilder
class ProductSpecEnhancedItem extends ConsumerWidget {
  /// 规格项数据
  final SpecItem specItem;
  
  /// 显示模式（纯文字/文字+价格/文字+步进器）
  final SpecDisplayMode displayMode;
  
  /// 当前选择状态
  final SpecItemSelectionState selectionState;
  
  /// 点击回调（用于单选/多选模式）
  final VoidCallback? onTap;
  
  /// 数量变化回调（用于步进器模式）
  final ValueChanged<int>? onQuantityChanged;
  
  // FormBuilder相关参数（步进器模式必需）
  
  /// FormBuilder字段名（步进器模式时必填）
  final String? formFieldName;
  
  /// FormBuilder验证器
  final FormFieldValidator<int>? validator;
  
  /// FormBuilder值变化回调
  final ValueChanged<int?>? onFormChanged;

  /// 所有规格组列表（用于Riverpod状态管理）
  final List<ProductSpecGroup>? allGroups;

  /// 实例ID，用于多实例状态管理
  final String? instanceId;

  /// 组序号（用于精确定位）
  final int? groupIndex;

  /// 项序号（用于精确定位）
  final int? itemIndex;

  const ProductSpecEnhancedItem({
    super.key,
    required this.specItem,
    required this.displayMode,
    required this.selectionState,
    this.onTap,
    this.onQuantityChanged,
    // FormBuilder参数
    this.formFieldName,
    this.validator,
    this.onFormChanged,
    // 新增：Riverpod状态管理需要的参数
    this.allGroups,
    this.instanceId,
    this.groupIndex,
    this.itemIndex,
  }) : assert(
    displayMode != SpecDisplayMode.textWithStepper || formFieldName != null,
    'formFieldName is required when displayMode is textWithStepper',
  );

  /// 获取当前实例ID，如果未传入则使用默认的单商品规格实例
  String _getInstanceId() => instanceId ?? 'product_spec';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      // 项目逻辑：只有非步进器模式且启用状态下才响应点击
      onTap: displayMode != SpecDisplayMode.textWithStepper && selectionState.enabled 
          ? onTap : null,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: _buildDecoration(context),
        child: _buildContent(context, ref),
      ),
    );
  }

  /// 构建容器装饰样式
  /// 根据选择状态和模式显示不同的边框和背景色
  BoxDecoration _buildDecoration(BuildContext context) {
    final bool isHighlighted = _isHighlighted();

    return BoxDecoration(
      borderRadius: BorderRadius.circular(6),
      // 选中时使用品牌色背景，未选中时使用灰色背景
      color: isHighlighted 
          ? const Color(0xFFFFF7E6)
          : const Color(0xFFFAFBFC),
      border: isHighlighted 
          ? Border.all(color: const Color(0xFFFF8500)) 
          : Border.all(color: const Color(0xFFFAFBFC)) ,
    );
  }

  /// 判断是否需要高亮显示
  /// 
  /// 项目逻辑：
  /// - 单选/多选模式：基于选中状态
  /// - 步进器模式：基于数量是否大于0
  bool _isHighlighted() {
    return selectionState.isSelected || 
           (displayMode == SpecDisplayMode.textWithStepper && 
            selectionState.quantity > 0);
  }

  /// 根据显示模式构建不同的内容
  Widget _buildContent(BuildContext context, WidgetRef ref) {
    switch (displayMode) {
      case SpecDisplayMode.textOnly:
        return _buildTextOnlyContent(context);
      case SpecDisplayMode.textWithPrice:
        return _buildTextWithPriceContent(context);
      case SpecDisplayMode.textWithStepper:
        return _buildTextWithStepperContent(context, ref);
    }
  }

  /// 构建纯文字内容（基础选项模式）
  /// 
  /// 项目应用场景：做法选择、辣度选择等基础属性
  Widget _buildTextOnlyContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTitle(context),
      ],
    );
  }

  /// 构建文字+价格内容
  /// 
  /// 项目应用场景：有额外费用的加料选项
  Widget _buildTextWithPriceContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildTitle(context),
        const SizedBox(height: 2),
        _buildPriceTag(context),
      ],
    );
  }

  /// 构建文字+步进器内容
  /// 
  /// 项目应用场景：可调节数量的附加项目
  Widget _buildTextWithStepperContent(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTitle(context),
              // 如果是附加项目，显示价格标签
              if (specItem.isAdditional && specItem.additionalPrice > 0) ...[
                const SizedBox(height: 2),
                _buildPriceTag(context),
              ],
            ],
          ),
        ),
        const SizedBox(height: 8),
        _buildFormBuilderStepper(context, ref),
      ],
    );
  }
 
  /// 构建标题文本
  Widget _buildTitle(BuildContext context) {
    final bool isHighlighted = _isHighlighted();

    return Text(
      specItem.name,
      style: isHighlighted ? KPFontStyle.headingXSmall.copyWith(
        color: KPColors.textBrandDefault) : 
         KPFontStyle.headingXSmall.copyWith(color: KPColors.textGrayPrimary),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建价格标签
  Widget _buildPriceTag(BuildContext context) {
    if (specItem.additionalPrice <= 0) return const SizedBox.shrink();
    final bool isHighlighted = _isHighlighted();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      child: Text(
        '+¥${specItem.additionalPrice.toStringAsFixed(2)}',
        style: isHighlighted ? KPFontStyle.bodySmall.copyWith(
          color: KPColors.textBrandDefault) : 
           KPFontStyle.bodySmall.copyWith(color: KPColors.textGraySecondary),
      ),
    );
  }

  /// 构建FormBuilder步进器
  /// 
  /// 项目特色：
  /// - 集成表单验证
  /// - 支持数字键盘弹窗
  /// - 实时同步Riverpod状态
  /// 构建FormBuilder步进器
/// 
/// 项目特色：
/// - 集成表单验证
/// - 支持数字键盘弹窗
/// - 实时同步Riverpod状态
Widget _buildFormBuilderStepper(BuildContext context, WidgetRef ref) {
  return KPFormBuilderStepper(
    name: formFieldName!,      
    initialValue: selectionState.quantity > 0 ? selectionState.quantity.toInt() : 0,
    min: 0,
    max: 999999, // 固定最大值，因为SpecItem没有maxQuantity属性
    step: 1,
    
    // 表单装饰
    decoration: const InputDecoration(
      border: InputBorder.none,
      contentPadding: EdgeInsets.zero,
    ),
    
    // 验证器
    validator: validator ?? _defaultValidator,
    
    // FormBuilder级别的变化处理
    onChanged: (value) {
      debugPrint("FormBuilder ${specItem.name} 数量变化: $value");
      
      // 🚀 关键修改：使用传入的位置信息，避免错误查找
      if (value != null && groupIndex != null && itemIndex != null) {
        final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);
        final specGroups = _getSpecGroups(ref);
        
        controller.setItemQuantity(
          itemId: specItem.id,
          groupIndex: groupIndex!,
          itemIndex: itemIndex!,
          quantity: value,
          allGroups: specGroups,
        );
      }
      
      // 触发FormBuilder回调
      onFormChanged?.call(value);
      // 触发原有的业务回调
      onQuantityChanged?.call(value ?? 0);
    },
    
    // KPStepper级别的变化处理
    onStepperChanged: (value) {
      debugPrint("KPStepper ${specItem.name} 内部逻辑: $value");
      _handleStepperChange(value);
      
      // 🚀 同样使用传入的位置信息
      if (groupIndex != null && itemIndex != null) {
        final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);
        final specGroups = _getSpecGroups(ref);
        
        controller.setItemQuantity(
          itemId: specItem.id,
          groupIndex: groupIndex!,
          itemIndex: itemIndex!,
          quantity: value,
          allGroups: specGroups,
        );
      }
    },
    
    // 🎯 数字键盘功能 - 传递context和ref
    showNumberKeyboardEvent: (value) => _showNumberKeyboard(context, ref, value),
    
    // 项目样式
    buttonColor: Colors.white,
    iconColor: Colors.blue,
    iconSize: 16,
  );
}

  /// 默认验证器
  String? _defaultValidator(int? value) {
    // 最小值验证
    if (value != null && value < 0) {
      return '${specItem.name}数量不能小于0';
    }
    
    // 最大值验证（固定为15，因为SpecItem没有maxQuantity属性）
    if (value != null && value > 15) {
      return '${specItem.name}数量不能超过15';
    }
    
    return null;
  }

  /// 处理步进器变化
  void _handleStepperChange(int value) {
    // 这里可以添加特定的业务逻辑
    // 比如：音效、动画、特殊验证等
    
    // 示例：当数量为0时的特殊处理
    if (value == 0) {
      debugPrint("${specItem.name} 数量归零");
    }
    
    // 示例：当达到最大值时的提示
    if (value >= 15) {
      debugPrint("${specItem.name} 已达到最大数量");
    }
  }

  /// 🎯 显示数字键盘的包装方法
  /// 在FormBuilder步进器中调用，传递必要的context和ref
  void _showNumberKeyboard(BuildContext context, WidgetRef ref, int currentValue) {
    _showNumberKeyboardEvent(currentValue, context, ref);
  }

  /// 🎯 显示数字键盘主要实现方法
  /// 弹出自定义数字键盘，集成Riverpod状态管理
  void _showNumberKeyboardEvent(int currentValue, BuildContext context, WidgetRef ref) {
    debugPrint("显示数字键盘 - ${specItem.name}: $currentValue");

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.white,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: 
                Padding(
                  padding: const EdgeInsets.symmetric( horizontal: 5, vertical: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      KPNumberKeyboard(onConfirmPressed: (newValue) {
                        final intValue = newValue?.toInt() ?? 0;
                        debugPrint("用户确认输入数量: $intValue");
                  
                        // 🚀 使用传入的位置信息更新状态
                        if (groupIndex != null && itemIndex != null) {
                          final controller = ref.read(productSpecSelectionControllerProvider(_getInstanceId()).notifier);
                          final specGroups = _getSpecGroups(ref);
                          
                          controller.setItemQuantity(
                            itemId: specItem.id,
                            groupIndex: groupIndex!,
                            itemIndex: itemIndex!,
                            quantity: intValue,
                            allGroups: specGroups,
                          );
                        }
                  
                        // 关闭弹窗
                        Navigator.of(context).pop();
                      }, buttonWidth: 101, buttonHeight: 54, title: '请输入数量',
                        minValue: 0.0,
                        maxValue: 15.0,
                        onCancelPressed: (){
                          debugPrint("用户取消输入");
                        },
                      ),
                    ],
                  ),
                ),
            ),
          );
        });
  }

  /// 🎯 获取所有规格组数据
  /// 优先使用传入的allGroups，否则尝试从演示数据获取
  List<ProductSpecGroup> _getSpecGroups(WidgetRef ref) {
    // 如果构造函数传入了allGroups，直接使用
    if (allGroups != null && allGroups!.isNotEmpty) {
      return allGroups!;
    }
    
    return <ProductSpecGroup>[];
  }

  }
