import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/product_discount_option.dart';
import '../../../store/domain/store_prepared_reason_item.dart';

part 'product_special_price_controller.g.dart';

// 🆕 管理真实售价状态
@riverpod
class RealSalesPrice extends _$RealSalesPrice {
  @override
  double? build() => null; // 初始状态为 null，表示使用原价

  void update(double? newPrice) {
    state = newPrice;
  }

  void clear() {
    state = null;
  }
}

// 管理自定义折扣的数值
@riverpod
class CustomDiscount extends _$CustomDiscount {
  @override
  num? build() => null;

  void update(num? newValue) {
    state = newValue;
  }
}

// 管理选中的折扣选项
@riverpod
class SelectedDiscountOption extends _$SelectedDiscountOption {
  @override
  ProductDiscountOption? build() => null;

  void update(ProductDiscountOption? newOption) {
    state = newOption;
  }
}

// 管理选中的折扣原因
@riverpod
class SelectedDiscountReason extends _$SelectedDiscountReason {
  @override
  StorePreparedReasonItem? build() => null;

  void update(StorePreparedReasonItem? newReason) {
    state = newReason;
  }
}

// 🆕 管理 Custom notes 状态
@riverpod
class CustomNotes extends _$CustomNotes {
  @override
  String build() => '';

  void update(String notes) {
   // debugPrint('🔧 CustomNotes Provider 更新: "$notes"');
    state = notes;
  }

  void clear() {
   // debugPrint('🔧 CustomNotes Provider 清空');
    state = '';
  }
}

// 🆕 管理多选折扣原因列表
@riverpod
class SelectedDiscountReasons extends _$SelectedDiscountReasons {
  @override
  List<String> build() => [];

  void update(List<String> newReasons) {
    state = [...newReasons]; // 创建新列表避免引用问题
  }

  void add(String reason) {
    if (!state.contains(reason)) {
      state = [...state, reason];
    }
  }

  void remove(String reason) {
    state = state.where((r) => r != reason).toList();
  }

  void clear() {
    state = [];
  }
}

// 🆕 监听 discount 属性变化的 Provider
@riverpod
class DiscountListener extends _$DiscountListener {
  @override
  Map<String, dynamic> build() {
    // 监听所有折扣相关的状态
    final selectedDiscountOption = ref.watch(selectedDiscountOptionProvider);
    final realSalesPrice = ref.watch(realSalesPriceProvider);
    final discountReason = ref.watch(selectedDiscountReasonProvider);
    final customDiscount = ref.watch(customDiscountProvider);

    // 返回组合状态
    return {
      'selectedDiscountOption': selectedDiscountOption,
      'realSalesPrice': realSalesPrice,
      'discountReason': discountReason,
      'customDiscount': customDiscount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  // 获取当前折扣信息的便捷方法
  ProductDiscountOption? get currentDiscountOption => state['selectedDiscountOption'];
  double? get currentRealSalesPrice => state['realSalesPrice'];
  StorePreparedReasonItem? get currentDiscountReason => state['discountReason'];
  num? get currentCustomDiscount => state['customDiscount'];

  // 检查是否有折扣被应用
  bool get hasDiscount => currentDiscountOption != null;

  // 检查是否有自定义折扣
  bool get hasCustomDiscount => currentDiscountOption?.isCustom == true && currentCustomDiscount != null;

  // 获取折扣描述文本
  String get discountDescription {
    if (!hasDiscount) return '无折扣';

    final option = currentDiscountOption!;
    if (option.isFree) return '免费';
    if (option.isCustom && currentCustomDiscount != null) {
      return '自定义${option.discountType == DiscountType.percentage ? '${currentCustomDiscount}%' : '-\$${currentCustomDiscount}'}';
    }
    return option.discount;
  }
}