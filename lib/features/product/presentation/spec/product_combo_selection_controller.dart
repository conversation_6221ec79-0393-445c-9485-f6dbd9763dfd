import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/product_combo.dart';
import '../../domain/product_combo_selection_state.dart';
import '../../application/product_combo_service.dart';
import '../../application/product_combo_modifier_config_service.dart';
import '../../domain/product_spec_group.dart';
import '../../domain/product_spec_validation_service.dart';
import '../../application/product_combo_initialization_service.dart';

part 'product_combo_selection_controller.g.dart';

/// 套餐选择状态控制器(初始化流程)
/// 统一管理套餐的所有选择状态和业务逻辑
@riverpod
class ProductComboSelectionController
    extends _$ProductComboSelectionController {
  @override
  ProductComboSelectionState build(ProductCombo combo) {

    // 使用统一初始化服务
    final initService = ref.read(productComboInitializationServiceProvider);

    // 同步初始化
    final initResult = _initializeComboSync(combo, initService);

    // 延迟初始化Modifier配置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _finalizeInitialization(initResult);
    });

    return initResult.selectionState;
  }

  /// 同步初始化套餐
ComboInitializationResult _initializeComboSync(
  ProductCombo combo, 
  ProductComboInitializationService initService
) {
    // 每次都重新收集默认选择，不保留之前的状态
  final defaultChoices = _collectDefaultChoices(combo);
  
  // 构建全新的选择状态，清除之前的FormBuilder数据
  final selectionState = _buildSelectionState(combo, defaultChoices);
  
  return ComboInitializationResult(
    selectionState: selectionState,
    defaultChoices: defaultChoices,
    formData: {},
  );
}

/// 🔧 优化：收集默认选择 - 简化版本
Map<String, DefaultChoiceInfo> _collectDefaultChoices(ProductCombo combo) {
  final defaultChoices = <String, DefaultChoiceInfo>{};
  
  for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
    final group = combo.groups[groupIndex];
    
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      
      if (choice.defaultEnable == 1) {
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        defaultChoices[choiceKey] = DefaultChoiceInfo(
          choice: choice,
          quantity: 1,
          groupId: group.id,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
          choiceKey: choiceKey,
          commodityId: choice.product.commodityId,
        );
      }
    }
  }

  return defaultChoices;
}

/// 🔧 优化：构建选择状态 - 简化版本
ProductComboSelectionState _buildSelectionState(
  ProductCombo combo,
  Map<String, DefaultChoiceInfo> defaultChoices
) {
  final quantities = <String, int>{};
  final groupSelections = <String, Set<String>>{};
  final groupTotals = <String, int>{};
  
  // 初始化所有分组
  for (final group in combo.groups) {
    groupSelections[group.id] = <String>{};
    groupTotals[group.id] = 0;
  }
  
  // 设置默认选择
  for (final info in defaultChoices.values) {
    quantities[info.choiceKey] = info.quantity;
    groupSelections[info.groupId]!.add(info.choiceKey);
    groupTotals[info.groupId] = (groupTotals[info.groupId] ?? 0) + info.quantity;
  }
  
  // 计算动态最大值
  final dynamicMaxValues = _calculateInitialDynamicMaxValues(combo, groupTotals);
  
  return ProductComboSelectionState(
    quantities: quantities,
    groupSelections: groupSelections,
    groupTotals: groupTotals,
    dynamicMaxValues: dynamicMaxValues,
    formBuilderValues: {},
  );
}

/// 🔧 新增：计算初始动态最大值
Map<String, int> _calculateInitialDynamicMaxValues(ProductCombo combo, Map<String, int> groupTotals) {
  final dynamicMaxValues = <String, int>{};
  
  for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
    final group = combo.groups[groupIndex];
    final groupTotal = groupTotals[group.id] ?? 0;
    
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: group.id,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      
      dynamicMaxValues[choiceKey] = _calculateDynamicMax(group, choice, groupTotal);
    }
  }
  
  return dynamicMaxValues;
}

/// 完成最终初始化
Future<void> _finalizeInitialization(ComboInitializationResult initResult) async {
  final modifierService = ref.read(productComboModifierConfigServiceProvider.notifier);
  
  for (final info in initResult.defaultChoices.values) {
    if (info.choice.specGroups.isEmpty) continue;
    
    modifierService.initializeModifiersForProduct(
      info.groupId,
      info.commodityId,
      info.groupIndex,
      info.choiceIndex,
      [info.choice],
      forceReset: false,
    );
  }
}

// 保留 _calculateDynamicMax 方法，但移动到私有方法区域
int _calculateDynamicMax(ProductComboGroup group, ProductComboChoice choice, int currentGroupTotal) {
  return group.maxSelections - currentGroupTotal + (choice.defaultEnable.toInt());
}

  /// 单选模式选择 - 使用组合键
  /// 单选模式选择 - 优化版本
void selectSingleChoice(String groupId, ProductComboChoice choice, int groupIndex, int choiceIndex) {
  final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
    groupId: groupId,
    commodityId: choice.product.commodityId,
    groupIndex: groupIndex,
    choiceIndex: choiceIndex,
  );

  final newQuantities = Map<String, int>.from(state.quantities);
  final newGroupSelections = Map<String, Set<String>>.from(state.groupSelections);

  // 清空分组并设置新选择
  _clearGroupAndSetChoice(combo.groups[groupIndex], groupId, groupIndex, choiceKey, newQuantities, newGroupSelections);

  // 延迟初始化Modifier配置
  if (choice.specGroups.isNotEmpty) {
    _scheduleModifierInitialization(choice, groupId, groupIndex, choiceIndex);
  }

  // 更新状态
  _updateStateWithNewQuantities(newQuantities, newGroupSelections);
}

/// 🔧 新增：清空分组并设置选择
void _clearGroupAndSetChoice(
  ProductComboGroup group,
  String groupId,
  int groupIndex,
  String newChoiceKey,
  Map<String, int> quantities,
  Map<String, Set<String>> groupSelections,
) {
  // 清空该分组的所有选择
  for (int i = 0; i < group.items.length; i++) {
    final itemKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
      groupId: groupId,
      commodityId: group.items[i].product.commodityId,
      groupIndex: groupIndex,
      choiceIndex: i,
    );
    quantities[itemKey] = 0;
  }
  
  // 设置新选择
  groupSelections[groupId] = {newChoiceKey};
  quantities[newChoiceKey] = 1;
}

/// 🔧 新增：调度Modifier初始化
void _scheduleModifierInitialization(ProductComboChoice choice, String groupId, int groupIndex, int choiceIndex) {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    try {
      final modifierService = ref.read(productComboModifierConfigServiceProvider.notifier);
      modifierService.initializeModifiersForProduct(
        groupId,
        choice.product.commodityId,
        groupIndex,
        choiceIndex,
        [choice],
        forceReset: false,
      );
    } catch (e) {
     //debugPrint('🚨 Modifier初始化失败: $e');
    }
  });
}

  /// 多选/步进器模式更新数量 - 使用组合键
  void updateQuantity(String groupId, ProductComboChoice choice,
      int newQuantity, int groupIndex, int choiceIndex) {
    final choiceKey =
        ProductComboSelectionStateExtension.generateComboChoiceKey(
      groupId: groupId,
      commodityId: choice.product.commodityId,
      groupIndex: groupIndex,
      choiceIndex: choiceIndex,
    );

    final newQuantities = Map<String, int>.from(state.quantities);
    final newGroupSelections =
        Map<String, Set<String>>.from(state.groupSelections);

    // 更新数量
    newQuantities[choiceKey] = newQuantity;

    // 更新分组选择记录
    if (newQuantity > 0) {
      newGroupSelections[groupId]!.add(choiceKey);
    } else {
      newGroupSelections[groupId]!.remove(choiceKey);
    }

    // 更新状态
    _updateStateWithNewQuantities(newQuantities, newGroupSelections);
  }

/// 状态更新 - 使用批量计算
  void _updateStateWithNewQuantities(
  Map<String, int> newQuantities,
  Map<String, Set<String>> newGroupSelections,
) {
  final service = ref.read(productComboServiceProvider);
  
  // 创建临时状态用于计算
  final tempState = state.copyWith(
    quantities: newQuantities,
    groupSelections: newGroupSelections,
  );
  
  // 批量计算所有派生状态
  final derivedState = _calculateDerivedState(combo, tempState, service);
  
  // 一次性更新完整状态
  state = tempState.copyWith(
    groupTotals: derivedState.groupTotals,
    dynamicMaxValues: derivedState.dynamicMaxValues,
    groupValidation: derivedState.groupValidation,
    isValid: derivedState.isValid,
    additionalPrice: derivedState.additionalPrice,
  );
}

/// 🔧 新增：批量计算派生状态
_DerivedState _calculateDerivedState(
  ProductCombo combo,
  ProductComboSelectionState tempState,
  ProductComboService service,
) {
  final groupTotals = service.calculateGroupTotals(combo, tempState);
  final groupValidation = _calculateGroupValidation(tempState, groupTotals);
  final isValid = service.validateSelection(combo, tempState);
  final additionalPrice = service.calculateAdditionalPrice(combo, tempState);
  final dynamicMaxValues = service.calculateDynamicMaxValues(combo, tempState);
  
  return _DerivedState(
    groupTotals: groupTotals,
    groupValidation: groupValidation,
    isValid: isValid,
    additionalPrice: additionalPrice,
    dynamicMaxValues: dynamicMaxValues,
  );
}


  /// 计算分组验证状态
  Map<String, bool> _calculateGroupValidation(
    ProductComboSelectionState state,
    Map<String, int> groupTotals,
  ) {
    final validation = <String, bool>{};

    for (final group in combo.groups) {
      final groupTotal = groupTotals[group.id] ?? 0;

      if (group.isRequired) {
        validation[group.id] = groupTotal >= group.minSelections;
      } else {
        validation[group.id] = true;
      }
    }

    return validation;
  }

  /// 重置状态
void resetState() {
  // 🔧 修改：完全清除之前的状态，重新初始化
  final initService = ref.read(productComboInitializationServiceProvider);
  final initResult = _initializeComboSync(combo, initService);

  // 🔧 重置为全新状态，清除所有FormBuilder数据
  state = initResult.selectionState.copyWith(
    formBuilderValues: {}, // 清空所有FormBuilder数据
  );

  // 🔧 清除Modifier配置缓存
  final modifierService = ref.read(productComboModifierConfigServiceProvider.notifier);
  modifierService.clearAllConfigs();

  // 重新初始化Modifier配置
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _finalizeInitialization(initResult);
  });

 //debugPrint('🔄 套餐状态已完全重置为默认状态');
}

  /// 获取指定商品的选择数量
  int getProductQuantity(String productId) {
    return state.getProductQuantity(productId);
  }

  /// 获取指定分组的选择总数
  int getGroupSelectionCount(String groupId) {
    return state.getGroupSelectionCount(groupId);
  }

  /// 获取指定商品的动态最大值
  int getProductDynamicMaxValue(String productId) {
    return state.dynamicMaxValues[productId] ?? 0;
  }

  /// 验证当前选择是否有效
  bool isSelectionValid() {
    return state.isValid;
  }

  /// 获取当前选择的附加价格
  double getAdditionalPrice() {
    return state.additionalPrice;
  }

  /// === FormBuilder相关方法 ===

  /// 更新指定商品的FormBuilder值
  void updateFormBuilderValues(String choiceKey, Map<String, dynamic> values) {
    final newFormBuilderValues =
        Map<String, Map<String, dynamic>>.from(state.formBuilderValues);
    newFormBuilderValues[choiceKey] = Map<String, dynamic>.from(values);

    state = state.copyWith(formBuilderValues: newFormBuilderValues);

   //debugPrint('📝 FormBuilder值已更新: $choiceKey -> ${values.keys.toList()}');
  }

  /// 获取指定商品的FormBuilder初始值（用于打开Modifier页面时）
  Map<String, dynamic> getInitialFormValues(
      String choiceKey, ProductComboChoice choice) {
    // 只返回用户真正修改过的值
    if (state.hasFormBuilderValues(choiceKey)) {
      final values = state.getFormBuilderValues(choiceKey);
     //debugPrint('📝 返回已保存的FormBuilder值: $choiceKey -> ${values.keys.toList()}');
      return values;
    }

   //debugPrint('📝 首次访问，返回空值让Modifier页面生成默认值: $choiceKey');
    return {};
  }


  /// 清空指定商品的FormBuilder数据
  void clearFormBuilderData(String choiceKey) {
    final newFormBuilderValues =
        Map<String, Map<String, dynamic>>.from(state.formBuilderValues);
    newFormBuilderValues.remove(choiceKey);

    state = state.copyWith(formBuilderValues: newFormBuilderValues);

   //debugPrint('🗑️ 已清空FormBuilder数据: $choiceKey');
  }

  /// 检查指定商品是否有规格配置
  bool hasFormBuilderValues(String choiceKey) {
    return state.hasFormBuilderValues(choiceKey);
  }

/// 从数据恢复套餐选择状态
/// 作用：用于编辑模式下恢复用户之前的套餐选择
void restoreFromState(ProductComboSelectionState data) {
  debugPrint('🔄 恢复套餐选择状态');
  debugPrint('🔄 选中数量配置: ${data.quantities.length}');
  
  // 直接设置状态
  state = data.copyWith(
    // 重新计算派生状态以确保数据一致性
    groupTotals: _calculateGroupTotals(data.quantities),
    dynamicMaxValues: _calculateDynamicMaxValues(data.quantities),
    groupValidation: _calculateGroupValidationGroup(data.quantities),
    isValid: _validateSelection(data.quantities),
    additionalPrice: _calculateAdditionalPrice(data.quantities),
  );
  
  debugPrint('🔄 套餐选择状态恢复完成');
}

/// 计算分组总数
Map<String, int> _calculateGroupTotals(Map<String, int> quantities) {
  final service = ref.read(productComboServiceProvider);
  return service.calculateGroupTotals(combo, state.copyWith(quantities: quantities));
}

/// 计算动态最大值
Map<String, int> _calculateDynamicMaxValues(Map<String, int> quantities) {
  final service = ref.read(productComboServiceProvider);
  return service.calculateDynamicMaxValues(combo, state.copyWith(quantities: quantities));
}

/// 计算分组验证状态
Map<String, bool> _calculateGroupValidationGroup(Map<String, int> quantities) {
  final groupTotals = _calculateGroupTotals(quantities);
  final validation = <String, bool>{};

  for (final group in combo.groups) {
    final groupTotal = groupTotals[group.id] ?? 0;
    if (group.isRequired) {
      validation[group.id] = groupTotal >= group.minSelections;
    } else {
      validation[group.id] = true;
    }
  }

  return validation;
}

/// 验证选择是否有效
bool _validateSelection(Map<String, int> quantities) {
  final service = ref.read(productComboServiceProvider);
  return service.validateSelection(combo, state.copyWith(quantities: quantities));
}

/// 计算附加价格
double _calculateAdditionalPrice(Map<String, int> quantities) {
  final service = ref.read(productComboServiceProvider);
  return service.calculateAdditionalPrice(combo, state.copyWith(quantities: quantities));
}


}



/// 🔧 新增：派生状态数据类
class _DerivedState {
  final Map<String, int> groupTotals;
  final Map<String, bool> groupValidation;
  final bool isValid;
  final double additionalPrice;
  final Map<String, int> dynamicMaxValues;
  
  _DerivedState({
    required this.groupTotals,
    required this.groupValidation,
    required this.isValid,
    required this.additionalPrice,
    required this.dynamicMaxValues,
  });
}
