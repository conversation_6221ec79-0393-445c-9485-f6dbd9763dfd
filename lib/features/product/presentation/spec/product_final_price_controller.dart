import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/product/presentation/spec/product_special_price_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_selection_controller.dart';

import '../providers/editing_cart_item_provider.dart';
import 'product_combo_selection_controller.dart';
import 'product_spec_screen.dart';
import '../../application/product_combo_calculation_service.dart';
import '../product_detail_controller.dart';
import '../../domain/product_discount_option.dart';

part 'product_final_price_controller.g.dart';

///  从ProductSpecPageData提取基础价格的Provider
/// 避免重复API调用，直接从已获取的页面数据中计算基础价格
@riverpod
double productBasePriceFromPageData(Ref ref, ProductItem product) {
  try {
    // 尝试获取已加载的ProductSpecPageData
    final pageDataAsync = ref.read(productSpecPageDataProvider(product.commodityId.toInt(), product));
    
    return pageDataAsync.when(
      data: (pageData) {
        if (product.commodityType == 3) {
          // 套餐商品：基础价格为0，价格由套餐配置决定
          return 0.0;
        } else {
          // 🔧 修复：基于商品的specType来判断
          if (product.specType == 1) {
            // 单规格商品：从specGroups[0].items[0].price获取基础价格
            if (pageData.specGroups.isNotEmpty) {
              final firstGroup = pageData.specGroups.first;
              if (firstGroup.items.isNotEmpty) {
                final basePrice = firstGroup.items.first.additionalPrice;
               //debugPrint('🔧 单规格商品 ${product.commodityName} 基础价格: $basePrice');
                return basePrice;
              }
            }
            // 如果specGroups为空，返回0
           //debugPrint('🔧 单规格商品 ${product.commodityName} 规格组为空，基础价格: 0.0');
            return 0.0;
          } else {
            // 多规格商品：基础价格为0，完全由规格选择决定
           //debugPrint('🔧 多规格商品 ${product.commodityName} 基础价格: 0.0');
            return 0.0;
          }
        }
      },
      loading: () => 0.0, // 数据加载中，返回0
      error: (_, __) => 0.0, // 出错时返回0
    );
  } catch (e) {
   //debugPrint('🔧 从页面数据获取基础价格失败: $e');
    return 0.0;
  }
}

/// 产品数量和价格状态模型
class ProductFinalPriceState {
  /// 产品模型
  final ProductItem product;

  /// 产品基础价格
  final double basePrice;
  
  /// 产品的"量"（可以是数量或重量）
  final num amount;
  
  /// 规格附加价格
  final double additionalPrice;
  
  /// 真实售价（用户修改的价格，null表示使用原价）
  final double? realSalesPrice;
  
  /// 选中的折扣选项
  final ProductDiscountOption? selectedDiscountOption;

  ///  自定义输入的价格折扣
  final double? customPrice;

  /// 计算真实售价（修改前的完整价格）
  double get actualSalesPrice {
    // 如果用户设置了真实售价，使用真实售价
    if (realSalesPrice != null) {
      return realSalesPrice!;
    }
    // 否则使用原始计算：(基础价格 + 规格附加价格) × 数量
    return (basePrice + additionalPrice) * amount;
  }

  ///  原始价格（未应用折扣前的价格）
  double get originalPrice {
    return actualSalesPrice;
  }
  
  ///  最终价格（应用折扣后的价格）
  double get finalPrice {
    return totalPrice;
  }
  
  /// 计算最终价格 = 真实售价 × 折扣
  double get totalPrice {
    // 如果有自定义输入的价格, 直接使用自定义输入的价格
    if (customPrice != null) {
      return customPrice!;
    }

    // 如果没有自定义输入的价格, 使用真实售价 × 折扣
    final salesPrice = actualSalesPrice;
    
    // 如果没有选择折扣，直接返回真实售价
    if (selectedDiscountOption == null) {
      return salesPrice;
    }

    // 应用折扣计算最终价格
    return ProductDiscountOptionUtils.calculateFinalPrice(selectedDiscountOption!, salesPrice);
  } 

  /// 总价格的getter方法(写死2位小数)
  String get formattedTotalPrice => totalPrice.toStringAsFixed(2);

  /// 真实售价的格式化显示
  String get formattedActualSalesPrice => actualSalesPrice.toStringAsFixed(2);

  const ProductFinalPriceState({
    required this.product,
    required this.basePrice,
    required this.amount,
    required this.additionalPrice,
    this.realSalesPrice, //  可为null
    this.selectedDiscountOption, //  可为null
    this.customPrice, //  可为null
  });


  /// 创建新的状态对象
  ProductFinalPriceState copyWith({
    ProductItem? product,
    double? basePrice,
    num? amount,
    double? additionalPrice,
    double? realSalesPrice, 
    ProductDiscountOption? selectedDiscountOption, 
    double? customPrice,
  }) {
    return ProductFinalPriceState(
      product: product ?? this.product,
      basePrice: basePrice ?? this.basePrice,
      amount: amount ?? this.amount,
      additionalPrice: additionalPrice ?? this.additionalPrice,
      realSalesPrice: realSalesPrice ?? this.realSalesPrice,
      selectedDiscountOption: selectedDiscountOption ?? this.selectedDiscountOption,
      customPrice: customPrice ?? this.customPrice,
    );
  }
}

/// 产品数量和价格控制器(Family控制器)
/// 负责管理产品数量和价格计算
@riverpod
class ProductFinalPriceController extends _$ProductFinalPriceController {
  @override
  ProductFinalPriceState build(ProductItem currentProduct) {
    // 监听规格选择状态变化
    final specSelectionState = ref.watch(productSpecSelectionControllerProvider('product_spec'));

    //  监听真实售价状态
    final realSalesPrice = ref.watch(realSalesPriceProvider);

    //  监听折扣选择状态
    final selectedDiscountOption = ref.watch(selectedDiscountOptionProvider);

    //  监听自定义折扣值
    final customDiscount = ref.watch(customDiscountProvider);

    // 监听当前活跃的标签页
    final currentTab = ref.watch(currentActiveTabProvider);
    
    // 监听套餐价格变化（仅在套餐标签页时）
    // 延迟套餐价格计算，确保数据已加载
  final comboPrice = (currentProduct.commodityType == 3) 
      ? _calculateComboPriceWithDelay(currentProduct) 
      : 0.0;
     //debugPrint('🔧 套餐价格计算结果: $comboPrice');
    // 根据商品类型，确定初始的"量"
    final num initialAmount = currentProduct.commodityType == 2 ? 1.0 : 1;
    
    // 根据当前标签页计算不同的基础价格和附加价格
    final double calculatedBasePrice;
    final double calculatedAdditionalPrice;
    
    if (currentProduct.commodityType == 3) {
      // 套餐商品：使用套餐价格
      calculatedBasePrice = comboPrice;
      calculatedAdditionalPrice = 0.0;
     //debugPrint('🔧 套餐商品 - 基础价格: $calculatedBasePrice, 附加价格: $calculatedAdditionalPrice');
    } else {
      // 单品商品：使用常规价格计算
      calculatedBasePrice = ref.watch(productBasePriceFromPageDataProvider(currentProduct));
      calculatedAdditionalPrice = specSelectionState.additionalPrice;
     //debugPrint('🔧 单品商品 - 基础价格: $calculatedBasePrice, 附加价格: $calculatedAdditionalPrice');
    }

     // 🆕 计算实际售价
    final calculatedActualSalesPrice = realSalesPrice ?? ((calculatedBasePrice + calculatedAdditionalPrice) * initialAmount);

    //  处理自定义折扣：如果是自定义折扣，直接设置真实售价
    //  预计算最终价格（包含自定义折扣处理）
    double calculatedTotalPrice = calculatedActualSalesPrice;
    if (selectedDiscountOption != null) {
      if (selectedDiscountOption.isCustom && customDiscount != null) {
        // 🆕 使用新的方法处理自定义折扣
        calculatedTotalPrice = ProductDiscountOptionUtils.calculateFinalPriceWithCustom(
          selectedDiscountOption,
          calculatedActualSalesPrice,
          customValue: customDiscount,
        );
      } else {
        // 使用原有方法处理普通折扣
        calculatedTotalPrice = ProductDiscountOptionUtils.calculateFinalPrice(
          selectedDiscountOption,
          calculatedActualSalesPrice,
        );
      }
    }
    
    // 返回包含新状态的价格状态
    final state = ProductFinalPriceState(
    product: currentProduct,
    basePrice: calculatedBasePrice,
    amount: initialAmount,
    additionalPrice: calculatedAdditionalPrice,
    realSalesPrice: realSalesPrice,
    selectedDiscountOption: selectedDiscountOption,
    customPrice: calculatedTotalPrice,
  );
  return state;
  
 //debugPrint('🔧 最终状态 - 总价格: ${state.totalPrice}, 格式化价格: ${state.formattedTotalPrice}');
  }

  /// 🔧 新增：延迟套餐价格计算方法
/// 确保在编辑模式下数据完全加载后再计算价格
/// 🔧 新增：延迟套餐价格计算方法
/// 确保在编辑模式下数据完全加载后再计算价格
/// 🔧 新增：延迟套餐价格计算方法
/// 确保在编辑模式下数据完全加载后再计算价格
double _calculateComboPriceWithDelay(ProductItem currentProduct) {
  try {
    // 检查是否为编辑模式
    final editingCartItem = ref.read(editingCartItemProvider);
    final isEditMode = editingCartItem != null;
    
    if (isEditMode) {
      // 🔧 关键修复：使用 watch 而不是 read，这样能监听数据变化
      final pageDataAsync = ref.watch(productSpecPageDataProvider(currentProduct.commodityId.toInt(), currentProduct));

      
      // 只有在数据完全加载后才计算价格
      return pageDataAsync.when(
        data: (pageData) {
          final combo = pageData.combo;
          if (combo != null) {
            
            // 检查套餐选择状态是否已初始化
            final selectionState = ref.read(productComboSelectionControllerProvider(combo));
           //debugPrint('🔧 编辑模式：套餐选择状态 - quantities数量: ${selectionState.quantities.length}');
            
            // 如果选择状态为空或未初始化，延迟计算
            if (selectionState.quantities.isEmpty) {
             //debugPrint('🔧 编辑模式：套餐选择状态未初始化，延迟计算价格');
              // 使用 Future.microtask 延迟到下一个微任务
              Future.microtask(() {
               //debugPrint('🔧 编辑模式：执行延迟重新计算');
                if (mounted) {
                  ref.invalidateSelf(); // 重新触发构建
                }
              });
              return 0.0;
            }
            
            // 数据已准备好，执行正常计算
           //debugPrint('🔧 编辑模式：数据已准备好，执行正常计算');
            final price = _calculateComboPrice(currentProduct);
           //debugPrint('🔧 编辑模式：计算结果价格: $price');
            return price;
          } else {
           //debugPrint('🔧 编辑模式：套餐数据为空');
          }
          return 0.0;
        },
        loading: () {
         //debugPrint('🔧 编辑模式：页面数据加载中，延迟价格计算');
          return 0.0;
        },
        error: (error, stackTrace) {
         //debugPrint('🔧 编辑模式：页面数据加载失败: $error');
          return 0.0;
        },
      );
    } else {
      // 新增模式：使用原有逻辑
     //debugPrint('🔧 新增模式：使用原有逻辑');
      final price = _calculateComboPrice(currentProduct);
     //debugPrint('🔧 新增模式：计算结果价格: $price');
      return price;
    }
  } catch (e) {
   //debugPrint('🔧 延迟套餐价格计算失败: $e');
    return 0.0;
  }
}
/// 检查控制器是否仍然挂载
bool get mounted {
  try {
    // 尝试访问 state，如果控制器已被销毁会抛出异常
    state;
    return true;
  } catch (e) {
    return false;
  }
}

  // 添加套餐价格计算方法
  /// 计算套餐价格（从当前状态获取）
  double _calculateComboPrice(ProductItem currentProduct) {
    try {
      // 🔧 关键修复：从页面数据获取套餐信息
      final pageDataAsync = ref.read(productSpecPageDataProvider(currentProduct.commodityId.toInt(), currentProduct));
      
      return pageDataAsync.when(
        data: (pageData) {
          final combo = pageData.combo;
          if (combo != null) {
            // 🔧 使用套餐总价格Provider
            final totalPrice = ref.watch(comboTotalPriceProvider(combo));
           //debugPrint('🔧 套餐价格计算: ${combo.name} = \$${totalPrice.toStringAsFixed(2)}');
            return totalPrice;
          }
         //debugPrint('🔧 套餐数据为空，价格为0');
          return 0.0;
        },
        loading: () {
         //debugPrint('🔧 套餐数据加载中，价格为0');
          return 0.0;
        },
        error: (error, stackTrace) {
         //debugPrint('🔧 套餐数据加载失败: $error');
          return 0.0;
        },
      );
    } catch (e) {
     //debugPrint('🔧 套餐价格计算失败: $e');
      return 0.0;
    }
  }

  //  设置真实售价
  void setRealSalesPrice(double newPrice) {
    ref.read(realSalesPriceProvider.notifier).update(newPrice);
  }

  //  清除真实售价（恢复使用原价）
  void clearRealSalesPrice() {
    ref.read(realSalesPriceProvider.notifier).clear();
  }

  //  设置折扣选项
  void setDiscountOption(ProductDiscountOption? option) {
    ref.read(selectedDiscountOptionProvider.notifier).update(option);
  }

  //  清除折扣选项
  void clearDiscountOption() {
    ref.read(selectedDiscountOptionProvider.notifier).update(null);
  }
  
  /// 设置产品的"量"（数量或重量）
  void setAmount(num newAmount) {
    // 称重商品的边界保护
    if (state.product.commodityType == 2) { // 重量商品
      if (newAmount <= 0) newAmount = 0.01; // 防止零重量或负重量
      // if (newAmount > 999999) newAmount = 999999; // 防止异常大的重量
    } else { // 计件商品的边界保护  
      if (newAmount < 1) newAmount = 1; // 防止买0件或负数件
      // if (newAmount > 999999) newAmount = 999999; // 防止误操作大量购买
    }

    state = state.copyWith(amount: newAmount);
  }
  
  /// 增加产品数量（仅对计件商品有效）
  void incrementAmount() {
    if (state.product.commodityType != 2) {
      setAmount(state.amount + 1);
    }
  }

  /// 减少产品数量（仅对计件商品有效）
  void decrementAmount() {
    if (state.product.commodityType != 2) {
      setAmount(state.amount - 1);
    }
  }
  
  /// 设置产品基础价格
  // void setBasePrice(double newBasePrice) {
  //   if (newBasePrice < 0) newBasePrice = 0; // 确保价格不为负
    
  //   state = state.copyWith(basePrice: newBasePrice);
  // }
  
  /// 更新附加价格（通常由规格选择控制器调用）
  void updateAdditionalPrice(double newAdditionalPrice) {
    state = state.copyWith(additionalPrice: newAdditionalPrice);
  }
  
  /// 重置状态
  void reset() {
    final initialAmount = state.product.commodityType == 2 ? 1.0 : 1;
    state = state.copyWith(
      basePrice: 0.00,
      amount: initialAmount,
      additionalPrice: 0,
    );
  }
  
  /// 格式化价格显示
  String formatPrice(double price) {
    return 'S\$${price.toStringAsFixed(2)}';
  }
  
  /// 获取格式化的总价格
  String get formattedTotalPrice {
    return formatPrice(state.totalPrice);
  }
}