import 'package:kpos/features/product/data/product_intranet_repository.dart';
import 'package:kpos/features/product/domain/product_category_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_categories_controller.g.dart';

@riverpod
class ProductCategoriesController extends _$ProductCategoriesController {
  @override
  Future<List<ProductCategoryItem>> build() async {
    return _fetch();
  }

  Future<List<ProductCategoryItem>> _fetch() async {
    return ref.read(productIntranetRepositoryProvider).getProductCategories();
  }
}


@riverpod
class SelectProductCategoryId extends _$SelectProductCategoryId {
  @override
  int build() {
    return 0;
  }

  void changeCategoryId(int id) {
    state = id;
  }

}