import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/utils/color_util.dart';
import 'package:kpos/features/product/presentation/categories/product_categories_controller.dart';

class ProductCategoriesView extends ConsumerStatefulWidget {
  const ProductCategoriesView({super.key});

  @override
  ConsumerState createState() => _CategoriesViewState();
}

class _CategoriesViewState extends ConsumerState<ProductCategoriesView> {
  late final ValueNotifier<int> selectedNotifier;

  @override
  void initState() {
    final categoryId = ref.read(selectProductCategoryIdProvider);
   //debugPrint(categoryId);
    selectedNotifier = ValueNotifier<int>(categoryId);
    super.initState();
  }

  @override
  void dispose() {
    selectedNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        width: 140,
        height: double.infinity,
        child: KPAsyncValueWidget(
            asyncValueProvider: productCategoriesControllerProvider,
            dataBuilder: (ct, ref, data) {
              return ListView.separated(
                itemCount: data.length,
                separatorBuilder: (_, __) => const SizedBox(height: 8),
                itemBuilder: (_, index) {
                  final item = data[index];
                  return ValueListenableBuilder(
                    valueListenable: selectedNotifier,
                    builder: (context,value,_) {
                      return GestureDetector(
                        onTap: () {
                          selectedNotifier.value = item.categoryId;
                          ref.read(selectProductCategoryIdProvider.notifier).changeCategoryId(item.categoryId);
                        },
                        child: Container(
                          width: 100,
                          height: 48,
                          decoration: BoxDecoration(
    // 选中时使用对应分类颜色的半透明蒙版，未选中时保持白色
    color: item.categoryId == value
        ? ColorUtil.parse(item.colorHex).withOpacity(0.12)
        : Colors.white,
    borderRadius: BorderRadius.circular(8),
    border: item.categoryId == value
        ? Border.all(
            color: ColorUtil.parse(item.colorHex), width: 1)
        : null,
  ),
                          padding: item.categoryId == value
                              ? const EdgeInsets.all(2)
                              : EdgeInsets.zero,
                          child: Row(
                            children: [
                              Container(
                                width: 8,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: ColorUtil.parse(item.colorHex),
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(8),
                                    bottomLeft: Radius.circular(8),
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Text(
                                    item.categoryName,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        color: item.categoryId == value
                                            ? const Color(0xFFFF4D4F)
                                            : Colors.black,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 12),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                  );
                },
              );
            }));
  }
}
