import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/kp_badge.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/cart/presentation/cart_screen.dart';
import 'package:kpos/common/components/kp_pagination.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/presentation/categories/product_categories_view.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';
import 'package:kpos/features/product/presentation/product_detail_controller.dart';
import 'package:kpos/features/product/presentation/product_pagination_controller.dart';
import 'package:kpos/features/product/presentation/providers/product_long_pressed_item_provider.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_screen.dart';
import 'package:kpos/features/product/sold_on/presentation/sold_out_products_controller.dart';
import 'package:kpos/features/store/application/store_prepared_reason_service.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import '../data/product_intranet_repository.dart';
import '../sold_on/presentation/sold_out_cancel_confirm_view.dart';
import 'widgets/product_item_tags.dart';

import '../../../assets/assets.gen.dart';
import '../sold_on/domain/sold_out_detail_product.dart';
import '../sold_on/presentation/sold_out_confirm_view.dart';



/// 商品展示主屏幕, 包含商品分类、商品列表网格和右侧的购物车.
class ProductScreen extends ConsumerStatefulWidget {
  final TableItem? table;
  final TableGroupItem? tableGroup;

  const ProductScreen({super.key, this.table,this.tableGroup});

  @override
  ConsumerState createState() => _ProductScreenState();
}

class _ProductScreenState extends ConsumerState<ProductScreen> {

  // 获取go_router实例, 用于路由监听
  late final GoRouter _goRouter;
  

  @override
  void initState() {
    // 初始化时, 获取GoRouter实例并添加路由监听器
    super.initState();
    _goRouter = ref.read(goRouterProvider);
    // 添加路由监听
    _goRouter.routerDelegate.addListener(_onRouteChanged);
    // 预加载所有预设原因数据
    _preloadPreparedReasons();
  }

  /// 预加载预设原因数据
  void _preloadPreparedReasons() {
    // 在后台异步预加载，不阻塞UI
    Future.microtask(() async {
      try {
        //debugPrint('🚀 开始预加载预设原因数据...');
        
        // 并行预加载所有类型的原因数据
        await Future.wait([
          ref.read(discountReasonsProvider.future),
          ref.read(returnReasonsProvider.future),
          ref.read(remarkReasonsProvider.future),
          ref.read(cancelReasonsProvider.future),
          ref.read(serviceFeeCancelReasonsProvider.future),
        ]);

        //debugPrint('✅ 所有预设原因数据预加载完成');
      } catch (e) {
        //debugPrint('❌ 预设原因数据预加载失败: $e');
      }
    });
  }

    // 路由变化监听器
  void _onRouteChanged() {
    // 当路由变化时，检查当前是否显示弹窗，并关闭
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  @override
    @override
  void dispose() {
    // 组件销毁时, 移除路由监听器以防止内存泄漏
    // 移除路由监听
    _goRouter.routerDelegate.removeListener(_onRouteChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: context.theme.grayColor3,
        body: Row(
          children: [
            const ProductCategoriesView(),
            Expanded(child: _buildProductView()),
            Container(
              margin: const EdgeInsets.only(left: 12),
              width: 360,
              color: Colors.white,
              height: double.infinity,
              child: CartScreen(table: widget.table,tableGroup: widget.tableGroup),
            ),
          ],
        ));
  }

  // 在_buildProductView方法中完全替换EasyRefresh部分：
Widget _buildProductView() {
  // KPAsyncValueWidget 可以优雅地处理数据加载中、加载成功和加载失败这三种状态。
  return KPAsyncValueWidget(
    asyncValueProvider: productControllerProvider,
    dataBuilder: (context, ref, data) {
      // 监听分页状态
      final paginationState = ref.watch(productPaginationProvider);
      // 获取当前页的产品数据
      final currentPageProducts = ref.watch(currentPageProductsProvider);
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      // 作用：
      // 这是Flutter的延迟执行机制
      // 等待当前Widget构建完成后，再执行回调函数
      // 确保在Widget构建期间不会修改Provider状态（避免构建时状态变更的警告）
      // ⭐️ 计算每页项目数并更新状态
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final itemCount = getColumnCount() * getRowCount();
        // ✅ 写死每页数量进行测试
        // final itemCount = 1; // 测试用：每页只显示2个商品
        // final itemCount = 1;
        if (paginationState.itemsPerPage != itemCount) {
          ref.read(productPaginationProvider.notifier).updateItemsPerPage(itemCount);
        }

        // // ✅ 打印当前显示的数据信息
        // //debugPrint('🎨 UI更新信息：');
        // //debugPrint('   总商品数: ${data.totalCount}');
        // //debugPrint('   当前页码: ${paginationState.currentPage}');
        // //debugPrint('   每页数量: ${itemCount}');
        // //debugPrint('   当前页显示: ${currentPageProducts.length} 个商品');
        // //debugPrint('   总页数: ${(data.totalCount / itemCount).ceil()}');
      });
      
      return data.totalCount <= 0
          ? const TDEmpty(emptyText: '暂无数据')
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: EasyRefresh(
                    onRefresh: () async {
                      try {
                        // 1. 重置分页到第一页
                        ref.read(productPaginationProvider.notifier).resetToFirstPage();
                        // 2. 刷新数据
                        ref.invalidate(productControllerProvider);
                        // 3. 可选：同时刷新分类数据
                        // ref.invalidate(productCategoriesControllerProvider);
                      } catch (e) {
                        //debugPrint('刷新失败: $e');
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('刷新失败，请重试')),
                          );
                        }
                      }
                    },
                    onLoad: () async {
                      try {
                        // 基于Riverpod的上拉加载逻辑
                        final currentPage = paginationState.currentPage;
                        final itemCount = getColumnCount() * getRowCount();
                        final totalPages = (data.totalCount / itemCount).ceil();
                        
                        if (currentPage < totalPages) {
                          // 更新到下一页
                          ref.read(productPaginationProvider.notifier).updatePage(currentPage + 1);
                        } else {
                          // 已经是最后一页，给用户提示
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('已显示全部商品'),
                                duration: Duration(seconds: 1),
                              ),
                            );
                          }
                        }
                      } catch (e) {
                        //debugPrint('加载更多失败: $e');
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('加载失败，请重试')),
                          );
                        }
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: LayoutBuilder(builder: (context, constraints) {
                        final width = constraints.maxWidth;
                        final height = constraints.maxHeight;
                        const columnGap = 12.0;
                        const rowGap = 12.0;
                        final columnCount = getColumnCount();
                        final rowCount = getRowCount();
                        final totalColumnGap = columnGap * (columnCount - 1);
                        final itemWidth = (width - totalColumnGap) / columnCount;
                        final totalRowGap = rowGap * (rowCount - 1);
                        final itemHeight = (height - totalRowGap) / rowCount;
                        final columnSizes = List.generate(
                            columnCount, (_) => FixedTrackSize(itemWidth));
                        final rowSizes =
                            List.generate(rowCount, (_) => FixedTrackSize(itemHeight));
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: LayoutGrid(
                            columnSizes: columnSizes,
                            rowSizes: rowSizes,
                            rowGap: rowGap,
                            columnGap: columnGap,
                            children: [
                              // 使用分页后的数据
                              for (var i = 0; i < currentPageProducts.length; i++)
                                _buildProductItem(currentPageProducts[i], i)
                            ],
                          ),
                        );
                      }),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                KPPagination(
                    // ✅ 正确的计算：使用实际的分页设置
                    totalPage: paginationState.itemsPerPage > 0
                        ? (data.totalCount / paginationState.itemsPerPage)
                            .ceil()
                        : 1,
                    selectedPage: paginationState.currentPage,
                    onPageSelected: (int newPage) {
                      //debugPrint('🔢 手动选择页码: $newPage');
                      ref
                          .read(productPaginationProvider.notifier)
                          .updatePage(newPage);
                    },
                  ),
                  const SizedBox(height: 8),
                ],
              );
    },
  );
}

    /// 构建单个商品卡片项.使用stack布局，将“已售罄 (Sold out)”的蒙层叠加在商品内容之上
  Widget _buildProductItem(ProductItem item, int index) {
  // 使用生成的provider监听长按状态
  final longPressedItemId = ref.watch(productLongPressedItemProvider);
  final isLongPressed = longPressedItemId == item.commodityId.toInt();
  final isSoldOut = item.soldOut == 1;

  return Stack(
    children: [
      _buildProductItemContent(item, index),        
      // 原有的售罄样式 - 传入item参数支持长按
      isSoldOut && !isLongPressed
          ? _buildSoldOutOverlay(item)  // 关键修改：传入item参数
          : const SizedBox(),
      // 长按后的操作覆盖层 - 根据当前状态显示不同操作
      isLongPressed 
          ? (isSoldOut 
              ? _buildLongPressMarkAvailableOverlay(item)  // 已售罄 → 显示恢复可用
              : _buildLongPressSoldOutOverlay(item))       // 可用 → 显示设置售罄
          : const SizedBox(),
    ],
  );
}

  /// 👇🏻👇🏻 售罄 👇🏻👇🏻
  
  /// 构建长按后的"Sold out"覆盖层 - 红色横条样式(底部)
  Widget _buildLongPressSoldOutOverlay(ProductItem item) {
    return GestureDetector(
      onTap: () {
        // 点击灰色区域取消长按状态
        ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0x80000000), // 半透明黑色背景
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Column(
            children: [
              // 上方空白区域，点击可取消
            const Expanded(child: SizedBox()),
             // 底部的红色"Sold out"横条
            GestureDetector(
              onTap: () => _showSoldOutConfirmDialog(item),
              child: Container(
                width: double.infinity,
                height: 40,
                margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
              color: KPColors.fillRedNormal, // 红色背景
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: Text(
                context.locale.soldOut,
                style: KPFontStyle.headingSmall.copyWith(
                  color: KPColors.textGrayInverse,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
              ),
            )
            ],
          )
        ),
      ),
    );
  }

  /// 构建长按后的"Mark available"覆盖层 - 取消售罄
Widget _buildLongPressMarkAvailableOverlay(ProductItem item) {
  return GestureDetector(
    onTap: () {
      // 点击灰色区域取消长按状态
      ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
    },
    child: Container(
      decoration: BoxDecoration(
        color: const Color(0x80000000), // 半透明黑色背景
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          children: [
            // 上方空白区域，点击可取消
            const Expanded(child: SizedBox()),
            // 底部的橙色边框"Mark available"按钮
            GestureDetector(
              onTap: () => _showMarkAvailableConfirmDialog(item),
              child: Container(
                width: double.infinity,
                height: 40,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white, // 白色背景
                  border: Border.all(
                    color: const Color(0xFFFF8500), // 橙色边框
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Center(
                  child: Text(
                    'Mark available', // 标记为可用
                    style: KPFontStyle.headingSmall.copyWith(
                      color: const Color(0xFFFF8500), // 橙色文字
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            )
          ],
        )
      ),
    ),
  );
}

  /// 构建原有的售罄覆盖层 - 斜角样式
  Widget _buildSoldOutOverlay(ProductItem item) {
    return GestureDetector(
      // 关键修改：添加长按事件透传，让已售罄商品也能响应长按
      onLongPress: () => _onItemLongPress(context, item),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xCCFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Transform.rotate(
            angle: -15 * 3.1415926 / 180,
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: const Color(0xFFF15B50), width: 1),
              ),
              width: 91,
              height: 34,
              child: const Text(
                'Sold out',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFFF15B50),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示售罄确认对话框
void _showSoldOutConfirmDialog(ProductItem item) async {
  try {
    // 直接获取数据并显示弹窗
    final affectedProducts = await ref.read(
      soldOutProductsControllerProvider(item.commodityId).future
    );
    
    if (mounted) {
      KPSlidePopup.showFromTarget(
        context: context,
        contentWidget: SoldOutConfirmView(
          product: item,
          affectedProducts: item.commodityType != 3 && affectedProducts.isNotEmpty 
            ? affectedProducts 
            : null,
            onConfirm: () => _confirmSoldOut(item),
            onCancel: () {
              ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
            },
        ),
        targetWidth: 0,
        allowMultipleLayers: true,
      );
    }
  } catch (e) {
    print('❌ 获取关联商品失败: $e');
  }
}
   /// 显示售罄确认对话框
  // void _showSoldOutConfirmDialog(ProductItem item) {

  //   KPSlidePopup.showFromTarget(
  //     context: context, 
  //     contentWidget: Consumer(
  //       builder: (context, ref, child) {
  //         final soldOutProductsAsync = ref.watch(
  //           soldOutProductsControllerProvider(item.commodityId.toInt())
  //         );

  //         return soldOutProductsAsync.when(
  //           // 数据加载中
  //           loading: () => ProductSoldOutConfirmView(
  //           product: item,
  //           affectedProducts: null, // 显示loading状态            
  //           onConfirm: () => _confirmSoldOut(item),
  //           onCancel: () {
  //             ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
  //           },
  //         ),

  //           // 数据加载完成, 展示关联商品
  //           data: (affectedProducts) => ProductSoldOutConfirmView(
  //             product: item,
  //             affectedProducts: item.commodityType != 3 && affectedProducts.isNotEmpty 
  //               ? affectedProducts 
  //               : null,
  //             onConfirm: () => _confirmSoldOut(item),
  //             onCancel: () {
  //               ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
  //             },
  //             ), 

  //           // 数据加载异常  
  //           error: (error, StackTrace stackTrace) => const TDEmpty(emptyText: '加载关联商品失败'),             
  //           );
  //       }
  //       ), 
  //     targetWidth: 0,
  //     allowMultipleLayers: true,
  //   );
  // }

//   单品 + 长按 → 点击"Mark available"按钮 → 直接执行API请求
//   套餐(无关联) + 长按 → 点击"Mark available"按钮 → 直接执行API请求
//   套餐(有关联) + 长按 → 点击"Mark available"按钮 → 显示二次确认弹窗 → 用户确认 → 执行API请求
  /// 显示取消售罄对话框
void _showMarkAvailableConfirmDialog(ProductItem item) async {
  // 清除长按状态
  ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
  
  try {
    // == 修改开始 ==
    // 1. 判断商品类型
    if (item.commodityType == 3) {
      // 2. 套餐商品：查询关联商品
      final affectedProducts = await ref.read(
        soldOutProductsControllerProvider(item.commodityId).future
      );
      
      if (affectedProducts.isEmpty) {
        // 2.1 套餐无关联商品：直接执行API请求
        await _callMarkAvailableApi(item);
      } else {
        // 2.2 套餐有关联商品：显示二次确认弹窗
        _showConfirmDialog(item, affectedProducts);
      }
    } else {
      // 3. 单品：直接执行API请求
      await _callMarkAvailableApi(item);
    }
    // == 修改结束 ==
  } catch (error) {
    final errorMessage = _extractServerMessage(
      error, 
      '处理 ${item.commodityName} 操作失败，请重试'
    );
    
    if (mounted) {
      KPToast.show(
        content: errorMessage,
        isGreen: false,
        position: ToastPosition.bottom,
      );
    }
    debugPrint('处理Mark available操作失败: $error');
  }
}

/// 取消售罄显示二次确认弹窗（仅套餐有关联商品时使用）
void _showConfirmDialog(ProductItem item, List<SoldOutDetailProduct> affectedProducts, {bool isCancelAllPopup = false, bool? disableAutoFetch}) {
  if (mounted) {
    KPSlidePopup.showFromTarget(
      context: context,
      contentWidget: SoldOutCancelConfirmView(
        product: item,
        affectedProducts: affectedProducts,
        isCancelAllPopup:isCancelAllPopup,
        disableAutoFetch: disableAutoFetch ?? false,
        onConfirm: () {
          // 确认后直接调用API
          _callMarkAvailableApi(item);
        },
        onCancel: () {
          // 取消操作，仅关闭弹窗
          KPSlidePopup.dismissFromTarget();
        },
        onShowConfirmDialog: (item, affectedProducts, {bool isCancelAllPopup = false, bool? disableAutoFetch}) {
          _showConfirmDialog(item, affectedProducts, isCancelAllPopup: isCancelAllPopup, disableAutoFetch: disableAutoFetch);
        },
        onRefreshAffectedProducts: () async {
  // 重新请求关联商品数据并刷新当前弹窗
  try {
    print('🔄 正在重新请求关联商品数据...');
    
    // 刷新数据源 - 这会触发所有监听该数据的Widget自动重建
    ref.invalidate(soldOutProductsControllerProvider(item.commodityId));
    
    print('🔄 关联商品数据已刷新，弹窗将自动更新');
    
  } catch (e) {
    print('❌ 刷新关联商品失败: $e');
  }
},
      ),
      targetWidth: 0,
      allowMultipleLayers: true,
    );
  }
}

/// 从API错误中提取服务器消息
String _extractServerMessage(dynamic error, String defaultMessage) {
  try {
    final errorStr = error.toString();
    final messageMatch = RegExp(r'"message"\s*:\s*"([^"]*)"').firstMatch(errorStr);
    if (messageMatch != null) {
      final serverMessage = messageMatch.group(1);
      if (serverMessage != null && serverMessage.isNotEmpty) {
        return serverMessage;
      }
    }
  } catch (e) {
    debugPrint('解析错误消息失败: $e');
  }
  return defaultMessage;
}

/// 调用恢复可用API
Future<void> _callMarkAvailableApi(ProductItem item) async {
  try {
    // == 修改开始 ==
    // 直接调用Repository，绕过Controller的异常处理
    final repository = ref.read(productIntranetRepositoryProvider);
    await repository.setProductSoldOut(
      type: 0, // 恢复为可用状态
      productId: item.commodityId,
    );
    
    // 只有真正成功（没抛异常）才执行这里
    if (mounted) {
      KPToast.show(
        content: '${item.commodityName} 已恢复可用',
        isGreen: true,
        position: ToastPosition.bottom,
      );
    }
    
    // 手动刷新数据（原本Controller做的事）
    ref.invalidate(productControllerProvider);
    ref.read(productPaginationProvider.notifier).resetToFirstPage();
    // == 修改结束 ==
    
  } catch (error) {
    // == 修改开始 ==
    // 使用工具方法提取服务器返回的错误消息
    final errorMessage = _extractServerMessage(
      error, 
      '恢复 ${item.commodityName} 可用状态失败，请重试'
    );
    
    if (mounted) {
      KPToast.show(
        content: errorMessage,
        isGreen: false,
        position: ToastPosition.bottom,
      );
    }
    debugPrint('恢复商品可用状态失败: $error');
    // == 修改结束 ==
  }
}

/// 处理错误
void _handleMarkAvailableError(ProductItem item, dynamic error) {
  if (mounted) {
    KPToast.show(
      content: '恢复 ${item.commodityName} 可用状态失败，请重试',
      isGreen: false,
      position: ToastPosition.bottom,
    );
  }
  debugPrint('恢复商品可用状态失败: $error');
}




    // 模拟关联商品数据
    // List<SoldOutDetailProduct>? affectedProducts;
    
    // // 单品商品：可能有关联商品（根据后台接口返回，现在写死假数据）
    // if (item.commodityType != 3) {
    //   // 使用SoldOutDetailProduct创建模拟数据
    //   affectedProducts = SoldOutDetailProduct.createMockData();
    // }
    // // 套餐商品：一定是无关联商品模式，affectedProducts 保持为 null
    
    // KPSlidePopup.showFromTarget(
    //   context: context,
    //   contentWidget: ProductSoldOutConfirmView(
    //     product: item,
    //     affectedProducts: affectedProducts,
    //     onConfirm: () => _confirmSoldOut(item),
    //     forceShowMode: false,
    //     onCancel: () {
    //       ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
    //     },
    //   ),
    //   targetWidth: 0,
    //   allowMultipleLayers: true,
    // );
  

  /// 确认售罄操作
  void _confirmSoldOut(ProductItem item) {
    // 清除长按状态
    ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
    
    // TODO: 调用后台API设置商品为售罄状态
    // 这里应该调用相应的service或provider来更新商品状态
    
    // 显示成功提示
    if (mounted) {
      KPToast.show(
        content: '${item.commodityName} 已设置为售罄',
        isGreen: true,
        position: ToastPosition.bottom,
      );
    }
    
    // 可选：刷新商品列表以反映状态变化
    // ref.invalidate(productControllerProvider);
  }

/// 确认恢复可用操作(取消售罄)
void _confirmMarkAvailable(ProductItem item) {
  // 清除长按状态
  ref.read(productLongPressedItemProvider.notifier).clearLongPressedItem();
  
  // TODO: 调用后台API设置商品为可用状态
  // 这里应该调用相应的service或provider来更新商品状态
  
  // 显示成功提示
  if (mounted) {
    KPToast.show(
      content: '${item.commodityName} 已恢复可用',
      isGreen: true,
      position: ToastPosition.bottom,
    );
  }
  
  // 可选：刷新商品列表以反映状态变化
  // ref.invalidate(productControllerProvider);
}


  /// 👆🏻👆🏻 售罄 👆🏻👆🏻

    /// 构建商品卡片的具体内容, 包括名称、图片、价格和各种标签.
  Widget _buildProductItemContent(ProductItem item, int index) {
    List<Widget> tagsWidget;

     /// 商品类型：1=计量商品，2=称重商品，3=套餐，4=加料，5=餐盒
    switch(item.commodityType) {
      case 1:
        tagsWidget = [ProductItemTags(product: item, style: ProductTagType.activityOnly),];
        break;
      case 2:
        tagsWidget = [ProductItemTags(product: item, style: ProductTagType.scalesAndActivity),];
        break;
      case 3:
        tagsWidget = [ProductItemTags(product: item, style: ProductTagType.comboAndActivity),];
        break;
      //4以下,都显示活动标签(1的写法)
      case 4:
        tagsWidget = [];
        break;
      case 5:
        tagsWidget = [];
        break;
      default:
        tagsWidget = [];
    }

    return GestureDetector(
      onTap: () => _onItemTap(context, item),
      onLongPress: () => _onItemLongPress(context, item),
      child: Container(
        width: double.maxFinite,
        height: double.maxFinite,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          KPBadge(
              value: item.cartCount,
              offsetX: 4,
              offsetY: 4,
              child: Container(
                height: 60,
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.fromLTRB(12, 8, 32, 8),
                child: Text(
                  item.commodityName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w700),
                ),
              ),
            ),
            Expanded(
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      bottom: Radius.circular(8),
                    ),
                    child: item.commodityImagePath.isEmpty
                        ? const SizedBox(
                            width: double.maxFinite,
                            height: double.maxFinite,
                          )
                        : CachedNetworkImage(
                            imageUrl: item.commodityImagePath,
                            width: double.infinity,
                            height: double.infinity,
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) => Assets.images.iconTest6
                                .image(
                                    width: double.infinity,
                                    height: double.infinity,
                                    fit: BoxFit.cover),
                          ),
                  ),
                  Positioned(
                    top: 4,
                    left: 12,
                    right: 12,
                    child: Wrap(
                      spacing: 6,
                      runSpacing: 6,
                      children: tagsWidget,
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: 36,
                    child: ClipRRect(
                            borderRadius:
                                const BorderRadius.vertical(bottom: Radius.circular(8)),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 0, sigmaY: 10),
                              child: Container(
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment(-1, 0),
                                    end: Alignment(1, 0),
                                    colors: [
                                      Color(0x00000000),
                                      Color(0x01000000),
                                      Color(0x03000000),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                  ),
                  Positioned( // 价格
                      left: 12,
                      right: 12,
                      bottom: 8,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text("${item.currencyUnit}${(item.price ?? 0.0).toStringAsFixed(2)}${item.commodityUnit.isNotEmpty ? "/${item.commodityUnit}" : ""}",
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w700,
                                color: (item.commodityImagePath ?? '').isEmpty
                                    ? Colors.black
                                    : Colors.white,
                              )),
                          const SizedBox(width: 6),
                          item.hasActivePromotion
                              ? Assets.images.iconProductItemBottomRomotion.svg(width: 16, height: 16)
                              : const SizedBox()
                                  
                        ],
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

//    不同屏幕尺寸的数据量示例
// 屏幕尺寸	宽度范围	高度范围	列数	行数	每页数据量
// 超大屏	≥1580px	≥940px	5列	5行	25个商品
// 大屏	≥1380px	≥790px	4列	4行	16个商品
// 中屏	≥1180px	≥640px	3列	3行	9个商品
// 小屏	<1180px	<640px	2列	2行	4个商品
    /// 根据屏幕宽度动态计算网格布局的列数, 以实现响应式UI.
  int getColumnCount() {
    final width = DeviceUtil.deviceWidth;
    if (width >= 1580) return 5;
    if (width >= 1380) return 4;
    if (width >= 1180) return 3;
    return 2;
  }

    /// 根据屏幕高度动态计算网格布局的行数, 以实现响应式UI.
  int getRowCount() {
    final height = DeviceUtil.deviceHeight;
    if (height >= 940) return 5;
    if (height >= 790) return 4;
    if (height >= 640) return 3;
    return 2;
  }

    ///⭐️ 处理商品卡片的点击事件.
  void _onItemTap(BuildContext context, ProductItem item) async {
    final initialTabIndex = item.commodityType == 3 ? 3 : 0;
    final productId = item.commodityId.toInt();
    
    try {
      //  这里调用的Provider已经内部区分了单品和套餐
      final pageData = await ref.watch(productSpecPageDataProvider(productId, item).future);

      if (mounted) {
        KPSlidePopup.showFromTarget(
          context: context,
          contentWidget: ProductSpecScreen(
            pageData: pageData,
            initialTabIndex: initialTabIndex,
          ),
          targetWidth: 360, 
          allowMultipleLayers: true,
        );
      }
    } catch (e) {
     //debugPrint('❌ 获取商品详情失败: $e');
      if (mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('获取商品详情失败: ${e.toString()}'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
    }
  }

    /// 处理商品卡片的长按事件（500ms）
  void _onItemLongPress(BuildContext context, ProductItem item) async {
    debugPrint('🔥 商品长按: ${item.commodityName}');
 
    // 使用生成的provider设置当前长按的商品ID
    ref.read(productLongPressedItemProvider.notifier).setLongPressedItem(item.commodityId.toInt());
  }

}
