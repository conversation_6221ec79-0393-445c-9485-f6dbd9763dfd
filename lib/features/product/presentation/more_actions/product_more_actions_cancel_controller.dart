import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_more_actions_cancel_controller.g.dart';

/// 取消操作状态
class CancelActionState {
  final bool isLoading;
  final String? errorMessage;

  const CancelActionState({
    this.isLoading = false,
    this.errorMessage,
  });

  CancelActionState copyWith({
    bool? isLoading,
    String? errorMessage,
  }) {
    return CancelActionState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// 取消操作控制器
@riverpod
class ProductMoreActionsCancelController extends _$ProductMoreActionsCancelController {
  @override
  CancelActionState build() {
    return const CancelActionState();
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// 设置错误信息
  void setError(String? error) {
    state = state.copyWith(errorMessage: error);
  }

  /// 清除状态
  void clearState() {
    state = const CancelActionState();
  }
}