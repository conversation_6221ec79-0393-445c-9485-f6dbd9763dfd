import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/product/domain/product_action_config.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/common/components/kp_toast.dart';
import 'package:oktoast/oktoast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../common/components/index.dart';
import '../../../cart/presentation/cart_controller.dart';
import '../../../cart/presentation/cart_product_tags.dart';

part 'product_more_actions_controller.g.dart';

/// 商品更多操作状态
class ProductMoreActionsState {
  final bool isLoading;
  final String? selectedStaffName;
  final String? errorMessage;

  const ProductMoreActionsState({
    this.isLoading = false,
    this.selectedStaffName,
    this.errorMessage,
  });

  ProductMoreActionsState copyWith({
    bool? isLoading,
    String? selectedStaffName,
    String? errorMessage,
  }) {
    return ProductMoreActionsState(
      isLoading: isLoading ?? this.isLoading,
      selectedStaffName: selectedStaffName ?? this.selectedStaffName,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// 商品更多操作控制器
@riverpod
class ProductMoreActionsController extends _$ProductMoreActionsController {
  @override
  ProductMoreActionsState build() {
    return const ProductMoreActionsState();
  }

  /// 执行操作
  Future<void> executeAction(
    ProductActionType actionType,
    ProductItem product,
    CartProductItem? cartItem,
  ) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      switch (actionType) {
        case ProductActionType.packaging:
          await _handlePackaging(cartItem);
          break;
        case ProductActionType.portionDish:
          await _handlePortionDish(cartItem);
          break;
        case ProductActionType.commissionStaff:
          await _handleCommissionStaff(cartItem);
          break;
        case ProductActionType.deleteDish:
          await _handleDeleteDish(cartItem);
          break;
        case ProductActionType.urgeDish:
          await _handleUrgeDish(cartItem);
          break;
        case ProductActionType.cancelUrgeDish:
          await _handleCancelUrgeDish(cartItem);
          break;
        case ProductActionType.returnDish:
          await _handleReturnDish(cartItem);
          break;
        case ProductActionType.dishServed:
          await _handleDishServed(cartItem);
          break;
        case ProductActionType.cancelDishServed:
          await _handleCancelDishServed(cartItem);
          break;
        case ProductActionType.cancelOrder:
          await _handleCancelOrder(cartItem);
          break;
      }
      
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      KPToast.show(content: '操作失败: $e', isGreen: false);
    }
  }

  /// 处理打包操作
  Future<void> _handlePackaging(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.updateCartItemStatus(
      cartItem.orderItemId, 
      CartItemStatus.packaging.value,
    );
    
    KPToast.show(content: '${cartItem.commodityName} 已标记为打包', isGreen: true);
  }

  /// 处理分菜操作
  Future<void> _handlePortionDish(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    // 这里可以添加分菜的具体逻辑
    KPToast.show(content: '${cartItem.commodityName} 分菜操作完成', isGreen: true);
  }

  /// 处理委托员工操作
  Future<void> _handleCommissionStaff(CartProductItem? cartItem) async {
    if (cartItem == null || state.selectedStaffName == null) return;
    
    // 这里可以添加委托员工的具体逻辑
    KPToast.show(
      content: '${cartItem.commodityName} 已委托给 ${state.selectedStaffName}', 
      isGreen: true,
    );
  }

  /// 处理删除菜品操作
  Future<void> _handleDeleteDish(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.deleteCartProduct(cartItem.orderItemId);
    
    ref.invalidate(cartControllerProvider);

    KPToast.show(content: '${cartItem.commodityName} 已删除', isGreen: true, position: ToastPosition.bottom);
    
    KPSlidePopup.dismissFromTarget();
  }

  /// 处理催菜操作
  Future<void> _handleUrgeDish(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.updateCartItemStatus(
      cartItem.orderItemId, 
      CartItemStatus.urgeDishes.value,
    );
    ref.invalidate(cartControllerProvider);
    
    KPToast.show(content: '${cartItem.commodityName} 催菜完成', isGreen: true);
  }

  /// 处理取消催菜操作
  Future<void> _handleCancelUrgeDish(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.cancelUrgeDishes(
      cartItem.orderItemId, 
    );
    ref.invalidate(cartControllerProvider);

    KPToast.show(content: '${cartItem.commodityName} 已取消催菜', isGreen: true, position: ToastPosition.bottom);
  }

  /// 处理退菜操作
  Future<void> _handleReturnDish(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.updateCartItemStatus(
      cartItem.orderItemId, 
      CartItemStatus.returnDish.value,
    );
    // 刷新购物车数据展示
    ref.invalidate(cartControllerProvider);
    
    print("退菜完成");
    // KPToast.show(content: '${cartItem.commodityName} 已划菜', isGreen: true);
  }

  /// 设置选中的员工
  void setSelectedStaff(String staffName) {
    state = state.copyWith(selectedStaffName: staffName);
  }

  /// 处理划菜操作
  Future<void> _handleDishServed(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.updateCartItemStatus(
      cartItem.orderItemId, 
      CartItemStatus.dishesServed.value,
    );
    // 刷新购物车数据展示
    ref.invalidate(cartControllerProvider);

    // print("划菜完成");
    KPToast.show(content: '${cartItem.commodityName} 划菜完成', isGreen: true, position: ToastPosition.bottom);
  }

  /// 处理取消划菜操作
  Future<void> _handleCancelDishServed(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    final cartService = ref.read(cartServiceProvider);
    await cartService.cancelDishesServed(
      cartItem.orderItemId, 
    );
    // 刷新购物车数据展示
    ref.invalidate(cartControllerProvider);

    // print("取消划菜完成");
    KPToast.show(content: '${cartItem.commodityName} 已取消划菜', isGreen: true, position: ToastPosition.bottom);
  }

  /// 处理取消订单操作
  Future<void> _handleCancelOrder(CartProductItem? cartItem) async {
    if (cartItem == null) return;
    
    // final cartService = ref.read(cartServiceProvider);
    // await cartService.cancelOrder(cartItem.orderItemId);

    //  // 刷新购物车数据展示
    // ref.invalidate(cartControllerProvider);

    // // print("取消划菜完成");
    // KPToast.show(content: '${cartItem.commodityName} 已取消订单', isGreen: true, position: ToastPosition.bottom);
  }
}

//   packaging,      // 打包
//   portionDish,    // 分菜
//   commissionStaff, // 委托员工
//   deleteDish,     // 删除菜品
//   urgeDish,       // 催菜
//   returnDish,     // 退菜
//  dishServed,     // 划菜

/// 商品操作配置 Provider
@riverpod
List<ProductActionConfig> productActionConfigs(
  Ref ref,
  ProductItem product,
  CartProductItem? cartItem,
) {
  final controller = ref.watch(productMoreActionsControllerProvider.notifier);

  // 实时获取最新的购物车数据,声明一个变量 latestCartItem 来存储最新的购物车商品数据
  CartProductItem? latestCartItem = cartItem;
  // 只有当商品已经在购物车中时（有orderItemId）才需要获取最新数据
  // 检查传入的 cartItem 是否有有效的 orderItemId
  /**
   * whenOrNull 是处理 AsyncValue 的安全方法
   * 只有当状态是 data（成功加载）时才执行回调
   * 如果是 loading 或 error 状态，返回 null
   * ?? cartItem 确保总是有一个有效值（fallback机制）
   */
  if (cartItem?.orderItemId != null) {
    // 当购物车数据发生任何变化时，这个Provider会自动重建
    final cartState = ref.watch(cartControllerProvider);
    latestCartItem = cartState.whenOrNull(
      data: (cartData) {
        // 从最新的购物车数据中查找对应的商品
        try {
          return cartData.firstWhere(
            (item) => item.orderItemId == cartItem!.orderItemId,
          );
        } catch (e) {
          // 如果找不到，使用原始数据
          return cartItem;
        }
      },
    ) ?? cartItem;
  }

  // 根据最新数据判断划菜状态
  final isServed = latestCartItem?.isServed == 1;
  // 判断催菜状态
  final isUrge = latestCartItem?.isUrge == 1;
  
  // 根据商品类型和状态动态生成配置
  final configs = <ProductActionConfig>[];
  
  // 划菜操作
  if (isServed) {
    configs.add(
      ProductActionConfig(
        id: 'cancel_dishServed',
        iconWidget: ProductActionType.cancelDishServed.iconWidget,
        title: ProductActionType.cancelDishServed.displayName,
        actionType: ProductActionType.cancelDishServed,
        enabled: cartItem != null,
        onTap: () => controller.executeAction(
          ProductActionType.cancelDishServed,
          product,
          latestCartItem,
        ),
      ),
    );
  } else {
    configs.add(
      ProductActionConfig(
        id: 'dishServed',
        iconWidget: ProductActionType.dishServed.iconWidget,
        title: ProductActionType.dishServed.displayName,
        actionType: ProductActionType.dishServed,
        enabled: cartItem != null,
        onTap: () => controller.executeAction(
          ProductActionType.dishServed,
          product,
          latestCartItem,
        ),
      ),
    );
  }

  // 打包操作
  configs.add(
     ProductActionConfig(
      id: 'packaging',
      iconWidget: ProductActionType.packaging.iconWidget,
      title: ProductActionType.packaging.displayName,
      actionType: ProductActionType.packaging,
      onTap: () => controller.executeAction(
        ProductActionType.packaging, 
        product, 
        cartItem,
      ),
    ),
  );

  // 催菜操作
  if (isUrge) {
    configs.add(
    ProductActionConfig(
        id: 'cancel_urgeDish',
        iconWidget: ProductActionType.cancelUrgeDish.iconWidget,
        title: ProductActionType.cancelUrgeDish.displayName,
        actionType: ProductActionType.cancelUrgeDish,
        enabled: cartItem != null,
        onTap: () => controller.executeAction(
          ProductActionType.cancelUrgeDish,
          product,
          latestCartItem,
        ),
      ),
    );
  } else {
    configs.add(
    ProductActionConfig(
        id: 'urgeDish',
        iconWidget: ProductActionType.urgeDish.iconWidget,
        title: ProductActionType.urgeDish.displayName,
        actionType: ProductActionType.urgeDish,
        enabled: cartItem != null,
        onTap: () => controller.executeAction(
          ProductActionType.urgeDish,
          product,
          latestCartItem,
        ),
      ),
    );
  }
    
    configs.addAll([
    
    ProductActionConfig(
      id: 'portion_dish',
      iconWidget: ProductActionType.portionDish.iconWidget,
      title: ProductActionType.portionDish.displayName,
      actionType: ProductActionType.portionDish,
      enabled: cartItem != null, // 只有购物车中的商品才能分菜
      onTap: () => controller.executeAction(
        ProductActionType.portionDish, 
        product, 
        cartItem,
      ),
    ),
    ProductActionConfig(
      id: 'commission_staff',
      iconWidget: ProductActionType.commissionStaff.iconWidget,
      title: ProductActionType.commissionStaff.displayName,
      trailingIconWidget: Icon(Icons.chevron_right),
      actionType: ProductActionType.commissionStaff,
      onTap: () {}, // 这个会在UI层处理，显示员工选择弹窗
    ),

    ProductActionConfig(
      id: 'return_dish',
      iconWidget: ProductActionType.returnDish.iconWidget,
      title: ProductActionType.returnDish.displayName,
      textColor: ProductActionType.returnDish.textColor,
      iconColor: ProductActionType.returnDish.iconColor,
      actionType: ProductActionType.returnDish,
      enabled: cartItem != null,
      onTap: () => controller.executeAction(
        ProductActionType.returnDish, 
        product, 
        cartItem,
      ),
    ),

  ]);

  // 删菜
  configs.add(
    ProductActionConfig(
      id: 'delete_dish',
      iconWidget: ProductActionType.deleteDish.iconWidget,
      title: ProductActionType.deleteDish.displayName,
      textColor: ProductActionType.deleteDish.textColor,
      iconColor: ProductActionType.deleteDish.iconColor,
      actionType: ProductActionType.deleteDish,
      shouldCloseDialog: true,
      enabled: cartItem != null,
      onTap: () => controller.executeAction(
        ProductActionType.deleteDish, 
        product, 
        cartItem,
      ),
    ),
  );

  // 取消订单
  configs.add(
    ProductActionConfig(
      id: 'cancel_order',
      iconWidget: ProductActionType.cancelOrder.iconWidget,
      title: ProductActionType.cancelOrder.displayName,
      textColor: ProductActionType.cancelOrder.textColor,
      iconColor: ProductActionType.cancelOrder.iconColor,
      actionType: ProductActionType.cancelOrder,
      shouldCloseDialog: true,
      enabled: cartItem != null,
      onTap: () => controller.executeAction(
        ProductActionType.cancelOrder, 
        product, 
        cartItem,
      ),
    ),
  );

  return configs;
}