import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/popup/kp_slide_popup.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/cart/presentation/cart_screen.dart';
import 'package:kpos/features/product/application/product_discount_service.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';
import '../../../../common/components/button/kp_filled_button.dart';
import '../../domain/product_more_actions_cancel_config.dart';

import 'product_more_actions_cancel_controller.dart';

/// 通用取消操作弹窗
class ProductMoreActionsCancelActionPopup extends ConsumerStatefulWidget {
  final CancelActionConfig config;
  final CartProductItem? cartItem; // 🔥 移除OrderInfo，所有数据从CartProductItem获取

  const ProductMoreActionsCancelActionPopup({
    super.key,
    required this.config,
    this.cartItem,
  });

  /// 显示弹窗的静态方法
  static void show({
    required BuildContext context,
    required CancelActionConfig config,
    CartProductItem? cartItem,
  }) {
    KPSlidePopup.showFromTarget(
      context: context,
      contentWidget: ProductMoreActionsCancelActionPopup(
        config: config,
        cartItem: cartItem,
      ),
      targetWidth: 360,
      onMaskTap: () {
        // KPSlidePopup.dismissFromTarget();
      },
    );
  }

  @override
  ConsumerState<ProductMoreActionsCancelActionPopup> createState() =>
      _ProductMoreActionsCancelActionPopupState();
}

class _ProductMoreActionsCancelActionPopupState
    extends ConsumerState<ProductMoreActionsCancelActionPopup> {
  // 🔥 照搬ProductSpecialPriceView的状态管理
  final TextEditingController _reasonEditController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final _formKey = GlobalKey<FormBuilderState>();

  // 局部状态变量（照搬原有模式）
  List<String> _localSelectedReasons = []; // 多选原因列表
  int _selectedQuantity = 1; // 退回数量
  bool _printReturnOrder = false; // 是否打印退单

  @override
  void initState() {
    super.initState();
    // 🔥 从CartProductItem初始化数量
    if (widget.cartItem != null) {
      _selectedQuantity = widget.cartItem!.quantity?.toInt() ?? 1;
    }
  }

  @override
  void dispose() {
    _reasonEditController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: FormBuilder(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和关闭按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    widget.config.title,
                    style: KPFontStyle.headingLarge.copyWith(
                      color: KPColors.textGrayPrimary,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      widget.config.onCancel?.call();
                      KPSlidePopup.dismissFromTarget();
                    },
                    icon: const Icon(Icons.close, size: 24),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 商品信息展示（如果有CartProductItem）
              if (widget.cartItem != null) ...[
                _buildProductInfo(),
                const SizedBox(height: 16),
              ],

              // 数量选择器 🔥 使用KPFormBuilderStepper
              if (widget.config.showQuantitySelector && widget.cartItem != null) ...[
                _buildQuantitySelector(),
                const SizedBox(height: 16),
              ],

              // 原因选择 🔥 复用ProductSpecialPriceView的实现
              Text(
                'Reason *',
                style: KPFontStyle.headingXSmall.copyWith(
                  color: KPColors.textGrayPrimary,
                ),
              ),
              const SizedBox(height: 8),
              _buildReasonTagsWithCache(),

              // 自定义备注 🔥 复用ProductSpecialPriceView的实现
              const SizedBox(height: 16),
              _buildCustomNotesTextField(),

              // 打印选项
              if (widget.config.showPrintOption) ...[
                const SizedBox(height: 16),
                _buildPrintOption(),
              ],

              // 底部按钮
              const SizedBox(height: 24),
              _buildBottomButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建商品信息展示区域 - 基于CartProductItem
  Widget _buildProductInfo() {
    final cartItem = widget.cartItem!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.yellow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: KPColors.borderGrayLightDark,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 商品名称
                Text(
                  cartItem.commodityName,
                  style: KPFontStyle.headingMedium.copyWith(
                    color: KPColors.textGrayPrimary,
                  ),
                ),
                const SizedBox(height: 4),

                // 🔥 从CartProductItem获取规格信息
                if (cartItem.commodityUnit?.isNotEmpty == true)
                  Text(
                    cartItem.commodityUnit!,
                    style: KPFontStyle.bodySmall.copyWith(
                      color: KPColors.textGraySecondary,
                    ),
                  ),

                // 🔥 显示备注信息（如果有）
                if (cartItem.remark?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Notes: ${cartItem.remark}',
                    style: KPFontStyle.bodySmall.copyWith(
                      color: KPColors.textGraySecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 价格信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'S\$${cartItem.price.toStringAsFixed(2)}',
                style: KPFontStyle.bodyLarge.copyWith(
                  color: KPColors.textGrayPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '×${cartItem.quantity}',
                style: KPFontStyle.bodyMedium.copyWith(
                  color: KPColors.textGraySecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建数量选择器 - 使用KPFormBuilderStepper保持UI一致性
  Widget _buildQuantitySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Return quantity',
          style: KPFontStyle.headingXSmall.copyWith(
            color: KPColors.textGrayPrimary,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 120,
          child: KPFormBuilderStepper(
            name: 'return_quantity',
            initialValue: _selectedQuantity,
            min: 1,
            max: widget.cartItem!.quantity?.toInt() ?? 99, // 🔥 最大值为购物车中的数量
            onChanged: (value) {
              setState(() {
                _selectedQuantity = value ?? 1;
              });
            },
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: KPColors.borderGrayLightDarkest,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(
                  color: KPColors.borderBrandDefault,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 🔄 使用缓存优先的原因标签构建器 - 直接从ProductSpecialPriceView复制
  Widget _buildReasonTagsWithCache() {
    // 先尝试从缓存获取数据
    final cache = ref.read(storePreparedReasonCacheProvider);
    final discountReasons = cache.getDiscountReasons();

    if (discountReasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: productDiscountReasonsProvider,
        dataBuilder: (context, ref, data) {
          return _buildReasonTagsWidget(
              data.map((e) => e.preparedReasonName).toList());
        },
      );
    }

    // 如果缓存中有数据，直接使用（无网络请求）
    return _buildReasonTagsWidget(
        discountReasons.map((e) => e.preparedReasonName).toList());
  }

  /// 🎯 构建原因标签组件 - 完全按照ProductSpecialPriceView的多选实现
  Widget _buildReasonTagsWidget(List<String> reasonNames) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormBuilderField<List<String>>(
            name: 'cancel_reasons',
            initialValue: _localSelectedReasons,
            builder: (FormFieldState<List<String>> field) {
              return Wrap(
                spacing: 8,
                runSpacing: 4,
                children: reasonNames.map((reasonName) {
                  final isSelected = field.value?.contains(reasonName) ?? false;

                  return ChoiceChip(
                    label: Text(
                      reasonName,
                      style: KPFontStyle.headingXSmall.copyWith(
                        color: isSelected
                            ? KPColors.textBrandDefault
                            : KPColors.textGrayPrimary,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      final currentValue =
                          List<String>.from(field.value ?? []);
                      if (selected) {
                        currentValue.add(reasonName);
                      } else {
                        currentValue.remove(reasonName);
                      }

                      field.didChange(currentValue);
                      _updateReasonsSelection(currentValue);
                    },
                    backgroundColor: Colors.white,
                    selectedColor: KPColors.fillBrandLightest,
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        color: isSelected
                            ? KPColors.borderBrandDefault
                            : KPColors.borderGrayLightDark,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 8),
                    showCheckmark: false,
                  );
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建自定义备注输入框 - 参考ProductSpecialPriceView的TextField实现
  Widget _buildCustomNotesTextField() {
    return TextField(
      focusNode: _focusNode,
      controller: _reasonEditController,
      maxLength: 64,
      maxLengthEnforcement: MaxLengthEnforcement.enforced,
      buildCounter: (
        BuildContext context, {
        required int currentLength,
        required bool isFocused,
        int? maxLength,
      }) =>
          null,
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderSide: const BorderSide(
              color: KPColors.borderGrayLightDarkest, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(
              color: KPColors.borderBrandDefault, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintText: 'Custom notes',
        hintStyle: KPFontStyle.headingXSmall.copyWith(
          color: KPColors.textGraySecondary,
        ),
      ),
      inputFormatters: [NoEmojiInputFormatter()],
      onChanged: (value) {
        // 实时更新状态，无需额外存储
      },
    );
  }

  /// 构建打印选项
  Widget _buildPrintOption() {
    return Row(
      children: [
        Checkbox(
          value: _printReturnOrder,
          onChanged: (value) {
            setState(() {
              _printReturnOrder = value ?? false;
            });
          },
          activeColor: Colors.blue,
        ),
        const SizedBox(width: 8),
        Text(
          'Print Return Order',
          style: KPFontStyle.bodyMedium.copyWith(
            color: KPColors.textGrayPrimary,
          ),
        ),
      ],
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomButtons() {
  final controller = ref.watch(productMoreActionsCancelControllerProvider);

  return Row(
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      // 取消按钮 - 使用系统自带样式
      _buildCustomButton(
        text: 'Cancel',
        backgroundColor: KPColors.fillGrayLightLighter,
        textColor: KPColors.textGrayPrimary,
        onTap: controller.isLoading ? null : () {
          widget.config.onCancel?.call();
          KPSlidePopup.dismissFromTarget();
        },
        width: 93,
      ),
      const SizedBox(width: 12),
      
      // 确认按钮 - 根据操作类型和状态动态显示
      _buildCustomButton(
        text: controller.isLoading 
            ? 'Processing...' 
            : widget.config.buttonText,
        backgroundColor: controller.isLoading 
            ? KPColors.borderBrandDefault 
            : Colors.red, // 退菜/取消订单使用红色
        textColor: Colors.white,
        onTap: controller.isLoading || !_isValidForm() ? null : () {
          final data = _getCancelActionData();
          widget.config.onConfirm(data);
        },
        width: _getConfirmButtonWidth(),
      ),
    ],
  );
}

/// 构建自定义按钮 - 直接复用sold_out_cancel_confirm_view的方法
Widget _buildCustomButton({
  required String text,
  required Color backgroundColor,
  required Color textColor,
  required VoidCallback? onTap,
  required double width,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: width,
      height: 44,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: backgroundColor == KPColors.fillGrayLightLighter
            ? Border.all(color: KPColors.borderGrayLightDark, width: 1)
            : null,
      ),
      child: Center(
        child: Text(
          text,
          style: KPFontStyle.bodyLarge.copyWith(
            color: textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    ),
  );
}

/// 根据按钮文案动态计算确认按钮宽度
double _getConfirmButtonWidth() {
  switch (widget.config.buttonText) {
    case 'Cancel dish':
      return 120;
    case 'Cancel order':
      return 130;
    default:
      return 120;
  }
}

  /// 🔥 照搬ProductSpecialPriceView的多选原因更新方法
  void _updateReasonsSelection(List<String> reasons) {
    setState(() {
      _localSelectedReasons = [...reasons];
    });
  }

  /// 验证表单是否有效
  bool _isValidForm() {
    // 至少需要选择一个原因或填写自定义备注
    return _localSelectedReasons.isNotEmpty ||
        _reasonEditController.text.trim().isNotEmpty;
  }

  /// 收集所有表单数据
  CancelActionData _getCancelActionData() {
    return CancelActionData(
      quantity: _selectedQuantity,
      selectedReasons: List.from(_localSelectedReasons),
      customNotes: _reasonEditController.text.trim(),
      printReturnOrder: _printReturnOrder,
    );
  }
}