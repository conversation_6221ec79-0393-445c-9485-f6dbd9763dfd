
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/product/application/product_discount_service.dart';
import 'package:kpos/features/product/domain/product_more_actions_cancel_config.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';
import 'package:path/path.dart';

import '../../../cart/domain/cart_product_item.dart';

class ProdcutMoreActionsCancelActionView extends ConsumerStatefulWidget{
  /// 购物车信息
  final CartProductItem? cartItem;
  final CancelActionConfig config;

  const ProdcutMoreActionsCancelActionView({
    super.key,
    this.cartItem,
    required this.config,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ProdcutMoreActionsCancelActionView();
}

class _ProdcutMoreActionsCancelActionView extends ConsumerState<ProdcutMoreActionsCancelActionView> {
  final TextEditingController _reasonEditController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final _formKey = GlobalKey<FormBuilderState>();

  // 多选原因列表
  List<String> _reasonsList = [];
  // 是否打印订单
  bool _printReturnOrder = false; 
   
   @override
  Widget build(BuildContext context) {
    return Container(
      width: 560,
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleBar(context),
          _buildInfoCard(),
          const SizedBox(height: KPSpacing.xxxl,),
          _buildReasonTtile(),
          _buildReasonTagsWithCache(context),
          _buildCustomNotesTextField(),
          _buildPrintOption()



        ],
      ),
    );
  }

 Widget _buildTitleBar(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl, vertical: KPSpacing.s),
    child: Row(
      children: [
        Expanded(
          child: Text(
            context.locale.cancelOrder,
            style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
          ),
        ),
        GestureDetector(
          onTap: _handleConfirm,
          child: const Icon(
            Icons.close, size: 24, color: KPColors.textGrayPrimary,
          ),
        )
      ],
      
    ),
    );
 }

 Widget _buildInfoCard() {
  return Container(
    height: 48,
    margin: const EdgeInsets.symmetric(horizontal: KPSpacing.xxxl),
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 13),
    decoration: BoxDecoration(
      color: const Color(0xFFFFF7E6), // 警告橙色背景
      borderRadius: BorderRadius.circular(6),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 22,
          height: 22,
          decoration: const BoxDecoration(
            color: Color(0xFFFF8C00), // 橙色圆形背景
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.priority_high,
            color: Colors.white,
            size: 16,
          ),
        ),
        const SizedBox(width: 8,),
        Expanded(child: Text(
          "Once cancel, the order cannot be edited or recovered.",
          style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
        ))
        
      ],
    ),
  );
 }

 Widget _buildReasonTtile() {
  return Text(
    "Reason *",
    style: KPFontStyle.headingXSmall.copyWith(color: KPColors.textGrayPrimary),
  );
 }

 Widget _buildReasonTagsWithCache(BuildContext context) {
  // 先尝试从缓存获取数据
  final cache = ref.read(storePreparedReasonCacheProvider);
  final cancelReasons = cache.getDiscountReasons(); 

  if (cancelReasons == null) {
    // 如果缓存中没有数据, 使用异步加载(首次访问)
    return KPAsyncValueWidget(
      asyncValueProvider: productDiscountReasonsProvider,
      dataBuilder: (context, ref, data) {
        return _buildReasonTagsWidget(data.map((e) => e.preparedReasonName).toList());
      },
    );
  }

  // 如果缓存中有数据, 直接使用(无网络请求)
  return _buildReasonTagsWidget(cancelReasons.map((e) => e.preparedReasonName).toList());
 }
 
 Widget _buildReasonTagsWidget(List<String> reasonNames) {
  return SingleChildScrollView(
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormBuilderField(
          name: "cancel_reasons",
          initialValue: _reasonsList,
          builder: (FormFieldState<List<String>> field) {
            return Wrap(
              spacing: 8, //主轴上两个相邻子组件之间的距离
              runSpacing: 4, //交叉轴上两个相邻“行”或“列”之间的距离
              children: reasonNames.map((reasonName){
                final isSelected = field.value?.contains(reasonName) ?? false;

                return ChoiceChip(
                  label: Text(
                    reasonName,
                    style: KPFontStyle.headingXSmall.copyWith(
                      color: isSelected
                      ? KPColors.textBrandDefault
                      : KPColors.textGrayPrimary,
                    ),
                  ), 
                  selected: isSelected,
                  onSelected: (selected) {
                    final currentValue = List<String>.from(field.value ?? []);
                    if (selected) {
                      currentValue.add(reasonName);
                    } else {
                      currentValue.remove(reasonName);
                    }

                    field.didChange(currentValue);
                    _updateResonsSelection(currentValue);
                  },
                  backgroundColor: Colors.white,
                  selectedColor: KPColors.fillBrandLightest,
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      color: isSelected
                      ? KPColors.borderBrandDefault
                      : KPColors.borderGrayDarkDark,
                      width: 1
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  );
              }).toList(),
            );
          },
          )
      ],
    ),
  );
 }

 Widget _buildCustomNotesTextField() {
  return TextField(
    focusNode: _focusNode,
    controller: _reasonEditController,
    maxLength: 64,
    maxLengthEnforcement: MaxLengthEnforcement.enforced,
    buildCounter: (context, {required currentLength, required isFocused, required maxLength}) => null,
    decoration: InputDecoration(
      border: OutlineInputBorder(
        borderSide: const BorderSide(
          color: KPColors.borderGrayLightDarkest, 
          width: 1,
          ),
        borderRadius: BorderRadius.circular(4),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: KPColors.borderBrandDefault, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintText: "Custom notes",
        hintStyle: KPFontStyle.headingXSmall.copyWith(color: KPColors.textGraySecondary),
      ),
      inputFormatters: [NoEmojiInputFormatter()],
      onChanged: (value) {
        // 实时更新,无需额外存储
      },
    );
 }

 /// 构建打印选项
 Widget _buildPrintOption() {
  return Row(
    children: [
      Checkbox(
        value: _printReturnOrder, 
        onChanged: (value) {
          setState(() {
            _printReturnOrder = value ?? false;
          });
        },
        activeColor: KPColors.iconGreenDefault,
        ),
        const SizedBox(width: 8,),
        Text(
          "Print Return Order",
          style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
        )
    ],
  );
 }

 /// 构建底部按钮区域
 Widget _buildBottomButtons() {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    child: Row(
      children: [
        Expanded(child: KPButton(text: "Cancel", onPressed: () {}))
      ],
    ),
  );
 }

  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 取消按钮
        Container(
          child: _buildCustomButton(
            text: context.locale.cancel,
            backgroundColor: KPColors.fillGrayLightLighter,
            textColor: KPColors.textGrayPrimary,
            onTap: _handleCancel,
          ),
        ),
        const SizedBox(width: 16,),
        // 确认按钮
        Container(
          child: _buildCustomButton(
            text: context.locale.confirm,
            backgroundColor: KPColors.fillRedNormal,
            textColor: KPColors.textGrayInverse,
            onTap: _handleConfirm,
          ),
        ),
      ],
    )
  }

  /// 构建自定义按钮
  Widget _buildCus
  
 void _updateResonsSelection(List<String> reasons) {
  setState(() {
    _reasonsList = [...reasons];
  });
 }

 void _handleConfirm() {
    // return Container(
    //   colo
    // )
 }
}