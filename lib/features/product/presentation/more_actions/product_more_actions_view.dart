import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/features/auth/presentation/re_login_screen_controller.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/product/domain/product_action_config.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/presentation/more_actions/product_more_actions_cancel_controller.dart';
import 'package:kpos/features/product/presentation/more_actions/product_more_actions_controller.dart';

import '../../../../common/components/index.dart';
import '../../../cart/presentation/cart_screen.dart';
import '../../domain/product_more_actions_cancel_config.dart';
import 'prodcut_more_actions_cancel_action_popup.dart';
import 'prodcut_more_actions_cancel_action_view.dart';

class ProductMoreActionsView extends ConsumerWidget {
  final ProductItem product;
  final CartProductItem? cartItem;

  const ProductMoreActionsView({
    super.key,
    required this.product,
    this.cartItem,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final configs = ref.watch(productActionConfigsProvider(product, cartItem));
    final state = ref.watch(productMoreActionsControllerProvider);

    return Container(
      color: Colors.white,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 16),
        itemCount: configs.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          thickness: 0.5,
          indent: 0, // 左下划线间距
          endIndent: 0, // 右下划线间距
          color: Colors.grey[300],
        ),
        itemBuilder: (context, index) {
          final config = configs[index];
          return _buildActionItem(context, ref, config, state);
        },
      ),
    );
  }

  /// 构建操作项
  Widget _buildActionItem(
    BuildContext context,
    WidgetRef ref,
    ProductActionConfig config,
    ProductMoreActionsState state,
  ) {
    final isEnabled = config.enabled && !state.isLoading;
    final themeColor = isEnabled ? (config.textColor ?? KPColors.textGrayPrimary) : KPColors.textGrayQuaternary;

    return ListTile(
      enabled: isEnabled,
      contentPadding: const EdgeInsets.only(
        left: 0, // 减少左侧内边距，让图标更靠近边缘
        right: 0, // 减少右侧内边距，让右侧图标更靠近边缘
        top: 8,
        bottom: 8,
      ),
      leading: Container(
        width: 24,
        height: 24,
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
            themeColor,
            BlendMode.srcIn,
          ),
          child: config.iconWidget,
        ),
      ),
      title: Text(
        config.title,
        style: KPFontStyle.bodyLarge.copyWith(color: themeColor),
      ),
      trailing: config.trailingIconWidget != null
          ? ColorFiltered(
          colorFilter: ColorFilter.mode(
            themeColor,
            BlendMode.srcIn,
          ),
          child: config.trailingIconWidget,
        )
          : null,
      onTap: config.enabled && !state.isLoading
          ? () => _handleActionTap(context, ref, config)
          : null,
    );
  }

  /// 处理操作点击
  void _handleActionTap(
    BuildContext context,
    WidgetRef ref,
    ProductActionConfig config,
  ) {
    switch (config.actionType) {
      case ProductActionType.commissionStaff:
        _showStaffSelectionDialog(context, ref);
        break;
      case ProductActionType.cancelOrder:
        _showCancelDishDialog(context, ref);
        break;
      default:
      //  根据配置决定是否关闭弹窗
      if (config.shouldCloseDialog) {
        KPSlidePopup.dismissFromTarget();;
      }

        config.onTap?.call();
        // 不关闭页面，因为这是在标签页内
    }
  }

  /// 显示员工选择对话框
  void _showStaffSelectionDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择员工'),
        content: SizedBox(
          width: double.maxFinite,
          child: KPAsyncValueWidget(
            asyncValueProvider: reLoginScreenControllerProvider,
            dataBuilder: (context, ref, staffList) {
              return ListView.builder(
                shrinkWrap: true,
                itemCount: staffList.length,
                itemBuilder: (context, index) {
                  final staff = staffList[index];
                  return ListTile(
                    title: Text(staff.employeeName),
                    onTap: () {
                      ref.read(productMoreActionsControllerProvider.notifier)
                          .setSelectedStaff(staff.employeeName);
                      
                      // 执行委托操作
                      ref.read(productMoreActionsControllerProvider.notifier)
                          .executeAction(
                            ProductActionType.commissionStaff,
                            product,
                            cartItem,
                          );
                      
                      Navigator.of(context).pop(); // 关闭员工选择对话框
                    },
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
  

  // /// 显示删除确认对话框
  // void _showDeleteConfirmDialog(
  //   BuildContext context,
  //   WidgetRef ref,
  //   ProductActionConfig config,
  // ) {
  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: const Text('确认删除'),
  //       content: Text('确定要删除 ${product.commodityName} 吗？'),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.of(context).pop(),
  //           child: const Text('取消'),
  //         ),
  //         TextButton(
  //           onPressed: () {
  //             config.onTap?.call();
  //             Navigator.of(context).pop();
  //             // KPSlidePopup.dismissFromTarget();// 关闭确认对话框
  //           },
  //           style: TextButton.styleFrom(foregroundColor: Colors.red),
  //           child: const Text('删除'),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // 添加显示退菜弹窗的方法
void _showCancelDishDialog(BuildContext context, WidgetRef ref) {
  final config = CancelActionConfigFactory.createCancelDishConfig(
    onConfirm: (data) async {
      debugPrint('退菜数据: $data');
      
      // 关闭弹窗
      KPSlidePopup.dismissFromTarget();
      ref.read(activePopupItemIdProvider.notifier).state = null;
      
      // 执行退菜逻辑
      await ref.read(productMoreActionsControllerProvider.notifier)
          .executeAction(ProductActionType.returnDish, product, cartItem);
    },
    onCancel: () {
      KPSlidePopup.dismissFromTarget();
      ref.read(activePopupItemIdProvider.notifier).state = null;
    },
  );
  
  // ProductMoreActionsCancelActionPopup.show(
  //   context: context,
  //   config: config,
  //   cartItem: cartItem,
  // );


  KPSlidePopup.showFromTarget(
          context: context,
          contentWidget: ProdcutMoreActionsCancelActionView(config: config),
          targetWidth: 360,
          allowMultipleLayers: true,
          onMaskTap: () {
            // 点击遮罩时清除activePopupItemId
            ref.read(activePopupItemIdProvider.notifier).state = null;
            KPSlidePopup.dismissFromTarget();
          },
        );

}






}