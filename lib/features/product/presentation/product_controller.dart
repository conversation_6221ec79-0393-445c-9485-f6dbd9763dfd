import 'package:flutter/foundation.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/product/data/product_intranet_repository.dart';
import 'package:kpos/features/product/domain/product_list.dart';
import 'package:kpos/features/product/presentation/categories/product_categories_controller.dart';
import 'package:kpos/routing/main_tab_bar_navigator_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'dart:convert';

import '../domain/product_item.dart'; //json格式化

part 'product_controller.g.dart';

@riverpod
class ProductController extends _$ProductController {
  @override
  FutureOr<ProductList> build() async {
    final categoryId = ref.watch(selectProductCategoryIdProvider);
    final orderId = ref.watch(currentOrderIdProvider);
    final keyword = ref.watch(mainTabBarSearchProvider);
    final result = await fetch(categoryId,orderId,keyword);
    return result;
  }

  Future<ProductList> fetch(int categoryId,int? orderId,String? keyword) async {
    final result = await ref
        .read(productIntranetRepositoryProvider)
        .getProducts(categoryId,orderId,keyword);
    return result;
  }

  // 刷新方法(其实是刷新当前分类)
  Future<void> refresh() async {
    //debugPrint('🔄 用户触发刷新');
    // 令Riverpod重新构建,触发数据刷新
    ref.invalidateSelf();
  }

  /// 根据指定分类ID刷新数据
  /// 如果需要切换分类并刷新数据时使用
  Future<void> refreshWithCategory(int categoryId) async {
    //debugPrint('🔄 切换分类并刷新，目标分类: $categoryId');
    // 先更新分类选择
    ref
        .read(selectProductCategoryIdProvider.notifier)
        .changeCategoryId(categoryId);
    // 触发刷新
    ref.invalidateSelf();
  }
}

//todo: cursor步骤2
