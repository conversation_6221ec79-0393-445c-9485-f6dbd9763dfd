/// 更多活动弹窗组件
/// 
/// 功能特性：
/// - 支持逗号分隔的活动字符串自动解析
/// - 动态计算弹窗高度
/// - 深色主题，箭头和背景颜色统一
/// - 支持自定义位置和样式
/// - 点击活动项的回调处理

import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

/// 👇🏻 使用示例
// // 方式1：直接使用MoreActivitiesTag组件
// MoreActivitiesTag(
//   count: 3,
//   activities: '限时特价,会员专享,新品上市',
//   onActivityTap: (activityName) {
//     print('点击了活动: $activityName');
//     // 处理活动点击逻辑
//   },
// )

// // 方式2：在任意位置直接调用弹窗
// LayoutBuilder(builder: (context, constraints) {
//   return GestureDetector(
//     onTap: () {
//       MoreActivitiesPopover.show(
//         context: context,
//         activities: 'Spend \$50 save \$5,5 for 1,Buy 1 get 1 free',
//         onActivityTap: (activityName) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(content: Text('选择了: $activityName')),
//           );
//         },
//         width: 250, // 自定义宽度
//         placement: TDPopoverPlacement.bottomLeft, // 自定义位置
//       );
//     },
//     child: Container(
//       padding: EdgeInsets.all(8),
//       decoration: BoxDecoration(
//         color: Colors.blue,
//         borderRadius: BorderRadius.circular(4),
//       ),
//       child: Text('点击查看活动', style: TextStyle(color: Colors.white)),
//     ),
//   );
// })

/// 更多活动弹窗组件
class MoreActivitiesPopover {
  /// 显示更多活动弹窗
  /// 
  /// [context] - 弹窗的上下文，通常是触发标签的上下文
  /// [activities] - 活动字符串，用逗号分隔，如："活动1,活动2,活动3"
  /// [onActivityTap] - 点击活动项的回调函数
  /// [width] - 弹窗宽度，默认200
  /// [placement] - 弹窗位置，默认底部居中
  /// [onDismiss] - 弹窗关闭时的回调（可选）
  static void show({
    required BuildContext context,
    required String activities,
    required Function(String activityName) onActivityTap,
    double width = 200,
    TDPopoverPlacement placement = TDPopoverPlacement.bottom,
    VoidCallback? onDismiss,
  }) {
    // 解析活动列表
    final activityList = _parseActivities(activities);
    
    // 计算弹窗高度
    final popoverHeight = _calculatePopoverHeight(activityList);
    
    KPPopover.showPopover(
      context: context,
      width: width,
      height: popoverHeight,
      contentWidget: _MoreActivitiesContent(
        activities: activityList,
        onActivityTap: onActivityTap,
      ),
      placement: placement,
      showArrow: true,
      arrowSize: 8,
      theme: TDPopoverTheme.dark, // 深色主题，确保箭头和背景颜色一致
      overlayColor: Colors.transparent,
      closeOnClickOutside: true,
      onComplete: (value) {
        if (onDismiss != null) onDismiss();
      },
    );
  }

  /// 解析活动字符串，支持逗号分隔
  /// 输入示例："活动1,活动2,活动3" 或 "Spend $50, save $5,5 for 1,Buy 1 get 1 free"
  static List<String> _parseActivities(String activitiesString) {
    if (activitiesString.isEmpty) {
      // 默认活动列表
      return ['Spend \$50, save \$5', '5 for 1'];
    }
    
    // 按逗号分割并清理空白字符
    return activitiesString
        .split(',')
        .map((activity) => activity.trim())
        .where((activity) => activity.isNotEmpty)
        .toList();
  }

  /// 根据活动数量计算弹框高度
  static double _calculatePopoverHeight(List<String> activities) {
    if (activities.isEmpty) return 50; // 最小高度
    
    // 计算公式：
  // - 顶部和底部padding: 20 (10 * 2) == 修改：减小内边距 ==
  // - 每行活动的高度: 约20 (包含文字高度和内边距) == 修改：从28减小到20 ==
  // - 行间距: 4 * (行数 - 1) == 修改：从8减小到4 ==
  const double verticalPadding = 20.0; // == 修改 ==
  const double itemHeight = 22.0; // == 修改 ==
  const double itemSpacing = 4.0; // == 修改 ==
    
    final double contentHeight = (activities.length * itemHeight) + 
                                ((activities.length - 1) * itemSpacing);
    
    return verticalPadding + contentHeight;
  }
}

/// 更多活动弹窗内容组件
class _MoreActivitiesContent extends StatelessWidget {
  final List<String> activities;
  final Function(String activityName) onActivityTap;

  const _MoreActivitiesContent({
    required this.activities,
    required this.onActivityTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      // 移除自定义decoration，让TDPopover的dark主题处理背景和箭头颜色
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 动态生成活动列表
          ...activities.asMap().entries.map((entry) {
            final index = entry.key;
            final activity = entry.value;
            
            return Column(
              children: [
                _MoreActivityItem(
                  title: activity,
                  onTap: () {
                    Navigator.of(context).pop(); // 关闭弹出层
                    onActivityTap(activity);
                  },
                ),
                // 如果不是最后一项，添加间距
                if (index < activities.length - 1) const SizedBox(height: 4),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }
}

/// 单个活动项组件
class _MoreActivityItem extends StatelessWidget {
  final String title;
  final VoidCallback onTap;

  const _MoreActivityItem({
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        width: double.infinity, // 确保点击区域占满宽度
        padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 4),
        child: Row(
          children: [
            // 白色圆点指示器
            Container(
              width: 3,
              height: 3,
              margin: const EdgeInsets.only(right: 10),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
            ),
            // 活动文字 - 支持自动换行
            Expanded(
              child: Text(
                title,
                style: KPFontStyle.bodySmall.copyWith(
                  color: KPColors.textGrayInverse,
                ),
                maxLines: 2, // 最多显示2行，避免单个活动过长
                overflow: TextOverflow.ellipsis, // 超长文本省略
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 更多活动标签组件
/// 用于显示"+N"样式的标签，点击时弹出活动列表
class MoreActivitiesTag extends StatelessWidget {
  final int count;
  final String activities;
  final Function(String activityName) onActivityTap;
  final double width;
  final TDPopoverPlacement placement;

  const MoreActivitiesTag({
    super.key,
    required this.count,
    required this.activities,
    required this.onActivityTap,
    this.width = 200,
    this.placement = TDPopoverPlacement.bottom,
  });

  @override
  Widget build(BuildContext context) {
    return count > 0 
        ? LayoutBuilder(builder: (tagContext, constraints) {
            return _buildMoreActiveTagUI(count).onTap(() {
              MoreActivitiesPopover.show(
                context: tagContext,
                activities: activities,
                onActivityTap: onActivityTap,
                width: width,
                placement: placement,
              );
            });
          })
        : const SizedBox.shrink();
  }

  /// 构建+N标签的UI
  Widget _buildMoreActiveTagUI(int number) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: KPColors.borderRedDefault, width: 1.0),
        color: const Color(0xE5FFF7E6).withAlpha(150),
      ),
      child: Text(
        "+$number",
        style: KPFontStyle.bodyXSmall.copyWith(color: KPColors.borderRedDefault),
      ),
    );
  }
}