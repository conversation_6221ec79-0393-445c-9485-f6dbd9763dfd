//商品标签(是否套餐 + 活动)
import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:path/path.dart';

import '../../domain/product_item.dart';

/// 定义商品标签的显示样式
enum ProductTagType {
  /// 显示套餐和活动
  comboAndActivity,
  /// 仅显示活动
  activityOnly,
  /// 显示称重和活动
  scalesAndActivity,
  /// 不显示任何标签
  none,
}

class ProductItemTags extends StatelessWidget {
  final ProductItem product;
  final ProductTagType style;
  final double? maxWidth;

  const ProductItemTags({
    super.key,
    required this.product,
    required this.style,
    this.maxWidth,
    });

  @override
  Widget build(BuildContext context) {
    List<Widget> tagWidgets;

    switch(style) {
      case ProductTagType.comboAndActivity:
       tagWidgets = [
          buildComboTypeUIWidget("combo"),
          const SizedBox(width: 4),
          buildActiveName("活动名"),
          const SizedBox(width: 4,),
          buildMoreActiveNum(2)
        ];
       break;
      case ProductTagType.activityOnly:
        tagWidgets = [
            buildActivesWidget(product)
          ];
        break;
      case ProductTagType.scalesAndActivity:
        tagWidgets = [
              buildScales(),
              const SizedBox(width: 4,),
              buildMoreActiveNum(2)
          ];
        break;
      default:
       tagWidgets = [];
        break;
    }

    // 如果没有标签，则返回一个零大小的部件，不占用任何空间
    if (tagWidgets.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: tagWidgets,
    );
  }

  /// 🌲 combo这种UI构图的组件,可复用
  Widget buildComboTypeUIWidget(String name) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: const Color(0xE5FFF7E6)
        ),
      child: Text(
        name,
        style:  KPFontStyle.bodyXSmall.copyWith(color: KPColors.fillBrandNormal),
      ),
    );
  }

  Widget buildActiveName(String name) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: KPColors.borderRedDefault, width: 1.0),
        color: const Color(0xE5FFF7E6).withAlpha(150)
        ),
      child: Text(
        name,
        style:  KPFontStyle.bodyXSmall.copyWith(color: KPColors.borderRedDefault),
      ),
    );
  }

  Widget buildMoreActiveNum(int number) {
    return number > 0 ? Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: KPColors.borderRedDefault, width: 1.0),
        color: const Color(0xE5FFF7E6).withAlpha(150)
        ),
      child: Text(
        "+$number",
        style:  KPFontStyle.bodyXSmall.copyWith(color: KPColors.borderRedDefault),
      ),
    )
    : SizedBox.shrink();
  }

  Widget buildScales() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4), color: Color(0xE6E9F2FF)),
      child: const Text(
        "Scales",
        style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w400,
            color: Color(0xFF0C66E4)),
      ),
    );
  }

  // 活动名称+剩余活动数量
  Widget buildActivesWidget(ProductItem product) {
    return Row(
      children: [
        product.promotionActives.isNotEmpty ? buildActiveName(product.promotionActives[0]["name"]) : SizedBox.shrink(),
        const SizedBox(width: 4,),
        buildMoreActiveNum(product.promotionActives.length - 1)
      ],
    );
  }

}
