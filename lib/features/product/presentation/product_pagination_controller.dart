// == 修改开始 ==
// lib/features/product/presentation/product_pagination_controller.dart

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';

part 'product_pagination_controller.g.dart';

@riverpod
class ProductPagination extends _$ProductPagination {
  @override
  ProductPaginationState build() {
    return const ProductPaginationState(
      currentPage: 1,
      itemsPerPage: 0, // 将在UI中动态计算
    );
  }

  void updatePage(int newPage) {
    state = state.copyWith(currentPage: newPage);
  }

  void updateItemsPerPage(int itemsPerPage) {
    state = state.copyWith(itemsPerPage: itemsPerPage);
  }

  void resetToFirstPage() {
    state = state.copyWith(currentPage: 1);
  }
}

class ProductPaginationState {
  final int currentPage;
  final int itemsPerPage;

  const ProductPaginationState({
    required this.currentPage,
    required this.itemsPerPage,
  });

  ProductPaginationState copyWith({
    int? currentPage,
    int? itemsPerPage,
  }) {
    return ProductPaginationState(
      currentPage: currentPage ?? this.currentPage,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
    );
  }
}

/// 计算当前页面应该显示的产品数据
@riverpod
List<ProductItem> currentPageProducts(Ref ref) {
  final productListAsync = ref.watch(productControllerProvider);
  final paginationState = ref.watch(productPaginationProvider);
  
  return productListAsync.when(
    data: (productList) {
      final allProducts = productList.data;
      final currentPage = paginationState.currentPage;
      final itemsPerPage = paginationState.itemsPerPage;
      
      // 如果每页数量还没设置，返回空列表等待设置
      if (itemsPerPage <= 0) return [];
      
      final startIndex = (currentPage - 1) * itemsPerPage;
      final endIndex = startIndex + itemsPerPage;
      
      // 边界检查,起始索引,超出数据范围，返回空列表
      if (startIndex >= allProducts.length) return [];
      
      // ✅ 确保结束索引不超出数组长度
      final safeEndIndex = endIndex > allProducts.length ? allProducts.length : endIndex;
      final result = allProducts.sublist(startIndex, safeEndIndex);
      
      //debugPrint('   安全结束索引: $safeEndIndex');
      //debugPrint('   返回商品数: ${result.length}');
      //debugPrint('   商品名称: ${result.map((e) => e.commodityName).join(", ")}');
      
      return result;
    },
    loading: () => [],
    error: (_, __) => [],
  );
}
// == 修改结束 ==