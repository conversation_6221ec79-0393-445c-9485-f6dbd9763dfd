import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/product/domain/product_detail.dart' as detail;
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/application/product_detail_converter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../cart/domain/cart_product_item.dart';
import '../application/product_combo_converter.dart';
import '../data/product_intranet_repository.dart';
import 'package:kpos/features/product/domain/product_spec_page_data.dart';
import '../domain/product_combo.dart';
import 'package:collection/collection.dart';

part 'product_detail_controller.g.dart';

@riverpod
Future<ProductSpecPageData> productSpecPageData(
  Ref ref, // 代码生成器会自动生成正确的 Ref 类型
  int productId,
  ProductItem product,
  {CartProductItem? cartItem}
) async {
  // 1. 获取 Repository
  final repository = ref.read(productIntranetRepositoryProvider);
 
 // 2.1
  if (product.commodityType == 3) {
    // 套餐商品：使用ProductCombo模型和转换器

    // 2.2 调用套餐详情API
    final ProductCombo comboDetail = await repository.getProductComboDetail(productId);
    
    // 🔑 如果有编辑数据，替换套餐默认值
    final modifiedCombo = cartItem != null 
        ? _replaceComboDefaults(comboDetail, cartItem)
        : comboDetail;

    // 2.3 转换为规格组
    final specGroups = ProductComboConverter.convertToSpecGroups(modifiedCombo);

    // 2.4 提取原因选项
    final reasonOptions = _extractReasonOptionsFromCombo(modifiedCombo);

    //  编辑模式根据购物车信息解析套餐备注信息
    final remarkData = cartItem != null 
        ? _parseRemarkFromCartItem(cartItem.remark, reasonOptions)
        : {'tags': <String>[], 'customRemark': ''};

    debugPrint('🔧 套餐页面数据 - 初始备注标签: ${remarkData['tags']}, 自定义备注: "${remarkData['customRemark']}"');

    // 2.5 构建页面数据
    return ProductSpecPageData(
      product: product,
      specGroups: specGroups,
      reasonOptions: reasonOptions,
      combo: modifiedCombo,
      initialRemarkTags: remarkData['tags'] as List<String>,
      initialCustomRemark: remarkData['customRemark'] as String,
    );
  } else {
    
    // 单品商品：使用ProductDetail模型和转换器
    final detail.ProductDetail productDetail = await repository.getProductDetail(productId);
   //debugPrint('🔧 商品详情查询完成: ${product.commodityName}, 价格: ${product.price}');
    
    final originalSpecGroups = ProductDetailConverter.convertToSpecGroups(productDetail);
    
    // 🔑 如果有编辑数据，替换单品规格默认值
    final modifiedSpecGroups = cartItem != null 
        ? _replaceSpecGroupDefaults(originalSpecGroups, cartItem, product)
        : originalSpecGroups;

    // 编辑模式根据购物车信息 解析单品备注信息
    final remarkData = cartItem != null 
        ? _parseRemarkFromCartItem(cartItem.remark, productDetail.reasonOptions)
        : {'tags': <String>[], 'customRemark': ''};
    
    debugPrint('🔧 单品页面数据 - 初始备注标签: ${remarkData['tags']}, 自定义备注: "${remarkData['customRemark']}"');
    
    return ProductSpecPageData(
      product: product,
      specGroups: modifiedSpecGroups,
      reasonOptions: productDetail.reasonOptions,
      combo: null, // 单品无套餐数据
      initialRemarkTags: remarkData['tags'] as List<String>,
      initialCustomRemark: remarkData['customRemark'] as String,
    );
  }
}

/// 替换单商品规格组的默认值
List<ProductSpecGroup> _replaceSpecGroupDefaults(
  List<ProductSpecGroup> originalSpecGroups, // 原始规格组数据
  CartProductItem cartItem, // 购物车中的商品数据
  ProductItem product, // 商品基础信息
) {
  //debugPrint('🔧 === 替换单商品规格组默认值 ===');
  //debugPrint('🔧 商品名称: ${cartItem.commodityName}');
  //debugPrint('🔧 购物车做法数量: ${cartItem.practiceList?.length ?? 0}');
  //debugPrint('🔧 购物车加料数量: ${cartItem.addonsList?.length ?? 0}');
  
  // 打印购物车详细数据
  if (cartItem.practiceList?.isNotEmpty == true) {
    //debugPrint('🔧 购物车做法详情:');
    for (final practice in cartItem.practiceList!) {
      //debugPrint('🔧   - ${practice.commodityPracticeName} (ID: ${practice.commodityPracticeId})');
    }
  }
  
  if (cartItem.addonsList?.isNotEmpty == true) {
    //debugPrint('🔧 购物车加料详情:');
    for (final addon in cartItem.addonsList!) {
      //debugPrint('🔧   - ${addon.commodityName} (ID: ${addon.commodityId})');
    }
  }
  
  // 过滤规格组,单规格商品只有一种规格选择，用户无需选择，所以隐藏规格组
  final filteredSpecGroups = product.specType == 1
      ? originalSpecGroups
          .where((group) => group.groupType != ProductSpecGroupType.spec)
          .toList()
      : originalSpecGroups;

  //debugPrint('🔧 原始规格组数量: ${originalSpecGroups.length}, 过滤后: ${filteredSpecGroups.length}');

  final result = filteredSpecGroups.map((group) {
    //debugPrint('🔧 处理规格组: ${group.name} (类型: ${group.groupType})');
    //debugPrint('🔧   原始项目数量: ${group.items.length}');
    
    final modifiedItems = group.items.map<SpecItem>((item) {
      //debugPrint('🔧   检查项目: ${item.name} (ID: ${item.id}, 原始选中: ${item.isSelected})');
      
      // 🔑 第一步：清空所有原始默认值
      bool shouldBeSelected = false;

      // 🔑 第二步：只根据购物车数据设置选中状态
      if (group.groupType == ProductSpecGroupType.practice) {
        // 检查做法列表中是否有匹配的ID
        shouldBeSelected = cartItem.practiceList?.any(
          (practice) => practice.commodityPracticeId == item.id
        ) ?? false;
        
        if (shouldBeSelected) {
          //debugPrint('🔧     ✅ 根据购物车设置做法选中: ${item.name} (ID: ${item.id})');
        } else {
          //debugPrint('🔧     ❌ 做法未匹配: ${item.name} (ID: ${item.id})');
        }
      } else if (group.groupType == ProductSpecGroupType.addons) {
        // 检查加料列表中是否有匹配的ID
        shouldBeSelected = cartItem.addonsList?.any(
          (addon) => addon.commodityId == item.id
        ) ?? false;
        
        if (shouldBeSelected) {
          //debugPrint('🔧     ✅ 根据购物车设置加料选中: ${item.name} (ID: ${item.id})');
        } else {
          //debugPrint('🔧     ❌ 加料未匹配: ${item.name} (ID: ${item.id})');
        }
      } else if (group.groupType == ProductSpecGroupType.spec) {
        // 🔧 规格类型：需要区分单规格和多规格商品
  if (product.specType == 1) {
    // 单规格商品：清空默认值（反正会被过滤掉）
    shouldBeSelected = false;
    //debugPrint('🔧     🚫 单规格商品，清空规格项: ${item.name} (ID: ${item.id})');
  } else {
    // 多规格商品：根据购物车的SKU ID匹配
    // item.skuId == cartItem.commoditySkuId 就选中回显
    if (cartItem.commoditySkuId !=  0) {
      shouldBeSelected = item.skuId == cartItem.commoditySkuId;
      if (shouldBeSelected) {
        //debugPrint('🔧     ✅ 多规格商品，根据SKU设置选中: ${item.name} (SKU: ${item.skuId})');
      } else {
        //debugPrint('🔧     ❌ 多规格商品，SKU不匹配: ${item.name} (SKU: ${item.skuId}, 购物车SKU: ${cartItem.commoditySkuId})');
      }
    } else {
      // 购物车中没有SKU信息，保持原始默认值
      shouldBeSelected = item.isSelected;
      //debugPrint('🔧     ⚠️ 多规格商品，无SKU信息，保持默认: ${item.name} (默认: ${item.isSelected})');
    }
  }
  }
      // 🔑 其他未知类型也清空默认值

      //debugPrint('🔧     最终状态: ${item.name} -> ${shouldBeSelected ? "选中" : "未选中"}');

      // 返回修改后的规格项
      return SpecItem(
        id: item.id,
        name: item.name,
        isAdditional: item.isAdditional,
        additionalPrice: item.additionalPrice,
        isSelected: shouldBeSelected, // 🔑 关键：清空原始默认值，只用购物车数据设置
        isMeasured: item.isMeasured,
        skuId: item.skuId,
      );
    }).toList();

    // 统计选中项数量用于验证
    final selectedCount = modifiedItems.where((item) => item.isSelected).length;
    //debugPrint('🔧   规格组 ${group.name} 处理完成: ${selectedCount}/${modifiedItems.length} 项被选中');
    
    // 打印选中的具体项目
    final selectedItems = modifiedItems.where((item) => item.isSelected).toList();
    if (selectedItems.isNotEmpty) {
      //debugPrint('🔧   选中的项目:');
      for (final item in selectedItems) {
        //debugPrint('🔧     - ${item.name} (ID: ${item.id})');
      }
    } else {
      //debugPrint('🔧   ⚠️ 该规格组没有选中任何项目');
    }

    // 返回修改后的规格组
    return ProductSpecGroup(
      id: group.id,
      name: group.name,
      isRequired: group.isRequired,
      isMultiSelect: group.isMultiSelect,
      upperLimit: group.upperLimit,
      lowerLimit: group.lowerLimit,
      items: modifiedItems,
      groupType: group.groupType,
    );
  }).toList();

  //debugPrint('🔧 === 单商品规格组默认值替换完成 ===');
  //debugPrint('🔧 总共处理了 ${result.length} 个规格组');
  
  // 统计总的选中项数量
  int totalSelected = 0;
  for (final group in result) {
    totalSelected += group.items.where((item) => item.isSelected).length;
  }
  //debugPrint('🔧 全部规格组总共选中 ${totalSelected} 个项目');
  
  return result;
}

/// 替换套餐的默认值
ProductCombo _replaceComboDefaults(
  ProductCombo originalCombo,
  CartProductItem cartItem,
) {
  //debugPrint('🔧 === 替换套餐默认值 ===');
  //debugPrint('🔧 套餐名称: ${cartItem.commodityName}');
  //debugPrint('🔧 购物车子商品数量: ${cartItem.productList?.length ?? 0}');
  
  // 打印购物车子商品详情
  if (cartItem.productList?.isNotEmpty == true) {
    //debugPrint('🔧 购物车子商品列表:');
    for (final subProduct in cartItem.productList!) {
      //debugPrint('🔧   - ${subProduct.commodityName} (ID: ${subProduct.commodityId}, 数量: ${subProduct.quantity})');
      //debugPrint('🔧     做法: ${subProduct.practiceList?.length ?? 0}个');
      //debugPrint('🔧     加料: ${subProduct.addonsList?.length ?? 0}个');
    }
  }
  
  // 修改套餐组中的默认选中状态
  final modifiedGroups = originalCombo.groups.map((group) {
    //debugPrint('🔧 处理套餐组: ${group.name} (ID: ${group.id})');
    
    final modifiedItems = group.items.map((choice) {
      // 🔑 第一步：清空所有原始默认值
      double defaultEnable = 0.0;
      
      // 🔑 第二步：检查购物车中是否包含此套餐项
      final cartSubProduct = cartItem.productList?.firstWhereOrNull(
        (subProduct) => subProduct.commodityId == choice.product.commodityId
      );

      // 🔑 第三步：处理子商品的规格组默认值
      final modifiedSpecGroups = choice.specGroups.map((specGroup) {
        //debugPrint('🔧   处理子商品规格组: ${specGroup.name} (类型: ${specGroup.groupType})');
        
        final modifiedSpecItems = specGroup.items.map((specItem) {
          // 🔑 关键：先清空所有默认值
          bool shouldBeSelected = false;

          // 🔑 只有在购物车中找到对应子商品时，才根据购物车数据设置选中状态
          if (cartSubProduct != null) {
            if (specGroup.groupType == ProductSpecGroupType.practice) {
              // 根据购物车做法列表设置选中状态
              shouldBeSelected = cartSubProduct.practiceList?.any(
                (practice) => practice.commodityPracticeId == specItem.id
              ) ?? false;
              
              if (shouldBeSelected) {
                //debugPrint('🔧     根据购物车设置做法选中: ${specItem.name} (ID: ${specItem.id})');
              }
            } else if (specGroup.groupType == ProductSpecGroupType.addons) {
              // 根据购物车加料列表设置选中状态
              shouldBeSelected = cartSubProduct.addonsList?.any(
                (addon) => addon.commodityId == specItem.id
              ) ?? false;
              
              if (shouldBeSelected) {
                //debugPrint('🔧     根据购物车设置加料选中: ${specItem.name} (ID: ${specItem.id})');
              }
            } else if (specGroup.groupType == ProductSpecGroupType.spec) {
              // 🔑 规格类型：清空默认值（套餐中的规格通常由系统处理）
              shouldBeSelected = false;
              //debugPrint('🔧     清空规格项默认值: ${specItem.name} (ID: ${specItem.id})');
            }
            // 🔑 其他未知类型也清空默认值
          } else {
            // 🔑 购物车中没有此子商品，清空所有默认值
            //debugPrint('🔧     购物车中无此子商品，清空规格项: ${specItem.name}');
          }

          return SpecItem(
            id: specItem.id,
            name: specItem.name,
            isAdditional: specItem.isAdditional,
            additionalPrice: specItem.additionalPrice,
            isSelected: shouldBeSelected, // 🔑 关键：清空默认值后用购物车数据设置
            isMeasured: specItem.isMeasured,
            skuId: specItem.skuId,
          );
        }).toList();

        // 统计选中项数量用于验证
        final selectedCount = modifiedSpecItems.where((item) => item.isSelected).length;
        //debugPrint('🔧   规格组 ${specGroup.name} 处理完成: ${selectedCount}/${modifiedSpecItems.length} 项被选中');

        return ProductSpecGroup(
          id: specGroup.id,
          name: specGroup.name,
          isRequired: specGroup.isRequired,
          isMultiSelect: specGroup.isMultiSelect,
          upperLimit: specGroup.upperLimit,
          lowerLimit: specGroup.lowerLimit,
          items: modifiedSpecItems,
          groupType: specGroup.groupType,
        );
      }).toList();

      // 🔑 第四步：如果购物车中包含此套餐项，设为默认选中
      if (cartSubProduct != null) {
        defaultEnable = cartSubProduct.quantity?.toDouble() ?? 1.0;
        //debugPrint('🔧   根据购物车设置套餐项选中: ${choice.product.commodityName} x${defaultEnable}');
      } else {
        //debugPrint('🔧   清空套餐项默认值: ${choice.product.commodityName}');
      }

      // 返回修改后的套餐选项
      return ProductComboChoice(
        product: choice.product,
        additionalPrice: choice.additionalPrice,
        defaultEnable: defaultEnable, // 🔑 关键：清空原始默认值，只用购物车数据设置
        specGroups: modifiedSpecGroups,
      );
    }).toList();

    // 统计选中的套餐项数量
    final selectedChoices = modifiedItems.where((item) => item.defaultEnable > 0).length;
    //debugPrint('🔧 套餐组 ${group.name} 处理完成: ${selectedChoices}/${modifiedItems.length} 项被选中');

    return ProductComboGroup(
      comboId: group.comboId,
      comboName: group.comboName,
      minSelection: group.minSelection,
      maxSelection: group.maxSelection,
      type: group.type,
      items: modifiedItems,
    );
  }).toList();

  //debugPrint('🔧 === 套餐默认值替换完成 ===');
  //debugPrint('🔧 已清空所有原始默认值，仅保留购物车匹配项');

  return ProductCombo(
    commodityId: originalCombo.commodityId,
    commodityName: originalCombo.commodityName,
    group: modifiedGroups,
    reasonOptions: originalCombo.reasonOptions,
  );
}

/// 🔧 从购物车备注中解析预设标签和自定义备注
Map<String, dynamic> _parseRemarkFromCartItem(String? remark, List<detail.ReasonOption> reasonOptions) {
  final List<String> selectedTags = [];
  String customRemark = '';
  
  if (remark == null || remark.isEmpty) {
    return {'tags': selectedTags, 'customRemark': customRemark};
  }
  
  debugPrint('🔧 解析购物车备注: "$remark"');
  debugPrint('🔧 可用的预设选项: ${reasonOptions.map((e) => e.name).toList()}');
  
  // 获取所有预设备注选项的名称
  final presetNames = reasonOptions.map((e) => e.name).toSet();
  
  // 按逗号分割备注内容
  final remarkParts = remark.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  
  final customParts = <String>[];
  
  for (final part in remarkParts) {
    if (presetNames.contains(part)) {
      // 如果是预设选项，添加到标签列表
      selectedTags.add(part);
      debugPrint('🔧 找到预设标签: "$part"');
    } else {
      // 否则作为自定义备注
      customParts.add(part);
      debugPrint('🔧 自定义备注部分: "$part"');
    }
  }
  
  customRemark = customParts.join(',');
  
  debugPrint('🔧 解析结果 - 预设标签: $selectedTags, 自定义备注: "$customRemark"');
  
  return {
    'tags': selectedTags,
    'customRemark': customRemark,
  };
}


/// 从套餐数据中提取原因选项
/// 
/// 根据套餐详情接口返回的数据结构，提取reasonOptions字段
List<detail.ReasonOption> _extractReasonOptionsFromCombo(ProductCombo combo) {
  return combo.reasonOptions ?? [];
}