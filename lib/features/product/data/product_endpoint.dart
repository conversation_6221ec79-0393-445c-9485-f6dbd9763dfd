
import 'package:kpos/common/services/networking/remote_service/api_endpoint.dart';

enum ProductEndpoint implements ApiEndpoint {

  //商品所有分类数据
  productCategories("/product/categories"),

  //所有商品数据
  products("/products"),

  ///商品详情
  productDetail("/product/detail"),

  ///套餐详情
  productComboDetail("/product/combo/detail"),

  getSoldOutProducts("/get/sold/out/products"),

  setProductSoldOut("/set/product/sold/out");

  const ProductEndpoint(this.path);

  @override
  final String path;

}