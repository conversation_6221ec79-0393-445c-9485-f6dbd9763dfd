import 'package:kpos/common/services/database/app_database.dart';

abstract class ProductLocalRepositoryInterface {
  Future<int> getTotalProductCount({
    int? leafCategoryId,
    String? keyword,
  });

  Future<List<Map<String, dynamic>>> getProducts({
    int? leafCategoryId,
    String currencyUnit = '',
    int? orderId,
    String? keyword,
  });

  Future<List<Map<String, dynamic>>> getProductCategories();
  Future<Map<String, dynamic>> getProductDetail(int productId);

  Future<Map<String, dynamic>> getProductComboDetail(int id);

  Future<List<Map<String, dynamic>>> getSoldOutProducts(int productId,String currencyUnit);

  Future<void> setProductSoldOut(int type, int productId);

}