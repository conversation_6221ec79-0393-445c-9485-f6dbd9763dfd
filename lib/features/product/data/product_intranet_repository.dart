import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:kpos/features/product/data/product_endpoint.dart';
import 'package:kpos/features/product/domain/product_category_item.dart';
import 'package:kpos/features/product/domain/product_list.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/remote_service/api_interface.dart';
import '../application/product_combo_converter.dart';
import '../domain/product_combo.dart';
import '../domain/product_detail.dart';
import '../sold_on/domain/sold_out_detail_product.dart';

part 'product_intranet_repository.g.dart';

class ProductIntranetRepository {
  final ApiInterface _apiService;

  ProductIntranetRepository({required ApiInterface apiService})
      : _apiService = apiService;

  Future<List<ProductCategoryItem>> getProductCategories() async {
    return await _apiService.getListData(
        endpoint: ProductEndpoint.productCategories,
        converter: ProductCategoryItem.fromJson);
  }

  Future<ProductList> getProducts(
      int categoryId, int? orderId, String? keyword) async {
    Map<String, dynamic> param = {"categoryId": categoryId};
    if (orderId != null) {
      param["orderId"] = orderId;
    }
    if (keyword != null) {
      param["keyword"] = keyword;
    }
    return await _apiService.getData(
        endpoint: ProductEndpoint.products,
        queryParams: param,
        converter: ProductList.fromJson);
  }

  Future<ProductDetail> getProductDetail(int productId) async {
    return await _apiService.getData(
      endpoint: ProductEndpoint.productDetail, //相当于链接
      queryParams: {"id": productId.toString()},
      converter: ProductDetail.fromJson, //转换器
    );
  }

  Future<ProductCombo> getProductComboDetail(int productId) async {
    // 获取原始API数据
    final rawData = await _apiService.getData(
      endpoint: ProductEndpoint.productComboDetail,
      queryParams: {"id": productId.toString()},
      converter: (json) => json, // 直接返回Map，不进行自动JSON转换
    );

    // 使用转换器将API数据转换为ProductCombo模型
    return ProductComboConverter.fromApiResponse(rawData);
  }

  /// 获取关联商品列表（受售罄影响的商品）
  /// 调用接口：/set/product/sold/out
  /// 参数：id - 商品ID
  Future<List<SoldOutDetailProduct>> getSoldOutProducts(int productId) async {
    // 移除不必要的类型转换，直接使用SoldOutDetailProduct.fromJson
    return await _apiService.getListData(
      endpoint: ProductEndpoint.getSoldOutProducts,
      queryParams: {"id": productId.toString()},
      converter: SoldOutDetailProduct.fromJson, // 直接使用fromJson方法
    );
  }

  /// 设置商品售罄状态
  /// 接口：/set/product/sold/out
  /// 参数：type - 类型(1=售罄，0=正常)，id - 商品ID
  Future<void> setProductSoldOut({
    required int type,
    required int productId,
  }) async {
  try {
    await _apiService.postObjectData(
      endpoint: ProductEndpoint.setProductSoldOut,
      data: {
        "type": type,
        "id": productId,
      },
    );
  } catch (error) {
    // 所有非200状态码或业务失败都会抛出异常
    debugPrint('设置售罄状态失败: $error');
    rethrow; // 重新抛出异常让上层处理
  }
  }
}

@riverpod
ProductIntranetRepository productIntranetRepository(Ref ref) {
  return ProductIntranetRepository(
      apiService: ref.watch(kposApiIntranetServiceProvider));
}
