import 'package:drift/drift.dart';
import 'package:kpos/features/product/data/product_local_repository_interface.dart';
import '../../../common/services/database/app_database.dart';

class ProductLocalRepository implements ProductLocalRepositoryInterface {
  final AppDatabase db;
  ProductLocalRepository(this.db);

  @override
  Future<List<Map<String, dynamic>>> getProducts({
    int? leafCategoryId,
    String currencyUnit = '',
    int? orderId,
    String? keyword,
  }) async {
    try {
      final whereClause = <String>[];
      final variables = <Variable>[];

      // 子查询中 orderId 在前面
      if (orderId != null) {
        variables.add(Variable.withInt(orderId));
      }

      if (leafCategoryId != null && leafCategoryId != 0) {
        whereClause.add('m.leaf_category_id = ?');
        variables.add(Variable.withInt(leafCategoryId));
      }

      if (keyword != null && keyword.isNotEmpty) {
        whereClause.add('m.commodity_name LIKE ?');
        variables.add(Variable.withString('%$keyword%'));
      }

      whereClause.add('m.standalone_sale_enable = 1');

      final whereSql = whereClause.isNotEmpty ? 'WHERE ${whereClause.join(' AND ')}' : '';

      final sql = '''
      SELECT
        m.commodity_id,
        m.commodity_name,
        m.commodity_image_path,
        m.commodity_type,
        m.spec_type,
        m.price_change_enable,
        m.discount_enable,
        m.standalone_sale_enable,
        m.leaf_category_id,
        m.price,
        m.commodity_unit,
        m.kp_sold_out,
        (
          SELECT IFNULL(SUM(oi.quantity), 0)
          FROM order_item oi
          WHERE oi.commodity_id = m.commodity_id
            ${orderId != null ? 'AND oi.order_id = ?' : ''}
            AND oi.deleted = 0
        ) AS cart_count
      FROM
        products m
      $whereSql
    ''';

      final result = await db.customSelect(sql, variables: variables).get();

      return result.map((row) => {
        'commodityId': row.read<int>('commodity_id'),
        'commodityName': row.read<String>('commodity_name'),
        'commodityType': row.read<int>('commodity_type'),
        'commodityImagePath': row.read<String>('commodity_image_path'),
        'priceChangeEnable': row.read<int>('price_change_enable'),
        'specType': row.read<int>('spec_type'),
        'standaloneSaleEnable': row.read<int>('standalone_sale_enable'),
        'price': row.readNullable<double>('price'),
        'discountEnable': row.read<int>('discount_enable'),
        'categoryId': row.read<int>('leaf_category_id'),
        'commodityUnit': row.read<String>('commodity_unit'),
        'currencyUnit': currencyUnit,
        'cartCount': row.read<int>('cart_count'),
        'soldOut': row.readNullable<int>('kp_sold_out') ?? 0,
      }).toList();
    } catch (e, stack) {
      print('Query failed: $e');
      print(stack);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getProductCategories() async {
    final list = await db.customSelect(
        'SELECT category_id, category_name, color_hex FROM product_categories'
    ).get();

    final categoryList = list.map((row) => {
      'categoryId': row.read<int>('category_id'),
      'categoryName': row.read<String>('category_name'),
      'colorHex': row.read<String>('color_hex'),
    }).toList();

    categoryList.insert(0, {
      'categoryId': 0.0,
      'categoryName': '全部',
      'colorHex': '#A2A6B1',
    });
    return categoryList;
  }

  @override
  Future<int> getTotalProductCount({
    int? leafCategoryId,
    String? keyword,
  }) async {
    final query = db.selectOnly(db.products)
      ..addColumns([db.products.commodityId.count()]);

    // 分类条件
    if (leafCategoryId != null && leafCategoryId != 0) {
      query.where(db.products.leafCategoryId.equals(leafCategoryId));
    }

    // 关键字模糊匹配
    if (keyword != null && keyword.isNotEmpty) {
      query.where(db.products.commodityName.like('%$keyword%'));
    }

    final totalCount = await query.getSingle();
    return totalCount.read(db.products.commodityId.count()) ?? 0;
  }

  /// 查询商品规格
  Future<List<Map<String, dynamic>>> getProductSpecs(int productId) async {
    try {
      // 查询规格组
      final specGroup = await db.customSelect(
        '''
    SELECT
      psg.commodity_spec_group_id,
      psg.commodity_spec_group_name
    FROM
      product_spec_group psg
    WHERE
      psg.commodity_id = ?
    ''',
        variables: [Variable.withInt(productId)],
      ).getSingleOrNull();

      // 查询规格项
      List<QueryRow> specs = await db.customSelect(
        '''
    SELECT
      ps.spec_id,
      ps.commodity_sku_id,
      ps.sku_name,
      ps.sku_price,
      ps.default_enable,
      ps.sort
    FROM
      product_sku ps
    WHERE
      ps.commodity_id = ?
    ORDER BY
      sort
    ''',
        variables: [Variable.withInt(productId)],
      ).get();

      List<Map<String, dynamic>> specResult = [];

      if (specGroup != null || specs.isNotEmpty) {
        specResult.add({
          "id": specGroup != null ? specGroup.read<int>('commodity_spec_group_id') : 0,
          "name": specGroup != null ? (specGroup.readNullable<String>('commodity_spec_group_name') ?? '') : '',
          "minSelection": 0,
          "maxSelection": 1,
          "allowDuplicate": 0,
          "items": specs
              .map((row) => {
            "id": row.readNullable<int>('spec_id') ?? 0,
            "skuId": row.read<int>('commodity_sku_id'),
            "name": row.readNullable<String>('sku_name') ?? '',
            "type": 1,
            "price": row.readNullable<double>('sku_price') ?? 0.0,
            "defaultEnable": row.readNullable<int>('default_enable') ?? 0,
          })
              .toList()
        });
      }

      return specResult;
    } catch (e, stack) {
      print('❌ getProductSpecs error: $e');
      print(stack);
      return [];
    }
  }

  ///查询做法数据
  Future<List<Map<String,dynamic>>> getProductPractice(int productId) async {
    final practices = await db.customSelect(
      '''SELECT
      ppg.commodity_practice_group_id,
      ppg.name,
      ppg.min_selection,
      ppg.max_selection,
      pp.practice_id,
      pp.practice_name,
      pp.price,
      pp.default_enable
      FROM
      product_practice_group ppg
      LEFT JOIN product_practice pp ON
      ppg.commodity_practice_group_id = pp.practice_group_id
      WHERE
      ppg.commodity_id = ?
      ORDER BY pp.sort''',
      variables: [Variable.withInt(productId)],
    ).get();

    if (practices.isEmpty) {
      return [];
    } else {
      // 分组
      final Map<int, Map<String, dynamic>> groupMap = {};

      for (final row in practices) {
        final int groupId = row.read<int>('commodity_practice_group_id');

        if (!groupMap.containsKey(groupId)) {
          groupMap[groupId] = {
            "id": groupId,
            "name": row.read<String>('name'),
            "minSelection": row.read<int>('min_selection'),
            "maxSelection": row.read<int>('max_selection'),
            "items": []
          };
        }

        (groupMap[groupId]!["items"] as List).add({
          "id": row.read<int>('practice_id'),
          "name": row.read<String>('practice_name'),
          "type": 1,  // == 新增 == 添加type字段，1表示普通做法项
          "price": row.read<double>('price'),
          "defaultEnable": row.read<int>('default_enable'),
        });
      }
      return groupMap.values.toList();
    }

  }

  /// 查询加料数据
  Future<List<Map<String,dynamic>>> getProductAddons(int productId) async {
    final addons = await db.customSelect(
        '''SELECT
        pag.commodity_addons_group_id,
        pag.commodity_addons_group_name,
        pag.min_selection,
        pag.max_selection,
        pag.allow_duplicate,
        pa.addons_id,
        pa.addons_commodity_id,
        pa.addons_name,
        pa.commodity_type,
        pa.price,              -- == 新增 == 添加price字段
    pa.default_enable,
    ps.commodity_sku_id
        FROM
        product_addons_group pag
        LEFT JOIN product_addons pa ON
        pag.commodity_addons_group_id = pa.addons_group_id
    LEFT JOIN product_sku ps ON
        pa.addons_commodity_id  = ps.commodity_id
        WHERE
        pag.commodity_id = ?
    ORDER BY
    pa.sort''',
      variables: [Variable.withInt(productId)],
    ).get();
    if (addons.isEmpty) {
      return [];
    } else {
      // 分组
      final Map<int, Map<String, dynamic>> addonsMap = {};

      for (final row in addons) {
        final int groupId = row.read<int>('commodity_addons_group_id');

        if (!addonsMap.containsKey(groupId)) {
          addonsMap[groupId] = {
            "id": groupId,
            "name": row.read<String>('commodity_addons_group_name'),
            "minSelection": row.read<int>('min_selection'),
            "maxSelection": row.read<int>('max_selection'),
            "allowDuplicate": row.read<int>('allow_duplicate'),
            "items": []
          };
        }

        (addonsMap[groupId]!["items"] as List).add({
          "id": row.read<int>('addons_commodity_id'),
          "skuId": row.readNullable<int>('commodity_sku_id') ?? 0,
          "name": row.read<String>('addons_name'),
          "type": row.read<int>('commodity_type'), // 确保是int类型
          "price": row.read<double>('price'), // 添加price字段
          "defaultEnable": row.read<int>('default_enable'), // 确保是int类型
          // == 修复结束 ==
        });
      }
      return addonsMap.values.toList();
    }
  }

  /// 查询预设原因
  Future<List<Map<String,dynamic>>> getReasonOptions(int type) async {
    final reasonOptions = await db.customSelect(
      '''SELECT
      spr.prepared_reason_id,
      spr.prepared_reason_name
      FROM
      store_prepared_reason spr
      WHERE
      spr.prepared_reason_type = ?
      ORDER BY
      spr.order_num''',
      variables: [Variable.withInt(type)]
    ).get();
    if (reasonOptions.isEmpty) {
      return [];
    } else {
      final list = reasonOptions.map((row) => {
        "id": row.read<int>('prepared_reason_id'),
        "name": row.read<String>('prepared_reason_name') ?? '',
      }).toList();
      return list;
    }
  }

  @override
  Future<Map<String, dynamic>> getProductDetail(int productId, [String currencyUnit = '']) async {
    final Map<String, dynamic> data = {};
    //查询商品基本信息
    final product = await db.customSelect(
      '''SELECT
      p.commodity_id,
      p.commodity_name,
      p.commodity_type,
      p.spec_type,
      p.commodity_unit,
      p.price_change_enable,
      p.discount_enable,
      p.spec_type
      FROM products p
      WHERE p.commodity_id = ?''',
      variables: [Variable.withInt(productId)],
    ).getSingleOrNull();
    if (product != null) {
      data['commodityId'] = product.read<int>('commodity_id');
      data['commodityName'] = product.read<String>('commodity_name');
      data['commodityUnit'] = product.read<String>('commodity_unit');
      data['commodityType'] = product.read<String>('commodity_type');
      data['currencyUnit'] = currencyUnit;
      data['priceChangeEnable'] = product.read<int>('price_change_enable');
      data['discountEnable'] = product.read<int>('discount_enable');
      data['specType'] = product.read<int>('spec_type');
    } else {
      throw Exception('Not find product');
    }

    //查询商品规格
    final specResult = await getProductSpecs(productId);
    if (specResult.isNotEmpty) {
      data["specGroups"] = specResult;
    } else {
      data["specGroups"] = [];
    }

    //查询做法
    final practiceResult = await getProductPractice(productId);
    if (practiceResult.isEmpty) {
      data["practiceGroups"] = [];
    } else {
      data["practiceGroups"] = practiceResult;
    }

    //查询加料
    final addonsResult = await getProductAddons(productId);
    if (addonsResult.isEmpty) {
      data["addonsGroups"] = [];
    } else {
      data["addonsGroups"] = addonsResult;
    }

    /// 查询预设原因
    final reasonOptions = await getReasonOptions(3);
    if (reasonOptions.isEmpty) {
      data["reasonOptions"] = [];
    } else {
      data["reasonOptions"] = reasonOptions;
    }
    return data;
  }

  @override
  Future<Map<String, dynamic>> getProductComboDetail(int id) async {
    final Map<String, dynamic> data = {};
    //查询套餐基本信息
    final product = await db.customSelect(
      '''SELECT
      p.commodity_id,
      p.commodity_name
      FROM products p
      WHERE p.commodity_id = ?''',
      variables: [Variable.withInt(id)],
    ).getSingleOrNull();
    if (product != null) {
      data['commodityId'] = product.read<int>('commodity_id');
      data['commodityName'] = product.read<String>('commodity_name');
    } else {
      throw Exception('Not find product');
    }

    //查询套餐详情信息
    final list = await db.customSelect(
        '''SELECT DISTINCT
        a.combo_grouping_id, --分组Id
    a.grouping_name, --分组名称
    a.grouping_type, --分组类型
    a.grouping_commodity_count_min, --最小
    a.grouping_commodity_count_max, --最大可选
    b.commodity_id, --商品id
    e.commodity_name, -- 商品名称
    b.additional_price,
    b.default_recommendation, --是否默认推荐
    e.commodity_image_path, --商品图片
    e.commodity_type, --商品类型
    e.spec_type --商品规格
    FROM
    product_combo_grouping a
    LEFT JOIN product_combo_grouping_detail b ON a.combo_grouping_id = b.combo_grouping_id
    LEFT JOIN product_sku c ON b.commodity_sku_id = c.commodity_sku_id
    LEFT JOIN products e ON b.commodity_id = e.commodity_id
    WHERE
    a.combo_id = ?
    ORDER BY a.sort,b.sort''',
      variables: [Variable.withInt(id)],
    ).get();
    if (list.isEmpty) {
      data["group"] = [];
    } else {
      // 分组
      final Map<int, Map<String, dynamic>> comboGroup = {};
      for (final row in list) {
        final int groupId = row.read<int>('combo_grouping_id');

        if (!comboGroup.containsKey(groupId)) {
          comboGroup[groupId] = {
            "comboId": groupId,
            "comboName": row.read<String>('grouping_name'),
            "minSelection": row.read<int>('grouping_commodity_count_min'),
            "maxSelection": row.read<int>('grouping_commodity_count_max'),
            "type": row.read<int>('grouping_type'),
            "items": []
          };
        }

        final commodityId = row.readNullable<int>('commodity_id');

        // 先构造基本信息
        final Map<String, dynamic> itemData = {
          "commodityId": commodityId,
          "commodityName": row.readNullable<String>('commodity_name') ?? '',
          "additionalPrice": row.readNullable<double>('additional_price') ?? 0.0,
          "defaultEnable": row.readNullable<int>('default_recommendation') ?? 0,
          "imageUrl": row.readNullable<String>('commodity_image_path') ?? '',
          "commodityType": row.readNullable<int>('commodity_type') ?? 0,
          "specType": row.readNullable<int>('spec_type') ?? 1,
          "specGroups": [],
          "practiceGroups": [],
          "addonsGroups": [],
        };
        if (commodityId != null) {
          final specGroups = await getProductSpecs(commodityId);
          final practiceGroups = await getProductPractice(commodityId);
          final addonsGroups = await getProductAddons(commodityId);
          itemData["specGroups"] = specGroups;
          itemData["practiceGroups"] = practiceGroups;
          itemData["addonsGroups"] = addonsGroups;
        }
        (comboGroup[groupId]!["items"] as List).add(itemData);
      }
      data["group"] = comboGroup.values.toList();
    }

    //查询预设原因
    final reasonOptions = await getReasonOptions(3);
    if (reasonOptions.isEmpty) {
      data["reasonOptions"] = [];
    } else {
      data["reasonOptions"] = reasonOptions;
    }

    return data;
  }

  @override
  Future<List<Map<String, dynamic>>> getSoldOutProducts(int productId,String currencyUnit) async {
    const sql = '''
    SELECT
      p.commodity_id,
      p.commodity_name,
      p.commodity_image_path,
      p.commodity_type,
      p.price
    FROM products p
    WHERE p.commodity_id = ?
    
    UNION ALL
    
    SELECT
      p.commodity_id, 
      p.commodity_name,
      p.commodity_image_path,
      p.commodity_type,
      p.price
    FROM product_combo_grouping g
    JOIN products p ON g.combo_id = p.commodity_id
    WHERE g.grouping_type = 1
      AND g.combo_grouping_id IN (
        SELECT combo_grouping_id
        FROM product_combo_grouping_detail
        WHERE commodity_id = ?
      )
  ''';

    final result = await db.customSelect(
      sql,
      variables: [Variable.withInt(productId), Variable.withInt(productId)],
    ).get();

    return result.map((row) => {
      'commodityId': row.read<int>('commodity_id'),
      'commodityName': row.readNullable<String>('commodity_name') ?? '',
      'commodityImagePath': row.readNullable<String>('commodity_image_path') ?? '',
      'commodityType': row.read<int>('commodity_type'),
      'price': row.read<double>('price'),
      'currencyUnit': currencyUnit,
    }).toList();
  }

  @override
  Future<void> setProductSoldOut(int type, int productId) async {
    final soldOutValue = type == 1 ? 1 : 0;
    final nowTimestamp = DateTime.now().millisecondsSinceEpoch;

    const sql = '''
    UPDATE products
    SET 
      kp_sold_out = ?,
      kp_sold_out_time = ?
    WHERE commodity_id IN (
      SELECT p.commodity_id
      FROM products p
      WHERE p.commodity_id = ?
      
      UNION
      
      SELECT p.commodity_id
      FROM product_combo_grouping_detail d
      JOIN product_combo_grouping g ON d.combo_grouping_id = g.combo_grouping_id
      JOIN products p ON g.combo_id = p.commodity_id
      WHERE d.commodity_id = ? AND g.grouping_type = 1
    )
  ''';

    await db.customUpdate(
      sql,
      variables: [
        Variable.withInt(soldOutValue),
        Variable.withInt(nowTimestamp),
        Variable.withInt(productId),
        Variable.withInt(productId),
      ],
      updates: {db.products},
    );
  }



}