import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/utils/kp_list_util.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/web_socket/stomp/stomp_destinations.dart';
import '../../../common/services/web_socket/stomp/stomp_request_service.dart';
part 'product_repository.g.dart';

class ProductRepository {

  final StompRequestService _stompRequestService;
  final Ref _ref;
  ProductRepository(this._ref, {required StompRequestService stompRequestService})
      : _stompRequestService = stompRequestService;

  Future<void> initializeProductCategory() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    try {
      final categoryList = await _stompRequestService.requestListData(
        sendDestination: StompDestinations.productCategories,
        subscribeDestination: StompDestinations.productCategoriesSub,
        body: {"storeId": boundStore.storeId},
        itemParser: (json) => json as Map<String, dynamic>,
        isPaged: false,
      );

      final categoryMap = <int, ProductCategoriesCompanion>{};
      if (categoryList.isNotEmpty) {
        for (final item in categoryList) {
          // 这里假设 item 里字段都存在且非null，必要时可以加更多 null 判断
          final categoryId = item['categoryId'] as int;
          final categoryName = item['categoryName'] as String? ?? '';
          final colorHex = item['colorHex'] as String? ?? '';

          final companion = ProductCategoriesCompanion.insert(
            categoryId: Value(categoryId),
            categoryName: categoryName,
            colorHex: colorHex,
          );
          categoryMap[categoryId] = companion;
        }

        final db = _ref.watch(databaseProvider);
        try {
          if (categoryMap.isNotEmpty) {
            await db.insertOrReplaceBatch(
              db.productCategories,
              categoryMap.values.toList(),
            );
            print('✅ Successfully inserted ${categoryMap.length} product categories');
          }
        } catch (e, st) {
          print('Product category batch insert failed: $e\n$st');
        }
      } else {
        print('initializeProductCategory: no data to insert.');
      }
    } catch (e, st) {
      print('initializeProductCategory error: $e');
      print(st);
    }
  }

  Future<void> initializeProducts() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
    final productList = await _stompRequestService.requestListData(
        sendDestination: StompDestinations.products,
        subscribeDestination: StompDestinations.productsSub,
        body: {"storeId": boundStore.storeId, "tenantId": tenantId ?? 1, "brandId": boundStore.brandId},
        itemParser: (json) => json as Map<String, dynamic>,
        isPaged: true
    );
    final productMap = <int, ProductsCompanion>{};
    if (productList.isNotEmpty) {
      for (final item in productList) {
        final companion = ProductsCompanion.insert(
          commodityId: Value(item['commodityId'] as int),
          commodityCode: item['commodityCode'] as String,
          commodityName: item['commodityName'] as String,
          leafCategoryId: item['leafCategoryId'] as int,
          commodityImagePath: Value(item['commodityImagePath'] as String?),
          commodityType: item['commodityType'] as int,
          specType: Value(item['specType'] as int?),
          priceChangeEnable: Value(item['priceChangeEnable'] as int),
          discountEnable: Value(item['discountEnable'] as int),
          standaloneSaleEnable: Value(item['standaloneSaleEnable'] as int),
          commodityNameSec: Value(item['commodityNameSec'] as String?),
          commodityDesc: Value(item['commodityDesc'] as String?),
          commodityUnit: item['commodityUnit'] as String,
          incrementQuantity: item['incrementQuantity'] as int,
          sellingTimeType: item['sellingTimeType'] as int,
          minOrderQuantity: Value(item['minOrderQuqntity'] as int),
          price: item['commodityPrice'] as double,
        );
        productMap[item['commodityId'] as int] = companion;        
      }
      

      final db = _ref.watch(databaseProvider);
      try {
        if (productMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
            db.products,
            productMap.values.toList(),
          );
        }

        print('✅  Successfully inserted: '
            '${productMap.length} products');
      } catch (e, st) {
        print('Product batch insert failed: $e\n$st');
      }
    }
  }

  Future<void> initializeProductPractice() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonList = await _stompRequestService.requestListData(
      sendDestination: StompDestinations.productPractice,
      subscribeDestination: StompDestinations.productPracticeSub,
      body: {
        "storeId": boundStore.storeId
      },
      itemParser: (json) => json as Map<String, dynamic>,
      isPaged: false,
    );

    if (jsonList.isEmpty) return;

    final db = _ref.watch(databaseProvider);

    // ✅ 这里先定义两个 List 来拆数据
    final List<Map<String, dynamic>> groupList = [];
    final List<Map<String, dynamic>> practiceList = [];

    try {
      // data本身就是 List
      for (var item in jsonList) {
        // 分组信息
        groupList.add({
          "commodityPracticeGroupId": item['commodityPracticeGroupId'],
          "practiceName": item['practiceName'],
          "commodityId": item['commodityId'],
          "minSelection": item['minSelection'],
          "maxSelection": item['maxSelection'],
        });

        // 做法项
        final List<dynamic> practices = KPListUtil.safeList(item['kposCommodityPractice']);
        for (var p in practices) {
          practiceList.add(p as Map<String, dynamic>);
        }
      }

      print('分组数量: ${groupList.length}');
      print('做法项数量: ${practiceList.length}');

      // ✅ 生成分组 Companion
      final groupMap = <int, ProductPracticeGroupCompanion>{};
      for (var g in groupList) {
        final companion = ProductPracticeGroupCompanion.insert(
          commodityPracticeGroupId: Value(g['commodityPracticeGroupId'] as int),
          name: g['practiceName'] as String,
          commodityId: g['commodityId'] as int,
          minSelection: g['minSelection'] != null
              ? Value(g['minSelection'] as int)
              : const Value.absent(),
          maxSelection: g['maxSelection'] != null
              ? Value(g['maxSelection'] as int)
              : const Value.absent(),
        );
        groupMap[g['commodityPracticeGroupId'] as int] = companion;
      }

      // ✅ 生成做法 Companion
      final practiceCompanionList = <ProductPracticeCompanion>[];
      for (var p in practiceList) {
        final companion = ProductPracticeCompanion.insert(
          practiceId: p['practiceId'] as int,
          practiceGroupId: p['commodityPracticeGroupId'] as int,
          practiceName: p['practiceName'] as String? ?? '',
          practiceNameSecondaryLanguage: p['practiceNameSecondaryLanguage'] != null
              ? Value(p['practiceNameSecondaryLanguage'] as String)
              : const Value.absent(),
          price: p['price'] != null
              ? Value((p['price'] as num).toDouble())
              : const Value.absent(),
          defaultEnable: p['defaultEnable'] != null
              ? Value(p['defaultEnable'] as int)
              : const Value.absent(),
          sort: p['sort'] != null
              ? Value(p['sort'] as int)
              : const Value.absent(),
        );
        practiceCompanionList.add(companion);
      }

      if (groupMap.isNotEmpty) {
        await db.insertOrReplaceBatch(
          db.productPracticeGroup,
          groupMap.values.toList(),
        );
      }

      if (practiceCompanionList.isNotEmpty) {
        await db.insertOrReplaceBatch(
          db.productPractice,
          practiceCompanionList,
        );
      }

      print('✅ Successfully inserted: '
          '${groupMap.length} practice groups, '
          '${practiceCompanionList.length} practices.');
    } catch (e, st) {
      print('❌ Error processing practice data: $e\n$st');
    }
  }

  Future<void> initializeProductSpec() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonMap = await _stompRequestService.requestMapListData(
      sendDestination: StompDestinations.specInfo,
      subscribeDestination: StompDestinations.specInfoSub,
      body: {
        "storeId":boundStore.storeId,
      },
      itemParser: (json) => json as Map<String, dynamic>,
    );

    if (jsonMap.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    final specGroupMap = <int, ProductSpecGroupCompanion>{};
    final List<ProductSkuCompanion> skuCompList = [];

    try {
      final specGroupList = KPListUtil.safeList(jsonMap['specGroup']);
      final skuList =
      KPListUtil.safeList(jsonMap['skuList']);

      for (var item in specGroupList) {
        final companion = ProductSpecGroupCompanion.insert(
          commoditySpecGroupId: Value(item['commoditySpecGroupId'] as int),
          commodityId: item['commodityId'] as int,
          customIf: Value(item['customIf'] as int),
          commoditySpecGroupName: Value(item['commoditySpecGroupName'] as String),
        );
        specGroupMap[item['commoditySpecGroupId'] as int] = companion;
      }

      for (var item in skuList) {
        final companion = ProductSkuCompanion.insert(
          commoditySkuId: Value(item['commoditySkuId'] as int),
          commodityId: item['commodityId'] != null
              ? Value(item['commodityId'] as int)
              : const Value.absent(),
          skuName: Value(item['skuName'] as String? ?? ""),
          skuCode: item['skuCode'] as String,
          skuPrice: (item['storeMenuPrice'] != null
              ? (item['storeMenuPrice'] as num).toDouble()
              : (item['skuPrice'] as num).toDouble()),
          barcode: Value(item['barcode'] as String? ?? ""),
          packageCommodityId: Value(item['packageCommodityId'] as int?),
          specId: item['commoditySpecRelationId'] != null
              ? Value(item['commoditySpecRelationId'] as int)
              : const Value.absent(),
          defaultEnable: const Value(0),
          sort: item['orderNum'] != null
              ? Value(item['orderNum'] as int)
              : const Value.absent(),
        );
        skuCompList.add(companion);
      }

      // 批量插入
      try {
        if (specGroupMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.productSpecGroup, specGroupMap.values.toList());
        }
        if (skuCompList.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.productSku, skuCompList);
        }

        print('✅  Successfully inserted: '
            '${specGroupMap.length}  productSpecGroup, '
            '${skuCompList.length} productSku');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing tax management, service fee management data: $e\n$st');
    }
  }

  Future<void> initializeProductAddons() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonList = await _stompRequestService.requestListData(
      sendDestination: StompDestinations.productAddons,
      subscribeDestination: StompDestinations.productAddonsSub,
      body: {
        "storeId": boundStore.storeId,
      },
      itemParser: (json) => json as Map<String, dynamic>,
      isPaged: false,
    );

    if (jsonList.isEmpty) return;

    final db = _ref.watch(databaseProvider);

    // 定义两个数组：分别存group和relation
    final List<Map<String, dynamic>> groupList = [];
    final List<Map<String, dynamic>> relationList = [];

    try {
      // data本身就是List
      for (var item in jsonList) {
        // group数据放在groupList
        groupList.add({
          "commodityAddonsGroupId": item['commodityAddonsGroupId'],
          "commodityAddonsGroupName": item['commodityAddonsGroupName'],
          "commodityId": item['commodityId'],
          "minSelection": item['minSelection'],
          "maxSelection": item['maxSelection'],
          "allowDuplicate": item['allowDuplicate'],
        });

        // relation数据全部展开放在relationList
        final List<dynamic> relations = KPListUtil.safeList(item['addonsGroupRelationList']);
        for (var rel in relations) {
          relationList.add(rel as Map<String, dynamic>);
        }
      }

      print('分组数量: ${groupList.length}');
      print('加料项数量: ${relationList.length}');

      // 再分别插入
      final addonsGroupMap = <int, ProductAddonsGroupCompanion>{};
      for (var item in groupList) {
        final companion = ProductAddonsGroupCompanion.insert(
          commodityAddonsGroupId: Value(item['commodityAddonsGroupId'] as int),
          commodityId: item['commodityId'] as int,
          commodityAddonsGroupName: item['commodityAddonsGroupName'] as String,
          minSelection: item['minSelection'] != null
              ? Value(item['minSelection'] as int)
              : const Value.absent(),
          maxSelection: item['maxSelection'] != null
              ? Value(item['maxSelection'] as int)
              : const Value.absent(),
          allowDuplicate: item['allowDuplicate'] != null
              ? Value(item['allowDuplicate'] as int)
              : const Value.absent(),
        );
        addonsGroupMap[item['commodityAddonsGroupId'] as int] = companion;
      }

      final addonsRelationMap = <int, ProductAddonsCompanion>{};
      for (var rel in relationList) {
        final companion = ProductAddonsCompanion.insert(
          addonsId: Value(rel['commodityAddonsRelationId'] as int),
          addonsGroupId: rel['commodityAddonsGroupId'] as int,
          addonsCommodityId: rel['addonsCommodityId'] as int,
          addonsName: rel['addonsCommodityName'] as String? ?? '',
          price: rel['addonsSkuPrice'] != null
              ? Value((rel['addonsSkuPrice'] as num).toDouble())
              : const Value.absent(),
          commodityType: rel['commodityAddonsType'] as int,
          defaultEnable: rel['defaultEnable'] != null
              ? Value(rel['defaultEnable'] as int)
              : const Value.absent(),
          sort: rel['orderNum'] != null
              ? Value(rel['orderNum'] as int)
              : const Value.absent(),
        );
        addonsRelationMap[rel['commodityAddonsRelationId'] as int] = companion;
      }

      if (addonsGroupMap.isNotEmpty) {
        await db.insertOrReplaceBatch(
          db.productAddonsGroup,
          addonsGroupMap.values.toList(),
        );
      }
      if (addonsRelationMap.isNotEmpty) {
        await db.insertOrReplaceBatch(
          db.productAddons,
          addonsRelationMap.values.toList(),
        );
      }

      print('✅ Successfully inserted: '
          '${addonsGroupMap.length} addon groups, '
          '${addonsRelationMap.length} addon relations.');
    } catch (e, st) {
      print('Error processing addons data: $e\n$st');
    }
  }

  // 套餐数据初始化
  Future<void> initializeComboInfoList() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonMap = await _stompRequestService.requestMapListData(
      sendDestination: StompDestinations.productComboGrouping,
      subscribeDestination: StompDestinations.productComboGroupingSub,
      body: {
        "storeId": boundStore.storeId,
      },
      itemParser: (json) => json as Map<String, dynamic>,
    );

    if (jsonMap.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    final comboGroupMap = <int, ProductComboGroupingCompanion>{};
    final comboGroupDetailMap = <int, ProductComboGroupingDetailCompanion>{};
    final comboPackagingConfigCompanionMap =
    <int, ProductComboPackagingConfigCompanion>{};

    try {
      final comboGroupList =
      KPListUtil.safeList(jsonMap['kposCommodityComboGrouping']);
      final comboGroupDetailList =
      KPListUtil.safeList(jsonMap['kposCommodityComboGroupingDetail']);
      final comboPackagingConfigCompanionList =
      KPListUtil.safeList(jsonMap['kposCommodityComboPackagingConfig']);

      for (var item in comboGroupList) {
        final companion = ProductComboGroupingCompanion.insert(
          comboGroupingId: Value(item['comboGroupingId'] as int),
          comboId: item['comboId'] as int,
          groupingName: item['groupingName'] as String,
          groupingNameSecondLanguage:
          Value(item['groupingNameSecondLanguage'] as String?),
          groupingType: Value(item['groupingType'] as int),
          groupingCommodityCountMax:
          Value(item['groupingCommodityCountMax'] as int),
          groupingCommodityCountMin:
          Value(item['groupingCommodityCountMin'] as int),
          sort: Value(item['sort'] as int),
        );
        comboGroupMap[item['comboGroupingId'] as int] = companion;
      }

      for (var item in comboGroupDetailList) {
        final companion = ProductComboGroupingDetailCompanion.insert(
          comboGroupingDetailId: Value(item['comboGroupingDetailId'] as int),
          comboGroupingId: item['comboGroupingId'] as int,
          commodityId: item['commodityId'] as int,
          commoditySkuId: item['commoditySkuId'] as int,
          comboId: item['comboId'] as int,
          quantity: Value(item['quantity'] as int),
          defaultRecommendation: Value(item['defaultRecommendation'] as int),
          sort: Value(item['sort'] as int),
          additionalPrice: Value((item['additionalPrice'] as num?)!.toDouble()),
        );
        comboGroupDetailMap[item['comboGroupingDetailId'] as int] = companion;
      }

      for (var item in comboPackagingConfigCompanionList) {
        final companion = ProductComboPackagingConfigCompanion.insert(
          comboPackagingConfigId: Value(item['comboPackagingConfigId'] as int),
          comboId: item['comboId'] as int,
          boxId: Value(item['boxId']),
          packagingSettingType: Value(item['packagingSettingType'] as int),
        );
        comboPackagingConfigCompanionMap[
        item['comboPackagingConfigId'] as int] = companion;
      }

      // 批量插入
      try {
        if (comboGroupMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.productComboGrouping, comboGroupMap.values.toList());
        }
        if (comboGroupDetailMap.isNotEmpty) {
          await db.insertOrReplaceBatch(db.productComboGroupingDetail,
              comboGroupDetailMap.values.toList());
        }

        if (comboPackagingConfigCompanionMap.isNotEmpty) {
          await db.insertOrReplaceBatch(db.productComboPackagingConfig,
              comboPackagingConfigCompanionMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${comboGroupMap.length}  groups, '
            '${comboGroupDetailMap.length} groupsDetail， '
            '${comboPackagingConfigCompanionMap
            .length}  groups packingConfig ');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing addons data: $e\n$st');
    }
  }

}

@riverpod
ProductRepository productRepository(Ref ref) {
  return ProductRepository(ref,stompRequestService: ref.watch(stompRequestServiceProvider));
}
