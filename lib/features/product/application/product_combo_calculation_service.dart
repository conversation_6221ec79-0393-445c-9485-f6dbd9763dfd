import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/product_combo.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_spec_group.dart';
import '../presentation/spec/product_combo_selection_controller.dart';
import 'product_combo_modifier_config_service.dart';
import '../domain/product_combo_modifier_config.dart';

/// 套餐价格计算服务
/// 负责计算套餐的总价格，包括基础价格、选项差价和Modifier规格价格
class ProductComboCalculationService {
  
  /// 计算套餐总价格
  /// 
  /// @param combo 套餐数据模型
  /// @param selectionState 套餐选择状态
  /// @param modifierConfigs Modifier配置状态映射
  /// @return 返回总价格
  double calculateTotalPrice({
    required ProductCombo combo,
    required ProductComboSelectionState selectionState,
    required Map<String, ModifierConfigData> modifierConfigs,
  }) {
    double totalPrice = 0.0;
    
    // 遍历套餐中的所有商品
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = selectionState.quantities[choiceKey] ?? 0;
        
        if (quantity > 0) {
          // 1. 计算商品基础价格
          double itemBasePrice = _getItemBasePrice(choice);
          
          // 2. 计算商品规格价格（从FormBuilder或默认规格）
          double itemSpecPrice = _getItemSpecPrice(choice, selectionState, choiceKey);
          
          // 3. 计算商品选项差价
          double itemOptionPrice = choice.additionalPrice;
          
          // 4. 计算单个商品的总价格
          double itemTotalPrice = (itemBasePrice + itemSpecPrice + itemOptionPrice) * quantity;
          totalPrice += itemTotalPrice;
          
          //print('🔧 商品价格计算 - ${choice.product.commodityName}:');
          //print('  基础价格: \$${itemBasePrice.toStringAsFixed(2)}');
          //print('  规格价格: \$${itemSpecPrice.toStringAsFixed(2)}');
          //print('  选项差价: \$${itemOptionPrice.toStringAsFixed(2)}');
          //print('  数量: $quantity');
          //print('  小计: \$${itemTotalPrice.toStringAsFixed(2)}');
        }
      }
    }
    
    //print('🔧 套餐总价格: \$${totalPrice.toStringAsFixed(2)}');
    return totalPrice;
  }

   /// 获取商品基础价格
  double _getItemBasePrice(ProductComboChoice choice) {
    if (choice.product.specType == 1 && choice.specGroups.isNotEmpty) {
      final firstGroup = choice.specGroups.first;
      if (firstGroup.items.isNotEmpty) {
        return firstGroup.items.first.additionalPrice;
      }
    }
    return 0.0;
  }

/// 获取商品规格价格（优先使用用户选择，否则使用默认）
  double _getItemSpecPrice(ProductComboChoice choice, ProductComboSelectionState selectionState, String choiceKey) {
    // 1. 优先从FormBuilder获取用户选择的规格价格
    final formBuilderData = selectionState.formBuilderValues[choiceKey];
    if (formBuilderData != null && formBuilderData.isNotEmpty) {
      final additionalPrice = formBuilderData['additionalPrice'] as double? ?? 0.0;
      return additionalPrice;
    }
    
    // 2. 如果没有用户选择，使用默认规格价格
    return _calculateDefaultSpecPrice(choice);
  }

  /// 计算默认选中的规格价格
  double _calculateDefaultSpecPrice(ProductComboChoice choice) {
    double defaultSpecPrice = 0.0;
    
    for (final specGroup in choice.specGroups) {
      for (final item in specGroup.items) {
        if (item.isSelected == true) {
          defaultSpecPrice += item.additionalPrice;
          //print('🔧 默认规格 - ${item.name}: +\$${item.additionalPrice.toStringAsFixed(2)}');
        }
      }
    }
    
    return defaultSpecPrice;
  }
  

  
  /// 计算套餐基础价格
  double _calculateBasePrice(ProductCombo combo, ProductComboSelectionState selectionState) {
    double totalBasePrice = 0.0;
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = selectionState.quantities[choiceKey] ?? 0;
        
        if (quantity > 0) {
          // 1. 计算商品基础价格（从specGroups获取）
          if (choice.product.specType == 1 && choice.specGroups.isNotEmpty) {
            final firstGroup = choice.specGroups.first;
            if (firstGroup.items.isNotEmpty) {
              final basePrice = firstGroup.items.first.additionalPrice;
              totalBasePrice += basePrice * quantity;
              //print('🔧 基础价格 - ${choice.product.commodityName}: \$${basePrice.toStringAsFixed(2)} × $quantity');
            }
          }
          
          // 2. 计算当前选中的规格价格（而不是默认规格价格）
          totalBasePrice += _calculateCurrentSpecPrice(choice, selectionState, choiceKey) * quantity;
        }
      }
    }
    
    return totalBasePrice;
  }

  /// 计算当前选中的规格价格（根据用户实际选择）
  double _calculateCurrentSpecPrice(ProductComboChoice choice, ProductComboSelectionState selectionState, String choiceKey) {
    double currentSpecPrice = 0.0;
    
    // 获取该商品的FormBuilder规格选择数据
    final formBuilderData = selectionState.formBuilderValues[choiceKey];
    
    if (formBuilderData == null || formBuilderData.isEmpty) {
      // 如果没有规格选择数据，使用默认规格价格
      return _calculateDefaultSpecPrice(choice);
    }
    
    // 从FormBuilder数据中获取附加价格
    final additionalPrice = formBuilderData['additionalPrice'] as double? ?? 0.0;
    currentSpecPrice += additionalPrice;
    
    if (additionalPrice > 0) {
      //print('🔧 当前规格价格 - ${choice.product.commodityName}: +\$${additionalPrice.toStringAsFixed(2)}');
    }
    
    return currentSpecPrice;
  }

  // /// 计算默认选中的规格价格（统一处理所有规格组类型）
  // double _calculateDefaultSpecPrice(ProductComboChoice choice) {
  //   double defaultSpecPrice = 0.0;
    
  //   // 遍历所有规格组，根据类型分别处理
  //   for (final specGroup in choice.specGroups) {
  //     switch (specGroup.groupType) {
  //       case ProductSpecGroupType.spec:
  //         defaultSpecPrice += _calculateDefaultGroupPrice(specGroup, '规格');
  //         break;
  //       case ProductSpecGroupType.practice:
  //         defaultSpecPrice += _calculateDefaultGroupPrice(specGroup, '做法');
  //         break;
  //       case ProductSpecGroupType.addons:
  //         defaultSpecPrice += _calculateDefaultGroupPrice(specGroup, '加料');
  //         break;
  //     }
  //   }
    
  //   return defaultSpecPrice;
  // }

  /// 计算单个规格组的默认价格
  double _calculateDefaultGroupPrice(ProductSpecGroup specGroup, String groupTypeName) {
    double groupPrice = 0.0;
    
    for (final item in specGroup.items) {
      if (item.isSelected == true) {
        groupPrice += item.additionalPrice;
        //print('🔧 默认$groupTypeName - ${item.name}: +\$${item.additionalPrice.toStringAsFixed(2)}');
      }
    }
    
    return groupPrice;
  }
  
  /// 计算选项价格差异 - 支持组合键
  double _calculateOptionPriceDifference(ProductCombo combo, ProductComboSelectionState selectionState) {
    double totalDifference = 0.0;
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = selectionState.quantities[choiceKey] ?? 0;
        
        if (quantity > 0) {
          // 只计算additionalPrice差价，不包含基础价格
          double itemPrice = choice.additionalPrice;
          totalDifference += itemPrice * quantity;
          
          //print('🔧 选项差价 - ${choice.product.commodityName}: \$${itemPrice.toStringAsFixed(2)} × $quantity = \$${(itemPrice * quantity).toStringAsFixed(2)}');
        }
      }
    }
    
    return totalDifference;
  }
  
  /// 计算Modifier规格价格
  double _calculateModifierPrice(Map<String, ModifierConfigData> modifierConfigs) {
    double totalModifierPrice = 0.0;
    
    for (final entry in modifierConfigs.entries) {
      final modifierId = entry.key;
      final configData = entry.value;

      // 🔧 修复：统一模式和独立模式的价格计算逻辑
      if (modifierId.endsWith('_unified_modifier')) {
        // 统一模式：直接使用formData中的additionalPrice
        final formData = configData.formData;
        final additionalPrice = formData['additionalPrice'] as double? ?? 0.0;
        totalModifierPrice += additionalPrice;
        
        if (additionalPrice > 0) {
          //print('🔧 统一Modifier价格 - $modifierId (${configData.productName}): +\$${additionalPrice.toStringAsFixed(2)}');
        }
      } else if (modifierId.contains('_modifier_')) {
        // 独立模式：只有配置完成的才计算价格
        if (configData.isConfigured) {
          final formData = configData.formData;
          final additionalPrice = formData['additionalPrice'] as double? ?? 0.0;
          totalModifierPrice += additionalPrice;
          
          if (additionalPrice > 0) {
            //print('🔧 独立Modifier价格 - $modifierId (${configData.productName}): +\$${additionalPrice.toStringAsFixed(2)}');
          }
        }
      }
    }
    
    return totalModifierPrice;
  }
  
  /// 获取价格明细
  /// 
  /// 返回详细的价格分解信息，用于UI展示
  Map<String, double> getPriceBreakdown({
    required ProductCombo combo,
    required ProductComboSelectionState selectionState,
    required Map<String, ModifierConfigData> modifierConfigs,
  }) {
    final totalPrice = calculateTotalPrice(
      combo: combo,
      selectionState: selectionState,
      modifierConfigs: modifierConfigs,
    );
    
    return {
      'basePrice': 0.0, // 不再分别计算，统一在总价格中
      'optionPrice': 0.0,
      'modifierPrice': 0.0,
      'totalPrice': totalPrice,
    };
  }

  /// 🔧 新增：验证价格计算的一致性
  bool validatePriceCalculation({
    required ProductCombo combo,
    required ProductComboSelectionState selectionState,
    required Map<String, ModifierConfigData> modifierConfigs,
  }) {
    try {
      final breakdown = getPriceBreakdown(
        combo: combo,
        selectionState: selectionState,
        modifierConfigs: modifierConfigs,
      );
      
      final calculatedTotal = breakdown['basePrice']! + 
                             breakdown['optionPrice']! + 
                             breakdown['modifierPrice']!;
      
      final directTotal = calculateTotalPrice(
        combo: combo,
        selectionState: selectionState,
        modifierConfigs: modifierConfigs,
      );
      
      // 允许小数点精度误差
      final difference = (calculatedTotal - directTotal).abs();
      return difference < 0.01;
    } catch (e) {
      //print('🔧 价格计算验证失败: $e');
      return false;
    }
  }
}

/// 套餐价格计算服务的Provider
final productComboCalculationServiceProvider = Provider<ProductComboCalculationService>((ref) {
  return ProductComboCalculationService();
});

/// 套餐总价格的Provider
/// 
/// 监听套餐选择状态和Modifier配置状态，自动计算总价格
/// comboTotalPriceProvider 执行
final comboTotalPriceProvider = Provider.family<double, ProductCombo>((ref, combo) {
  // 获取选择状态
  final selectionState = ref.watch(productComboSelectionControllerProvider(combo));
  // 获取Modifier配置
  final modifierConfigs = ref.watch(productComboModifierConfigServiceProvider);
  // 获取计算服务
  final calculationService = ref.watch(productComboCalculationServiceProvider);
  // 计算总价格
  return calculationService.calculateTotalPrice(
    combo: combo,
    selectionState: selectionState,
    modifierConfigs: modifierConfigs,
  );
});

/// 套餐价格明细的Provider
final comboPriceBreakdownProvider = Provider.family<Map<String, double>, ProductCombo>((ref, combo) {
  final selectionState = ref.watch(productComboSelectionControllerProvider(combo));
  final modifierConfigs = ref.watch(productComboModifierConfigServiceProvider);
  final calculationService = ref.watch(productComboCalculationServiceProvider);
  
  return calculationService.getPriceBreakdown(
    combo: combo,
    selectionState: selectionState,
    modifierConfigs: modifierConfigs,
  );
});