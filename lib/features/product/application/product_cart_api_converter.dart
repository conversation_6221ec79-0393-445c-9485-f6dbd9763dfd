import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../domain/product_combo.dart';
import '../domain/product_combo_modifier_config.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_spec_group.dart';
import '../domain/product_spec_selection_data.dart';
import '../../cart/domain/cart_add_request.dart';
import '../presentation/spec/product_combo_selection_controller.dart';
import 'product_combo_modifier_config_service.dart';

/// 商品购物车API转换器
/// 将UI层的规格选择状态转换为Cart模块需要的API格式
/// 这个转换器完全在Product模块内部，不影响Cart模块
class ProductCartApiConverter {
  ProductCartApiConverter._();

  static ProductConfig convertSpecSelectionToProductConfig({
  required ProductSpecSelectionData selectionData,
  required List<ProductSpecGroup> specGroups,
  required int mainProductSkuId,
}) {
  //debugPrint('🔧 === ProductCartApiConverter 调试开始 ===');
  //debugPrint('🔧 选中的规格ID: ${selectionData.selectedItemIds}');
  //debugPrint('🔧 可用的规格组: ${specGroups.length}个');
  //debugPrint('🔧 主商品SKU ID: $mainProductSkuId');
  
  // 🔧 新增：打印规格组详细信息
  for (int i = 0; i < specGroups.length; i++) {
    final group = specGroups[i];
    //debugPrint('🔧 规格组[$i]: ${group.name} (ID: ${group.id}, 类型: ${group.groupType})');
    for (int j = 0; j < group.items.length; j++) {
      final item = group.items[j];
      //debugPrint('🔧   项目[$j]: ${item.name} (ID: ${item.id})');
    }
  }
  
  final List<PracticeConfig> practiceConfigList = [];
  final List<AddonsConfig> addonsConfigList = [];
  
  // 遍历所有选中的规格ID
  for (final selectedId in selectionData.selectedItemIds) {
    //debugPrint('🔧 处理选择ID: $selectedId');
    
    // 解析复合键：groupId_itemId_groupIndex_itemIndex
    final parts = selectedId.split('_');
    if (parts.length != 4) {
      //debugPrint('🚨 无效的选择ID格式: $selectedId');
      continue;
    }

    final groupId = int.tryParse(parts[0]);
    final itemId = int.tryParse(parts[1]);
    final groupIndex = int.tryParse(parts[2]);

    if (groupId == null || itemId == null || groupIndex == null) {
      //debugPrint('🚨 无法解析选择ID: $selectedId');
      continue;
    }

    //debugPrint('🔧 解析结果: groupId=$groupId, itemId=$itemId, groupIndex=$groupIndex');

    // 🔧 关键修复：改为通过 groupId 查找，而不是 groupIndex
    ProductSpecGroup? targetGroup;
    SpecItem? targetItem;

    // 🎯 修复：通过 groupId 查找规格组
    try {
      targetGroup = specGroups.firstWhere(
        (group) => group.id == groupId,
      );
      //debugPrint('🔧 通过groupId找到规格组: ${targetGroup.name} (ID: ${targetGroup.id})');
    } catch (e) {
      //debugPrint('🚨 找不到规格组: groupId=$groupId');
      //debugPrint('🚨 可用的规格组ID: ${specGroups.map((g) => '${g.name}(${g.id})').toList()}');
      continue;
    }

    // 在找到的规格组中查找规格项
    try {
      targetItem = targetGroup.items.firstWhere(
        (item) => item.id == itemId,
      );
      //debugPrint('🔧 找到规格项: ${targetItem.name} (ID: ${targetItem.id})');
    } catch (e) {
      //debugPrint('🚨 找不到规格项: itemId=$itemId');
      //debugPrint('🚨 该组可用的规格项: ${targetGroup.items.map((i) => '${i.name}(${i.id})').toList()}');
      continue;
    }

    //debugPrint('🔧 处理规格项: ${targetItem.name}, 规格组类型: ${targetGroup.groupType}, SKU ID: ${targetItem.skuId}');

    // 🎯 根据规格组的groupType字段判断规格类型
    if (targetGroup.groupType == ProductSpecGroupType.practice) {
      // 做法类规格 (Practice)
      practiceConfigList.add(PracticeConfig(
        commodityPracticeGroupId: targetGroup.id,
        practiceItemId: targetItem.id,
      ));
      //debugPrint('🔧 添加做法选项: ${targetItem.name}');
    } else if (targetGroup.groupType == ProductSpecGroupType.addons) {
      // 加料类规格 (Addons)
      final quantity = selectionData.itemQuantities[selectedId] ?? 1;
      addonsConfigList.add(AddonsConfig(
        commodityAddonsGroupId: targetGroup.id,
        addonsCommodityId: targetItem.id,
        unitQuantity: quantity,
        addonsCommoditySkuId: targetItem.skuId,
      ));
      //debugPrint('🔧 添加加料选项: ${targetItem.name}, SKU ID: ${targetItem.skuId}, 数量: $quantity');
    } else {
      //debugPrint('🚨 未处理的规格组类型: ${targetGroup.groupType}');
    }
  }
  
  final config = ProductConfig(
    commoditySkuId: mainProductSkuId,
    practiceConfigList: practiceConfigList.isNotEmpty ? practiceConfigList : [],
    addonsConfigList: addonsConfigList.isNotEmpty ? addonsConfigList : [],
  );
  
  //debugPrint('🔧 转换结果: ${practiceConfigList.length}个做法, ${addonsConfigList.length}个加料');
  //debugPrint('🔧 === ProductCartApiConverter 调试结束 ===');

  return config;
}
  /// 🔧 新增：获取选中规格的总加料价格
  /// 用于价格计算和验证
  static double calculateAddonsPrice({
    required ProductSpecSelectionData selectionData,
    required List<ProductSpecGroup> specGroups,
  }) {
    double totalPrice = 0.0;

    for (final selectedId in selectionData.selectedItemIds) {
      final parts = selectedId.split('_');
      if (parts.length != 4) continue;

      final groupId = int.tryParse(parts[0]);
      final itemId = int.tryParse(parts[1]);
      if (groupId == null || itemId == null) continue;

      try {
        final group = specGroups.firstWhere((g) => g.id == groupId);
        final item = group.items.firstWhere((item) => item.id == itemId);
        
        // 只计算加料类型的价格 (groupType为addons)
        if (group.groupType == ProductSpecGroupType.addons) {
          final quantity = selectionData.itemQuantities[selectedId] ?? 1;
          totalPrice += item.additionalPrice * quantity;
        }
      } catch (e) {
        //debugPrint('🚨 计算加料价格时找不到规格组或规格项: groupId=$groupId, itemId=$itemId');
      }
    }
    
    return totalPrice;
  }

  /// 套餐数据收集器
  /// 从各个Provider收集完整的套餐选择数据
  static ComboSelectionCollectedData collectComboSelectionData({
    required Ref ref,
    required ProductCombo combo,
  }) {
    //debugPrint('🔧 === 套餐数据收集开始 ===');
    
    // 1. 收集套餐基础选择数据
    final comboSelectionState = ref.read(productComboSelectionControllerProvider(combo));
    
    // 2. 收集Modifier配置数据
    final modifierConfigs = ref.read(productComboModifierConfigServiceProvider);
    
    // 3. 构建收集结果
    final collectedData = ComboSelectionCollectedData(
      combo: combo,
      comboSelectionState: comboSelectionState,
      modifierConfigs: modifierConfigs,
    );
    
    //debugPrint('🔧 套餐数据收集完成:');
    //debugPrint('🔧   套餐: ${combo.name}');
    //debugPrint('🔧   分组数量: ${combo.groups.length}');
    //debugPrint('🔧   选择数量: ${comboSelectionState.quantities.length}');
    //debugPrint('🔧   Modifier配置数量: ${modifierConfigs.length}');
    
    return collectedData;
  }

  /// 将套餐选择数据转换为ComboConfig列表
  /// 🎓 教学重点：套餐的规格配置更复杂，需要按分组处理
  static List<ComboConfig> convertComboSelectionToComboConfig({
    required ComboSelectionCollectedData collectedData,
  }) {
    //debugPrint('🔧 === 套餐转换开始 ===');
    
    final List<ComboConfig> comboConfigs = [];
    final combo = collectedData.combo;
    final selectionState = collectedData.comboSelectionState;
    final modifierConfigs = collectedData.modifierConfigs;
    
    // 遍历所有套餐分组
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      //debugPrint('🔧 处理套餐分组: ${group.name} (ID: ${group.id})');
      
      // 遍历分组中的所有选项
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        // 生成组合键
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = selectionState.quantities[choiceKey] ?? 0;
        
        // 只处理被选中的商品
        if (quantity > 0) {
          //debugPrint('🔧   处理选中商品: ${choice.product.commodityName} (数量: $quantity)');
          
          // 为每个数量生成一个ComboConfig
          for (int itemIndex = 0; itemIndex < quantity; itemIndex++) {
            final comboConfig = _createComboConfigForItem(
               group: group,
              choice: choice,
              itemIndex: itemIndex,
              modifierConfigs: modifierConfigs,
              groupIndex: groupIndex,
              choiceIndex: choiceIndex,
            );
            
            if (comboConfig != null) {
              comboConfigs.add(comboConfig);
              //debugPrint('🔧   生成ComboConfig: ${choice.product.commodityName} - 第${itemIndex + 1}个');
            }
          }
        }
      }
    }
    
    //debugPrint('🔧 套餐转换完成，生成${comboConfigs.length}个ComboConfig');
    return comboConfigs;
  }
  
/// 为单个套餐项创建ComboConfig
static ComboConfig? _createComboConfigForItem({
  required ProductComboGroup group,
  required ProductComboChoice choice,
  required int itemIndex,
  required Map<String, ModifierConfigData> modifierConfigs,
  required int groupIndex,
  required int choiceIndex,
}) {
  //debugPrint('🔧 创建ComboConfig: ${choice.product.commodityName} - 第${itemIndex + 1}个');
  
  final commodityId = choice.product.commodityId;
  final baseKey = '${commodityId}_${group.id}_${groupIndex}_${choiceIndex}';
  final modifierId = '${baseKey}_modifier_$itemIndex';
  final unifiedKey = '${baseKey}_unified_modifier';
  
  // 🔧 修复：优先查找具体的modifier配置
  ModifierConfigData? modifierConfig = modifierConfigs[modifierId];
  
  if (modifierConfig != null && modifierConfig.isConfigured) {
    //debugPrint('🔧 找到具体Modifier配置: $modifierId (已配置: ${modifierConfig.isConfigured})');
    return _createComboConfigFromModifier(group, choice, modifierConfig);
  }
  
  // 🔧 修复：如果具体配置不存在或未配置，优先使用unified配置
  final unifiedConfig = modifierConfigs[unifiedKey];
  if (unifiedConfig != null && unifiedConfig.isConfigured) {
    //debugPrint('🔧 使用unified配置: $unifiedKey (已配置: ${unifiedConfig.isConfigured})');
    return _createComboConfigFromModifier(group, choice, unifiedConfig);
  }
  
  // 🔧 最后才使用默认配置
  //debugPrint('🔧 创建默认配置: ${choice.product.commodityName} - 第${itemIndex + 1}个');
  final defaultFormData = _getDefaultFormDataForChoice(choice);
  final defaultConfig = ModifierConfigData(
    modifierId: modifierId,
    modifierChoice: choice,
    formData: defaultFormData,
    isConfigured: defaultFormData['isConfigured'] as bool,
  );
  return _createComboConfigFromModifier(group, choice, defaultConfig);
}


/// 从ModifierConfig创建ComboConfig
static ComboConfig _createComboConfigFromModifier(
  ProductComboGroup group,
  ProductComboChoice choice,
  ModifierConfigData modifierConfig,
) {
  final formData = modifierConfig.formData;
  
  //debugPrint('🔧 === 开始创建ComboConfig ===');
  //debugPrint('🔧 商品: ${choice.product.commodityName}');
  //debugPrint('🔧 FormData内容: $formData');
  //debugPrint('🔧 配置状态: ${modifierConfig.isConfigured}');
  
  // 🔧 修复：当formData为空时，尝试从unified配置获取默认数据
  Map<String, dynamic> effectiveFormData = formData;
  if (formData.isEmpty || !modifierConfig.isConfigured) {
    //debugPrint('🔧 FormData为空或未配置，尝试获取默认配置...');
    effectiveFormData = _getDefaultFormDataForChoice(choice);
    //debugPrint('🔧 使用默认FormData: $effectiveFormData');
  }
  
  // 🔧 修复：正确提取commoditySkuId
  int commoditySkuId = _extractCommoditySkuId(effectiveFormData, choice);
  
  // 🔧 修复：正确提取规格配置
  final practiceConfigList = _extractPracticeConfigFromFormData(effectiveFormData, choice);
  final addonsConfigList = _extractAddonsConfigFromFormData(effectiveFormData, choice);
  final itemRemark = _extractItemRemark(effectiveFormData);
  
  //debugPrint('🔧 提取结果:');
  //debugPrint('🔧   commoditySkuId: $commoditySkuId');
  //debugPrint('🔧   practiceConfigList: ${practiceConfigList.length}个');
  //debugPrint('🔧   addonsConfigList: ${addonsConfigList.length}个');
  //debugPrint('🔧   remark: $itemRemark');
  
  return ComboConfig(
    comboGroupingId: int.parse(group.id),
    commodityId: choice.product.commodityId,
    commoditySkuId: commoditySkuId,
    unitQuantity: 1,
    practiceConfigList: practiceConfigList.isNotEmpty ? practiceConfigList : null,
    addonsConfigList: addonsConfigList.isNotEmpty ? addonsConfigList : null,
    remark: itemRemark.isNotEmpty ? itemRemark : null,
  );
}

/// 🔧 新增：为选择项生成默认FormData
static Map<String, dynamic> _getDefaultFormDataForChoice(ProductComboChoice choice) {
  final Set<String> selectedItemIds = {};
  final Map<String, int> itemQuantities = {};
  double totalAdditionalPrice = 0.0;
  
  // 处理默认选中的规格项
  for (final specGroup in choice.specGroups) {
    for (final item in specGroup.items) {
      if (item.isSelected) {
        final itemKey = '${specGroup.id}_${item.id}_${specGroup.groupType.index}_${specGroup.items.indexOf(item)}';
        selectedItemIds.add(itemKey);
        itemQuantities[itemKey] = 1;
        totalAdditionalPrice += item.additionalPrice;
        
        //debugPrint('🔧 添加默认选中项: ${item.name} (${specGroup.name})');
      }
    }
  }
  
  return {
    'selectedItemIds': selectedItemIds.toList(),
    'itemQuantities': itemQuantities,
    'selectedRemarkTags': <String>[],
    'customRemark': '',
    'additionalPrice': totalAdditionalPrice,
    'isValid': true,
    'isConfigured': selectedItemIds.isNotEmpty,
  };
}


/// 🔧 从FormData中提取commoditySkuId
static int _extractCommoditySkuId(Map<String, dynamic> formData, ProductComboChoice choice) {
  // 1. 优先从formData中的commoditySkuId字段获取
  if (formData.containsKey('commoditySkuId')) {
    final skuId = formData['commoditySkuId'] as int?;
    if (skuId != null && skuId != choice.product.commodityId) {
      //debugPrint('🔧 从formData获取SKU ID: $skuId');
      return skuId;
    }
  }
  
  // 2. 从规格组中查找SKU ID
  for (final specGroup in choice.specGroups) {
    if (specGroup.groupType == ProductSpecGroupType.spec && specGroup.items.isNotEmpty) {
      
      // 🔧 判断商品规格类型
      if (choice.product.specType == 1) {
        // 单规格商品：取第一个
        final firstItem = specGroup.items.first;
        if (firstItem.skuId > 0) {
          //debugPrint('🔧 套餐单规格商品-使用第一个SKU ID: ${firstItem.skuId} (${firstItem.name})');
          return firstItem.skuId;
        }
      } else {
        // 多规格商品：从用户选择中查找，没有选中就返回0
        final selectedItemIds = formData['selectedItemIds'] as List<dynamic>? ?? [];
        
        for (final selectedId in selectedItemIds) {
          final parts = selectedId.toString().split('_');
          if (parts.length >= 4) {
            final groupId = int.tryParse(parts[0]);
            final itemId = int.tryParse(parts[1]);
            
            if (groupId == specGroup.id && itemId != null) {
              final specItem = specGroup.items.firstWhere(
                (item) => item.id == itemId,
                orElse: () => throw StateError('规格项不存在'),
              );
              
              if (specItem.skuId > 0) {
                //debugPrint('🔧 套餐多规格商品-从选中项获取SKU ID: ${specItem.skuId} (${specItem.name})');
                return specItem.skuId;
              }
            }
          }
        }
        
        // 多规格商品没有选中项，返回0
        //debugPrint('🔧 套餐多规格商品-没有选中项，返回0');
        return 0;
      }
    }
  }
  
  // 3. 没有找到规格组，返回0
  //debugPrint('🔧 没有找到规格组，返回0');
  return 0;
}

/// 🔧 从FormData中提取做法配置
static List<PracticeConfig> _extractPracticeConfigFromFormData(
  Map<String, dynamic> formData, 
  ProductComboChoice choice
) {
  final List<PracticeConfig> practiceConfigs = [];
  
  // 从selectedItemIds中提取做法类型的选择
  final selectedItemIds = formData['selectedItemIds'] as List<dynamic>? ?? [];
  
  for (final selectedId in selectedItemIds) {
    final parts = selectedId.toString().split('_');
    if (parts.length >= 4) {
      final groupId = int.tryParse(parts[0]);
      final itemId = int.tryParse(parts[1]);
      
      if (groupId != null && itemId != null) {
        // 查找对应的规格组
        for (final specGroup in choice.specGroups) {
          if (specGroup.id == groupId && specGroup.groupType == ProductSpecGroupType.practice) {
            practiceConfigs.add(PracticeConfig(
              commodityPracticeGroupId: groupId,
              practiceItemId: itemId,
            ));
            //debugPrint('🔧 添加做法配置: groupId=$groupId, itemId=$itemId (${specGroup.name})');
            break;
          }
        }
      }
    }
  }
  
  return practiceConfigs;
}

/// 🔧 从FormData中提取加料配置
static List<AddonsConfig> _extractAddonsConfigFromFormData(
  Map<String, dynamic> formData, 
  ProductComboChoice choice
) {
  final List<AddonsConfig> addonsConfigs = [];
  
  // 从selectedItemIds中提取加料类型的选择
  final selectedItemIds = formData['selectedItemIds'] as List<dynamic>? ?? [];
  final itemQuantities = formData['itemQuantities'] as Map<String, dynamic>? ?? {};
  
  for (final selectedId in selectedItemIds) {
    final parts = selectedId.toString().split('_');
    if (parts.length >= 4) {
      final groupId = int.tryParse(parts[0]);
      final itemId = int.tryParse(parts[1]);
      
      if (groupId != null && itemId != null) {
        // 查找对应的规格组
        for (final specGroup in choice.specGroups) {
          if (specGroup.id == groupId && specGroup.groupType == ProductSpecGroupType.addons) {
            final quantity = itemQuantities[selectedId.toString()] as int? ?? 1;
            
            addonsConfigs.add(AddonsConfig(
              commodityAddonsGroupId: groupId,
              addonsCommodityId: itemId,
              unitQuantity: quantity,
            ));
            //debugPrint('🔧 添加加料配置: groupId=$groupId, itemId=$itemId, quantity=$quantity (${specGroup.name})');
            break;
          }
        }
      }
    }
  }
  
  return addonsConfigs;
}

/// 从formData中提取商品备注
static String _extractItemRemark(Map<String, dynamic> formData) {
  final selectedRemarkTags = formData['selectedRemarkTags'] as List<dynamic>? ?? [];
  final customRemark = formData['customRemark'] as String? ?? '';
  
  final remarkParts = <String>[];
  remarkParts.addAll(selectedRemarkTags.map((e) => e.toString()));
  if (customRemark.isNotEmpty) {
    remarkParts.add(customRemark);
  }
  
  return remarkParts.join(', ');
}


}

/// 套餐选择数据收集结果
class ComboSelectionCollectedData {
  final ProductCombo combo;
  final ProductComboSelectionState comboSelectionState;
  final Map<String, ModifierConfigData> modifierConfigs;
  
  const ComboSelectionCollectedData({
    required this.combo,
    required this.comboSelectionState,
    required this.modifierConfigs,
  });

}