import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/product_combo_modifier_config.dart';
import '../domain/product_combo.dart';
import 'product_combo_initialization_service.dart';

/// 核心改进：
/// 1. 使用新格式键：${commodityId}_${groupId}_${groupIndex}_${choiceIndex}_modifier_${index}
/// 2. 支持套餐级别的状态隔离
/// 3. 避免不同套餐选择之间的状态污染
/// 4. 支持多商品并发配置和状态持久化
class ProductComboModifierConfigService extends StateNotifier<Map<String, ModifierConfigData>> {
  ProductComboModifierConfigService() : super({});

  /// 批量初始化 - 减少重复调用
void batchInitializeModifiers(Map<String, DefaultChoiceInfo> defaultChoices) {
  final Map<String, ModifierConfigData> newState = Map.from(state);
  
  for (final info in defaultChoices.values) {
    if (info.choice.specGroups.isEmpty) continue;
    
    final baseKey = '${info.commodityId}_${info.groupId}_${info.groupIndex}_${info.choiceIndex}';
    final unifiedKey = '${baseKey}_unified_modifier';
    
    if (!newState.containsKey(unifiedKey)) {
      newState[unifiedKey] = ModifierConfigData(
        modifierId: unifiedKey,
        modifierChoice: info.choice,
      );
    }
  }
  
  state = newState;
}

  /// 支持新格式的初始化方法
  /// [groupId] 套餐组ID
  /// [commodityId] 商品ID
  /// [groupIndex] 组索引
  /// [choiceIndex] 选择索引
  /// [modifierChoices] 该商品的Modifier选项列表
  /// [forceReset] 是否强制重置该商品的所有配置
  void initializeModifiersForProduct(
      String groupId,
      int commodityId,
      int groupIndex,
      int choiceIndex,
      List<ProductComboChoice> modifierChoices, 
      {bool forceReset = false}
    ) {
    //debugPrint('🔧 [${commodityId}] 开始初始化商品Modifier配置 (强制重置: $forceReset)');
    //debugPrint('🔧 [${commodityId}] Modifier数量: ${modifierChoices.length}');
    
    final Map<String, ModifierConfigData> newState = Map.from(state);

    // 生成基础键前缀
    final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
    
    // 如果强制重置，只清理该商品的配置
    if (forceReset) {
     final keysToRemove = newState.keys.where(
      (key) => key.startsWith("${baseKey}_")
     ).toList();
     for (final key in keysToRemove) {
      newState.remove(key);
      //debugPrint('🔧 [${commodityId}] 清理配置: $key');
     }
    }

    // 生成统一模式配置键
    final unifiedKey = '${baseKey}_unified_modifier';
    if(!newState.containsKey(unifiedKey) && modifierChoices.isNotEmpty){
      //debugPrint('🔧 [${commodityId}] 创建统一模式配置: $unifiedKey');
      newState[unifiedKey] = ModifierConfigData(
        modifierId: unifiedKey, 
        modifierChoice: modifierChoices.first,
        );
    }

    // 为每个Modifier创建独立配置
    for (int i = 0; i < modifierChoices.length; i++) {
      final modifierKey = '${baseKey}_modifier_$i';

      if(forceReset || !newState.containsKey(modifierKey)){
        //debugPrint('🔧 [${commodityId}] 创建Modifier配置: $modifierKey');
        newState[modifierKey] = ModifierConfigData(
          modifierId: modifierKey, 
          modifierChoice: modifierChoices[i],
          );
      } else {
        //debugPrint('🔧 [${commodityId}] 保留已有配置: $modifierKey (已配置: ${newState[modifierKey]!.isConfigured})');
      }
    }

    // 清理该商品多余的配置
    final obsoleteKeys = newState.keys
    .where((key) => key.startsWith('${baseKey}_modifier_'))
    .where((key) {
      final parts = key.split("_");
      if(parts.length >= 6){
        final index = int.tryParse(parts[5]); // modifier_后的索引
        return index != null && index >= modifierChoices.length;
      }
      return false;
    }).toList();
    
    for (final key in obsoleteKeys) {
      newState.remove(key);
      //debugPrint('🔧 [${commodityId}] 移除多余配置: $key');
    }

    // 延迟更新状态，避免在构建过程中修改
    WidgetsBinding.instance.addPostFrameCallback((_) {
      state = newState;
    });
     //debugPrint('🔧 [${commodityId}] 初始化完成，该商品配置数量: ${_getProductConfigCount(groupId, commodityId, groupIndex, choiceIndex)}');
  }

  /// 获取指定商品的配置数量,统计指定商品的配置项数量
 int _getProductConfigCount(String groupId, int commodityId, int groupIndex, int choiceIndex) {
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  return state.keys.where((key) => key.startsWith('${baseKey}_')).length;
}

bool _validateFormDataFormat(Map<String, dynamic> formData) {
  // 检查必需字段
  final requiredFields = ['selectedItemIds', 'itemQuantities', 'additionalPrice'];
  
  for (final field in requiredFields) {
    if (!formData.containsKey(field)) {
     //debugPrint('🚨 表单数据缺少必需字段: $field');
      return false;
    }
  }
  
  // 检查字段类型
  if (formData['selectedItemIds'] is! List) {
   //debugPrint('🚨 selectedItemIds 字段类型错误，期待 List');
    return false;
  }
  
  if (formData['itemQuantities'] is! Map) {
   //debugPrint('🚨 itemQuantities 字段类型错误，期待 Map');
    return false;
  }
  
  if (formData['additionalPrice'] is! num) {
   //debugPrint('🚨 additionalPrice 字段类型错误，期待 num');
    return false;
  }
  
  return true;
}

/// 更新商品的Modifier配置时添加格式验证
void updateModifierConfigForProduct(
  String groupId,
  int commodityId, 
  int groupIndex,
  int choiceIndex,
  String modifierId,
  Map<String, dynamic> formData, {
  bool? isConfigured,  // == 修改：从 bool 改为 bool? ==
}) {

  if (!_validateFormDataFormat(formData)) {
   //debugPrint('🚨 表单数据格式验证失败，跳过更新: $modifierId');
    return;
  }

  // 生成全局唯一的配置键
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final globalModifierId = '${baseKey}_$modifierId';
  
  //debugPrint('🔧 [${commodityId}] 更新配置: $modifierId -> $globalModifierId');
  //debugPrint('🔧 [${commodityId}] 表单数据字段: ${formData.keys.toList()}');
  
  if (state.containsKey(globalModifierId)) {
    final updatedConfig = state[globalModifierId]!.copyWith(
      formData: formData,
      isConfigured: isConfigured ?? _isFormDataComplete(formData),  // == 修改：处理空值 ==
    );
    
    state = {
      ...state,
      globalModifierId: updatedConfig,
    };
    
    //debugPrint('🔧 [${commodityId}] 配置更新完成: ${updatedConfig.isConfigured ? "已配置" : "未配置"}');
    
  } else {
    //debugPrint('🔧 [${commodityId}] ❌ 错误：配置不存在 $globalModifierId');
    //debugPrint('🔧 [${commodityId}] 💡 解决方案：请先调用 initializeModifiersForProduct 初始化该商品的配置');
  }
}

/// 🔧 新增：安全的配置更新方法，确保配置存在
void safeUpdateModifierConfigForProduct(
  String groupId,
  int commodityId,
  int groupIndex,
  int choiceIndex,
  String localModifierId,
  Map<String, dynamic> formData,
  {bool? isConfigured,
  ProductComboChoice? fallbackModifierChoice}
) {
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final globalModifierId = '${baseKey}_$localModifierId';
  
  // 检查配置是否存在，如果不存在则尝试创建
  if (!state.containsKey(globalModifierId)) {
    //debugPrint('🔧 [${commodityId}] 配置不存在，尝试创建: $localModifierId');
    
    if (fallbackModifierChoice != null) {
      // == 修改开始 == 🔧 修复构造函数参数
      state = {
        ...state,
        globalModifierId: ModifierConfigData(
          modifierId: globalModifierId,  // == 新增：必需参数 ==
          modifierChoice: fallbackModifierChoice,
          formData: {},
          isConfigured: false,
          // == 移除：productName 参数不存在 ==
        ),
      };
      // == 修改结束 ==
      //debugPrint('🔧 [${commodityId}] 使用备用选择创建了配置');
    } else {
      //debugPrint('🔧 [${commodityId}] ❌ 无法创建配置：缺少 modifierChoice 参数');
      return;
    }
  }
  
  // 现在安全地更新配置
  updateModifierConfigForProduct(
    groupId, 
    commodityId, 
    groupIndex, 
    choiceIndex, 
    localModifierId, 
    formData, 
    isConfigured: isConfigured
  );
}

  /// 获取指定商品的所有配置
Map<String, ModifierConfigData> getProductConfigs(String groupId, int commodityId, int groupIndex, int choiceIndex) {
  final productConfigs = <String, ModifierConfigData>{};
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  
  for (final entry in state.entries) {
    if (entry.key.startsWith('${baseKey}_')) {
      // 提取本地配置ID（去除前缀）
      final localKey = entry.key.substring('${baseKey}_'.length);
      productConfigs[localKey] = entry.value;
    }
  }
  
  //debugPrint('🔧 [${commodityId}] 获取商品配置: ${productConfigs.keys.toList()}');
  return productConfigs;
}

  /// 获取指定商品的特定配置
ModifierConfigData? getProductModifierConfig(String groupId, int commodityId, int groupIndex, int choiceIndex, String localModifierId) {
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final globalKey = '${baseKey}_$localModifierId';
  final config = state[globalKey];
  
  if (config != null) {
    //debugPrint('🔧 [${commodityId}] 找到配置: $localModifierId (已配置: ${config.isConfigured})');
  } else {
    //debugPrint('🔧 [${commodityId}] 配置不存在: $localModifierId');
  }
  
  return config;
}

  /// 检查指定商品的所有配置是否完整
bool isProductConfigComplete(String groupId, int commodityId, int groupIndex, int choiceIndex) {
  final productConfigs = getProductConfigs(groupId, commodityId, groupIndex, choiceIndex);
  if (productConfigs.isEmpty) {
    //debugPrint('🔧 [${commodityId}] 配置检查: 无配置数据');
    return false;
  }
  
  final configuredCount = productConfigs.values.where((config) => config.isConfigured).length;
  final totalCount = productConfigs.length;
  final isComplete = configuredCount == totalCount;
  
  //debugPrint('🔧 [${commodityId}] 配置检查: $configuredCount/$totalCount 已配置，完整性: $isComplete');
  return isComplete;
}

  /// 获取指定商品的配置完成度统计
Map<String, int> getProductConfigStats(String groupId, int commodityId, int groupIndex, int choiceIndex) {
  final productConfigs = getProductConfigs(groupId, commodityId, groupIndex, choiceIndex);
  final configuredCount = productConfigs.values.where((config) => config.isConfigured).length;
  
  return {
    'total': productConfigs.length,
    'configured': configuredCount,
    'remaining': productConfigs.length - configuredCount,
  };
}

  /// 重置指定商品的所有配置
void resetProductConfigs(String groupId, int commodityId, int groupIndex, int choiceIndex) {
  final newState = Map<String, ModifierConfigData>.from(state);
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final keysToRemove = newState.keys
      .where((key) => key.startsWith('${baseKey}_'))
      .toList();
  
  for (final key in keysToRemove) {
    newState.remove(key);
  }
  
  state = newState;
  //debugPrint('🔧 [${commodityId}] 已重置所有配置，移除 ${keysToRemove.length} 个配置项');
}

  /// 重置指定商品的特定配置
void resetProductModifierConfig(String groupId, int commodityId, int groupIndex, int choiceIndex, String localModifierId) {
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final globalKey = '${baseKey}_$localModifierId';
  
  if (state.containsKey(globalKey)) {
    state = {
      ...state,
      globalKey: state[globalKey]!.copyWith(
        formData: {},
        isConfigured: false,
      ),
    };
    //debugPrint('🔧 [${commodityId}] 已重置配置: $localModifierId');
  } else {
    //debugPrint('🔧 [${commodityId}] 重置失败，配置不存在: $localModifierId');
  }
}

  /// 获取所有商品的配置概览
  Map<int, Map<String, int>> getAllProductsConfigOverview() {
    final overview = <int, Map<String, int>>{};
    final productIds = <int>{};
    
    // 提取所有商品ID
    for (final key in state.keys) {
      final parts = key.split('_');
      if (parts.length >= 2) {
        final commodityId = int.tryParse(parts[0]);
        if (commodityId != null) {
          productIds.add(commodityId);
        }
      }
    }
    
    // 为每个商品生成统计
    for (final commodityId in productIds) {
      // 注意：这里需要传入具体的参数，但由于是概览方法，暂时保留原逻辑
      // 实际使用时需要根据具体需求调整
      final stats = <String, int>{
        'total': 0,
        'configured': 0,
        'remaining': 0,
      };
      
      // 统计该商品的所有配置
      final productKeys = state.keys.where((key) => key.startsWith('${commodityId}_')).toList();
      stats['total'] = productKeys.length;
      stats['configured'] = productKeys.where((key) => state[key]?.isConfigured == true).length;
      stats['remaining'] = stats['total']! - stats['configured']!;
      
      overview[commodityId] = stats;
    }
    
    //debugPrint('🔧 全局配置概览: ${overview.length} 个商品，总配置项: ${state.length}');
    return overview;
  }

  /// 清理所有配置（谨慎使用）
  void clearAllConfigs() {
    final previousCount = state.length;
    state = {};
    //debugPrint('🔧 已清理所有配置，移除 $previousCount 个配置项');
  }

  /// 检查表单数据是否完整
  bool _isFormDataComplete(Map<String, dynamic> formData) {
    if (formData.isEmpty) return false;
    
    // 🔧 支持多种数据格式的检查
    final selectedItemIds = formData['selectedItemIds'] as List<String>? ?? [];
    final itemQuantities = formData['itemQuantities'] as Map<String, int>? ?? {};
    final selectedRemarkTags = formData['selectedRemarkTags'] as List<String>? ?? [];
    final customRemark = formData['customRemark'] as String? ?? '';
    
    // 检查是否有任何有效的选择
    final hasItemSelections = selectedItemIds.isNotEmpty;
    final hasQuantitySelections = itemQuantities.values.any((qty) => qty > 0);
    final hasRemarkSelections = selectedRemarkTags.isNotEmpty || customRemark.trim().isNotEmpty;
    
    return hasItemSelections || hasQuantitySelections || hasRemarkSelections;
  }
}

/// 套餐Modifier配置服务的Provider
final productComboModifierConfigServiceProvider = StateNotifierProvider<ProductComboModifierConfigService, Map<String, ModifierConfigData>>((ref) {
  return ProductComboModifierConfigService();
});