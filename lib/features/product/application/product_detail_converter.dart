// lib/features/product/application/product_detail_converter.dart

import 'package:kpos/features/product/domain/product_detail.dart' as detail;
import '../domain/product_spec_group.dart';

// 商品详情数据转换工具类
class ProductDetailConverter {
  // 私有构造函数,防止实例化
  ProductDetailConverter._();

  /// 将API返回的商品详情数据转换为UI所需的规格组数据
  static List<ProductSpecGroup> convertToSpecGroups(detail.ProductDetail productDetail) {
    final List<ProductSpecGroup> result = [];

    // 🆕 1. 转换规格组数据 (specGroups)
    // 注意：单规格商品(specType=1)也需要转换，用于提取价格信息
    if (productDetail.specGroups.isNotEmpty) {
     //debugPrint('🔧 转换规格组，specType=${productDetail.specType}');
      for (final group in productDetail.specGroups) {
        final items = <SpecItem>[];
        
        // 转换规格项，做好空值判断
        if (group.items.isNotEmpty) {
          for (final item in group.items) {
            items.add(SpecItem(
              id: item.id,
              name: item.name,
              isAdditional: item.price > 0, 
              additionalPrice: item.price, 
              isSelected: item.defaultEnable == 1, // defaultEnable = 1表示默认选中
              isMeasured: group.allowDuplicate == 1, //是否步进器类型
              skuId: item.skuId, // 🔧 新增：传递SKU ID
            ));
          }
        }

        result.add(ProductSpecGroup(
          id: group.id,
          name: group.name,
          isRequired: true, // 规格分组,写死是必选,因为后台没有返回最大最小值
          isMultiSelect: false, // 写死单选
          upperLimit: 1,
          lowerLimit: 1,
          items: items,
          groupType: ProductSpecGroupType.spec, // 🎯 设置为规格类型
        ));
      }
    }

    // 2. 转换做法组 (practiceGroups) - 不受specType影响
    if (productDetail.practiceGroups.isNotEmpty) {
      for (final group in productDetail.practiceGroups) {
        final items = <SpecItem>[];
        
        // 转换做法项
        if (group.items.isNotEmpty) {
          for (final item in group.items) {
            items.add(SpecItem(
              id: item.id,
              name: item.name,
              isAdditional: item.price > 0, 
              additionalPrice: item.price, 
              isSelected: item.defaultEnable == 1, // defaultEnable = 1表示默认选中
              isMeasured: false, //是否步进器类型
              skuId: item.skuId, // 🔧 新增：传递SKU ID（做法组通常skuId为0）
            ));
          }
        }

        result.add(ProductSpecGroup(
          id: group.id,
          name: group.name,
          isRequired: group.minSelection > 0, // 最小选择数 > 0表示必选
          isMultiSelect: group.allowDuplicate == 1, // 多选
          upperLimit: group.maxSelection,
          lowerLimit: group.minSelection,
          items: items,
          groupType: ProductSpecGroupType.practice, // 🎯 设置为做法类型
        ));
      }
    }

    // 3. 转换加料组 (addonsGroups) - 不受specType影响
    if (productDetail.addonsGroups.isNotEmpty) {
      for (final group in productDetail.addonsGroups) {
        final items = <SpecItem>[];
        
        // 转换加料项 - 🔧 加料组的skuId很重要，用于商品关联
        if (group.items.isNotEmpty) {
          for (final item in group.items) {
            items.add(SpecItem(
              id: item.id,
              name: item.name,
              isAdditional: item.price > 0, 
              additionalPrice: item.price, 
              isSelected: item.defaultEnable == 1, // defaultEnable = 1表示默认选中
              isMeasured: group.allowDuplicate == 1, //是否步进器类型
              skuId: item.skuId, // 🔧 关键：加料的SKU ID，用于后续下单
            ));
          }
        }

        result.add(ProductSpecGroup(
          id: group.id,
          name: group.name,
          isRequired: group.minSelection > 0, // 最小选择数 > 0表示必选
          isMultiSelect: group.allowDuplicate == 1, // 多选
          upperLimit: group.maxSelection,
          lowerLimit: group.minSelection,
          items: items,
          groupType: ProductSpecGroupType.addons, // 🎯 设置为加料类型
        ));
      }
    }

    return result;
  }

  /// 将商品详情转换为商品基础信息
  static Map<String, dynamic> convertToProductBasicInfo(detail.ProductDetail productDetail) {
    // 🆕 提取单规格商品的基础价格
    double basePrice = 0.0;
    if (productDetail.specType == 1 && productDetail.specGroups.isNotEmpty) {
      // 单规格商品：提取第一个规格项的价格作为基础价格
      final firstGroup = productDetail.specGroups.first;
      if (firstGroup.items.isNotEmpty) {
        basePrice = firstGroup.items.first.price;
       //debugPrint('🔧 单规格商品基础价格: $basePrice');
      }
    }
    
    return {
      'commodityId': productDetail.commodityId,
      'commodityName': productDetail.commodityName,
      'commodityType': productDetail.commodityType,
      'commodityUnit': productDetail.commodityUnit,
      'currencyUnit': productDetail.currencyUnit,
      'priceChangeEnable': productDetail.priceChangeEnable,
      'discountEnable': productDetail.discountEnable,
      'specType': productDetail.specType,
      'basePrice': basePrice, // 🆕 新增：单规格商品的基础价格
      'hasSpecs': productDetail.specGroups.isNotEmpty || 
                 productDetail.practiceGroups.isNotEmpty || 
                 productDetail.addonsGroups.isNotEmpty,
      'reasonOptions': productDetail.reasonOptions
          .map((option) => {
        'id': option.id,
        'name': option.name,
              })
          .toList(),
    };
  }

  /// 获取商品规格摘要（用于UI显示）
  static String getSpecsSummary(detail.ProductDetail productDetail) {
    int totalGroups = productDetail.specGroups.length + 
                     productDetail.practiceGroups.length + 
                     productDetail.addonsGroups.length;
    
    if (totalGroups == 0) {
      return '无规格选项';
    }
    
    return '$totalGroups个规格组';
  }

  /// 🔧 新增：调试方法 - 输出解析后的数据结构
  static void debugPrintConvertedData(detail.ProductDetail productDetail) {
   //debugPrint('🔧 === 商品详情转换调试 ===');
   //debugPrint('🔧 商品ID: ${productDetail.commodityId}');
   //debugPrint('🔧 商品名称: ${productDetail.commodityName}');
    
    // 调试规格组
    if (productDetail.specGroups.isNotEmpty) {
     //debugPrint('🔧 规格组 (${productDetail.specGroups.length}个):');
      for (final group in productDetail.specGroups) {
       //debugPrint('🔧   - ${group.name} (ID: ${group.id})');
        for (final item in group.items) {
         //debugPrint('🔧     * ${item.name} (ID: ${item.id}, SKU: ${item.skuId}, 价格: ${item.price})');
        }
      }
    }

    // 调试做法组
    if (productDetail.practiceGroups.isNotEmpty) {
     //debugPrint('🔧 做法组 (${productDetail.practiceGroups.length}个):');
      for (final group in productDetail.practiceGroups) {
       //debugPrint('🔧   - ${group.name} (ID: ${group.id})');
        for (final item in group.items) {
         //debugPrint('🔧     * ${item.name} (ID: ${item.id}, SKU: ${item.skuId}, 价格: ${item.price})');
        }
      }
    }

    // 调试加料组
    if (productDetail.addonsGroups.isNotEmpty) {
     //debugPrint('🔧 加料组 (${productDetail.addonsGroups.length}个):');
      for (final group in productDetail.addonsGroups) {
       //debugPrint('🔧   - ${group.name} (ID: ${group.id})');
        for (final item in group.items) {
         //debugPrint('🔧     * ${item.name} (ID: ${item.id}, SKU: ${item.skuId}, 价格: ${item.price})');
        }
      }
    }

   //debugPrint('🔧 === 转换调试结束 ===');
  }
}