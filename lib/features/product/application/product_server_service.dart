import 'dart:convert';
import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_response.dart';
import 'package:kpos/features/product/data/product_local_repository.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';

import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import '../../../common/services/networking/intranet_service/api_intranet_exception.dart';
import '../../../common/services/networking/intranet_service/api_intranet_localization.dart';

part 'product_server_service.g.dart';

class ProductServerService {
  final Ref _ref;

  ProductServerService({required Ref ref}) : _ref = ref;

  Locale get currentLocale => _ref.read(languageSettingsServiceProvider).currentLocale;

  Future<Response> getProducts(Request request) async {
    try {
      // 1. 解析请求参数
      final payload = await request.readAsString();
      final data = jsonDecode(payload);
      final leafCategoryIdStr = data['categoryId']?.toString();
      final int? leafCategoryId = leafCategoryIdStr != null ? int.tryParse(leafCategoryIdStr) : null;
      // orderId
      final orderIdStr = data['orderId']?.toString();
      final int? orderId =
      orderIdStr != null ? int.tryParse(orderIdStr) : null;
      final keyword = data['keyword']?.toString();
      // 2. 调用本地Repository
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      final productLocal = ProductLocalRepository(_ref.watch(databaseProvider));
      final list = await productLocal.getProducts(leafCategoryId: leafCategoryId,currencyUnit: boundStore.currentUnit,orderId: orderId,keyword: keyword);
      final totalCount = await productLocal.getTotalProductCount(leafCategoryId: leafCategoryId,keyword: keyword);
      // 3. 返回API响应
      return ApiIntranetResponse.success({"totalCount":totalCount,"data":list});
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getProductCategories(Request request) async {
    try {
      final productLocal = ProductLocalRepository(_ref.watch(databaseProvider));
      final list = await productLocal.getProductCategories();
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getProductDetail(Request request) async {
    try {
      final payload = await request.readAsString();
      final data = jsonDecode(payload);
      final productIdStr = data['id']?.toString();
      
      if (productIdStr == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      
      final productId = int.parse(productIdStr);
      final productLocal = ProductLocalRepository(_ref.watch(databaseProvider));
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      final result = await productLocal.getProductDetail(productId,boundStore.currentUnit);
      
      return ApiIntranetResponse.success(result);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }
  
  Future<Response> getProductComboDetail(Request request) async {
    try {
      final payload = await request.readAsString();
      final data = jsonDecode(payload);
      final productIdStr = data['id']?.toString();
      final int? productId = productIdStr != null ? int.tryParse(productIdStr) : null;
      if (productId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final productLocal = ProductLocalRepository(_ref.watch(databaseProvider));
      final result = await productLocal.getProductComboDetail(productId);
      return ApiIntranetResponse.success(result);
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getSoldOutProducts(Request request) async {
    try {
      final payload = await request.readAsString();
      final data = jsonDecode(payload);
      final productIdStr = data['id']?.toString();

      if (productIdStr == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }

      final productId = int.parse(productIdStr);
      final productLocal = ProductLocalRepository(_ref.watch(databaseProvider));
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      final result = await productLocal.getSoldOutProducts(productId,boundStore.currentUnit);
      return ApiIntranetResponse.success(result);
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setProductSoldOut(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["type"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["id"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final local = ProductLocalRepository(_ref.watch(databaseProvider));
      await local.setProductSoldOut(data["type"],data["id"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }


}

@riverpod
ProductServerService productServerService(Ref ref) {
  return ProductServerService(ref: ref);
}