import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../domain/product_combo.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_combo_validator.dart';

part 'product_combo_service.g.dart';

/// 套餐业务服务
class ProductComboService {
  final Ref _ref;
  
  ProductComboService(this._ref);
  
  /// 计算套餐附加价格
  double calculateAdditionalPrice(
    ProductCombo combo,
    ProductComboSelectionState state,
  ) {
    double totalPrice = 0.0;
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        // 使用新的组合键格式
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = state.quantities[choiceKey] ?? 0;
        
        if (quantity > 0) {
          totalPrice += choice.priceDifference * quantity;
        }
      }
    }
    
    return totalPrice;
  }
  
  /// 计算分组总数
  Map<String, int> calculateGroupTotals(
  ProductCombo combo,
  ProductComboSelectionState state,
) {
    final totals = <String, int>{};
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      int groupTotal = 0;
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        // 使用新的组合键格式
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final quantity = state.quantities[choiceKey] ?? 0;
        groupTotal += quantity;
      }
      
      totals[group.id] = groupTotal;
     //debugPrint('🔧 分组总数计算 - ${group.name}: $groupTotal (最大: ${group.maxSelections})');
    }
    
    return totals;
  }
  
  /// 计算动态最大值
  Map<String, int> calculateDynamicMaxValues(
  ProductCombo combo,
  ProductComboSelectionState state,
) {
    final maxValues = <String, int>{};
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      
      // 实时计算分组总数
      int currentGroupTotal = 0;
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        currentGroupTotal += state.quantities[choiceKey] ?? 0;
      }
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
          groupId: group.id,
          commodityId: choice.product.commodityId,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
        );
        
        final currentQuantity = state.quantities[choiceKey] ?? 0;
        
        if (group.maxSelections <= 0) {
          // 无限制
          maxValues[choiceKey] = 99;
        } else {
          // 使用实时计算的分组总数
          final remainingQuota = group.maxSelections - currentGroupTotal + currentQuantity;
          maxValues[choiceKey] = remainingQuota.clamp(0, 99);
        }
      }
    }
    
    return maxValues;
  }
  
  /// 验证套餐选择
  bool validateSelection(
    ProductCombo combo,
    ProductComboSelectionState state,
  ) {
    return ProductComboValidator.validateComboSelection(combo, state);
  }
}

/// 套餐服务提供者
@riverpod
ProductComboService productComboService(Ref ref) {
  return ProductComboService(ref);
}