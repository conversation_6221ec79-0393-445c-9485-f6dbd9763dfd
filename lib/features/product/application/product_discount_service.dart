import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../store/application/store_prepared_reason_service.dart';
import '../../store/application/store_prepared_reason_cache.dart';
import '../../store/domain/store_prepared_reason_item.dart';
import '../domain/product_discount_option.dart'; // 🔄 更新导入

part 'product_discount_service.g.dart';

/// 产品折扣数据服务
class ProductDiscountService {
  final Ref _ref;

  ProductDiscountService({required Ref ref}) : _ref = ref;

  /// 获取折扣选项列表（配置化数据）
  List<ProductDiscountOption> getDiscountOptions() {
    return ProductDiscountOptionUtils.getPresetDiscountOptions(); // 🔄 使用新的工具类
  }

  /// 获取折扣原因列表（从缓存或数据库）
  List<StorePreparedReasonItem> getDiscountReasons() {
    final cache = _ref.read(storePreparedReasonCacheProvider);
    final discountReasons = cache.getDiscountReasons();
    return discountReasons ?? [];
  }

  /// 检查折扣原因是否已加载
  bool isDiscountReasonsLoaded() {
    final cache = _ref.read(storePreparedReasonCacheProvider);
    return cache.isDataLoaded(PreparedReasonType.discount);
  }

  /// 根据折扣选项计算实际折扣金额
  double calculateDiscountAmount(ProductDiscountOption option, double originalPrice) {
    return ProductDiscountOptionUtils.calculateDiscountAmount(option, originalPrice); // 🔄 使用工具类方法
  }

  /// 根据折扣选项和原价计算最终价格
  double calculateFinalPrice(ProductDiscountOption option, double originalPrice) {
    return ProductDiscountOptionUtils.calculateFinalPrice(option, originalPrice); // 🔄 使用工具类方法
  }
}

/// Riverpod Provider
@riverpod
ProductDiscountService productDiscountService(Ref ref) {
  return ProductDiscountService(ref: ref);
}

/// 折扣选项列表 Provider
@riverpod
List<ProductDiscountOption> discountOptions(Ref ref) { // 🔄 更新返回类型
  final service = ref.watch(productDiscountServiceProvider);
  return service.getDiscountOptions();
}

/// 折扣原因列表 Provider（异步）
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> productDiscountReasons(Ref ref) {
  // 复用Store模块的折扣原因Provider
  return ref.watch(discountReasonsProvider.future);
}