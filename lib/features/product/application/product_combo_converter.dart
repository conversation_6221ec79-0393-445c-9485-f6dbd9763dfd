// == 修改开始 == 修正字段名和转换逻辑
import 'package:kpos/features/product/domain/product_combo.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/domain/product_detail.dart' show ReasonOption;

class ProductComboConverter {
  ProductComboConverter._();

  /// 将接口返回的原始数据转换为ProductCombo模型
  static ProductCombo fromApiResponse(Map<String, dynamic> apiData) {
    final groups = <ProductComboGroup>[];
    final groupList = apiData['group'] as List<dynamic>? ?? [];

    for (final groupData in groupList) {
      final items = <ProductComboChoice>[];
      final itemList = groupData['items'] as List<dynamic>? ?? [];

      for (final itemData in itemList) {
        final specType = _parseToInt(itemData['specType']) ?? 0;

        final product = ProductItem(
          commodityId: (itemData['commodityId'] as num).toInt(),
          commodityName: itemData['commodityName'] as String? ?? '',
          commodityType: (itemData['commodityType'] as num?)?.toInt() ?? 0,
          commodityImagePath: itemData['imageUrl'] as String? ?? '',
          priceChangeEnable: 0,
          standaloneSaleEnable: 1,
          specType: specType,
          price: (itemData['additionalPrice'] as num?)?.toDouble() ?? 0.0,
          categoryId: 0,
          discountEnable: 1,
        );

        // 🆕 获取套餐项的specType字段
        // final specType = _parseToInt(itemData['specType']) ?? 1;
        //print('🔧 套餐项 ${product.commodityName} 的 specType: $specType');

        // 解析nested spec groups - 🆕 传递specType参数
        final specGroups = _parseSpecGroups(
          itemData['specGroups'] as List<dynamic>? ?? [], 
          specType, 
          ProductSpecGroupType.spec
        );
        final practiceGroups = _parseSpecGroups(
          itemData['practiceGroups'] as List<dynamic>? ?? [], 
          specType, 
          ProductSpecGroupType.practice
        );
        final addonsGroups = _parseSpecGroups(
          itemData['addonsGroups'] as List<dynamic>? ?? [], 
          specType, 
          ProductSpecGroupType.addons
        );
        
        // 合并所有规格组
        final allSpecGroups = [...specGroups, ...practiceGroups, ...addonsGroups];

        items.add(ProductComboChoice(
          product: product,
          additionalPrice: (itemData['additionalPrice'] as num?)?.toDouble() ?? 0.0,
          defaultEnable: (itemData['defaultEnable'] as num?)?.toDouble() ?? 0.0,
          specGroups: allSpecGroups,
        ));
      }

      groups.add(ProductComboGroup(
        comboId: (groupData['comboId'] as num).toString(),
        comboName: groupData['comboName'] as String? ?? '',
        minSelection: (groupData['minSelection'] as num?)?.toInt() ?? 0,
        maxSelection: (groupData['maxSelection'] as num?)?.toInt() ?? 0,
        type: (groupData['type'] as num?)?.toInt() ?? 1,
        items: items,
      ));
    }

    // 解析reasonOptions
    final reasonOptions = _parseReasonOptions(apiData['reasonOptions'] as List<dynamic>? ?? []);

    return ProductCombo(
      commodityId: (apiData['commodityId'] as num).toString(),
      commodityName: apiData['commodityName'] as String? ?? '',
      group: groups,
      reasonOptions: reasonOptions,
    );
  }

  /// 🆕 解析规格组数据 - 支持specType控制
  static List<ProductSpecGroup> _parseSpecGroups(
    List<dynamic> specGroupsData, 
    int specType, 
    ProductSpecGroupType groupType
  ) {
    final specGroups = <ProductSpecGroup>[];
    
    //print('🔧 === 解析规格组开始 ===');
    //print('🔧 规格组类型: $groupType');
    //print('🔧 specType: $specType');
    //print('🔧 规格组数据数量: ${specGroupsData.length}');
    
    for (final specGroupData in specGroupsData) {
      final items = <SpecItem>[];
      final itemList = specGroupData['items'] as List<dynamic>? ?? [];

      final allowDuplicate = (specGroupData['allowDuplicate'] as num?)?.toInt() ?? 0;

      //print('🔧 处理规格组: ${specGroupData['name']}');
      
      for (final itemData in itemList) {
        items.add(SpecItem(
          id: (itemData['id'] as num).toInt(),
          name: itemData['name'] as String? ?? '',
          isAdditional: (itemData['price'] as num?)?.toDouble() != null && 
                        (itemData['price'] as num).toDouble() > 0,
          additionalPrice: (itemData['price'] as num?)?.toDouble() ?? 0.0,
          isSelected: (itemData['defaultEnable'] as num?)?.toInt() == 1,
          isMeasured: allowDuplicate == 1,
          skuId: (itemData['skuId'] as num?)?.toInt() ?? 0, // 添加skuId支持
        ));
      }
      
      final minSelection = (specGroupData['minSelection'] as num?)?.toInt() ?? 0;
      final rawMaxSelection = (specGroupData['maxSelection'] as num?)?.toInt() ?? 1;

      // 🔧 关键修复：处理 maxSelection 为 0 的情况
      int upperLimit;
      if (rawMaxSelection == 0) {
        // maxSelection 为 0 时，根据业务逻辑处理
        if (minSelection > 0) {
          // 如果有最小选择要求，maxSelection 为 0 通常表示"至少选择minSelection个"
          upperLimit = minSelection;
        } else {
          // 如果没有最小选择要求，maxSelection 为 0 表示无限制
          upperLimit = 99; // 设置一个合理的上限
        }
      } else {
        upperLimit = rawMaxSelection;
      }

      final specGroup = ProductSpecGroup(
        id: (specGroupData['id'] as num).toInt(),
        name: specGroupData['name'] as String? ?? '',
        isRequired: minSelection > 0, // minSelection > 0 表示必选
        isMultiSelect: upperLimit > 1 || allowDuplicate == 1,
        upperLimit: upperLimit,
        lowerLimit: minSelection,
        items: items,
        groupType: groupType, // 🎯 使用传递的类型
      );
      
       specGroups.add(specGroup);
      
      // 🔧 详细调试信息
      //print('🔧 规格组详情:');
      //print('🔧   名称: ${specGroup.name}');
      //print('🔧   ID: ${specGroup.id}');
      //print('🔧   必选: ${specGroup.isRequired}');
      //print('🔧   下限: ${specGroup.lowerLimit}');
      //print('🔧   上限: ${specGroup.upperLimit}');
      //print('🔧   原始maxSelection: $rawMaxSelection');
      //print('🔧   项目数: ${specGroup.items.length}');
      
      // 打印默认选中的项目
      final defaultItems = specGroup.items.where((item) => item.isSelected).toList();
      if (defaultItems.isNotEmpty) {
        //print('🔧   默认选中项: ${defaultItems.map((item) => item.name).join(', ')}');
      }
      
      if (specGroup.isRequired) {
        //print('🔧   ⚠️ 这是必选项！');
      }
    }
    
    //print('🔧 === 解析规格组完成，共 ${specGroups.length} 个 ===');
    
    return specGroups;
  }

  /// 🆕 辅助方法：安全转换为int
  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }

  /// 解析ReasonOptions
  static List<ReasonOption> _parseReasonOptions(List<dynamic> reasonOptionsData) {
    final reasonOptions = <ReasonOption>[];
    
    for (final optionData in reasonOptionsData) {
      reasonOptions.add(ReasonOption(
        id: (optionData['id'] as num).toInt(),
        name: optionData['name'] as String? ?? '',
      ));
    }
    
    return reasonOptions;
  }

  /// 将ProductCombo转换为规格组数据（用于UI复用）
static List<ProductSpecGroup> convertToSpecGroups(ProductCombo combo) {
    final List<ProductSpecGroup> specGroups = [];
    
    // 处理套餐中的规格组,保持原有的选中状态
    for (final group in combo.groups) {
      for (final choice in group.items) {
        // 处理每个选项的规格组
        for (final specGroup in choice.specGroups) {
          // 转换规格项
          final List<SpecItem> items = [];
          for (final item in specGroup.items) {
            items.add(SpecItem(
              id: item.id,
              name: item.name,
              isAdditional: item.additionalPrice > 0,
              additionalPrice: item.additionalPrice,
              isSelected: item.isSelected, // 保留原始数据中的isSelected标志
              isMeasured: item.isMeasured,
              skuId: item.skuId, 
            ));
          }
          
          // 添加转换后的规格组
          specGroups.add(ProductSpecGroup(
            id: specGroup.id,
            name: specGroup.name,
            isRequired: specGroup.isRequired,
            isMultiSelect: specGroup.isMultiSelect,
            upperLimit: specGroup.upperLimit,
            lowerLimit: specGroup.lowerLimit,
            items: items,
            groupType: specGroup.groupType, // 🔧 保留原始的groupType
          ));
        }
      }
    }
    
    return specGroups;
  }


}
// == 修改结束 ==