
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../common/services/database/tables/product_spec_group.dart' as db;
import '../domain/product_combo.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_spec_group.dart';
import '../presentation/spec/product_combo_selection_controller.dart';
import 'product_combo_modifier_config_service.dart';

part 'product_combo_initialization_service.g.dart';

/// 套餐初始化统一服务 - 消除重复逻辑
class ProductComboInitializationService {
  final Ref _ref;
  
  ProductComboInitializationService(this._ref);

  /// 统一的套餐初始化入口
  Future<ComboInitializationResult> initializeCombo(ProductCombo combo) async {
    //print('🔧 开始初始化套餐为默认状态: ${combo.name}');
  
  // 1. 收集所有默认选中信息
  final defaultChoices = _collectDefaultChoices(combo);
  
  // 2. 构建初始选择状态（清除之前的FormBuilder数据）
  final selectionState = _buildSelectionState(combo, defaultChoices);
  
  // 3. 清除并重新初始化Modifier配置
  await _clearAndInitializeModifierConfigs(defaultChoices);
  
  // 4. 生成默认表单数据
  final formData = _generateDefaultFormData(defaultChoices);
  
  //print('🔧 套餐初始化完成，使用默认选项');
  
  return ComboInitializationResult(
    selectionState: selectionState,
    defaultChoices: defaultChoices,
    formData: formData,
  );
  }

  /// 统一收集默认选中商品信息
  Map<String, DefaultChoiceInfo> _collectDefaultChoices(ProductCombo combo) {
    final Map<String, DefaultChoiceInfo> defaultChoices = {};
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        
        if (choice.defaultEnable > 0) {
          final choiceKey = _generateChoiceKey(
            group.id, choice.product.commodityId, groupIndex, choiceIndex
          );
          
          defaultChoices[choiceKey] = DefaultChoiceInfo(
            choice: choice,
            quantity: choice.defaultEnable.toInt(),
            groupId: group.id,
            groupIndex: groupIndex,
            choiceIndex: choiceIndex,
            choiceKey: choiceKey,
            commodityId: choice.product.commodityId,
          );
        }
      }
    }
    
    return defaultChoices;
  }

  /// 统一的组合键生成
  String _generateChoiceKey(String groupId, int commodityId, int groupIndex, int choiceIndex) {
    return ProductComboSelectionStateExtension.generateComboChoiceKey(
      groupId: groupId,
      commodityId: commodityId,
      groupIndex: groupIndex,
      choiceIndex: choiceIndex,
    );
  }

  /// 构建选择状态
  ProductComboSelectionState _buildSelectionState(
    ProductCombo combo, 
    Map<String, DefaultChoiceInfo> defaultChoices
  ) {
    final initialQuantities = <String, int>{};
    final initialGroupSelections = <String, Set<String>>{};
    final initialGroupTotals = <String, int>{};
    final initialDynamicMaxValues = <String, int>{};

    // 基于默认选择构建状态
    for (final info in defaultChoices.values) {
      initialQuantities[info.choiceKey] = info.quantity;
      
      initialGroupSelections
        .putIfAbsent(info.groupId, () => <String>{})
        .add(info.choiceKey);
      
      initialGroupTotals[info.groupId] = 
        (initialGroupTotals[info.groupId] ?? 0) + info.quantity;
    }

    // 计算动态最大值
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      final groupTotal = initialGroupTotals[group.id] ?? 0;
      
      for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
        final choice = group.items[choiceIndex];
        final choiceKey = _generateChoiceKey(
          group.id, choice.product.commodityId, groupIndex, choiceIndex
        );
        
        initialDynamicMaxValues[choiceKey] = _calculateDynamicMax(
          group, choice, groupTotal
        );
      }
    }

    return ProductComboSelectionState(
      quantities: initialQuantities,
      groupSelections: initialGroupSelections,
      groupTotals: initialGroupTotals,
      dynamicMaxValues: initialDynamicMaxValues,
      formBuilderValues: {},
    );
  }

  /// 清除并重新初始化Modifier配置
Future<void> _clearAndInitializeModifierConfigs(Map<String, DefaultChoiceInfo> defaultChoices) async {
  final modifierService = _ref.read(productComboModifierConfigServiceProvider.notifier);
  
  // 🔧 先清除所有现有配置
  modifierService.clearAllConfigs();
  
  // 🔧 重新初始化默认配置
  for (final info in defaultChoices.values) {
    if (info.choice.specGroups.isEmpty) continue;
    
    modifierService.initializeModifiersForProduct(
      info.groupId,
      info.commodityId,
      info.groupIndex,
      info.choiceIndex,
      [info.choice],
      forceReset: true, // 🔧 强制重置
    );
  }
}

  /// 统一生成默认表单数据
  Map<String, Map<String, dynamic>> _generateDefaultFormData(
    Map<String, DefaultChoiceInfo> defaultChoices
  ) {
    final Map<String, Map<String, dynamic>> allFormData = {};
    
    for (final info in defaultChoices.values) {
      if (info.choice.specGroups.isEmpty) continue;
      
      final formData = _generateFormDataForChoice(info.choice);
      if (formData.isNotEmpty) {
        allFormData[info.choiceKey] = formData;
      }
    }
    
    return allFormData;
  }

  /// 统一的表单数据生成逻辑
  Map<String, dynamic> _generateFormDataForChoice(ProductComboChoice choice) {
    final Set<String> selectedItemIds = {};
    final Map<String, int> itemQuantities = {};
    final Map<String, bool> groupValidation = {};
    double totalAdditionalPrice = 0.0;

    //  遍历所有规格组，根据 groupType 分别处理
    for (int groupIndex = 0; groupIndex < choice.specGroups.length; groupIndex++) {
      final specGroup = choice.specGroups[groupIndex];
      
      // 根据规格组类型处理默认选择
      switch (specGroup.groupType) {
        case ProductSpecGroupType.spec:
          final result = _processSpecGroup(specGroup, choice, selectedItemIds, itemQuantities);
          totalAdditionalPrice += result.additionalPrice;
          groupValidation['spec_${specGroup.id}'] = result.hasSelection || !specGroup.isRequired;
          break;
          
        case ProductSpecGroupType.practice:
          final result = _processPracticeGroup(specGroup, choice, selectedItemIds, itemQuantities);
          totalAdditionalPrice += result.additionalPrice;
          groupValidation['practice_${specGroup.id}'] = result.hasSelection || !specGroup.isRequired;
          break;
          
        case ProductSpecGroupType.addons:
          final result = _processAddonsGroup(specGroup, choice, selectedItemIds, itemQuantities);
          totalAdditionalPrice += result.additionalPrice;
          groupValidation['addons_${specGroup.id}'] = result.hasSelection || !specGroup.isRequired;
          break;
      }
    }

    final isValid = groupValidation.values.every((isValid) => isValid == true);

    //print('🔧 商品 ${choice.product.commodityName} 初始化完成，额外价格: \$${totalAdditionalPrice.toStringAsFixed(2)}');

    return {
      'selectedItemIds': selectedItemIds.toList(),
      'itemQuantities': itemQuantities,
      'selectedRemarkTags': <String>[],
      'customRemark': '',
      'additionalPrice': totalAdditionalPrice,
      'groupValidation': groupValidation,
      'isValid': isValid,
    };
  }

  /// 处理规格组的默认选择
  _ProcessResult _processSpecGroup(
    ProductSpecGroup specGroup,
    ProductComboChoice choice,
    Set<String> selectedItemIds,
    Map<String, int> itemQuantities,
  ) {
    bool hasSelection = false;
    double additionalPrice = 0.0;
    final defaultItems = specGroup.items.where((item) => item.isSelected == true).toList();
    
    if (defaultItems.isNotEmpty) {
      if (specGroup.isMultiSelect) {
        // 多选：添加所有默认选中项
        for (final item in defaultItems) {
          final itemKey = '${choice.product.commodityId}_${specGroup.id}_${item.id}';
          selectedItemIds.add(itemKey);
          itemQuantities[itemKey] = 1;
          additionalPrice += item.additionalPrice;
          hasSelection = true;
        }
      } else {
        // 单选：只添加第一个默认选中项
        final defaultItem = defaultItems.first;
        final itemKey = '${choice.product.commodityId}_${specGroup.id}_${defaultItem.id}';
        selectedItemIds.add(itemKey);
        itemQuantities[itemKey] = 1;
        additionalPrice += defaultItem.additionalPrice;
        hasSelection = true;
      }
    }
    
    return _ProcessResult(hasSelection: hasSelection, additionalPrice: additionalPrice);
  }

  /// 处理做法组的默认选择
  _ProcessResult _processPracticeGroup(
    ProductSpecGroup practiceGroup,
    ProductComboChoice choice,
    Set<String> selectedItemIds,
    Map<String, int> itemQuantities,
  ) {
    bool hasSelection = false;
    double additionalPrice = 0.0;
    final defaultItems = practiceGroup.items.where((item) => item.isSelected == true).toList();
    
    if (defaultItems.isNotEmpty) {
      // 做法组通常是单选
      final defaultItem = defaultItems.first;
      final itemKey = '${choice.product.commodityId}_${practiceGroup.id}_${defaultItem.id}';
      selectedItemIds.add(itemKey);
      itemQuantities[itemKey] = 1;
      additionalPrice += defaultItem.additionalPrice;
      hasSelection = true;
      
      //print('🔧 默认做法初始化 - ${defaultItem.name}: +\$${defaultItem.additionalPrice}');
    }
    
    return _ProcessResult(hasSelection: hasSelection, additionalPrice: additionalPrice);
  }

  /// 处理加料组的默认选择
  _ProcessResult _processAddonsGroup(
    ProductSpecGroup addonsGroup,
    ProductComboChoice choice,
    Set<String> selectedItemIds,
    Map<String, int> itemQuantities,
  ) {
    bool hasSelection = false;
    double additionalPrice = 0.0;
    final defaultItems = addonsGroup.items.where((item) => item.isSelected == true).toList();
    
    for (final item in defaultItems) {
      final itemKey = '${choice.product.commodityId}_${addonsGroup.id}_${item.id}';
      selectedItemIds.add(itemKey);
      itemQuantities[itemKey] = 1;
      additionalPrice += item.additionalPrice;
      hasSelection = true;
      
      //print('🔧 默认加料初始化 - ${item.name}: +\$${item.additionalPrice}');
    }
    
    return _ProcessResult(hasSelection: hasSelection, additionalPrice: additionalPrice);
  }

  /// 计算动态最大值
  int _calculateDynamicMax(ProductComboGroup group, ProductComboChoice choice, int currentGroupTotal) {
    return group.maxSelections - currentGroupTotal + (choice.defaultEnable.toInt());
  }
}

/// 处理结果数据类
class _ProcessResult {
  final bool hasSelection;
  final double additionalPrice;
  
  _ProcessResult({
    required this.hasSelection,
    required this.additionalPrice,
  });
}

/// 默认选择信息数据类
class DefaultChoiceInfo {
  final ProductComboChoice choice;
  final int quantity;
  final String groupId;
  final int groupIndex;
  final int choiceIndex;
  final String choiceKey;
  final int commodityId;
  
  DefaultChoiceInfo({
    required this.choice,
    required this.quantity,
    required this.groupId,
    required this.groupIndex,
    required this.choiceIndex,
    required this.choiceKey,
    required this.commodityId,
  });
}

/// 初始化结果数据类
class ComboInitializationResult {
  final ProductComboSelectionState selectionState;
  final Map<String, DefaultChoiceInfo> defaultChoices;
  final Map<String, Map<String, dynamic>> formData;
  
  ComboInitializationResult({
    required this.selectionState,
    required this.defaultChoices,
    required this.formData,
  });
}

@riverpod
ProductComboInitializationService productComboInitializationService(Ref ref) {
  return ProductComboInitializationService(ref);
}

