import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/product_combo.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_combo_validation_result.dart';
import '../presentation/spec/product_combo_selection_controller.dart';
import 'product_combo_modifier_config_service.dart';
import '../domain/product_combo_modifier_config.dart';
import '../application/product_combo_initialization_service.dart';

/// 套餐验证服务
/// 负责验证套餐选择的完整性，包括必选项和Modifier配置
class ProductComboValidationService {

  // 验证结果缓存
  final Map<String, List<ProductValidationItem>> _validationItemsCache = {};
  
  /// 验证套餐选择的完整性
  /// 
  /// @param combo 套餐数据模型
  /// @param selectionState 套餐选择状态
  /// @param modifierConfigs Modifier配置状态映射
  /// @return 返回验证结果
  ProductComboValidationResult validateComboSelection({
    required ProductCombo combo,
    required ProductComboSelectionState selectionState,
    required Map<String, ModifierConfigData> modifierConfigs,
  }) {
    final List<String> errors = [];
    final Map<String, List<String>> missingModifierConfigs = {};
    
   // 1. 验证套餐必选项
    final comboErrors = _validateComboRequiredItems(combo, selectionState);
    errors.addAll(comboErrors);
    
    // 2. 验证Modifier配置 - 使用优化版本
    final modifierErrors = _validateModifierConfigurations(
      combo, 
      selectionState, 
      modifierConfigs,
      missingModifierConfigs,
    );
    errors.addAll(modifierErrors);
    
    // 3. 构建验证结果
    return ProductComboValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      missingModifierConfigs: missingModifierConfigs,
    );
  }

  /// 🔧 新增：清理缓存方法
  void clearCache() {
    _validationItemsCache.clear();
  }
  
  /// 验证套餐必选项 - 🔧 修复默认选择的验证逻辑
List<String> _validateComboRequiredItems(
  ProductCombo combo, 
  ProductComboSelectionState selectionState,
) {
 //debugPrint('🔧 === 开始验证套餐必选项 ===');
  final List<String> errors = [];
  
  for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
    final group = combo.groups[groupIndex];
    
    // 🔧 跳过非必选分组
    if (!group.isRequired ) {
     //debugPrint('🔧 跳过非必选分组: ${group.name}');
      continue;
    }
   //debugPrint('🔧 验证必选分组: ${group.name}');
    
    // 🔧 关键修复：使用组合键计算选择数量
    int selectedCount = 0;
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      
      final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: group.id,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      
      selectedCount += selectionState.quantities[choiceKey] ?? 0;
    }
    
   //debugPrint('🔧 分组 ${group.name} 已选数量: $selectedCount, 最小要求: ${group.minSelections}');
    
    // 检查是否满足最小选择数量
    if (selectedCount < group.minSelections) {
      if (group.minSelections == 1) {
        errors.add('请选择${group.name}');
      } else {
        errors.add('${group.name}至少需要选择${group.minSelections}个');
      }
     //debugPrint('🔧 分组 ${group.name} 验证失败');
    } else {
     //debugPrint('🔧 分组 ${group.name} 验证通过');
    }
    
    // 检查是否超过最大选择数量
    if (group.maxSelections > 0 && selectedCount > group.maxSelections) {
      errors.add('${group.name}最多只能选择${group.maxSelections}个');
     //debugPrint('🔧 分组 ${group.name} 超过最大限制');
    }
  }
  
 //debugPrint('🔧 === 套餐必选项验证完成，错误数量: ${errors.length} ===');
  return errors;
}
  
  
/// 验证Modifier配置 - 支持组合键
List<String> _validateModifierConfigurations(
  ProductCombo combo,
  ProductComboSelectionState selectionState,
  Map<String, ModifierConfigData> modifierConfigs,
  Map<String, List<String>> missingModifierConfigs,
) {
  final List<String> errors = [];
  
 //debugPrint('🔧 ========================================');
 //debugPrint('🔧 【套餐Modifier验证】开始验证流程');
 //debugPrint('🔧 ========================================');

  // 🔧 优化：使用辅助方法收集需要验证的商品信息
  final validationItems = _collectValidationItems(combo, selectionState);
  
  // 🔧 优化：批量验证所有商品
  for (final item in validationItems) {
    final validationResult = _validateSingleProductModifier(item, modifierConfigs);
    
    if (validationResult.isNotEmpty) {
     //debugPrint('🔧   ❌ ${item.productName} 配置缺失: ${validationResult.join('、')}');
      missingModifierConfigs[item.productName] = validationResult;
    } else {
     //debugPrint('🔧   ✅ ${item.productName} 配置完整');
    }
  }
  
  // 生成错误信息
  if (missingModifierConfigs.isNotEmpty) {
    for (final entry in missingModifierConfigs.entries) {
      final productName = entry.key;
      final missingConfigs = entry.value;
      errors.add('$productName 缺少规格配置: ${missingConfigs.join('、')}');
    }
  }
  
  // // 遍历所有分组和商品 - 使用组合键
  // for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
  //   final group = combo.groups[groupIndex];
    
  //   for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
  //     final choice = group.items[choiceIndex];

  //     if (choice.product.specType == 1) {
  //      //debugPrint('🔧 商品 ${choice.product.commodityName} 是单规格(specType=1)，跳过规格验证');
  //       continue;
  //     }
      
  //     // 🔧 使用组合键获取数量
  //     final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
  //       groupId: group.id,
  //       commodityId: choice.product.commodityId,
  //       groupIndex: groupIndex,
  //       choiceIndex: choiceIndex,
  //     );
      
  //     final quantity = selectionState.quantities[choiceKey] ?? 0;
      
  //     // 只有当商品被选中且需要Modifier配置时才进行校验
  //     if (quantity > 0 && _productNeedsModifierConfig(choice)) {
  //      //debugPrint('🔧 验证商品: ${choice.product.commodityName} (数量: $quantity)');
  //      //debugPrint('🔧 组合键: $choiceKey');
        
  //       // 检测配置模式并相应验证
  //       final hasUnifiedConfig = _checkUnifiedModifierConfig(
  //         choice, 
  //         quantity, 
  //         modifierConfigs,
  //         group.id,
  //         choice.product.commodityId,
  //         groupIndex,
  //         choiceIndex,
  //       );
        
  //       if (hasUnifiedConfig) {
  //        //debugPrint('🔧   ✅ 检测到统一模式配置，验证通过');
  //         continue; // 统一模式配置存在，跳过独立模式验证
  //       }
        
  //       // 独立模式验证：检查每个modifier_X配置
  //       final missingConfigs = _checkIndividualModifierConfigs(
  //         choice,
  //         quantity,
  //         modifierConfigs,
  //         group.id,
  //         choice.product.commodityId,
  //         groupIndex,
  //         choiceIndex,
  //       );
        
  //       if (missingConfigs.isNotEmpty) {
  //        //debugPrint('🔧   ❌ 独立模式配置缺失: ${missingConfigs.join('、')}');
  //         missingModifierConfigs[choice.product.commodityName] = missingConfigs;
  //       } else {
  //        //debugPrint('🔧   ✅ 独立模式配置完整');
  //       }
  //     }
  //   }
  // }
  
  // // 生成错误信息
  // if (missingModifierConfigs.isNotEmpty) {
  //   for (final entry in missingModifierConfigs.entries) {
  //     final productName = entry.key;
  //     final missingConfigs = entry.value;
  //     errors.add('$productName 缺少规格配置: ${missingConfigs.join('、')}');
  //   }
  // }
  
 //debugPrint('🔧 【套餐Modifier验证】完成，错误数量: ${errors.length}');
 //debugPrint('🔧 ========================================');
  
  return errors;
}

/// 🔧 新增：收集需要验证的商品信息
List<ProductValidationItem> _collectValidationItems(
  ProductCombo combo,
  ProductComboSelectionState selectionState,
) {
  final List<ProductValidationItem> items = [];
  
  for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
    final group = combo.groups[groupIndex];
    
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];

      // 跳过单规格商品
      if (choice.product.specType == 1) {
       //debugPrint('🔧 商品 ${choice.product.commodityName} 是单规格(specType=1)，跳过规格验证');
        continue;
      }
      
      // 生成组合键
      final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: group.id,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      
      final quantity = selectionState.quantities[choiceKey] ?? 0;
      
      // 只收集被选中且需要Modifier配置的商品
      if (quantity > 0 && _productNeedsModifierConfig(choice)) {
        items.add(ProductValidationItem(
          choice: choice,
          quantity: quantity,
          groupId: group.id,
          groupIndex: groupIndex,
          choiceIndex: choiceIndex,
          choiceKey: choiceKey,
          productName: choice.product.commodityName,
        ));
        
       //debugPrint('🔧 收集验证商品: ${choice.product.commodityName} (数量: $quantity)');
      }
    }
  }
  
  return items;
}

/// 🔧 新增：验证单个商品的Modifier配置
List<String> _validateSingleProductModifier(
  ProductValidationItem item,
  Map<String, ModifierConfigData> modifierConfigs,
) {
 //debugPrint('🔧 验证商品: ${item.productName} (数量: ${item.quantity})');
 //debugPrint('🔧 组合键: ${item.choiceKey}');
  
  // 检测配置模式并相应验证
  final hasUnifiedConfig = _checkUnifiedModifierConfig(
    item.choice, 
    item.quantity, 
    modifierConfigs,
    item.groupId,
    item.choice.product.commodityId,
    item.groupIndex,
    item.choiceIndex,
  );
  
  if (hasUnifiedConfig) {
   //debugPrint('🔧   ✅ 检测到统一模式配置，验证通过');
    return []; // 统一模式配置存在，验证通过
  }
  
  // 独立模式验证：检查每个modifier_X配置
  final missingConfigs = _checkIndividualModifierConfigs(
    item.choice,
    item.quantity,
    modifierConfigs,
    item.groupId,
    item.choice.product.commodityId,
    item.groupIndex,
    item.choiceIndex,
  );
  
  return missingConfigs;
}

/// == 新增方法 == 检查统一模式配置
bool _checkUnifiedModifierConfig(
  ProductComboChoice choice,
  int quantity,
  Map<String, ModifierConfigData> modifierConfigs,
  String groupId,
  int commodityId,
  int groupIndex,
  int choiceIndex,
) {
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  final unifiedConfig = modifierConfigs['${baseKey}_unified_modifier'];
 
  
  if (unifiedConfig == null) {
    return false;
  }
  
  // 检查配置状态和内容
  if (unifiedConfig.isConfigured) {
    return true;
  }
  
  // 备用检查：即使isConfigured为false，但如果有实际配置内容也认为有效
  final hasConfigContent = _hasValidConfigContent(unifiedConfig.formData);
  if (hasConfigContent) {
    return true;
  }
  
  return false;
}

/// == 新增方法 == 检查独立模式配置
List<String> _checkIndividualModifierConfigs(
  ProductComboChoice choice,
  int quantity,
  Map<String, ModifierConfigData> modifierConfigs,
  String groupId,
  int commodityId,
  int groupIndex,
  int choiceIndex,
) {
  final missingConfigs = <String>[];
  final baseKey = '${commodityId}_${groupId}_${groupIndex}_${choiceIndex}';
  
  for (int i = 0; i < quantity; i++) {
    final expectedModifierId = '${baseKey}_modifier_$i';
    final configData = modifierConfigs[expectedModifierId];
    
    if (configData == null) {
      missingConfigs.add('第${i + 1}个商品的规格配置');
      continue;
    }
    
    // 🔧 关键修复：检查是否有有效的配置内容
    final hasValidConfig = _hasValidConfigContent(configData.formData);
    
    if (!configData.isConfigured && !hasValidConfig) {
      missingConfigs.add('第${i + 1}个商品的规格配置');
    } else {
     //debugPrint('🔧   ✅ modifier_$i 配置完整 (isConfigured: ${configData.isConfigured}, hasValidConfig: $hasValidConfig)');
    }
  }
  
  return missingConfigs;
}

/// 增强配置内容验证，检查必选项
bool _hasValidConfigContent(Map<String, dynamic> formData) {
  if (formData.isEmpty) return false;
  
  // 检查是否有选择内容
  final selectedIds = formData['selectedItemIds'] as List? ?? [];
  final itemQuantities = formData['itemQuantities'] as Map? ?? {};
  final remarkTags = formData['selectedRemarkTags'] as List? ?? [];
  final customRemark = formData['customRemark'] as String? ?? '';

  // 是否有任何选择
  final hasAnySelection = selectedIds.isNotEmpty || 
         itemQuantities.values.any((qty) => (qty as num) > 0) ||
         remarkTags.isNotEmpty ||
         customRemark.trim().isNotEmpty;
  
  if (!hasAnySelection) {
   //debugPrint('🔧     配置内容检查：无任何选择');
    return false;
  }
  
  // 🔧 关键修复：检查必选项是否满足
  final groupValidation = formData['groupValidation'] as Map? ?? {};
  final isValid = formData['isValid'] as bool? ?? false;
  
  // 如果有 groupValidation 数据，检查所有必选组是否都通过验证
  if (groupValidation.isNotEmpty) {
    final allRequiredGroupsValid = groupValidation.values.every((isValid) => isValid == true);
    if (!allRequiredGroupsValid) {
     //debugPrint('🔧     配置内容检查：必选组验证未通过');
      return false;
    }
  }
  
  // 如果有 isValid 标志，直接使用它
  if (!isValid) {
   //debugPrint('🔧     配置内容检查：isValid=false');
    return false;
  }
  
 //debugPrint('🔧     配置内容检查：通过 (selectedIds: ${selectedIds.length}, isValid: $isValid)');
  return true;
}

/// 判断商品是否需要Modifier配置
/// 增强判断逻辑，避免假数据干扰
bool _productNeedsModifierConfig(ProductComboChoice choice) {

  final hasSpecGroups = choice.specGroups.isNotEmpty;
  if (hasSpecGroups) {
    for (final group in choice.specGroups) {
    }
  }
  
  return hasSpecGroups;
}

}

/// 套餐验证服务的Provider
final productComboValidationServiceProvider = Provider<ProductComboValidationService>((ref) {
  return ProductComboValidationService();
});

/// 套餐验证结果的Provider
final comboValidationResultProvider = Provider.family<ProductComboValidationResult, ProductCombo>((ref, combo) {
  final selectionState = ref.watch(productComboSelectionControllerProvider(combo)); //套餐选择状态
  final modifierConfigs = ref.watch(productComboModifierConfigServiceProvider); //Modifier配置状态
  final validationService = ref.watch(productComboValidationServiceProvider); //套餐验证服务
  
  return validationService.validateComboSelection(
    combo: combo,
    selectionState: selectionState,
    modifierConfigs: modifierConfigs,
  );
});

/// 商品验证项数据类
class ProductValidationItem {
  final ProductComboChoice choice;
  final int quantity;
  final String groupId;
  final int groupIndex;
  final int choiceIndex;
  final String choiceKey;
  final String productName;
  
  ProductValidationItem({
    required this.choice,
    required this.quantity,
    required this.groupId,
    required this.groupIndex,
    required this.choiceIndex,
    required this.choiceKey,
    required this.productName,
  });
}