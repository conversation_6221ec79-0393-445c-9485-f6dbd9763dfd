import 'dart:convert';
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:oktoast/oktoast.dart';
import 'package:path/path.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/product/domain/product_spec_selection_data.dart';
import 'package:kpos/features/product/presentation/spec/product_spec_selection_controller.dart';
import 'package:kpos/features/product/presentation/spec/product_final_price_controller.dart';
import 'package:kpos/features/product/presentation/spec/product_special_price_controller.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import '../../../common/components/button/kp_loading_button.dart';
import '../../../common/components/index.dart';
import '../../cart/domain/cart_add_request.dart';
import '../domain/product_combo.dart';
import '../domain/product_combo_selection_state.dart';
import '../domain/product_discount_option.dart';
import '../domain/product_spec_validation_service.dart';
import '../presentation/providers/editing_cart_item_provider.dart';
import '../presentation/spec/product_combo_selection_controller.dart';
import 'product_cart_api_converter.dart';
import 'package:collection/collection.dart';
import '../../cart/domain/cart_product_item.dart';
import '../../cart/domain/practice_item.dart';
import '../../cart/domain/addons_item.dart';
import 'product_combo_modifier_config_service.dart';

part 'product_spec_service.g.dart';

/// 产品规格验证异常
class ProductSpecValidationException implements Exception {
  final List<ProductSpecValidationError> errors;

  ProductSpecValidationException(this.errors);

  @override
  String toString() {
    return 'ProductSpecValidationException: ${errors.map((e) => e.userFriendlyMessage).join(', ')}';
  }
}

/// 商品规格异常
class ProductSpecException implements Exception {
  final String message;
  ProductSpecException(this.message);

  @override
  String toString() => message;
}

/// 已选规格信息数据类
class SelectedSpecInfo {
  final int groupId;
  final String groupName;
  final int itemId;
  final String itemName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  const SelectedSpecInfo({
    required this.groupId,
    required this.groupName,
    required this.itemId,
    required this.itemName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });
}

/// 备注数据类
class RemarkData {
  final List<String> tags;
  final String customRemark;
  final String combinedRemark;

  const RemarkData({
    required this.tags,
    required this.customRemark,
    required this.combinedRemark,
  });
}

/// 商品规格处理应用服务
/// 负责商品规格选择的业务流程编排和数据转换
/// 负责协调领域服务，实现具体的用例
class ProductSpecService {
  final ProductSpecValidationService _validationService;
  final Ref _ref;
  final String _instanceId; // 🔧 新增：实例ID

  ProductSpecService({
    required Ref ref,
    required String instanceId, // 🔧 新增：必传实例ID
    ProductSpecValidationService? validationService,
  })  : _ref = ref,
        _instanceId = instanceId,
        _validationService =
            validationService ?? ProductSpecValidationService();

  /// 验证产品规格选择
  ///
  /// 用例：验证当前规格选择是否符合业务规则
  ProductSpecValidationResult validateProductSpec({
    required List<ProductSpecGroup> specGroups,
    required ProductSpecSelectionData selectionData,
  }) {
    return _validationService.validateSpecSelection(
      specGroups: specGroups,
      selectionData: selectionData,
    );
  }

  /// 获取规格组选择数量
  ///
  /// 用例：为UI层提供规格组的当前选择数量
  int getGroupSelectionCount({
    required ProductSpecGroup group,
    required ProductSpecSelectionData selectionData,
    required int groupIndex,
  }) {
    return _validationService.getGroupSelectionCount(
      group,
      selectionData,
      groupIndex, //groupIndex:获取当前组在列表中的索引
    );
  }

  /// 🔧 更新套餐提交方法使用多选+自定义组合逻辑
  Future<void> submitComboToCartNewApi({
    required ProductItem product,
    required ProductCombo combo,
    required num quantity,
    String? remark,
    BuildContext? context,
    String? customNotes, //自定义备注
  }) async {
    try {
      //debugPrint('📝 ============================================');
      //debugPrint('📝 开始提交套餐选择到购物车');
      //debugPrint('📝 套餐: ${product.commodityName} (ID: ${product.commodityId})');
      //debugPrint('📝 数量: $quantity');
      //debugPrint('📝 备注: ${remark ?? "无"}');
      //debugPrint('📝 ============================================');

      // 1. 验证套餐基础数据
      if (product.commodityType != 3) {
        throw ProductSpecException('商品类型不是套餐，无法使用套餐提交方法');
      }

      // 2. 使用数据收集器收集套餐选择数据
      final collectedData = ProductCartApiConverter.collectComboSelectionData(
        ref: _ref,
        combo: combo,
      );

      // 3. 转换为ComboConfig列表
      final comboConfig =
          ProductCartApiConverter.convertComboSelectionToComboConfig(
        collectedData: collectedData,
      );

      if (comboConfig.isEmpty) {
        throw ProductSpecException('套餐配置为空，请检查选择数据');
      }

      // 4. 获取特殊价格和折扣状态
      final finalPriceState =
          _ref.read(productFinalPriceControllerProvider(product));
      final selectedDiscountOption = _ref.read(selectedDiscountOptionProvider);
      final realSalesPrice = _ref.read(realSalesPriceProvider);
      final selectedReasons =
          _ref.read(selectedDiscountReasonsProvider); //  多选预设原因
      final customNotes = _ref.read(customNotesProvider); //  自定义备注

      // 5. 准备特殊价格和折扣数据
      double updatePrice =
          double.parse(finalPriceState.formattedTotalPrice); // 默认使用总价格
      int? discountType;
      double? discount;
      String? discountReasonText;

      // 处理真实售价（修改后的价格）
      if (realSalesPrice != null) {
        updatePrice = realSalesPrice;
        //debugPrint('🔧 使用真实售价: $updatePrice');
      } else {
        //debugPrint('🔧 使用计算总价: $updatePrice');
      }

      // 处理折扣数据
      if (selectedDiscountOption != null) {
        discountType = selectedDiscountOption.discountType.index + 1; // 转换为1/2

        if (selectedDiscountOption.isFree) {
          // 免费情况：discountType和discount都传1
          discountType = 1;
          discount = 1.0;
        } else {
          discount = selectedDiscountOption.value;
        }

        //  使用增强的多选+自定义组合逻辑
        discountReasonText = _getMultipleDiscountReasonText(
          selectedDiscountOption,
          selectedReasons,
          customNotes, //  传入自定义备注
        );

        //debugPrint('🔧 套餐折扣配置详情:');
        //debugPrint('🔧   预设原因: $selectedReasons');
        //debugPrint('🔧   自定义备注: "$customNotes"');
        //debugPrint('🔧   最终原因文本: "$discountReasonText"');
        //debugPrint('🔧   折扣类型: $discountType, 折扣值: $discount');
      }

      // == 新增：输出最终提交的数据 ==
      //debugPrint('🚀 === 套餐最终提交的数据 ===');
      _logFinalSubmissionData(
        product: product,
        quantity: quantity,
        remark: remark,
        comboConfig: comboConfig,
        updatePrice: updatePrice,
        discountType: discountType,
        discount: discount,
        discountReasonText: discountReasonText,
      );

      // 6. 调用Cart模块的套餐服务器端方法
      await _ref.read(cartServiceProvider).addComboToCartServer(
            productId: product.commodityId.toInt(),
            quantity: quantity.toInt(),
            remark: remark,
            comboConfig: comboConfig,
            updatePrice: updatePrice,
            discountType: discountType,
            discount: discount,
            discountReason: discountReasonText, //  多选+自定义组合的折扣原因
          );

      //debugPrint('✅ ============================================');
      //debugPrint('✅ 套餐选择提交成功');
      //debugPrint('✅ ============================================');
    } catch (e, stackTrace) {
      //debugPrint('❌ ============================================');
      //debugPrint('❌ 套餐选择提交失败: $e');
      //debugPrint('❌ 堆栈跟踪: $stackTrace');
      //debugPrint('❌ ============================================');
      throw ProductSpecException('提交套餐选择失败: ${e.toString()}');
    }
  }

  /// 智能提交单商品：自动判断是添加还是更新
  /// 作用：统一的提交入口，根据编辑状态自动选择添加或更新逻辑
  Future<void> submitSpecToCartApiSmart({
    required ProductItem product,
    required List<ProductSpecGroup> specGroups,
    required num quantity,
    String? remark,
    BuildContext? context,
    String? customNotes,
    bool? isEditMode = false,
  }) async {
    // 检查是否存在正在编辑的购物车项
    final editingCartItem = _ref.read(editingCartItemProvider);

    if (editingCartItem != null) {
      //debugPrint('🔄 检测到编辑模式，更新购物车商品');
      // 编辑模式：更新现有商品
      await _updateExistingCartItem(
        cartItem: editingCartItem,
        product: product,
        specGroups: specGroups,
        quantity: quantity,
        remark: remark,
        customNotes: customNotes,
      );

      // 清除编辑状态
      _ref.read(editingCartItemProvider.notifier).state = null;
    } else {
      //debugPrint('🔄 添加模式，新增商品到购物车');
      // 添加模式：原有逻辑
      await submitSpecToCartApi(
        product: product,
        specGroups: specGroups,
        quantity: quantity,
        remark: remark,
        context: context,
        customNotes: customNotes,
      );
    }
  }

  /// 智能提交套餐：自动判断是添加还是更新
  /// 作用：统一的套餐提交入口，根据编辑状态自动选择添加或更新逻辑
  Future<void> submitComboToCartApiSmart({
    required ProductItem product,
    required ProductCombo combo,
    required num quantity,
    String? remark,
    BuildContext? context,
    String? customNotes,
    bool? isEditMode = false,
  }) async {
    final editingCartItem = _ref.read(editingCartItemProvider);

    if (editingCartItem != null) {
      //debugPrint('🔄 检测到编辑模式，更新购物车套餐');
      // 编辑模式：更新现有套餐
      await _updateExistingComboItem(
        cartItem: editingCartItem,
        product: product,
        combo: combo,
        quantity: quantity,
        remark: remark,
        customNotes: customNotes,
      );

      // 清除编辑状态
      _ref.read(editingCartItemProvider.notifier).state = null;
    } else {
      //debugPrint('🔄 添加模式，新增套餐到购物车');
      // 添加模式：原有逻辑
      await submitComboToCartNewApi(
        product: product,
        combo: combo,
        quantity: quantity,
        remark: remark,
        context: context,
        customNotes: customNotes,
      );
    }
  }

  /// 更新现有购物车商品
  /// 作用：处理单商品的更新逻辑
  Future<void> _updateExistingCartItem({
    required CartProductItem cartItem,
    required ProductItem product,
    required List<ProductSpecGroup> specGroups,
    required num quantity,
    String? remark,
    String? customNotes,
  }) async {
    try {
      //debugPrint('📝 === 开始更新购物车商品 ===');
      //debugPrint('📝 商品: ${product.commodityName} (ID: ${product.commodityId})');
      //debugPrint('📝 购物车项ID: ${cartItem.orderItemId}');

      // 获取当前规格选择状态和价格状态
      final specSelectionState =
          _ref.read(productSpecSelectionControllerProvider(_instanceId));
      final finalPriceState =
          _ref.read(productFinalPriceControllerProvider(product));
      final selectedDiscountOption = _ref.read(selectedDiscountOptionProvider);
      final realSalesPrice = _ref.read(realSalesPriceProvider);
      final selectedReasons = _ref.read(selectedDiscountReasonsProvider);

      // 构建完整的备注信息
      final remarkData = _buildRemarkData(specSelectionState);
      final finalRemark = _buildFinalRemark(remark, remarkData);

      // 转换为API格式
      final productConfig =
          ProductCartApiConverter.convertSpecSelectionToProductConfig(
        selectionData: specSelectionState,
        specGroups: specGroups,
        mainProductSkuId: _getMainProductSkuId(specGroups, product),
      );

      // == 新增开始 == 🔧 准备特殊价格和折扣数据
      double? updatePrice;
      int? discountType;
      double? discount;
      String? discountReasonText;

      // 处理真实售价（修改后的价格）
      if (realSalesPrice != null) {
        updatePrice = realSalesPrice;
        //debugPrint('🔧 使用真实售价: $updatePrice');
      } else {
        updatePrice = double.parse(finalPriceState.formattedTotalPrice);
        //debugPrint('🔧 使用计算总价: $updatePrice');
      }

      // 处理折扣数据
      if (selectedDiscountOption != null) {
        discountType = selectedDiscountOption.discountType.index + 1;

        if (selectedDiscountOption.isFree) {
          discountType = 1;
          discount = 1.0;
        } else {
          discount = selectedDiscountOption.value;
        }

        discountReasonText = _getMultipleDiscountReasonText(
          selectedDiscountOption,
          selectedReasons,
          customNotes,
        );

        //debugPrint('🔧 更新商品折扣配置: 类型=$discountType, 值=$discount, 原因="$discountReasonText"');
      }
      // == 新增结束 ==

      // == 修改开始 == 🔧 调用Cart服务更新商品
      // ⭐️⭐️ 等文彬写方法
      KPToast.show(
        content: '更新购物车方法这种调试中~',
        duration: const Duration(milliseconds: 2500), // 持续时间
        position: ToastPosition.center, // 位置
        isGreen: false, // 是否为绿色成功样式
        backgroundColor: Colors.black, // 背景色
        textColor: Colors.white, // 文字颜色
        onDismiss: () {}, // 消失回调
      );

      // await _ref.read(cartServiceProvider).updateCartItem(
      //   orderItemId: cartItem.orderItemId,
      //   quantity: quantity.toInt(),
      //   remark: finalRemark,
      //   productConfig: productConfig,
      //   updatePrice: updatePrice,
      //   discountType: discountType,
      //   discount: discount,
      //   discountReason: discountReasonText,
      // );
      // == 修改结束 ==

      //debugPrint('📝 ✅ 购物车商品更新成功');

      // 清理状态
      _clearSpecStates();
    } catch (e) {
      //debugPrint('📝 ❌ 更新购物车商品失败: $e');
      rethrow;
    }
  }

  /// 更新现有购物车套餐
  /// 作用：处理套餐的更新逻辑
  Future<void> _updateExistingComboItem({
    required CartProductItem cartItem,
    required ProductItem product,
    required ProductCombo combo,
    required num quantity,
    String? remark,
    String? customNotes,
  }) async {
    try {
      //debugPrint('📝 开始更新购物车套餐');
      //debugPrint('📝 套餐: ${product.commodityName} (ID: ${product.commodityId})');
      //debugPrint('📝 购物车项ID: ${cartItem.orderItemId}');

      // 收集套餐选择数据
      final collectedData = ProductCartApiConverter.collectComboSelectionData(
        ref: _ref,
        combo: combo,
      );

      // // 调用Cart服务更新套餐
      // await _ref.read(cartServiceProvider).updateCartCombo(
      //   orderItemId: cartItem.orderItemId,
      //   quantity: quantity.toInt(),
      //   remark: remark,
      //   comboConfig: collectedData.comboConfig,
      // );
      //debugPrint('📝 $collectedData');
      //debugPrint('📝 购物车套餐更新成功');
    } catch (e) {
      //debugPrint('📝 更新购物车套餐失败: $e');
      rethrow;
    }
  }

  /// 🔧 新增：输出最终提交数据的详细日志
  void _logFinalSubmissionData({
    required ProductItem product,
    required num quantity,
    String? remark,
    required List<ComboConfig> comboConfig,
    required double updatePrice,
    int? discountType,
    double? discount,
    String? discountReasonText,
  }) {
    //debugPrint('🚀 === 套餐最终提交的数据 ===');
    //debugPrint('🚀 === 购物车传参格式 ===');

    // 构建完整的JSON结构用于调试
    final jsonData = {
      'orderId': 596511441457123328,
      'productId': product.commodityId,
      'quantity': quantity,
      'remark': remark,
      'updatePrice': updatePrice,
      if (discountType != null) 'discountType': discountType,
      if (discount != null) 'discount': discount,
      if (discountReasonText != null) 'discountReason': discountReasonText,
      'comboConfig': comboConfig
          .map((config) => {
                'comboGroupingId': config.comboGroupingId,
                'commodityId': config.commodityId,
                'unitQuantity': config.unitQuantity,
                'commoditySkuId': config.commoditySkuId,
                if (config.practiceConfigList != null)
                  'practiceConfigList': config.practiceConfigList!
                      .map((practice) => {
                            'commodityPracticeGroupId':
                                practice.commodityPracticeGroupId,
                            'practiceItemId':
                                practice.practiceItemId, // 🔧 确保使用正确的字段名
                          })
                      .toList(),
                if (config.addonsConfigList != null)
                  'addonsConfigList': config.addonsConfigList!
                      .map((addons) => {
                            'commodityAddonsGroupId':
                                addons.commodityAddonsGroupId,
                            'addonsCommodityId': addons.addonsCommodityId,
                            'unitQuantity': addons.unitQuantity,
                          })
                      .toList(),
                if (config.remark != null) 'remark': config.remark,
              })
          .toList(),
    };

    // 输出格式化的JSON
    //debugPrint('🚀 购物车传参 JSON:');
    //debugPrint(_formatJson(jsonData));

    // 输出摘要信息
    //debugPrint('🚀 === 传参摘要 ===');
    //debugPrint('🚀 商品: ${product.commodityName} (ID: ${product.commodityId})');
    //debugPrint('🚀 数量: $quantity');
    //debugPrint('🚀 套餐配置项: ${comboConfig.length}个');
    //debugPrint('🚀 修改价格: \$${updatePrice.toStringAsFixed(2)}');

    for (int i = 0; i < comboConfig.length; i++) {
      final config = comboConfig[i];
      //debugPrint('🚀 配置[$i]:');
      //debugPrint('🚀   分组ID: ${config.comboGroupingId}');
      //debugPrint('🚀   商品ID: ${config.commodityId}');
      //debugPrint('🚀   SKU ID: ${config.commoditySkuId}');
      //debugPrint('🚀   数量: ${config.unitQuantity}');

      if (config.practiceConfigList != null &&
          config.practiceConfigList!.isNotEmpty) {
        //debugPrint('🚀   做法配置: ${config.practiceConfigList!.length}项');
        for (int j = 0; j < config.practiceConfigList!.length; j++) {
          final practice = config.practiceConfigList![j];
          //debugPrint('🚀     [$j] 做法组ID: ${practice.commodityPracticeGroupId}');
          //debugPrint('🚀     [$j] 做法项ID: ${practice.practiceItemId}'); // 🔧 确保显示正确的itemId
        }
      }

      if (config.addonsConfigList != null &&
          config.addonsConfigList!.isNotEmpty) {
        //debugPrint('🚀   加料配置: ${config.addonsConfigList!.length}项');
        for (int j = 0; j < config.addonsConfigList!.length; j++) {
          final addons = config.addonsConfigList![j];
          //debugPrint('🚀     [$j] 加料组ID: ${addons.commodityAddonsGroupId}');
          //debugPrint('🚀     [$j] 加料商品ID: ${addons.addonsCommodityId}');
          //debugPrint('🚀     [$j] 数量: ${addons.unitQuantity}');
        }
      }

      if (config.remark != null) {
        //debugPrint('🚀   商品备注: ${config.remark}');
      }
    }
  }

  /// == 新增辅助方法：格式化 JSON 输出 ==
  String _formatJson(Map<String, dynamic> json) {
    try {
      // 使用 Dart 的 JsonEncoder 来格式化 JSON
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    } catch (e) {
      return json.toString();
    }
  }

  /// 汇总所有规格相关数据
  /// 这是数据整理的核心方法
  Map<String, dynamic> _gatherAllSpecData(
    ProductItem product,
    List<ProductSpecGroup> specGroups,
    num quantity,
  ) {
    // 1. 获取规格选择状态 - 使用实例ID
    final specSelectionState =
        _ref.read(productSpecSelectionControllerProvider(_instanceId));

    // 2. 获取价格状态
    final finalPriceState =
        _ref.read(productFinalPriceControllerProvider(product));

    // 3. 获取特殊价格状态
    final selectedDiscountOption = _ref.read(selectedDiscountOptionProvider);

    // 4. 构建已选规格详情
    final selectedSpecs =
        _buildSelectedSpecDetails(specGroups, specSelectionState);

    // 5. 构建备注信息
    final remarkData = _buildRemarkData(specSelectionState);

    // 6. 汇总所有数据
    return {
      // 基础商品信息
      'product': product,
      'productId': product.commodityId,
      'productName': product.commodityName,
      'productImageUrl': product.commodityImagePath ?? '',

      // 数量信息
      'quantity': quantity,
      'isWeightProduct': product.commodityType == 2,

      // 价格信息
      'basePrice': finalPriceState.basePrice,
      'specAdditionalPrice': specSelectionState.additionalPrice,
      'totalPrice': finalPriceState.totalPrice,
      'selectedDiscountOption': selectedDiscountOption,

      // 规格选择信息
      'specSelectionState': specSelectionState,
      'selectedSpecs': selectedSpecs,
      'isSpecValid': specSelectionState.isValid,
      'groupValidation': specSelectionState.groupValidation,

      // 备注信息
      'remarkData': remarkData,

      // 元数据
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 构建已选规格详情列表
  List<SelectedSpecInfo> _buildSelectedSpecDetails(
    List<ProductSpecGroup> specGroups,
    ProductSpecSelectionData specSelectionState,
  ) {
    final List<SelectedSpecInfo> selectedSpecs = [];

    for (final group in specGroups) {
      for (final item in group.items) {
        if (specSelectionState.selectedItemIds.contains(item.id)) {
          final itemQuantity = specSelectionState.itemQuantities[item.id] ?? 1;

          selectedSpecs.add(SelectedSpecInfo(
            groupId: group.id,
            groupName: group.name,
            itemId: item.id,
            itemName: item.name,
            quantity: itemQuantity,
            unitPrice: item.additionalPrice,
            totalPrice: item.additionalPrice * itemQuantity,
          ));
        }
      }
    }

    return selectedSpecs;
  }

  /// 构建备注数据
  RemarkData _buildRemarkData(ProductSpecSelectionData specSelectionState) {
    final List<String> allRemarks = [];

    // 🔧 添加详细调试信息
    //debugPrint('🔧 === 备注数据构建调试 ===');
    //debugPrint('🔧 规格选择状态中的备注标签: ${specSelectionState.selectedRemarkTags}');
    //debugPrint('🔧 规格选择状态中的自定义备注: "${specSelectionState.customRemark}"');
    //debugPrint('🔧 备注标签是否为空: ${specSelectionState.selectedRemarkTags.isEmpty}');
    //debugPrint('🔧 自定义备注是否为空: ${specSelectionState.customRemark.isEmpty}');

    // 添加预设备注标签
    if (specSelectionState.selectedRemarkTags.isNotEmpty) {
      allRemarks.addAll(specSelectionState.selectedRemarkTags);
      //debugPrint('🔧 添加了备注标签: ${specSelectionState.selectedRemarkTags}');
    }

    // 添加自定义备注
    if (specSelectionState.customRemark.isNotEmpty) {
      allRemarks.add(specSelectionState.customRemark);
      //debugPrint('🔧 添加了自定义备注: "${specSelectionState.customRemark}"');
    }

    final remarkData = RemarkData(
      tags: specSelectionState.selectedRemarkTags,
      customRemark: specSelectionState.customRemark,
      combinedRemark: allRemarks.isNotEmpty ? allRemarks.join(',') : '',
    );

    //debugPrint('🔧 最终构建的RemarkData: tags=${remarkData.tags}, customRemark="${remarkData.customRemark}", combinedRemark="${remarkData.combinedRemark}"');
    //debugPrint('🔧 === 备注数据构建调试结束 ===');

    return remarkData;
  }

  /// 验证规格数据
  void _validateSpecData(
      Map<String, dynamic> specData, List<ProductSpecGroup> specGroups) {
    final List<String> errors = [];

    // 获取商品信息并检查是否为单规格商品
    final product = specData['product'] as ProductItem;
    // 单规格商品跳过所有验证
    if (product.specType == 1) {
      ////debugPrint('🔧 === 单规格商品验证跳过 ===');
      ////debugPrint('🔧 商品: ${product.commodityName} (specType=${product.specType})');
      ////debugPrint('🔧 跳过所有规格验证，直接通过');
      return; // 直接返回，不进行任何验证
    }

    // 1. 规格选择验证 - 使用新的验证服务
    final specSelectionState =
        specData['specSelectionState'] as ProductSpecSelectionData;

    // // 🔧 添加详细调试信息
    ////debugPrint('🔧 === 规格验证详细信息 ===');
    ////debugPrint('🔧 选中的规格项ID: ${specSelectionState.selectedItemIds}');
    ////debugPrint('🔧 规格项数量映射: ${specSelectionState.itemQuantities}');
    ////debugPrint('🔧 是否整体有效: ${specSelectionState.isValid}');
    ////debugPrint('🔧 规格组验证状态: ${specSelectionState.groupValidation}');

    for (final group in specGroups) {
      ////debugPrint('🔧 规格组 "${group.name}" (ID: ${group.id}):');
      ////debugPrint('🔧   是否必选: ${group.isRequired}');
      ////debugPrint('🔧   下限: ${group.lowerLimit}, 上限: ${group.upperLimit}');
      ////debugPrint(
      //     '🔧   规格项: ${group.items.map((item) => '${item.name}(ID:${item.id})').toList()}');

      // 计算该组的选择数量
      int groupSelectionCount = 0;
      for (final item in group.items) {
        if (specSelectionState.selectedItemIds.contains(item.id)) {
          final quantity = specSelectionState.itemQuantities[item.id] ?? 1;
          groupSelectionCount += quantity;
          ////debugPrint('🔧   选中项: ${item.name}(ID:${item.id}), 数量: $quantity');
        }
      }
      ////debugPrint('🔧   该组总选择数量: $groupSelectionCount');
      ////debugPrint('🔧   该组验证状态: ${specSelectionState.groupValidation[group.id]}');
    }

    final validationResult = _validationService.validateSpecSelection(
      specGroups: specGroups,
      selectionData: specSelectionState,
    );

    if (!validationResult.isValid) {
      //debugPrint('🔧 验证失败，错误详情:');
      for (final error in validationResult.errors) {
        //debugPrint('🔧   错误: ${error.userFriendlyMessage}');
        //debugPrint('🔧   组名: ${error.groupName}, 当前数量: ${error.currentCount}, 要求数量: ${error.requiredCount}');
      }
      // 添加具体的验证错误信息
      errors.addAll(validationResult.errors.map((e) => e.userFriendlyMessage));
    }

    // 2. 数量验证
    final quantity = specData['quantity'] as num;
    final isWeightProduct = specData['isWeightProduct'] as bool;

    if (isWeightProduct) {
      if (quantity <= 0 || quantity > 999) {
        errors.add('重量必须在0.01kg到999kg之间');
      }
    } else {
      if (quantity < 1 || quantity > 15) {
        errors.add('数量必须在1到15之间');
      }
    }

    // 3. 价格验证
    final totalPrice = specData['totalPrice'] as double;
    if (totalPrice < 0) {
      errors.add('商品价格异常，请重新选择');
    }

    // 4. 如果有错误，抛出异常
    if (errors.isNotEmpty) {
      throw ProductSpecException(errors.first);
    }
  }

  /// 将规格数据转换为CartProductItem格式
  CartProductItem _convertSpecToCartProductItem(Map<String, dynamic> specData) {
    final product = specData['product'] as ProductItem;
    final quantity = specData['quantity'] as num;
    final isWeightProduct = specData['isWeightProduct'] as bool;
    final selectedSpecs = specData['selectedSpecs'] as List<SelectedSpecInfo>;
    final remarkData = specData['remarkData'] as RemarkData;
    final totalPrice = specData['totalPrice'] as double;

    // 构建规格信息文本
    final List<String> specTexts = [];
    for (final spec in selectedSpecs) {
      String specText = spec.itemName;

      if (spec.quantity > 1) {
        specText += ' x${spec.quantity}';
      }
      if (spec.unitPrice > 0) {
        specText += ' (+S\$${spec.unitPrice.toStringAsFixed(2)})';
      }
      specTexts.add(specText);
    }

    // 构建完整的商品备注
    final List<String> allNotes = [];

    // 添加重量信息（如果是称重商品）
    if (isWeightProduct) {
      allNotes.add('重量：${quantity}kg');
    }

    // 添加规格信息
    if (specTexts.isNotEmpty) {
      allNotes.add('规格：${specTexts.join('、')}');
    }

    // 添加备注信息
    if (remarkData.combinedRemark.isNotEmpty) {
      allNotes.add('备注：${remarkData.combinedRemark}');
    }

    // 最终数量（称重商品统一为1，重量体现在备注中）
    final int finalQuantity = isWeightProduct ? 1 : quantity.toInt();

    // 生成 PracticeItem 和 AddonsItem 列表 - 传空列表
    final practiceList = <PracticeItem>[];
    final addonsList = <AddonsItem>[];

    return CartProductItem(
      orderItemId: _generateOrderItemId(product.commodityId),
      commodityId: product.commodityId,
      commodityName: product.commodityName,
      commodityType: product.commodityType,
      commoditySkuId: product.commodityId,
      price: totalPrice,
      quantity: finalQuantity,
      remark: allNotes.isNotEmpty ? allNotes.join('；') : '',
      practiceList: practiceList,
      addonsList: addonsList,
    );
  }

  /// 生成订单项ID
  int _generateOrderItemId(int commodityId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$commodityId$timestamp'.hashCode.abs();
  }

  /// 🔧 更新清理规格选择相关状态方法
  void _clearSpecStates() {
    // 清空规格选择状态
    _ref
        .read(productSpecSelectionControllerProvider(_instanceId).notifier)
        .clearAllSelections();

    // 重置特殊价格状态
    _ref.read(selectedDiscountOptionProvider.notifier).update(null);
    _ref.read(selectedDiscountReasonsProvider.notifier).clear(); //  清空多选原因
    _ref.read(customNotesProvider.notifier).clear(); //  清空自定义备注
    _ref.read(realSalesPriceProvider.notifier).clear();

    // 注意：价格控制器会自动响应规格选择的清空而重置
  }

  /// 获取当前规格选择摘要（用于UI显示）
  String getSpecSelectionSummary({
    required List<ProductSpecGroup> specGroups,
  }) {
    final specSelectionState =
        _ref.read(productSpecSelectionControllerProvider(_instanceId));
    final selectedSpecs =
        _buildSelectedSpecDetails(specGroups, specSelectionState);

    if (selectedSpecs.isEmpty) {
      return '未选择规格';
    }

    final specTexts = selectedSpecs.map((spec) {
      String text = spec.itemName;
      if (spec.quantity > 1) {
        text += ' x${spec.quantity}';
      }
      return text;
    }).toList();

    return specTexts.join('、');
  }

  /// 计算规格附加价格总和（用于价格显示）
  double calculateSpecAdditionalPrice({
    required List<ProductSpecGroup> specGroups,
  }) {
    final specSelectionState =
        _ref.read(productSpecSelectionControllerProvider(_instanceId));
    return specSelectionState.additionalPrice;
  }

  /// 🔧 更新单商品提交方法使用多选+自定义组合逻辑
  Future<void> submitSpecToCartApi({
    required ProductItem product,
    required List<ProductSpecGroup> specGroups,
    required num quantity,
    String? remark,
    BuildContext? context,
    String? customNotes, //自定义备注
  }) async {
    try {
      // debugPrint('📝 开始提交规格选择到购物车');
      // debugPrint('📝 商品: ${product.commodityName} (ID: ${product.commodityId})');
      // debugPrint('📝 数量: $quantity');

      // 1. 获取当前规格选择状态
      final specSelectionState =
          _ref.read(productSpecSelectionControllerProvider(_instanceId));

      // debugPrint('🔍 === 提交时的完整数据检查 ===');
      // debugPrint('🔍 传入的customNotes: "$customNotes"');
      // debugPrint('🔍 specSelectionState.customRemark: "${specSelectionState.customRemark}"');
      // debugPrint('🔍 传入的remark: "$remark"');

      // 2. 获取价格状态和折扣相关状态
      final finalPriceState =
          _ref.read(productFinalPriceControllerProvider(product));
      final selectedDiscountOption = _ref.read(selectedDiscountOptionProvider);
      final realSalesPrice = _ref.read(realSalesPriceProvider);
      final selectedReasons =
          _ref.read(selectedDiscountReasonsProvider); //  多选预设原因

      // 3. 验证规格数据完整性
      final specData = _gatherAllSpecData(product, specGroups, quantity);
      _validateSpecData(specData, specGroups);

      // 4. 🔧 构建完整的备注信息
      final remarkData = specData['remarkData'] as RemarkData;
      final finalRemark = _buildFinalRemark(remark, remarkData);

      //debugPrint('📝 最终备注: $finalRemark');

      // 5. 使用转换器生成ProductConfig
      final productConfig =
          ProductCartApiConverter.convertSpecSelectionToProductConfig(
        selectionData: specSelectionState,
        specGroups: specGroups,
        mainProductSkuId: _getMainProductSkuId(specGroups, product),
      );

      // 6.  准备特殊价格和折扣数据
      double? updatePrice;
      int? discountType;
      double? discount;
      String? discountReasonText;

      // 处理真实售价（修改后的价格）
      if (realSalesPrice != null) {
        updatePrice = realSalesPrice;
        //debugPrint('🔧 使用真实售价: $updatePrice');
      } else {
        updatePrice = double.parse(finalPriceState.formattedTotalPrice);
        //debugPrint('🔧 使用计算总价: $updatePrice');
      }

      // 处理折扣数据
      if (selectedDiscountOption != null) {
        debugPrint('🔍 折扣选项存在，开始构建原因文本');

        discountType = selectedDiscountOption.discountType.index + 1; // 转换为1/2

        if (selectedDiscountOption.isFree) {
          // 免费情况：discountType和discount都传1
          discountType = 1;
          discount = 1.0;
        } else {
          discount = selectedDiscountOption.value;
        }

        //  使用增强的多选+自定义组合逻辑
        discountReasonText = _getMultipleDiscountReasonText(
          selectedDiscountOption,
          selectedReasons,
          customNotes, //  传入自定义备注
        );

        // debugPrint('🔧 单商品折扣配置详情:');
        // debugPrint('🔧   预设原因: $selectedReasons');
        // debugPrint('🔧   自定义备注: "$customNotes"');
        // debugPrint('🔧   最终原因文本: "$discountReasonText"');
        // debugPrint('🔧   折扣类型: $discountType, 折扣值: $discount');
      }

      // 🔧 新增：详细打印最终传递给购物车的完整数据
      // debugPrint('🛒 === 单商品最终购物车数据打印 ===');
      // debugPrint('🛒 商品ID: ${product.commodityId.toInt()}');
      // debugPrint('🛒 商品名称: ${product.commodityName}');
      // debugPrint('🛒 数量: ${quantity.toInt()}');
      // debugPrint('🛒 备注: $finalRemark');
      // debugPrint('🛒 特殊价格: $updatePrice');
      // debugPrint('🛒 折扣类型: $discountType');
      // debugPrint('🛒 折扣值: $discount');
      // debugPrint('🛒 折扣原因: $discountReasonText');

      // 7. 调用Cart模块的服务器端方法
      await _ref.read(cartServiceProvider).addSingleProductToCartServer(
            productId: product.commodityId.toInt(),
            quantity: quantity,
            remark: finalRemark,
            productConfig: productConfig,
            updatePrice: updatePrice, //  修改后的价格
            discountType: discountType, //  折扣类型
            discount: discount, //  折扣值
            discountReason: discountReasonText, //  多选+自定义组合的折扣原因
          );

      // 8. 清理规格选择状态
      _clearSpecStates();

      //debugPrint('✅ 单商品规格选择提交成功');
    } catch (e, stackTrace) {
      //debugPrint('❌ 单商品规格选择提交失败: $e');
      //debugPrint('❌ 堆栈跟踪: $stackTrace');
      throw ProductSpecException('提交规格选择失败: ${e.toString()}');
    }
  }

  /// 🔧 新增：构建最终备注信息的辅助方法
  /// 🎓 教学重点：多源备注数据的合并策略
  String? _buildFinalRemark(String? externalRemark, RemarkData remarkData) {
    final List<String> allRemarks = [];

    // 1. 添加外部传入的备注（如果有）
    if (externalRemark != null && externalRemark.trim().isNotEmpty) {
      allRemarks.add(externalRemark.trim());
      //debugPrint('🔧 添加了外部备注: "${externalRemark.trim()}"');
    }

    // 2. 添加规格选择中的备注标签
    if (remarkData.tags.isNotEmpty) {
      allRemarks.add('${remarkData.tags.join(',')}');
      //debugPrint('🔧 添加了备注标签: ${remarkData.tags}');
    }

    // 3. 添加自定义备注
    if (remarkData.customRemark.trim().isNotEmpty) {
      allRemarks.add('${remarkData.customRemark.trim()}');
      //debugPrint('🔧 添加了自定义备注: "${remarkData.customRemark.trim()}"');
    }

    // 4. 如果有任何备注信息，则组合返回；否则返回null
    final finalRemark = allRemarks.isNotEmpty ? allRemarks.join(',') : null;

    return finalRemark;
  }

  /// 🔧 新增：从套餐选择数据中提取备注信息
  /// 🎓 教学重点：复杂数据结构中的信息提取
  RemarkData _extractComboRemarkData(Map<String, dynamic> comboSelectionData) {
    final remarkTags = <String>[];
    final customRemarks = <String>[];

    // 从套餐分组中提取备注
    final groups = comboSelectionData['groups'] as List<dynamic>? ?? [];
    for (final groupData in groups) {
      if (groupData is Map<String, dynamic>) {
        final groupRemark = groupData['remark'] as String?;
        if (groupRemark != null && groupRemark.isNotEmpty) {
          customRemarks.add(groupRemark);
        }

        // 处理选中项目的备注
        final selectedItems =
            groupData['selectedItems'] as List<dynamic>? ?? [];
        for (final itemData in selectedItems) {
          if (itemData is Map<String, dynamic>) {
            final itemRemark = itemData['remark'] as String?;
            if (itemRemark != null && itemRemark.isNotEmpty) {
              customRemarks.add(itemRemark);
            }
          }
        }
      }
    }

    return RemarkData(
      tags: remarkTags,
      customRemark: customRemarks.join(', '),
      combinedRemark: [...remarkTags, ...customRemarks].join(', '),
    );
  }

  /// 根据商品规格类型获取正确的SKU ID
  int _getMainProductSkuId(
      List<ProductSpecGroup> specGroups, ProductItem product) {
    // 查找规格组
    for (final group in specGroups) {
      if (group.groupType == ProductSpecGroupType.spec &&
          group.items.isNotEmpty) {
        // 判断商品规格类型
        if (product.specType == 1) {
          // 单规格商品：取第一个
          //debugPrint('🔧 单规格商品-使用第一个SKU ID: ${group.items.first.skuId} (${group.items.first.name})');
          return group.items.first.skuId;
        } else {
          // 多规格商品：取用户选中的
          for (final item in group.items) {
            if (item.isSelected) {
              //debugPrint('🔧 多规格商品-从选中项获取SKU ID: ${item.skuId} (${item.name})');
              return item.skuId;
            }
          }

          // 如果没有找到选中项，
          return 0;
        }
      }
    }

    //debugPrint('🚨 警告：未找到规格组，使用默认值');
    return 0;
  }

  // 在 lib/features/product/application/product_spec_service.dart 中添加

  ///  获取多选折扣原因文本的辅助方法（支持多选+自定义组合）
  String? _getMultipleDiscountReasonText(
    ProductDiscountOption? selectedDiscountOption,
    List<String> selectedReasons, //  多选预设原因列表
    String? customNotes, //  自定义备注内容
  ) {
    if (selectedDiscountOption == null) return null;

    // 🔧 收集所有有效的原因文本
    final List<String> allReasons = [];

    // 1. 🥇 最高优先级：添加用户选择的多个预设原因
    if (selectedReasons.isNotEmpty) {
      allReasons.addAll(selectedReasons);
      //debugPrint('🔧 添加预设原因: $selectedReasons');
    }

    // 2. 🥈 添加自定义备注（如果有内容）
    if (customNotes != null && customNotes.trim().isNotEmpty) {
      allReasons.add(customNotes.trim());
      //debugPrint('🔧 添加自定义备注: "${customNotes.trim()}"');
    }

    // 3. 🔧 如果有任何用户输入的原因，用逗号连接返回
    if (allReasons.isNotEmpty) {
      final combinedReasons = allReasons.join(',');
      //debugPrint('🔧 组合后的原因文本: "$combinedReasons"');
      return combinedReasons;
    }

    // 4. 🥉 最低优先级：如果用户没有输入任何原因，使用默认原因
    if (selectedDiscountOption.isCustom) {
      return "自定义折扣";
    } else {
      return selectedDiscountOption.discount;
    }
  }
}

/// 产品规格服务提供者
@riverpod
ProductSpecService productSpecService(Ref ref, String instanceId) {
  return ProductSpecService(
    ref: ref,
    instanceId: instanceId,
    validationService: ProductSpecValidationService(),
  );
}
