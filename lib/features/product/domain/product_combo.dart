import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'product_item.dart';
import 'product_detail.dart';

part 'product_combo.freezed.dart';
part 'product_combo.g.dart';

/// 定义了套餐选项组的选择模式
enum SelectionMode {
  /// 单选模式
  single,
  /// 多选模式
  multiple,
  /// 步进器模式
  stepper,
}

/// 套餐主体结构 (例如:"浩浩减肥套餐")
@freezed
class ProductCombo with _$ProductCombo {
  const factory ProductCombo({
    required String commodityId,
    required String commodityName,
    required List<ProductComboGroup> group,
    @Default([]) List<ReasonOption> reasonOptions,
  }) = _ProductCombo;

  factory ProductCombo.fromJson(Map<String, dynamic> json) =>
      _$ProductComboFromJson(json);
}

//为ProductCombo添加扩展方法，保持向后兼容
extension ProductComboExtension on ProductCombo {
  /// 兼容旧代码的id字段
  String get id => commodityId;
  
  /// 兼容旧代码的name字段
  String get name => commodityName;
  
  /// 兼容旧代码的groups字段
  List<ProductComboGroup> get groups => group;
}

/// 套餐内的分组 (例如: "主食", "小食", "饮品")
@freezed
class ProductComboGroup with _$ProductComboGroup {
  const factory ProductComboGroup({
    // required bool isRequired,
    // required String id,
    // required String name,
    // required String price,
    // // 定义该组的选择模式
    // required SelectionMode selectionMode,
    // required int stepperNum,
    // // 最小选择数
    // required int minSelections,
    // // 最大选择数
    // required int maxSelections,

    required String comboId,   
    required String comboName,    
    required int minSelection,  
    required int maxSelection,  
    required int type,  

    // 组内可选项
    @Default([]) List<ProductComboChoice> items,
  }) = _ProductComboGroup;

  factory ProductComboGroup.fromJson(Map<String, dynamic> json) =>
      _$ProductComboGroupFromJson(json);
}

// 为ProductComboGroup添加扩展方法，保持向后兼容
extension ProductComboGroupExtension on ProductComboGroup {
  /// 兼容旧代码的id字段
  String get id => comboId;
  
  /// 兼容旧代码的name字段
  String get name => comboName;
  
  /// 兼容旧代码的minSelections字段
  int get minSelections => minSelection;
  
  /// 兼容旧代码的maxSelections字段
  int get maxSelections => maxSelection;
  
  /// 根据type值计算选择模式
  SelectionMode get selectionMode {
    // 🎯 核心逻辑：根据最大选择数判断模式
  if (maxSelection > 1) {
    return SelectionMode.multiple; // 可以选择多个
  } else if (maxSelection == 1) {
    return SelectionMode.single;   // 只能选择一个
  } else {
    // maxSelection == 0 的情况，可能表示无限制或使用步进器
    return SelectionMode.stepper;
  }
  }
  
  /// 是否为必选组
  bool get isRequired => minSelection > 0;
  
  /// 步进器数量（暂时固定为1）
  int get stepperNum => 1;
  
  /// 价格字段（暂时返回空字符串）
  String get price => '';
}

@freezed
class ProductComboModifier with _$ProductComboModifier {
  const factory ProductComboModifier({
    required String id,
    required String name, 
  }) = _ProductComboModifier;

  factory ProductComboModifier.fromJson(Map<String, dynamic> json) =>
      _$ProductComboModifierFromJson(json);
}

/// 分组内的具体选项 (例如: "米饭", "辣椒炒肉", "可乐")
@freezed
class ProductComboChoice with _$ProductComboChoice {
  const factory ProductComboChoice({
    required ProductItem product,
    @Default(0.0) double additionalPrice, 
    @Default(0.0) double defaultEnable, 
    // 选择此项需要加/减的价格
    @Default(0) double priceDifference,
    // 是否默认选中
    // @Default(false) bool isDefault,
    // 该选项自身包含的规格组，用于实现“嵌套规格”
    @Default([]) List<ProductSpecGroup> specGroups,     // 对应接口的 defaultEnable
    
  }) = _ProductComboChoice;

  factory ProductComboChoice.fromJson(Map<String, dynamic> json) =>
      _$ProductComboChoiceFromJson(json);
}

// 为ProductComboChoice添加扩展方法，保持向后兼容
extension ProductComboChoiceExtension on ProductComboChoice {
  /// 兼容旧代码的priceDifference字段
  double get priceDifference => additionalPrice;
  
  /// 🔧 关键修复：根据defaultEnable值计算是否默认选中
  bool get isDefault => defaultEnable > 0;
}