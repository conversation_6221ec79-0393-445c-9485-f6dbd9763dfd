import 'package:freezed_annotation/freezed_annotation.dart';
import 'product_combo.dart';
import 'product_item.dart';

part 'product_combo_selection_state.freezed.dart';
part 'product_combo_selection_state.g.dart';

/// 套餐选择状态统一模型
/// 整合了所有套餐相关的状态数据
@freezed
class ProductComboSelectionState with _$ProductComboSelectionState {
  const factory ProductComboSelectionState({
    /// 基础选择数据
    /// Key: 商品ID (commodityId.toString()), Value: 选择的数量
    @Default({}) Map<String, int> quantities,
    
    /// 分组选择记录
    /// Key: 分组ID, Value: 该分组中选中的商品ID集合
    @Default({}) Map<String, Set<String>> groupSelections,
    
    /// 计算状态（由基础数据动态计算）
    /// Key: 分组ID, Value: 该分组的总选择数量
    @Default({}) Map<String, int> groupTotals,
    
    /// 动态最大值限制
    /// Key: 商品ID, Value: 该商品当前可选择的最大数量
    @Default({}) Map<String, int> dynamicMaxValues,
    
    /// 验证状态
    /// Key: 分组ID, Value: 该分组是否验证通过
    @Default({}) Map<String, bool> groupValidation,
    
    /// 整体验证状态
    @Default(false) bool isValid,
    
    /// 价格计算
    @Default(0.0) double additionalPrice,
    
    /// 嵌套配置支持（为未来扩展预留）
    /// Key: 商品ID, Value: 该商品的嵌套规格配置
    @Default({}) Map<String, Map<String, dynamic>> nestedConfigs,

     /// FormBuilder表单值存储
    /// Key: choiceKey, Value: 该商品的FormBuilder表单值
    /// 包含用户选择的所有规格项ID和数量
    @Default({}) Map<String, Map<String, dynamic>> formBuilderValues,
  }) = _ProductComboSelectionState;

  factory ProductComboSelectionState.fromJson(Map<String, dynamic> json) =>
      _$ProductComboSelectionStateFromJson(json);
}

/// 套餐选择状态扩展方法
extension ProductComboSelectionStateExtension on ProductComboSelectionState {
  /// 创建初始状态
  static ProductComboSelectionState initial() => const ProductComboSelectionState();
  
  /// 获取指定分组的选择数量
  int getGroupSelectionCount(String groupId) {
    return groupTotals[groupId] ?? 0;
  }
  
  /// 获取指定商品的选择数量
  int getProductQuantity(String productId) {
    return quantities[productId] ?? 0;
  }
  
  /// 检查指定分组是否有效
  bool isGroupValid(String groupId) {
    return groupValidation[groupId] ?? false;
  }
  
  /// 获取选择摘要文本
  String getSelectionSummary() {
    if (quantities.isEmpty) return '未选择';
    
    final totalItems = quantities.values.fold(0, (sum, qty) => sum + qty);
    return '已选择 $totalItems 项';
  }
  
  /// 生成套餐商品的组合键
  /// 格式：groupId_commodityId_groupIndex_choiceIndex
  /// 这样可以确保即使不同分组有相同的商品ID，也不会出现冲突
  static String generateComboChoiceKey({
    required String groupId,
    required int commodityId,
    required int groupIndex,
    required int choiceIndex,
  }) {
    return "${groupId}_${commodityId}_${groupIndex}_${choiceIndex}";
  }
  
  /// 解析组合键
  /// 返回Map包含：groupId, commodityId, groupIndex, choiceIndex
  static Map<String, dynamic> parseComboChoiceKey(String choiceKey) {
    final parts = choiceKey.split('_');
    if (parts.length != 4) {
      throw ArgumentError('Invalid choice key format: $choiceKey');
    }
    return {
      'groupId': parts[0],
      'commodityId': int.parse(parts[1]),
      'groupIndex': int.parse(parts[2]),
      'choiceIndex': int.parse(parts[3]),
    };
  }
  
  /// 根据分组ID和索引获取该分组的所有选择键
  List<String> getGroupChoiceKeys(String groupId, int groupIndex, List<ProductComboChoice> choices) {
    final keys = <String>[];
    for (int choiceIndex = 0; choiceIndex < choices.length; choiceIndex++) {
      final choice = choices[choiceIndex];
      final key = generateComboChoiceKey(
        groupId: groupId,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      keys.add(key);
    }
    return keys;
  }
  
  /// 检查指定组合键是否被选中
  bool isChoiceSelected(String choiceKey) {
    return (quantities[choiceKey] ?? 0) > 0;
  }
  
  /// 获取指定组合键的选择数量
  int getChoiceQuantity(String choiceKey) {
    return quantities[choiceKey] ?? 0;
  }

  /// === FormBuilder相关方法 ===
  
  /// 获取指定商品的FormBuilder值
  Map<String, dynamic> getFormBuilderValues(String choiceKey) {
    return formBuilderValues[choiceKey] ?? {};
  }
  
  /// 检查指定商品是否有FormBuilder记录（即是否已配置过规格）
  bool hasFormBuilderValues(String choiceKey) {
    final values = getFormBuilderValues(choiceKey);
    return values.isNotEmpty;
  }
  
  /// 检查指定商品是否需要显示Modifier按钮
  bool shouldShowModifierButton(String choiceKey, ProductComboChoice choice) {
    // 如果商品有规格组，就显示Modifier按钮
    return choice.specGroups.isNotEmpty;
  }
  
  /// 获取指定商品的有效规格选择（从FormBuilder值中提取）
  List<String> getSelectedSpecItemIds(String choiceKey) {
    final formValues = getFormBuilderValues(choiceKey);
    return List<String>.from(formValues['selectedItemIds'] ?? []);
  }
  
  /// 获取指定商品的规格项数量配置
  Map<String, int> getSpecItemQuantities(String choiceKey) {
    final formValues = getFormBuilderValues(choiceKey);
    final quantities = formValues['itemQuantities'] ?? {};
    return Map<String, int>.from(quantities);
  }
  
  /// 检查指定商品的规格配置是否有效
  bool isSpecConfigValid(String choiceKey) {
    final formValues = getFormBuilderValues(choiceKey);
    return formValues['isValid'] ?? false;
  }
  
  /// 获取指定商品的规格附加价格
  double getSpecAdditionalPrice(String choiceKey) {
    final formValues = getFormBuilderValues(choiceKey);
    return (formValues['additionalPrice'] ?? 0.0).toDouble();
  }
}