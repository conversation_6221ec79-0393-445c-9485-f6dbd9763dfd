import 'package:freezed_annotation/freezed_annotation.dart';

import 'product_item.dart';
import 'product_spec_group.dart';
import 'product_detail.dart';
import 'product_combo.dart';

part 'product_spec_page_data.freezed.dart';
part 'product_spec_page_data.g.dart';

/// 商品规格页面的完整数据模型 (上下文)
///
/// 封装了进入规格选择页面所需的所有静态数据，
/// 包括商品本身的信息和其拥有的所有规格组。
/// 这确保了UI层具有完成其工作所需的全部上下文。
@freezed
class ProductSpecPageData with _$ProductSpecPageData {
  const factory ProductSpecPageData({
    /// 当前正在配置的商品
    required ProductItem product,

    /// 该商品拥有的所有规格组
    required List<ProductSpecGroup> specGroups,
    /// 原因选项列表（如不加冰、少糖等）
    @Default([]) List<ReasonOption> reasonOptions,
    /// 套餐详情数据（仅当商品类型为套餐时有值）
    /// 
    /// 当 product.commodityType == 3 时，此字段包含完整的套餐配置信息，
    /// 包括套餐分组、选项、价格等。对于单品商品，此字段为 null。
    ProductCombo? combo,

    @Default([])  List<String> initialRemarkTags,  // 初始选中的备注标签
    @Default("") String initialCustomRemark,      // 初始自定义备注
  }) = _ProductSpecPageData;

  factory ProductSpecPageData.fromJson(Map<String, dynamic> json) =>
      _$ProductSpecPageDataFromJson(json);
}