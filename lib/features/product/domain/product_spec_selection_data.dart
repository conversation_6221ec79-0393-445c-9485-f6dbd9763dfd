import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_spec_selection_data.freezed.dart';
part 'product_spec_selection_data.g.dart';

/// 规格项显示模式枚举
/// 定义三种不同的规格展示UI方式
enum SpecDisplayMode {
  @JsonValue('text_only')
  textOnly,        // 纯文字模式：只显示规格名称
  
  @JsonValue('text_with_price')
  textWithPrice,   // 文字+价格模式：显示规格名称和额外价格
  
  @JsonValue('text_with_stepper')
  textWithStepper, // 文字+步进器模式：显示规格名称和数量选择器
}

/// 商品规格选择数据模型
/// 用于管理用户的规格选择状态和计算结果
@freezed
class ProductSpecSelectionData with _$ProductSpecSelectionData {
  const factory ProductSpecSelectionData({
    // == 修改开始 == 将key类型从int改为String以支持四元组复合键
    /// 选中的规格项复合键列表（用于单选/多选模式）
    /// 复合键格式：groupId_itemId_groupIndex_itemIndex
    @Default([]) List<String> selectedItemIds,
    /// 规格项数量映射（规格项复合键 -> 数量，用于步进器模式）
    /// 复合键格式：groupId_itemId_groupIndex_itemIndex
    @Default({}) Map<String, int> itemQuantities,
    // == 修改结束 ==
    /// 规格组选择状态（规格组ID -> 是否有效选择）
    @Default({}) Map<int, bool> groupValidation,
    /// 是否整体有效选择（所有必选组都已选择）
    @Default(false) bool isValid,
    /// 计算出的额外价格总和
    @Default(0.0) double additionalPrice,
    /// 可选固定备注标签列表
    @Default([]) List<String> selectedRemarkTags,
    /// 自定义备注
    @Default("") String customRemark,

  }) = _ProductSpecSelectionData;

  factory ProductSpecSelectionData.fromJson(Map<String, dynamic> json) =>
      _$ProductSpecSelectionDataFromJson(json);
}

/// ProductSpecSelectionData扩展方法
extension ProductSpecSelectionDataExtension on ProductSpecSelectionData {
  // == 新增开始 == 添加四元组复合键生成工具方法
  /// 生成四元组复合键：groupId_itemId_groupIndex_itemIndex
  /// [groupId] 规格组ID
  /// [itemId] 规格项ID  
  /// [groupIndex] 规格组序号(第几组，从0开始)
  /// [itemIndex] 规格项在组内的序号(从0开始)
  static String generateItemKey(int groupId, int itemId, int groupIndex, int itemIndex) {
    return "${groupId}_${itemId}_${groupIndex}_${itemIndex}";
  }
  
  /// 解析四元组复合键
  /// 返回Map包含：groupId, itemId, groupIndex, itemIndex
  static Map<String, int> parseItemKey(String itemKey) {
    final parts = itemKey.split('_');
    if (parts.length != 4) {
      throw ArgumentError('Invalid item key format: $itemKey');
    }
    return {
      'groupId': int.parse(parts[0]),
      'itemId': int.parse(parts[1]), 
      'groupIndex': int.parse(parts[2]),
      'itemIndex': int.parse(parts[3]),
    };
  }
  // == 新增结束 ==
  
  /// 转换为Map格式，用于状态管理
  Map<String, dynamic> toMap() {
    return {
      'selectedItemIds': selectedItemIds,
      'itemQuantities': itemQuantities,
      'additionalPrice': additionalPrice,
      'selectedRemarkTags': selectedRemarkTags,
      'customRemark': customRemark,
      'isValid': isValid,
      'groupValidation': groupValidation,
    };
  }
}

/// 单个规格项的选择状态
/// 用于在UI组件中传递状态信息
@freezed
class SpecItemSelectionState with _$SpecItemSelectionState {
  const factory SpecItemSelectionState({
    /// 是否选中
    @Default(false) bool isSelected,
    /// 当前数量（步进器模式使用）
    @Default(0) num quantity,
    /// 是否启用
    @Default(true) bool enabled,
  }) = _SpecItemSelectionState;

  factory SpecItemSelectionState.fromJson(Map<String, dynamic> json) =>
      _$SpecItemSelectionStateFromJson(json);
}