import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_detail.freezed.dart';
part 'product_detail.g.dart';

// 主实体：商品详情
@freezed
class ProductDetail with _$ProductDetail {
  const factory ProductDetail({
    required int commodityId,
    required String commodityName,
    @Default(1) int commodityType,
    String? commodityUnit,        // 商品单位
    String? currencyUnit,         // 货币单位  
    @Default(0) int priceChangeEnable,  // 是否允许改价
    @Default(0) int discountEnable,     // 是否允许折扣
    @Default(1) int specType,           // 规格类型：1=单规格，2=多规格
    required List<SpecGroup> specGroups,
    required List<SpecGroup> practiceGroups,
    required List<SpecGroup> addonsGroups,
    required List<ReasonOption> reasonOptions,
  }) = _ProductDetail;

  factory ProductDetail.fromJson(Map<String, dynamic> json) {
    return ProductDetail(
      commodityId: json['commodityId'] as int,
      commodityName: json['commodityName'] as String,
      commodityType: _parseToInt(json['commodityType']) ?? 1,
      commodityUnit: json['commodityUnit'] as String?,
      currencyUnit: json['currencyUnit'] as String?,
      priceChangeEnable: _parseToInt(json['priceChangeEnable']) ?? 0,
      discountEnable: _parseToInt(json['discountEnable']) ?? 0,
      specType: _parseToInt(json['specType']) ?? 1,
      specGroups: (json['specGroups'] as List<dynamic>? ?? [])
          .map((e) => SpecGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      practiceGroups: (json['practiceGroups'] as List<dynamic>? ?? [])
          .map((e) => SpecGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      addonsGroups: (json['addonsGroups'] as List<dynamic>? ?? [])
          .map((e) => SpecGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      reasonOptions: (json['reasonOptions'] as List<dynamic>? ?? [])
          .map((e) => ReasonOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    if (value is double) return value.toInt();
    return null;
  }
}

// 规格分组
@freezed
class SpecGroup with _$SpecGroup {
  const factory SpecGroup({
    required int id,
    required String name,
    @Default(1) int minSelection,
    @Default(1) int maxSelection,
    @Default(1) int allowDuplicate,
    required List<SpecItem> items,
  }) = _SpecGroup;

  factory SpecGroup.fromJson(Map<String, dynamic> json) =>
      _$SpecGroupFromJson(json);
}

// 规格项
@freezed
class SpecItem with _$SpecItem {
  const factory SpecItem({
    required int id,
    required String name,
    @Default(0.0) type,
    @Default(0.0) defaultEnable,
    @Default(0.0) price,
    @Default(0) int skuId,
  }) = _SpecItem;

  factory SpecItem.fromJson(Map<String, dynamic> json) => _$SpecItemFromJson(json);
}

// 原因选项（例如不加冰、少糖等）
@freezed
class ReasonOption with _$ReasonOption {
  const factory ReasonOption({
    required int id,
    required String name,
  }) = _ReasonOption;

  factory ReasonOption.fromJson(Map<String, dynamic> json) =>
      _$ReasonOptionFromJson(json);
}

// // 套餐信息 (如果存在)
// @freezed
// class ProductCombo with _$ProductCombo {
//   const factory ProductCombo({
//     // 根据你的实际套餐结构定义字段
//     required int id,
//     required String name,
//     // ... 其他套餐相关字段
//   }) = _ProductCombo;

//   factory ProductCombo.fromJson(Map<String, dynamic> json) =>
//       _$ProductComboFromJson(json);
// }