import 'product_combo.dart';
import 'product_combo_selection_state.dart';

/// 套餐验证服务
/// 负责验证套餐选择是否符合业务规则
class ProductComboValidator {
  
  /// 验证整个套餐选择
  static bool validateComboSelection(
    ProductCombo combo,
    ProductComboSelectionState state,
  ) {
    // 验证所有分组
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      if (!validateGroup(group, state, groupIndex)) {
        return false;
      }
    }
    return true;
  }
  
   /// 验证单个分组
  static bool validateGroup(
    ProductComboGroup group,
    ProductComboSelectionState state,
    int groupIndex,
  ) {
    // 计算分组总选择数量
    int groupTotal = 0;
    for (int choiceIndex = 0; choiceIndex < group.items.length; choiceIndex++) {
      final choice = group.items[choiceIndex];
      final choiceKey = ProductComboSelectionStateExtension.generateComboChoiceKey(
        groupId: group.id,
        commodityId: choice.product.commodityId,
        groupIndex: groupIndex,
        choiceIndex: choiceIndex,
      );
      groupTotal += state.quantities[choiceKey] ?? 0;
    }
    
    // 检查最小选择要求
    if (group.isRequired && groupTotal < group.minSelections) {
      return false;
    }
    
    // 检查最大选择限制
    if (group.maxSelections > 0 && groupTotal > group.maxSelections) {
      return false;
    }
    
    return true;
  }
  
  /// 计算分组验证状态
  static Map<String, bool> calculateGroupValidation(
    ProductCombo combo,
    ProductComboSelectionState state,
  ) {
    final validation = <String, bool>{};
    
    for (int groupIndex = 0; groupIndex < combo.groups.length; groupIndex++) {
      final group = combo.groups[groupIndex];
      validation[group.id] = validateGroup(group, state, groupIndex);
    }
    
    return validation;
  }
}