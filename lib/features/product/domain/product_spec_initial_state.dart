import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_spec_initial_state.freezed.dart';

/// ProductSpec初始状态数据模型
/// 用于编辑模式下的数据回显
@freezed
class ProductSpecInitialState with _$ProductSpecInitialState {
  const factory ProductSpecInitialState({
    required int quantity,
    String? remark,
    required bool isCombo,
    
    // 套餐相关
    Map<String, int>? comboSelections,
    Map<String, Map<String, dynamic>>? modifierConfigs,
    String? globalRemark,
    
    // 单品相关
    Set<String>? selectedSpecs,
    Map<String, int>? specQuantities,
  }) = _ProductSpecInitialState;
  
  // 套餐初始状态工厂方法
  factory ProductSpecInitialState.combo({
    required int quantity,
    required Map<String, int> comboSelections,
    required Map<String, Map<String, dynamic>> modifierConfigs,
    String? globalRemark,
  }) {
    return ProductSpecInitialState(
      quantity: quantity,
      isCombo: true,
      comboSelections: comboSelections,
      modifierConfigs: modifierConfigs,
      globalRemark: globalRemark,
    );
  }
  
  // 单品初始状态工厂方法
  factory ProductSpecInitialState.singleProduct({
    required int quantity,
    required Set<String> selectedSpecs,
    required Map<String, int> specQuantities,
    String? remark,
  }) {
    return ProductSpecInitialState(
      quantity: quantity,
      isCombo: false,
      selectedSpecs: selectedSpecs,
      specQuantities: specQuantities,
      remark: remark,
    );
  }
}

/// 编辑结果数据模型
@freezed
class ProductSpecEditResult with _$ProductSpecEditResult {
  const factory ProductSpecEditResult({
    required bool isCombo,
    required int quantity,
    String? remark,
    
    // 套餐结果
    Map<String, int>? comboSelections,
    Map<String, Map<String, dynamic>>? modifierConfigs,
    
    // 单品结果
    Set<String>? selectedSpecs,
    Map<String, int>? specQuantities,
    List<String>? practiceIds,
    List<Map<String, dynamic>>? addonsData,
  }) = _ProductSpecEditResult;
  
  factory ProductSpecEditResult.combo({
    required int quantity,
    required Map<String, int> comboSelections,
    required Map<String, Map<String, dynamic>> modifierConfigs,
    String? remark,
  }) {
    return ProductSpecEditResult(
      isCombo: true,
      quantity: quantity,
      comboSelections: comboSelections,
      modifierConfigs: modifierConfigs,
      remark: remark,
    );
  }
  
  factory ProductSpecEditResult.singleProduct({
    required int quantity,
    required Set<String> selectedSpecs,
    required Map<String, int> specQuantities,
    required List<String> practiceIds,
    required List<Map<String, dynamic>> addonsData,
    String? remark,
  }) {
    return ProductSpecEditResult(
      isCombo: false,
      quantity: quantity,
      selectedSpecs: selectedSpecs,
      specQuantities: specQuantities,
      practiceIds: practiceIds,
      addonsData: addonsData,
      remark: remark,
    );
  }
}