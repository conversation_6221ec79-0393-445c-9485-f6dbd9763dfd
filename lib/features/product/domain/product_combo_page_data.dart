import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/product/domain/product_item.dart';
import 'package:kpos/features/product/domain/product_combo.dart';

part 'product_combo_page_data.freezed.dart';

/// 为套餐规格选择页面提供完整的、不可变的数据上下文。
///
/// 这个类封装了套餐页面展示和操作所需的所有静态数据，
/// 确保UI层能够以一种清晰、一致的方式接收其依赖。
@freezed
class ProductComboPageData with _$ProductComboPageData {
  const factory ProductComboPageData({
    /// 套餐商品本身的核心信息。
    required ProductItem product,
    
    /// 套餐的完整结构定义，包含所有分组和选项。
    required ProductCombo combo,
  }) = _ProductComboPageData;
}