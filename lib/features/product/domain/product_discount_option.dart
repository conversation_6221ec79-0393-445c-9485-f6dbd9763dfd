import 'package:freezed_annotation/freezed_annotation.dart';
import '../../store/domain/store_prepared_reason_item.dart';

part 'product_discount_option.freezed.dart';
part 'product_discount_option.g.dart';

/// 折扣类型枚举
enum DiscountType {
  /// 打折（百分比）
  @JsonValue(1)
  percentage,
  
  /// 减价（固定金额）
  @JsonValue(2)
  fixedAmount,
}
// '5% off',
//     '80% off',
//     '70% off',
//     '50% off',
//     '-S\$5',
//     '-S\$10',
//     '-S\$20',
//     'Custom',
//     'Free',
/// 折扣选项数据模型
@freezed
class ProductDiscountOption with _$ProductDiscountOption {
  const factory ProductDiscountOption({
    /// 折扣类型：1-打折，2-减价
    required DiscountType discountType,
    /// 折扣显示文本
    required String discount,
    /// 折扣值（用于计算）
    required double value,
    /// 是否为自定义选项
    @Default(false) bool isCustom,
    /// 是否为免费选项
    @Default(false) bool isFree,
  }) = _ProductDiscountOption;

  factory ProductDiscountOption.fromJson(Map<String, dynamic> json) =>
      _$ProductDiscountOptionFromJson(json);
}

/// 折扣原因数据模型（复用Store模块的StorePreparedReasonItem）
typedef ProductDiscountReason = StorePreparedReasonItem;

/// 折扣选项工具类
class ProductDiscountOptionUtils {
  ProductDiscountOptionUtils._();
  
  /// 获取预设折扣选项列表
  static List<ProductDiscountOption> getPresetDiscountOptions() {
    //value暂时不用,只传discount
    return const [
      // 打折类型
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: '5% off',
        value: 5.0,
      ),
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: '80% off',
        value: 80.0,
      ),
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: '70% off',
        value: 70.0,
      ),
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: '50% off',
        value: 50.0,
      ),
      
      // 减价类型
      ProductDiscountOption(
        discountType: DiscountType.fixedAmount,
        discount: '-S\$5',
        value: 5.0,
      ),
      ProductDiscountOption(
        discountType: DiscountType.fixedAmount,
        discount: '-S\$10',
        value: 10.0,
      ),
      ProductDiscountOption(
        discountType: DiscountType.fixedAmount,
        discount: '-S\$20',
        value: 20.0,
      ),
      
      // 特殊选项
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: 'Free',
        value: 0.0,
        isFree: true,
      ),
      ProductDiscountOption(
        discountType: DiscountType.percentage,
        discount: 'Custom',
        value: 0.0,
        isCustom: true,
      ),
    ];
  }
  
  /// 根据折扣选项计算实际折扣金额
  static double calculateDiscountAmount(ProductDiscountOption option, double originalPrice) {
    if (option.isFree) return originalPrice;
    
    switch (option.discountType) {
      case DiscountType.percentage:
        return originalPrice * (option.value / 100);
      case DiscountType.fixedAmount:
        return option.value;
    }
  }

  /// 根据折扣选项和原价计算最终价格
  static double calculateFinalPrice(ProductDiscountOption option, double originalPrice, {num? customValue}) {
    if (option.isFree) return 0.0;
    
    // 🆕 处理自定义折扣
  if (option.isCustom && customValue != null) {
    if (customValue < 0) {
      // 负数表示固定金额折扣
      final finalPrice = originalPrice + customValue; // customValue是负数，所以用加法
      return finalPrice < 0 ? 0 : finalPrice;
    } else {
      // 正数表示百分比折扣
      return originalPrice * (1 - customValue / 100);
    }
  }
    
    // 原有的折扣计算逻辑保持不变
    final discountAmount = calculateDiscountAmount(option, originalPrice);
    
    switch (option.discountType) {
      case DiscountType.percentage:
        return originalPrice - discountAmount;
      case DiscountType.fixedAmount:
        final finalPrice = originalPrice - discountAmount;
        return finalPrice < 0 ? 0 : finalPrice;
    }
  }

   
   /// 🆕 新增支持自定义值的方法
  static double calculateFinalPriceWithCustom(ProductDiscountOption option, double originalPrice, {num? customValue}) {
    if (option.isFree) return 0.0;
    
    // 🆕 处理自定义折扣
    if (option.isCustom && customValue != null) {
      if (customValue < 0) {
        // 负数表示固定金额折扣
        final finalPrice = originalPrice + customValue; // customValue是负数，所以用加法
        return finalPrice < 0 ? 0 : finalPrice;
      } else {
        // 正数表示百分比折扣
        return originalPrice * (1 - customValue / 100);
      }
    }
    
    final discountAmount = calculateDiscountAmount(option, originalPrice);
    
    switch (option.discountType) {
      case DiscountType.percentage:
        return originalPrice - discountAmount;
      case DiscountType.fixedAmount:
        final finalPrice = originalPrice - discountAmount;
        return finalPrice < 0 ? 0 : finalPrice;
    }
  }
  
}

