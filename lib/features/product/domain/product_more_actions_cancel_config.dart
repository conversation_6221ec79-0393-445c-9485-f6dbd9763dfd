import 'package:flutter/material.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/order/domain/order_info.dart';

/// 取消操作类型枚举
enum CancelActionType {
  cancelDish,    // 退菜
  cancelOrder,   // 取消订单
}

/// 取消操作配置
class CancelActionConfig {
  final String title;                    // 弹窗标题
  final String buttonText;               // 确认按钮文案
  final CancelActionType actionType;     // 操作类型
  final bool showQuantitySelector;       // 是否显示数量选择
  final bool showPrintOption;           // 是否显示打印选项
  final Function(CancelActionData) onConfirm; // 确认回调
  final VoidCallback? onCancel;         // 取消回调

  const CancelActionConfig({
    required this.title,
    required this.buttonText,
    required this.actionType,
    this.showQuantitySelector = true,
    this.showPrintOption = true,
    required this.onConfirm,
    this.onCancel,
  });
}

/// 取消操作数据
class CancelActionData {
  final int quantity;                    // 退回数量
  final List<String> selectedReasons;   // 选中的原因列表
  final String customNotes;             // 自定义备注
  final bool printReturnOrder;          // 是否打印退单

  const CancelActionData({
    required this.quantity,
    required this.selectedReasons,
    required this.customNotes,
    required this.printReturnOrder,
  });
  
  @override
  String toString() {
    return 'CancelActionData(quantity: $quantity, reasons: $selectedReasons, notes: "$customNotes", print: $printReturnOrder)';
  }
}

/// 配置工厂类
class CancelActionConfigFactory {
  /// 创建退菜配置
  static CancelActionConfig createCancelDishConfig({
    required Function(CancelActionData) onConfirm,
    VoidCallback? onCancel,
  }) {
    return CancelActionConfig(
      title: 'Cancel dish',
      buttonText: 'Cancel dish',
      actionType: CancelActionType.cancelDish,
      showQuantitySelector: true,
      showPrintOption: true,
      onConfirm: onConfirm,
      onCancel: onCancel,
    );
  }
  
  /// 创建取消订单配置
  static CancelActionConfig createCancelOrderConfig({
    required Function(CancelActionData) onConfirm,
    VoidCallback? onCancel,
  }) {
    return CancelActionConfig(
      title: 'Cancel order',
      buttonText: 'Cancel order', 
      actionType: CancelActionType.cancelOrder,
      showQuantitySelector: false, // 取消订单不需要数量选择
      showPrintOption: true,
      onConfirm: onConfirm,
      onCancel: onCancel,
    );
  }
}