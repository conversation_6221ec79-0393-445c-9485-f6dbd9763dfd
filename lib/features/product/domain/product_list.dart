import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/product/domain/product_item.dart';

part 'product_list.freezed.dart';
part 'product_list.g.dart';

@freezed
class ProductList with _$ProductList {
  const factory ProductList({
    required int totalCount,
    required List<ProductItem> data,
  }) = _ProductList;

  factory ProductList.fromJson(Map<String, dynamic> json) =>
      _$ProductListFromJson(json);
}
