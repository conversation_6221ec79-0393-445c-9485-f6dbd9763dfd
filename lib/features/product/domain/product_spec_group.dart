import 'package:freezed_annotation/freezed_annotation.dart';

import 'product_spec_selection_data.dart';

part 'product_spec_group.freezed.dart';
part 'product_spec_group.g.dart';

/// 规格组类型枚举
enum ProductSpecGroupType {
  /// 普通规格（如尺寸、颜色等）
  @JsonValue('spec')
  spec,
  
  /// 做法规格（如烹饪方式、辣度等）
  @JsonValue('practice')
  practice,
  
  /// 加料规格（如配菜、调料等）
  @JsonValue('addons')
  addons,
}

/// 商品规格组数据模型
@freezed
class ProductSpecGroup with _$ProductSpecGroup {
  const factory ProductSpecGroup({
    required int id,                    // 规格组ID
    required String name,               // 规格组名称，如"做法"、"加料"
    required bool isRequired,           // 是否必选
    required bool isMultiSelect,        // 是否多选
    required int upperLimit,            // 上限
    required int lowerLimit,            // 下限
    @Default([]) List<int> selectedItemIds,  // 已选规格组ID
    @Default([]) List<SpecItem> items,  // 规格项列表
    @Default(ProductSpecGroupType.spec) ProductSpecGroupType groupType, //规格组类型，用于区分规格/做法/加料
  }) = _ProductSpecGroup;

  factory ProductSpecGroup.fromJson(Map<String, dynamic> json) =>
      _$ProductSpecGroupFromJson(json);
}

/// 规格项数据模型
@freezed
class SpecItem with _$SpecItem {
  const factory SpecItem({
    /// 规格项ID
    required int id,
    /// 规格项名称
    required String name,
    /// 是否是额外价格
    @Default(false) bool isAdditional,
    /// 额外价格
    @Default(0.0) double additionalPrice,
    /// 是否是计量商品
    @Default(false) bool isMeasured,
    /// 计量商品的值
    @Default(0.0) double measuredValue,
    /// 是否被选中
    @Default(false) bool isSelected,
    /// skuId
    @Default(0) int skuId,
  }) = _SpecItem;

  factory SpecItem.fromJson(Map<String, dynamic> json) =>
      _$SpecItemFromJson(json);
}

/// 规格项扩展方法
extension SpecItemExtension on SpecItem {
  /// 获取显示模式
  /// 根据规格项的属性自动判断应该使用哪种显示模式
  SpecDisplayMode get displayMode {
    // 文字+价格
    if (isAdditional && !isMeasured) {
      return SpecDisplayMode.textWithPrice;
    }
    // 计量(文字+步进器)
    if (isMeasured) {
      return SpecDisplayMode.textWithStepper;
    }
    // 其他情况使用纯文字模式
    return SpecDisplayMode.textOnly;
  }
}

/// 规格组扩展方法
extension ProductSpecGroupExtension on ProductSpecGroup {
  /// 获取推荐的显示模式
  SpecDisplayMode get recommendedDisplayMode {
    if (items.any((item) => item.isMeasured)) {
      return SpecDisplayMode.textWithStepper;
    }

    if (items.any((item) => item.isAdditional && !item.isMeasured)) {
      return SpecDisplayMode.textWithPrice;
    }

    return SpecDisplayMode.textOnly;
  }

  /// 验证选择是否有效
  bool validateSelection(ProductSpecSelectionData selectionData) {
    if (!isRequired) return true;

    if (recommendedDisplayMode == SpecDisplayMode.textWithStepper) {
      return selectionData.itemQuantities.values.any((qty) => qty > 0);
    } else {
      return selectionData.selectedItemIds.isNotEmpty;
    }
  }

  /// 计算规格组总价格
  double calculateGroupPrice(ProductSpecSelectionData selectionData) {
    double totalPrice = 0.0;

    // 计算选中项价格
  for (final itemKey in selectionData.selectedItemIds) {
    final parsedKey = ProductSpecSelectionDataExtension.parseItemKey(itemKey);
    final itemId = parsedKey['itemId']!;
    final item = items.firstWhere(
          (item) => item.id == itemId,
      orElse: () => throw StateError('规格项 $itemId 不存在于规格组 $name 中'),
    );
    totalPrice += item.additionalPrice;
  }

  // 计算数量相关价格
  for (final entry in selectionData.itemQuantities.entries) {
    final parsedKey = ProductSpecSelectionDataExtension.parseItemKey(entry.key);
    final itemId = parsedKey['itemId']!;
    final item = items.firstWhere(
          (item) => item.id == itemId,
      orElse: () => throw StateError('规格项 $itemId 不存在于规格组 $name 中'),
    );
    totalPrice += item.additionalPrice * entry.value;
  }
    return totalPrice;
  }

  /// 获取选中项目名称列表
List<String> getSelectedItemNames(ProductSpecSelectionData selectionData) {
  final selectedNames = <String>[];

  // 添加选中项名称
  for (final itemKey in selectionData.selectedItemIds) {
    final parsedKey = ProductSpecSelectionDataExtension.parseItemKey(itemKey);
    final itemId = parsedKey['itemId']!;
    final item = items.firstWhere(
          (item) => item.id == itemId,
      orElse: () => throw StateError('规格项 $itemId 不存在'),
    );
    selectedNames.add(item.name);
  }

  // 添加有数量的项目名称
  for (final entry in selectionData.itemQuantities.entries) {
    final quantity = entry.value;
    if (quantity > 0) {
      final parsedKey = ProductSpecSelectionDataExtension.parseItemKey(entry.key);
      final itemId = parsedKey['itemId']!;
      final item = items.firstWhere(
            (item) => item.id == itemId,
        orElse: () => throw StateError('规格项 $itemId 不存在'),
      );
      selectedNames.add('${item.name} x $quantity');
    }
  }

  return selectedNames;
}

  /// 检查是否有任何选择
  bool hasAnySelection(ProductSpecSelectionData selectionData) {
    return selectionData.selectedItemIds.isNotEmpty || selectionData.itemQuantities.values.any((qty) => qty > 0);
  }

  /// 获取选择摘要文本
  String getSelectionSummary(ProductSpecSelectionData selectionData) {
    if (!hasAnySelection(selectionData)) {
      return isRequired ? '请选择' : '未选择';
    }

    final selectedNames = getSelectedItemNames(selectionData);
    if (selectedNames.isEmpty) {
      return '未选择';
    }

    if (selectedNames.length > 3) {
      return '已选择 ${selectedNames.length} 项';
    }

    return selectedNames.join('、');
  }
}

/// 规格组工具类
class ProductSpecGroupUtils {
  ProductSpecGroupUtils._();

  /// 验证规格组数据有效性
  static bool validateSpecGroups(List<ProductSpecGroup> specGroups) {
    for (final group in specGroups) {
      if (group.id <= 0) return false;
      if (group.name.trim().isEmpty) return false;
      if (group.items.isEmpty) return false;

      for (final item in group.items) {
        if (item.id <= 0) return false;
        if (item.name.trim().isEmpty) return false;
        if (item.additionalPrice < 0) return false;
      }
    }

    return true;
  }

  /// 计算价格范围
  static Map<String, double> calculatePriceRange(List<ProductSpecGroup> specGroups) {
    double minPrice = 0.0;  // 最低价格
    double maxPrice = 0.0;  // 最高价格

    for (final group in specGroups) {
      if (group.isRequired) {
        // 必选项的最低和最高价格
        final minGroupPrice = group.items
            .map((item) => item.additionalPrice)
            .reduce((a, b) => a < b ? a : b);
        minPrice += minGroupPrice;

        final maxGroupPrice = group.items
            .map((item) => item.additionalPrice)
            .reduce((a, b) => a > b ? a : b);
        maxPrice += maxGroupPrice;
      } else {
        // 可选项只影响最高价格
        final allItemsPrice = group.items
            .map((item) => item.additionalPrice)
            .fold(0.0, (sum, price) => sum + price);
        maxPrice += allItemsPrice;
      }
    }

    return {
      'min': minPrice,
      'max': maxPrice,
    };
  }
}