
/// 套餐验证结果
/// 
/// 包含验证状态、错误信息和缺失配置的详细信息
class ProductComboValidationResult {
  /// 是否验证通过
  final bool isValid;
  
  /// 错误信息列表
  final List<String> errors;
  
  /// 缺失的Modifier配置映射
  /// Key: 商品名称, Value: 缺失的配置项列表
  final Map<String, List<String>> missingModifierConfigs;
  
  const ProductComboValidationResult({
    required this.isValid,
    required this.errors,
    required this.missingModifierConfigs,
  });
  
  /// 获取主要错误信息
  String get primaryErrorMessage {
    if (errors.isEmpty) return '';
    return errors.first;
  }
  
  /// 获取所有错误信息的合并文本
  String get allErrorsText {
    return errors.join('\n');
  }
  
  /// 是否有Modifier配置缺失
  bool get hasModifierConfigIssues {
    return missingModifierConfigs.isNotEmpty;
  }
  
  /// 复制并更新验证结果
  ProductComboValidationResult copyWith({
    bool? isValid,
    List<String>? errors,
    Map<String, List<String>>? missingModifierConfigs,
  }) {
    return ProductComboValidationResult(
      isValid: isValid ?? this.isValid,
      errors: errors ?? this.errors,
      missingModifierConfigs: missingModifierConfigs ?? this.missingModifierConfigs,
    );
  }
  
  @override
  String toString() {
    return 'ProductComboValidationResult(isValid: $isValid, errors: $errors, missingModifierConfigs: $missingModifierConfigs)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ProductComboValidationResult &&
        other.isValid == isValid &&
        _listEquals(other.errors, errors) &&
        _mapEquals(other.missingModifierConfigs, missingModifierConfigs);
  }
  
  @override
  int get hashCode {
    return isValid.hashCode ^
        errors.hashCode ^
        missingModifierConfigs.hashCode;
  }
  
  // 辅助方法：比较List是否相等
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
  
  // 辅助方法：比较Map是否相等
  bool _mapEquals<T, U>(Map<T, U>? a, Map<T, U>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final T key in a.keys) {
      if (!b.containsKey(key) || b[key] != a[key]) return false;
    }
    return true;
  }
}