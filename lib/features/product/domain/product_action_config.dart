import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';

/// 商品操作配置项
class ProductActionConfig {
  final String id;
  final Widget iconWidget;
  final String title;
  final Color? textColor;
  final Color? iconColor;
  final Widget? trailingIconWidget;
  final bool enabled;
  final ProductActionType actionType;
  final VoidCallback? onTap;
  final bool shouldCloseDialog; // 添加是否关闭弹窗的标志

  const ProductActionConfig({
    required this.id,
    required this.iconWidget,
    required this.title,
    this.textColor,
    this.iconColor,
    this.trailingIconWidget,
    this.enabled = true,
    required this.actionType,
    this.onTap,
    this.shouldCloseDialog = false, // 默认关闭弹窗
  });
}

/// 操作类型枚举
enum ProductActionType {
  packaging,      // 打包
  portionDish,    // 分菜
  commissionStaff, // 委托员工
  deleteDish,     // 删除菜品
  urgeDish,       // 催菜
  cancelUrgeDish, // 取消催菜
  returnDish,     // 退菜
  dishServed,     // 划菜
  cancelDishServed, // 取消划菜
  cancelOrder, // 取消划菜
}

/// 商品操作类型扩展
extension ProductActionTypeExtension on ProductActionType {
  String get displayName {
    switch (this) {
      case ProductActionType.packaging:
        return '打包';
      case ProductActionType.portionDish:
        return '分菜';
      case ProductActionType.commissionStaff:
        return '委托员工';
      case ProductActionType.deleteDish:
        return '删除菜品';
      case ProductActionType.urgeDish:
        return '催菜';
      case ProductActionType.cancelUrgeDish:
        return '取消催菜';
      case ProductActionType.returnDish:
        return '退菜';
      case ProductActionType.dishServed:
        return '划菜';
      case ProductActionType.cancelDishServed:
        return '取消划菜';
      case ProductActionType.cancelOrder:
        return '取消订单';
    }
  }

  Widget get iconWidget {
    switch (this) {
      case ProductActionType.packaging:
        return Assets.images.iconShoppingBag.svg(width: 24, height: 24,);
      case ProductActionType.portionDish:
        return Assets.images.iconPortionDish.svg(width: 24, height: 24,);
      case ProductActionType.commissionStaff:
        return Assets.images.iconCommissionStaff.svg(width: 24, height: 24,);
      case ProductActionType.deleteDish:
        return Assets.images.iconReverseLeftRed.svg(width: 24, height: 24,);
      case ProductActionType.urgeDish:
        return Assets.images.iconUrgeDish.svg(width: 24, height: 24,);
      case ProductActionType.cancelUrgeDish:
        return Assets.images.iconUrgeDish.svg(width: 24, height: 24,);
      case ProductActionType.returnDish:
        return Assets.images.iconCheckCircle.svg(width: 24, height: 24,);
      case ProductActionType.dishServed:
        return Assets.images.iconTrashRed.svg(width: 24, height: 24,);
      case ProductActionType.cancelDishServed:
        return Assets.images.iconTrashRed.svg(width: 24, height: 24,);
      case ProductActionType.cancelOrder:
        return Assets.images.iconCancelOrder.svg(width: 24, height: 24,); 
    }
  }

  Color? get textColor {
    switch (this) {
      case ProductActionType.deleteDish:
        return KPColors.textRedDefault;
      case ProductActionType.returnDish:
        return KPColors.textRedDefault;
        case ProductActionType.cancelOrder:
        return KPColors.textRedDefault;
      default:
        return KPColors.textGrayPrimary;
    }
  }

  Color? get iconColor {
    switch (this) {
      case ProductActionType.deleteDish:
        return Colors.red;
      default:
        return null;
    }
  }
}