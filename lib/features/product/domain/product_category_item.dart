import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_category_item.g.dart';
part 'product_category_item.freezed.dart';

@freezed
class ProductCategoryItem with _$ProductCategoryItem {
  const factory ProductCategoryItem({
    required int categoryId,
    required String categoryName,
    required String colorHex,
  }) = _ProductCategoryItem;

  factory ProductCategoryItem.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryItemFromJson(json);

}
