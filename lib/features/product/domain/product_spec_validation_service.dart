import 'package:freezed_annotation/freezed_annotation.dart';
import 'product_spec_group.dart';
import 'product_spec_selection_data.dart';

part 'product_spec_validation_service.freezed.dart';

/// 产品规格验证结果
@freezed
class ProductSpecValidationResult with _$ProductSpecValidationResult {
  const factory ProductSpecValidationResult({
    required bool isValid,
    required List<ProductSpecValidationError> errors,
  }) = _ProductSpecValidationResult;
  
  /// 创建有效结果
  factory ProductSpecValidationResult.valid() {
    return const ProductSpecValidationResult(
      isValid: true,
      errors: [],
    );
  }
  
  /// 创建无效结果
  factory ProductSpecValidationResult.invalid(List<ProductSpecValidationError> errors) {
    return ProductSpecValidationResult(
      isValid: false,
      errors: errors,
    );
  }
}

/// 产品规格验证错误
@freezed
class ProductSpecValidationError with _$ProductSpecValidationError {
  const factory ProductSpecValidationError({
    required String groupName,
    required ProductSpecValidationErrorType type,
    required int currentCount,
    required int requiredCount,
    String? customMessage,
  }) = _ProductSpecValidationError;
}

/// 验证错误类型
enum ProductSpecValidationErrorType {
  /// 未满足最低选择要求
  belowMinimum,
  /// 超过最大选择限制
  aboveMaximum,
  /// 自定义错误
  custom,
}

/// 组合键生成工具
extension ProductSpecSelectionDataExtension on ProductSpecSelectionData {
  static String generateItemKey(int groupId, int itemId, int groupIndex, int itemIndex) {
    return '${groupId}_${itemId}_${groupIndex}_${itemIndex}';
  }
}

/// 产品规格验证服务（领域服务）
/// 
/// 负责验证产品规格选择是否符合业务规则
/// 这是纯领域逻辑，不依赖于任何外部框架
class ProductSpecValidationService {
  
  /// 验证产品规格选择
  /// 
  /// [specGroups] 产品的所有规格组
  /// [selectionData] 当前的选择状态
  /// 
  /// 返回验证结果，包含是否有效和具体错误信息
  ProductSpecValidationResult validateSpecSelection({
    required List<ProductSpecGroup> specGroups,
    required ProductSpecSelectionData selectionData,
  }) {
    final List<ProductSpecValidationError> errors = [];
    
    // 验证每个规格组（带组序号）
    for (int groupIndex = 0; groupIndex < specGroups.length; groupIndex++) {
      final group = specGroups[groupIndex];
      final groupErrors = _validateSpecGroup(
        group, 
        selectionData,
        groupIndex, // 传递组序号
      );
      errors.addAll(groupErrors);
    }
    
    return errors.isEmpty 
        ? ProductSpecValidationResult.valid()
        : ProductSpecValidationResult.invalid(errors);
  }
  
  /// 验证单个规格组
  List<ProductSpecValidationError> _validateSpecGroup(
    ProductSpecGroup group,
    ProductSpecSelectionData selectionData,
    int groupIndex, // 组序号
  ) {
    final List<ProductSpecValidationError> errors = [];
    
    // 计算当前组的总选择数量
    final currentCount = _calculateGroupSelectionCount(
      group, 
      selectionData,
      groupIndex,
    );
    
    // 验证最低选择要求
    if (currentCount < group.lowerLimit) {
      errors.add(ProductSpecValidationError(
        groupName: group.name,
        type: ProductSpecValidationErrorType.belowMinimum,
        currentCount: currentCount,
        requiredCount: group.lowerLimit,
      ));
    }
    
    // 验证最大选择限制
    if (group.upperLimit > 0 && currentCount > group.upperLimit) {
      errors.add(ProductSpecValidationError(
        groupName: group.name,
        type: ProductSpecValidationErrorType.aboveMaximum,
        currentCount: currentCount,
        requiredCount: group.upperLimit,
      ));
    }
    
    return errors;
  }
  
  /// 计算规格组的选择数量
  int _calculateGroupSelectionCount(
    ProductSpecGroup group,
    ProductSpecSelectionData selectionData,
    int groupIndex, // 组序号
  ) {
    int count = 0;
    
    for (int itemIndex = 0; itemIndex < group.items.length; itemIndex++) {
      final item = group.items[itemIndex];
      final itemKey = ProductSpecSelectionDataExtension.generateItemKey(
        group.id, 
        item.id, 
        groupIndex, 
        itemIndex,
      );
      count += selectionData.itemQuantities[itemKey] ?? 0;
    }
    
    return count;
  }
  
  /// 检查规格组是否有效
  bool isGroupValid(
    ProductSpecGroup group,
    ProductSpecSelectionData selectionData,
    int groupIndex, // 新增组序号参数
  ) {
    final errors = _validateSpecGroup(
      group, 
      selectionData,
      groupIndex,
    );
    return errors.isEmpty;
  }
  
  /// 获取规格组的当前选择数量
  int getGroupSelectionCount(
    ProductSpecGroup group,
    ProductSpecSelectionData selectionData,
    int groupIndex, // 新增组序号参数
  ) {
    return _calculateGroupSelectionCount(
      group, 
      selectionData,
      groupIndex,
    );
  }
}

/// 验证错误扩展方法
extension ProductSpecValidationErrorExtension on ProductSpecValidationError {
  /// 生成用户友好的错误消息
  String get userFriendlyMessage {
    switch (type) {
      case ProductSpecValidationErrorType.belowMinimum:
        if (requiredCount == 1) {
          return '请选择"$groupName"';
        } else {
          return '"$groupName"至少需要选择${requiredCount}项，当前已选${currentCount}项';
        }
      case ProductSpecValidationErrorType.aboveMaximum:
        return '"$groupName"最多只能选择${requiredCount}项，当前已选${currentCount}项';
      case ProductSpecValidationErrorType.custom:
        return customMessage ?? '规格选择有误';
    }
  }
}