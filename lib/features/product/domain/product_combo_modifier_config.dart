import 'product_combo.dart';

/// 单个汉堡的配置数据模型
class ModifierConfigData {
  final String modifierId; //单汉堡的id
  final ProductComboChoice modifierChoice; //单汉堡的商品
  final bool isConfigured; //是否配置
  final Map<String, dynamic> formData; //表单数据(规格)

  const ModifierConfigData({
    required this.modifierId,
    required this.modifierChoice,
    this.isConfigured = false,
    this.formData = const {},
  });

  ModifierConfigData copyWith({
    String? modifierId,
    ProductComboChoice? modifierChoice,
    bool? isConfigured,
    Map<String, dynamic>? formData,
  }) {
    return ModifierConfigData(
      modifierId: modifierId ?? this.modifierId,
      modifierChoice: modifierChoice ?? this.modifierChoice,
      isConfigured: isConfigured ?? this.isConfigured,
      formData: formData ?? this.formData,
    );
  }

  /// 获取配置完成状态的文本描述
  String get configStatusText => isConfigured ? '已配置' : '未配置';
  
  /// 检查是否有表单数据
  bool get hasFormData => formData.isNotEmpty;
  
  /// 获取商品名称
  String get productName => modifierChoice.product.commodityName ?? 'Unknown Product';
  
  /// 转换为调试字符串
  @override
  String toString() {
    return 'ModifierConfigData(id: $modifierId, product: $productName, configured: $isConfigured)';
  }
  
  /// 重写相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ModifierConfigData &&
        other.modifierId == modifierId &&
        other.isConfigured == isConfigured;
  }
  
  @override
  int get hashCode => Object.hash(modifierId, isConfigured);
}