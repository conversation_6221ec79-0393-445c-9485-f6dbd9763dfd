import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_item.freezed.dart';
part 'product_item.g.dart';

/// 商品类型
enum ProductType {
  /// 普通商品
  @JsonValue(0)
  normal,

  /// 套餐商品
  @JsonValue(1)
  combo,
}
@freezed
class ProductItem with _$ProductItem {
  const factory ProductItem({
    required int commodityId,
    required String commodityName,
    /// 商品类型：1=计量商品，2=称重商品，3=套餐，4=加料，5=餐盒
    required int commodityType,  
    required String commodityImagePath,
    /// 能否修改价格
    required int priceChangeEnable,
    required int standaloneSaleEnable,
    required int specType,
    required double? price,
    required int categoryId,
    required int discountEnable,
    int? commoditySkuId,
    String? imageUrl,
    @Default("") String commodityUnit,
    @Default("") String currencyUnit,
    @Default(ProductType.normal) ProductType productType,
    @Default(0) int cartCount,
    @Default(0) int soldOut,

    // 0.4促销活动需求新增
    @Default(false) bool hasActivePromotion, //是否有生效的促销
    @Default([]) List<Map<String, dynamic>> promotionActives, // 参与的促销活动ID列表
    
    

  }) = _ProductItem;

  factory ProductItem.fromJson(Map<String, dynamic> json) =>
      _$ProductItemFromJson(json);
}
