import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:kpos/features/cart/domain/cart_add_request.dart';
import 'package:kpos/features/cart/domain/cart_order_item.dart';
import 'package:kpos/features/cart/domain/create_order_item.dart';
import 'package:kpos/features/cart/domain/order_draft_item.dart';
import 'package:kpos/features/store/domain/service_fee_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/remote_service/api_interface.dart';
import '../domain/dining_style_item.dart';
import 'cart_endpoint.dart';

part 'cart_intranet_repository.g.dart';

class CartIntranetRepository {
  final ApiInterface _apiService;

  CartIntranetRepository({required apiService}) : _apiService = apiService;

  Future<CartOrderItem> getCart(int orderId) async {
    return await _apiService.getData(
      endpoint: CartEndpoint.getCart,
      queryParams: {"orderId": orderId},
      converter: CartOrderItem.fromJson,
    );
  }

  Future<CreateOrderItem> createOrder({String? tableNumber}) async {
    return await _apiService.getData(
      endpoint: CartEndpoint.createOrder,
      queryParams: {"tableNumber": tableNumber},
      converter: CreateOrderItem.fromJson,
    );
  }

  Future<void> addToCart(CartAddRequest request) async {
    final data = deepToJson(request);
    await _apiService.postData(endpoint: CartEndpoint.addCart, data: data);
  }

  Future<void> deleteCartProduct(int orderItemId) async {
    await _apiService.postData(endpoint: CartEndpoint.deleteOrderItem, data: {"orderItemId": orderItemId});
  }

  Future<void> clearCart(int orderId) async {
    await _apiService.postData(endpoint: CartEndpoint.clearCart, data: {"orderId": orderId});
  }

  dynamic deepToJson(dynamic value) {
    if (value is Map) {
      final result = <String, dynamic>{};
      value.forEach((k, v) {
        if (k == 'runtimeType') return;
        result[k] = deepToJson(v);
      });
      return result;
    } else if (value is List) {
      return value.map(deepToJson).toList();
    } else if (value is Object && value is! num && value is! String && value is! bool) {
      // 尝试调用 toJson
      try {
        final json = (value as dynamic).toJson();
        return deepToJson(json);
      } catch (_) {
        return value;
      }
    } else {
      return value;
    }
  }

  Future<List<DiningStyleItem>> getDiningStyleItemList() async {
    return await _apiService.getListData(
        endpoint: CartEndpoint.getDiningStyleList, converter: DiningStyleItem.fromJson);
  }

  Future<void> setDiningMethod({required int orderId, required int method}) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setDiningMethod,
        data: {"orderId": orderId, "method": method});
  }

  Future<void> setDiningNumber({required int orderId, required int number}) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setDiningNumber,
        data: {"orderId": orderId, "number": number});
  }

  Future<void> setOrderRemark({required int orderId, required String remark}) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setOrderRemark,
        data: {"orderId": orderId, "remark": remark});
  }

  Future<void> setOrderDisCount({
    required int orderId,
    required double discount,
    required int discountType,//1-打折，2-减价,如果打折是100%就是免费
    required String reason,
  }) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setOrderDiscount,
        data: {"orderId": orderId, "discount": discount, "reason": reason,"discountType":discountType});
  }

  Future<void> saveDraft(int orderId) async {
    await _apiService.postData(endpoint: CartEndpoint.saveDraft, data: {"orderId": orderId});
  }

  Future<void> pickDraft(int orderId) async {
    await _apiService.postData(endpoint: CartEndpoint.deleteDraft, data: {"orderId": orderId});
  }

  Future<List<OrderDraftItem>> getOrderDraftList() async {
    return await _apiService.getListData(
        endpoint: CartEndpoint.getDrafts, converter: OrderDraftItem.fromJson);
  }

  //修改某个商品或套餐的数量
  Future<void> setCartItemQuantity({required int orderItemId, required double quantity}) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setOrderItemQuantity,
        data: {"orderItemId": orderItemId, "quantity": quantity});
  }

  //设置餐牌号
  Future<void> setMealVoucher({required int orderId, required String mealVoucher}) async {
    await _apiService.postData(
        endpoint: CartEndpoint.setOrderMealVoucher,
        data: {"orderId": orderId, "mealVoucher": mealVoucher});
  }

  Future<List<Map<String, dynamic>>> getCartPrice() async {
    List<Map<String, dynamic>> priceMap = [
      {'title': 'Subtotal', 'price': 45.00},
      {'title': 'Discount', 'price': -5.52},
      {'title': 'Tax（5%）', 'price': 4.50},
    ];
    // List<CartItem> cartProducts = await getCart();
    double totalPrice = 0.0;
    // for (var cartProduct in cartProducts) {
    //   double productPrice = cartProduct.price;
    //   totalPrice += productPrice;
    // }
    totalPrice = priceMap.fold<double>(0, (sum, item) => sum + item['price']);
    priceMap.removeWhere((e) => e['price'] == 0);
    priceMap
        .add({'title': 'Total', 'price': double.parse(totalPrice.toStringAsFixed(2))});
    return priceMap;
  }

  Future<List<ServiceFeeItem>> getServiceFeeItemList({required int orderId}) async {
    return await _apiService.getListData(
      endpoint: CartEndpoint.getOrderServiceFee,
      queryParams: {"orderId": orderId},
      converter: ServiceFeeItem.fromJson,
    );
  }

  Future<void> setOrderServiceFee({required int orderId, required String idStr, String? reason}) async {
    final data = {
      "orderId": orderId,
      "idStr": idStr,
    };
    if (reason != null && reason.isNotEmpty) {
      data["reason"] = reason;
    }
    await _apiService.postData(
      endpoint: CartEndpoint.setOrderServiceFee,
      data: data,
    );
  }
}

@riverpod
CartIntranetRepository cartIntranetRepository(Ref ref) {
  return CartIntranetRepository(apiService: ref.watch(kposApiIntranetServiceProvider));
}

@riverpod
Future<List<Map<String, dynamic>>> cartPrice(Ref ref) {
  return ref.watch(cartIntranetRepositoryProvider).getCartPrice();
}
