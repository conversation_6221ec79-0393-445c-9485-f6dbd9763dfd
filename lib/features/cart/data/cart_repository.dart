import 'package:kpos/common/services/networking/remote_service/api_interface.dart';
import 'package:kpos/common/services/networking/remote_service/kpos_api_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

part 'cart_repository.g.dart';

class CartRepository {
  final ApiInterface _apiService;

  CartRepository({required apiService}) : _apiService = apiService;

}

@riverpod
CartRepository cartRepository(Ref ref) {
  return CartRepository(apiService: ref.watch(kposApiServiceProvider));
}