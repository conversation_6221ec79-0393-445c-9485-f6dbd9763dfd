abstract class CartLocalRepositoryInterface {
  Future<List<Map<String, dynamic>>> getDiningStyleList();

  Future<bool> addCart(
      int tenantId,
      int storeId,
      int orderId,
      int productId,
      double quantity, {
        String? remark,
        double? updatePrice,
        int? discountType,
        double? discount,
        String? discountReason,
        Map<String, dynamic>? productConfigMap,
        List<dynamic>? comboConfig,
      });

  Future<bool> updateCart(
      int tenantId,
      int storeId,
      int orderId,
      int productId,
      double quantity,
      int orderItemId,
      {
        String? remark,
        double? updatePrice,
        int? discountType,
        double? discount,
        String? discountReason,
        Map<String, dynamic>? productConfigMap,
        List<dynamic>? comboConfig,
      });

  Future<Map<String, dynamic>> getCart(int orderId);

  Future<bool> clearCart(int orderId);

  Future<void> saveDraft(int orderId);

  Future<void> deleteDraft(int orderId);

  Future<List<Map<String,dynamic>>> getDrafts();

  Future<void> setDiningMethod(int orderId,int method);

  Future<void> setDiningNumber(int orderId,int number);

  Future<void> setOrderRemark(int orderId,String remark);

  Future<void> setOrderDiscount(int orderId,int discountType,double discount,String reason);

  Future<void> setOrderMealVoucher(int orderId,String mealVoucher);

  Future<void> setOrderItemQuantity(int orderItemId,double quantity);

  Future<void> setOrderServiceFee(int orderId, int tenantId, String idStr,String? reason);

  Future<List<Map<String, dynamic>>> getOrderServiceFee({
    required int orderId,
    required int tenantId,
  });

  Future<void> setOrderItemDiscount(String orderItemIdStr,int discountType,double discount,String reason);
}