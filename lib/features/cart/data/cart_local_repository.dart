import 'dart:ffi';

import 'package:drift/drift.dart';
import 'package:kpos/common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_exception.dart';
import 'package:kpos/features/cart/data/cart_local_repository_interface.dart';
import 'package:kpos/common/utils/snowflake_id_generator.dart';

import '../../../common/services/database/app_database.dart';

class CartLocalRepository implements CartLocalRepositoryInterface {
  final AppDatabase db;

  CartLocalRepository(this.db);

  @override
  Future<List<Map<String, dynamic>>> getDiningStyleList() async {
    final rows = await db.select(db.diningStyle).get();
    final result = rows.map((row) => {
      'saleType': row.saleType,
      'saleTypeName': row.saleTypeName,
    }).toList();
    return result;
  }

  @override
  Future<Map<String, dynamic>> getCart(int orderId) async {
    final Map<String, dynamic> data = {};

    /// 查询订单数据
    final order = await db.customSelect(
      '''
    SELECT
      o.order_id,
      o.dining_method,
      o.dining_number,
      o.table_number,
      o.meal_voucher,
      o.kp_discount_type,
      o.kp_discount_amount,
      o.kp_discount_reason,
      o.remark,
      o.order_state
    FROM
      orders o
    WHERE
      o.order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    data["orderId"] = order.read<int?>('order_id') ?? 0;
    data["diningMethod"] = order.read<int?>('dining_method') ?? 0;
    data["diningNumber"] = order.read<int?>('dining_number') ?? 0;
    data["tableNumber"] = order.read<String?>('table_number') ?? '';
    data["mealVoucher"] = order.read<String?>('meal_voucher') ?? '';
    data["remark"] = order.read<String?>('remark') ?? '';
    data["discount"] = order.readNullable<double>('kp_discount_amount') ?? 0;
    data["discountType"] = order.readNullable<int>('kp_discount_type') ?? 0;
    data["discountReason"] = order.readNullable<String>('kp_discount_reason');
    data["orderStatus"] = order.read<int>('order_state');

    /// 查询购物车商品和加料
    final orderProducts = await db.customSelect(
      '''
    SELECT
      oi.order_item_id, 
	    oi.commodity_id,
	    oi.commodity_name,
	    oi.parent_order_item_id,
	    oi.commodity_unit,
	    oi.commodity_type,
	    oi.quantity,
	    oi.commodity_sku_id,
	    oi.return_flag,
	    oi.return_reason,
	    oi.kp_urge_dish_flag,
	    oi.kp_urge_dish_time,
	    oi.kp_served_dish_flag,
	    oi.kp_served_dish_time,
	    COALESCE(oi.price, 0) AS price,
	    CASE 
    WHEN oi.commodity_sku_name IS NULL OR oi.commodity_sku_name = '' THEN 1
    ELSE 2
  END AS spec_type,
	    oi.remark
    FROM
      order_item oi
    WHERE
      oi.order_id = ? AND oi.deleted = 0
    ''',
      variables: [Variable.withInt(orderId)],
    ).get();

    /// 查询做法
    final orderItemIds =
        orderProducts.map((r) => r.read<int>('order_item_id')).toList();
    final practicesResult = orderItemIds.isEmpty
        ? <QueryRow>[]
        : await db.customSelect(
            '''
          SELECT 
            oip.order_item_id,
            oip.order_item_practice_id,
            oip.commodity_practice_id,
            oip.commodity_practice_name,
            oip.price
          FROM
            order_item_practice oip
          WHERE
            oip.order_item_id IN (${orderItemIds.join(",")}) AND oip.deleted = 0
          ''',
          ).get();

    /// 做法分组
    final Map<int, List<Map<String, dynamic>>> practicesByOrderItemId = {};
    for (var p in practicesResult) {
      final itemId = p.read<int>('order_item_id');
      practicesByOrderItemId.putIfAbsent(itemId, () => []).add({
        "orderItemPracticeId": p.read<int>('order_item_practice_id'),
        "commodityPracticeId": p.read<int>('commodity_practice_id'),
        "commodityPracticeName": p.read<String>('commodity_practice_name'),
        "price": p.read<double>('price'),
      });
    }

    /// 查询顶级商品
    final topLevelItems = orderProducts
        .where((r) => r.read<int>('parent_order_item_id') == 0)
        .toList();

    final List<Map<String, dynamic>> cartItemList = topLevelItems.map((r) {
      final orderItemId = r.read<int>('order_item_id');
      final commodityType = r.read<int>('commodity_type');

      if (commodityType == 3) {
        // 套餐 (不返回 practiceList)
        return {
          "orderItemId": orderItemId,
          "commodityId": r.read<int>('commodity_id'),
          "commodityName": r.read<String>('commodity_name'),
          "commodityType": commodityType,
          "quantity": r.read<double>('quantity'),
          "price": r.read<double>('price'),
          "commoditySkuId": r.read<int>('commodity_sku_id'),
          "isUrge":r.readNullable<int>('kp_urge_dish_flag') ?? 0,
          "urgeTime":r.readNullable<int>('kp_urge_dish_time') ?? 0,
          "isReturn":r.readNullable<int>('return_flag') ?? 0,
          "returnReason":r.readNullable<String>('return_reason') ?? '',
          "isServed":r.readNullable<int>('kp_served_dish_flag') ?? 0,
          "productList": buildProductList(
              orderProducts, practicesByOrderItemId, orderItemId),
        };
      } else {
        // 加料商品
        final addons = findChildren(orderProducts, orderItemId).where((item) {
          final type = item.read<int>('commodity_type');
          return type == 4;
        });

        return {
          "orderItemId": orderItemId,
          "commodityId": r.read<int>('commodity_id'),
          "commodityName": r.read<String>('commodity_name'),
          "commodityUnit": r.read<String>('commodity_unit'),
          "commodityType": commodityType,
          "price": r.read<double>('price'),
          "quantity": r.read<double>('quantity'),
          "specType": r.read<int>('spec_type'),
          "commoditySkuId": r.read<int>('commodity_sku_id'),
          "remark":r.readNullable<String>('remark') ?? '',
          "isUrge":r.readNullable<int>('kp_urge_dish_flag') ?? 0,
          "urgeTime":r.readNullable<int>('kp_urge_dish_time') ?? 0,
          "isReturn":r.readNullable<int>('return_flag') ?? 0,
          "returnReason":r.readNullable<String>('return_reason') ?? '',
          "isServed":r.readNullable<int>('kp_served_dish_flag') ?? 0,
          "practiceList": practicesByOrderItemId[orderItemId] ?? [],
          "addonsList": addons.map((a) {
            final addonId = a.read<int>('order_item_id');
            return {
              "orderItemId": addonId,
              "commodityId": a.read<int>('commodity_id'),
              "commodityName": a.read<String>('commodity_name'),
              "commodityUnit": a.read<String>('commodity_unit'),
              "commodityType": a.read<int>('commodity_type'),
              "price": a.read<double>('price'),
              "quantity": a.read<double>('quantity'),
              "commoditySkuId": a.readNullable<int>('commodity_sku_id') ?? 0,
              "isUrge":a.readNullable<int>('kp_urge_dish_flag') ?? 0,
              "urgeTime":a.readNullable<int>('kp_urge_dish_time') ?? 0,
              "isReturn":a.readNullable<int>('return_flag') ?? 0,
              "returnReason":a.readNullable<String>('return_reason') ?? '',
              "isServed":a.readNullable<int>('kp_served_dish_flag') ?? 0,
            };
          }).toList(),
        };
      }
    }).toList();

    data["cartItemList"] = cartItemList;
    return data;
  }

  /// 查询指定父项的子项
  List<QueryRow> findChildren(List<QueryRow> orderProducts, int parentId) =>
      orderProducts.where((r) => r.read<int>('parent_order_item_id') == parentId).toList();

  /// 构造套餐下的商品列表
  List<Map<String, dynamic>> buildProductList(
      List<QueryRow> orderProducts,
      Map<int, List<Map<String, dynamic>>> practicesByOrderItemId,
      int parentId) {
    final products = findChildren(orderProducts, parentId).where((r) {
      final type = r.read<int>('commodity_type');
      return type == 1 || type == 2;
    }).toList();

    return products.map((r) {
      final orderItemId = r.read<int>('order_item_id');
      final addons = findChildren(orderProducts, orderItemId).where((item) {
        final type = item.read<int>('commodity_type');
        return type == 4;
      });

      return {
        "orderItemId": orderItemId,
        "commodityId": r.read<int>('commodity_id'),
        "commodityName": r.read<String>('commodity_name'),
        "commodityType": r.read<int>('commodity_type'),
        "commodityUnit": r.read<String>('commodity_unit'),
        "price": r.read<double>('price'),
        "quantity": r.read<double>('quantity'),
        "specType": r.read<int>('spec_type'),
        "commoditySkuId": r.read<int>('commodity_sku_id'),
        "isUrge":r.readNullable<int>('kp_urge_dish_flag') ?? 0,
        "urgeTime":r.readNullable<int>('kp_urge_dish_time') ?? 0,
        "isReturn":r.readNullable<int>('return_flag') ?? 0,
        "returnReason":r.readNullable<String>('return_reason') ?? '',
        "isServed":r.readNullable<int>('kp_served_dish_flag') ?? 0,
        "practiceList": practicesByOrderItemId[orderItemId] ?? [],
        "addonsList": addons.map((a) {
          final addonId = a.read<int>('order_item_id');
          return {
            "orderItemId": addonId,
            "commodityId": a.read<int>('commodity_id'),
            "commodityName": a.read<String>('commodity_name'),
            "commodityType": a.read<int>('commodity_type'),
            "commodityUnit": a.read<String>('commodity_unit'),
            "price": a.read<double>('price'),
            "quantity": a.read<double>('quantity'),
            "commoditySkuId": a.readNullable<int>('commodity_sku_id') ?? 0,
            "isUrge":a.readNullable<int>('kp_urge_dish_flag') ?? 0,
            "urgeTime":a.readNullable<int>('kp_urge_dish_time') ?? 0,
            "isReturn":a.readNullable<int>('return_flag') ?? 0,
            "returnReason":a.readNullable<String>('return_reason') ?? '',
            "isServed":a.readNullable<int>('kp_served_dish_flag') ?? 0,
          };
        }).toList(),
      };
    }).toList();
  }

  @override
  Future<bool> addCart(
      int tenantId,
      int storeId,
      int orderId,
      int productId,
      double quantity, {
        String? remark,
        double? updatePrice,
        int? discountType,
        double? discount,
        String? discountReason,
        Map<String, dynamic>? productConfigMap,
        List<dynamic>? comboConfig,
      }) async {
    if (productConfigMap == null && comboConfig == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }
    final order = await db.customSelect(
      '''
      SELECT *
      FROM orders
      WHERE order_id = ?''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (order == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    ///如果是加入商品
    if (productConfigMap != null) {
      List<OrderItemPracticeCompanion> practiceList = [];
      final List<OrderItemCompanion> addonsList = [];
      OrderItemCompanion currentOrderItem;
      ///1、增加orderItem数据
      final int commoditySkuId = productConfigMap['commoditySkuId'];
      final orderItemId = SnowflakeIdGenerator().nextId();
      final orderItemCompanion = await getOrderItem(
          orderItemId, orderId, storeId, productId, tenantId, quantity,
          remark: remark,
          updatePrice: updatePrice,
          discountType: discountType,
          discount: discount,
          discountReason: discountReason,commoditySkuId: commoditySkuId);
      currentOrderItem = orderItemCompanion;

      ///2、增加订单做法数据
      final List<Map<String, dynamic>> practiceConfigList =
      (productConfigMap['practiceConfigList'] as List<dynamic>? ?? []).cast<Map<String, dynamic>>();
      if (practiceConfigList.isNotEmpty) {
        practiceList = await getOrderPracticeItem(
            orderId, orderItemId, tenantId, practiceConfigList);
      }

      ///3、增加订单加料数据
      final List<Map<String, dynamic>> addonsConfigList =
      (productConfigMap['addonsConfigList'] as List<dynamic>? ?? []).cast<Map<String, dynamic>>();
      if (addonsConfigList.isNotEmpty) {
        for (var item in addonsConfigList) {
          final addonsOrderIemId = SnowflakeIdGenerator().nextId();
          final unitQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
          final addonsItem = await getOrderItem(
              addonsOrderIemId,
              orderId,
              storeId,
              item["addonsCommodityId"],
              tenantId,
              unitQuantity,
              remark:item["remark"],
              parentOrderItemId: orderItemId);
          addonsList.add(addonsItem);
        }
      }

      try {
        await db.transaction(() async {
          await db.batch((batch) {
            batch.insert(db.orderItem, currentOrderItem);
            if (practiceList.isNotEmpty) {
              batch.insertAll(db.orderItemPractice, practiceList);
            }
            if (addonsList.isNotEmpty) {
              batch.insertAll(db.orderItem, addonsList);
            }
          });
        });
        return true; // 成功
      } catch (e, st) {
        print(e);
        return false; // 失败
      }

    } else {
      OrderItemCompanion currentOrderItem;
      ///1、增加套餐数据到orderItem
      final comboOrderItemId = SnowflakeIdGenerator().nextId();
      final orderItemCompanion = await getOrderItem(
          comboOrderItemId, orderId, storeId, productId, tenantId, quantity,
          remark: remark,
          updatePrice: updatePrice,
          discountType: discountType,
          discountReason: discountReason,
          discount: discount);
      currentOrderItem = orderItemCompanion;

      ///2、增加套餐商品数据到orderItem,这时候parentOrderItemId是套餐orderItem的id
      /// 所有套餐商品数组
      final List<OrderItemCompanion> comboOrderItemList = [];
      /// 所有的套餐商品做法数组
      final List<OrderItemPracticeCompanion> orderPracticeList = [];
      /// 所有的套餐加料做法数组
      final List<OrderItemCompanion> orderAddonsList = [];
      final comboList = (comboConfig as List)
          .map((e) => e as Map<String, dynamic>)
          .toList();
      for (var item in comboList) {
        /// 拼接套餐商品数据
        final int commoditySkuId = item['commoditySkuId'];
        final orderItemId = SnowflakeIdGenerator().nextId();
        final productQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
        final comboOrderItem = await getOrderItem(orderItemId, orderId, storeId,
            item["commodityId"], tenantId, productQuantity,
            remark: item["remark"],
            comboGroupId: item["comboGroupingId"],
            parentOrderItemId: comboOrderItemId,
            commoditySkuId: commoditySkuId);
        comboOrderItemList.add(comboOrderItem);

        /// 拼接套餐商品做法数据
        final practiceListRaw = item["practiceConfigList"];
        List<Map<String, dynamic>>? practiceList;
        if (practiceListRaw != null && practiceListRaw is List) {
          practiceList = practiceListRaw.map((e) => e as Map<String, dynamic>).toList();
        }
        if (practiceList != null &&
            practiceList.isNotEmpty) {
          final orderPractices = await getOrderPracticeItem(
              orderId, orderItemId, tenantId, practiceList);
          orderPracticeList.addAll(orderPractices);
        }

        /// 拼接套餐商品加料数据
        final addonsListRaw = item["addonsConfigList"];
        List<Map<String, dynamic>>? addonsList;
        if (addonsListRaw != null && addonsListRaw is List) {
          addonsList = addonsListRaw.map((e) => e as Map<String, dynamic>).toList();
        }
        if (addonsList != null &&
            addonsList.isNotEmpty) {
          for (var item in addonsList) {
            final addonsOrderItemId = SnowflakeIdGenerator().nextId();
            final addonsQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
            final addonsItem = await getOrderItem(
                addonsOrderItemId,
                orderId,
                storeId,
                item["addonsCommodityId"],
                tenantId,
                addonsQuantity,
                remark:item["remark"],
                parentOrderItemId:orderItemId);
            orderAddonsList.add(addonsItem);
          }
        }
      }
      try {
        await db.transaction(() async {
          await db.batch((batch) {
            batch.insert(db.orderItem, currentOrderItem);
            if (comboOrderItemList.isNotEmpty) {
              batch.insertAll(db.orderItem, comboOrderItemList);
            }
            if (orderPracticeList.isNotEmpty) {
              batch.insertAll(db.orderItemPractice, orderPracticeList);
            }
            if (orderAddonsList.isNotEmpty) {
              batch.insertAll(db.orderItem, orderAddonsList);
            }
          });
        });
        return true; // 成功
      } catch (e, st) {
        print('❌ 插入套餐到购物车失败: $e\n$st');
        return false;
      }
    }
  }

  /// 配置订单做法数据
  Future<List<OrderItemPracticeCompanion>> getOrderPracticeItem(
      int orderId,
      int orderItemId,
      int tenantId,
      List<Map<String, dynamic>> practiceList) async {
    final List<OrderItemPracticeCompanion> practiceCompList = [];
    for (var item in practiceList) {
      final result = await (db.select(db.productPractice)
        ..where((t) =>
        t.practiceId.equals(item['practiceItemId']) &
        t.practiceGroupId.equals(item['commodityPracticeGroupId'])
        ))
          .map((row) => {
        'practiceName': row.practiceName,
        'price': row.price,
      })
          .getSingleOrNull();
      final practiceCompanion = OrderItemPracticeCompanion.insert(
        orderItemPracticeId: Value(SnowflakeIdGenerator().nextId()),
        // TODO 需要根据规则生成
        orderId: orderId,
        orderItemId: orderItemId,
        tenantId: tenantId,
        commodityPracticeGroupId: item['commodityPracticeGroupId'] as int,
        commodityPracticeId: item['practiceItemId'] as int,
        commodityPracticeName: result?["practiceName"] as String? ?? "",
        quantity: 1,
        createTimezone: 'Asia/Hong_Kong',
        modifyTimezone: 'Asia/Hong_Kong',
        createTime: DateTime.now().millisecondsSinceEpoch,
        createAccountId: 1,
        price: result?["price"] as double? ?? 0,
      );
      practiceCompList.add(practiceCompanion);
    }
    return practiceCompList;
  }

  /// 配置订单item数据
  Future<OrderItemCompanion> getOrderItem(
      int orderItemId,
      int orderId,
      int storeId,
      int productId,
      int tenantId,
      double quantity, {
        String? remark,
        int? comboGroupId,
        int? parentOrderItemId,
        double? updatePrice,
        int? discountType,
        double? discount,
        String? discountReason,
        int? commoditySkuId,
      }) async {
    try {
      /// 这里如果传了commoditySkuId说明是加入商品的，没传就是加料的数据
      const sql = '''
SELECT 
  p.*, 
  pc.category_name, 
  ps.commodity_sku_id, 
  ps.sku_code, 
  ps.sku_name,
  COALESCE(ps.sku_price, 0) AS sku_price
FROM products p
LEFT JOIN product_categories pc
  ON p.leaf_category_id = pc.category_id
LEFT JOIN product_sku ps
  ON p.commodity_id = ps.commodity_id
WHERE p.commodity_id = ?
''';
      final variables = [Variable.withInt(productId)];

      final product = await db
          .customSelect(sql, variables: variables)
          .get()
          .then((rows) => rows.isNotEmpty ? rows.first : null);

      if (product == null) {
        throw Exception('Product not found for commodityId: $productId');
      }

      QueryRow? productSku;
      if (commoditySkuId != null) {
        const sql = '''
SELECT
  *
FROM
  product_sku ps
WHERE
  ps.commodity_sku_id = ?
  AND ps.commodity_id = ?
''';
        final variables = [
          Variable.withInt(commoditySkuId),
          Variable.withInt(productId),
        ];
        productSku = await db
            .customSelect(sql, variables: variables)
            .get()
            .then((rows) => rows.isNotEmpty ? rows.first : null);
        if (productSku == null) {
          throw Exception('ProductSku not found for commoditySkuId: $commoditySkuId');
        }
      }

      final orderItemCompanion = OrderItemCompanion.insert(
        orderItemId: Value(orderItemId),
        orderId: orderId,
        storeId: storeId,
        commodityId: productId,
        tenantId: tenantId,
        commodityCategoryId: product.read<int>('leaf_category_id'),
        commodityCategoryName: product.read<String>('category_name'),
        commodityCode: product.read<String>('commodity_code'),
        commodityName: product.read<String>('commodity_name'),
        commodityNameSec: Value(product.read<String>('commodity_name_sec')),
        commodityImagePath: Value(product.read<String>('commodity_image_path')),
        commodityType: product.read<int>('commodity_type'),
        commodityUnit: product.read<String>('commodity_unit'),
        quantity: quantity,
        commoditySkuId: productSku != null
            ? productSku.read<int>('commodity_sku_id')
            : product.read<int>('commodity_sku_id'),
        commoditySkuCode: productSku != null
            ? productSku.read<String>('sku_code')
            : product.read<String>('sku_code'),
        commoditySkuName: productSku != null
            ? Value(productSku.read<String>('sku_name'))
            : Value(product.read<String>('sku_name')),
        comboGroupingId: Value(comboGroupId),
        parentOrderItemId: parentOrderItemId ?? 0,
        createTimezone: 'Asia/Hong_Kong',
        modifyTimezone: 'Asia/Hong_Kong',
        createTime: DateTime.now().millisecondsSinceEpoch,
        createAccountId: 1,
        remark: remark != null ? Value(remark) : const Value.absent(),
        price: updatePrice != null
            ? Value(updatePrice)
            : Value(product.read<double>('sku_price')),
        kpDiscountType:
        discountType != null ? Value(discountType) : const Value.absent(),
        kpDiscountAmount:
        discount != null ? Value(discount) : const Value.absent(),
        kpDiscountReason:
        discountReason != null ? Value(discountReason) : const Value.absent(),
      );

      return orderItemCompanion;
    } catch (e, stackTrace) {
      print('getOrderItem error: $e\n$stackTrace');
      rethrow; // 或者 throw Exception('获取商品失败: $e');
    }
  }

  Future<int> createOrder(
      int tenantId,
      int storeId,
      String storeName,
      int brandId,
      String brandName,
      String? tableNumber,
      ) async {
    if (tableNumber != null && tableNumber.isNotEmpty) {
      final result = await db.customSelect(
        '''
  SELECT *
  FROM orders o
  WHERE o.table_number = ?
    AND o.state NOT IN (6, 7, 8)
  ORDER BY o.create_time DESC
  LIMIT 1
  ''',
        variables: [Variable.withString(tableNumber)],
      ).getSingleOrNull();
      if (result != null) {
        return result.read<int>("order_id");
      }
    }

    final query = db.customSelect(
      '''
      SELECT
        ms.store_id, 
        ms.store_timezone,
        ms.address,
        ms.currency,
        COALESCE(NULLIF(ms.store_code, ''), '000000') AS store_code
      FROM
        merchant_store ms
      WHERE
        ms.store_id = ?
      ''',
      variables: [Variable.withInt(storeId)],
    );
    final store = await query.getSingleOrNull();
    if (store == null) {
      throw Exception(MessageKey.invalidParam);
    }

    // 生成订单 ID
    final orderId = SnowflakeIdGenerator().nextId();
    final orderSerial = await generateOrderSerial();
    final businessDate = await calculateBusinessDate();
    final storeCode = store.read<String>('store_code');
    final orderNum = '$storeCode${businessDate.replaceAll('-', '')}01$orderSerial';
    await db.into(db.orders).insert(
      OrdersCompanion(
        orderId: Value(orderId),
        tenantId: Value(tenantId),
        orderNumber: Value(orderNum),
        orderSource: const Value(1),
        mealVoucher: const Value.absent(),
        tableNumber: tableNumber != null ? Value(tableNumber) : const Value.absent(),
        businessDate: Value(businessDate),
        orderState: const Value(1),
        storeId: Value(storeId),
        storeName: Value(storeName),
        storeAddress: Value(store.readNullable<String>('address') ?? ''),
        brandId: Value(brandId),
        brandName: Value(brandName),
        totalAmount: const Value(0),
        payAmount: const Value(0),
        discountAmount: const Value(0),
        commodityDiscountAmount: const Value(0),
        commodityTaxAmount: const Value(0),
        serviceFeeAmount: const Value(0),
        tipAmount: const Value(0),
        mergeTableFlag: const Value(0),
        payCurrency: Value(store.readNullable<String>('currency') ?? ''),
        createTimezone: Value(store.readNullable<String>('store_timezone') ?? ''),
        modifyTimezone: Value(store.readNullable<String>('store_timezone') ?? ''),
        createTime: Value(DateTime.now().millisecondsSinceEpoch),
        createAccountId: Value(0), // TODO: 登录后填真实用户
        voucher: const Value(''),
        serviceWay: const Value(0),
        payType: const Value(1),
        multiOrderingFlag: const Value(0),
        diningMethod: const Value(1),
        diningNumber: const Value(0),
      ),
    );

    return orderId;
  }

  Future<String> generateOrderSerial() async {
    // 直接执行，不包transaction
    final row = await (db.select(db.orderSerial)
      ..where((tbl) => tbl.id.equals(1)))
        .getSingle();

    int next = row.currentSerial + 1;

    await (db.update(db.orderSerial)
      ..where((tbl) => tbl.id.equals(1)))
        .write(OrderSerialCompanion(
      currentSerial: Value(next),
    ));

    if (next < 10000) {
      return next.toString().padLeft(4, '0');
    } else {
      return next.toString();
    }
  }

  /// 计算营业日期
  Future<String> calculateBusinessDate() async {
    final orderDateTime = DateTime.now();
    final weekDay = orderDateTime.weekday;

    // 查询该门店当天所有营业时间段
    final businessHoursList = await (db.select(db.merchantStoreBusinessHours)
      ..where((tbl) =>
      tbl.weekDay.equals(weekDay)))
        .get();

    final dayStart = DateTime(orderDateTime.year, orderDateTime.month, orderDateTime.day);

    for (final businessHours in businessHoursList) {
      // 解析开始时间
      final startParts = businessHours.startTime.split(":").map(int.parse).toList();
      final startDateTime = dayStart.add(Duration(hours: startParts[0], minutes: startParts[1]));

      // 解析结束时间
      final endParts = businessHours.endTime.split(":").map(int.parse).toList();
      DateTime endDateTime = dayStart.add(Duration(hours: endParts[0], minutes: endParts[1]));

      if (businessHours.nextDay == 1 && endDateTime.isBefore(startDateTime)) {
        // 跨天
        endDateTime = endDateTime.add(Duration(days: 1));

        // 如果落在0点~结束时间之间，算前一天
        if (orderDateTime.isBefore(endDateTime) &&
            orderDateTime.isAfter(dayStart.subtract(const Duration(milliseconds: 1)))) {
          final prevDay = orderDateTime.subtract(const Duration(days: 1));
          return "${prevDay.year}-${prevDay.month.toString().padLeft(2, '0')}-${prevDay.day.toString().padLeft(2, '0')}";
        }
      }

      // 普通时间段
      if (orderDateTime.isAfter(startDateTime) && orderDateTime.isBefore(endDateTime)) {
        return "${orderDateTime.year}-${orderDateTime.month.toString().padLeft(2, '0')}-${orderDateTime.day.toString().padLeft(2, '0')}";
      }
    }

    // 没匹配任何区间，默认当天
    return "${orderDateTime.year}-${orderDateTime.month.toString().padLeft(2, '0')}-${orderDateTime.day.toString().padLeft(2, '0')}";
  }


  @override
  Future<void> saveDraft(int orderId) async {
    final order = await db.customSelect(
      '''
      SELECT *
      FROM orders
      WHERE order_id = ?''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        isDraftOrder: const Value(1),
        draftOrderTime:Value(DateTime.now().millisecondsSinceEpoch),
      ),
    );
  }

  @override
  Future<void> deleteDraft(int orderId) async {
    final order = await db.customSelect(
      '''
      SELECT *
      FROM orders
      WHERE order_id = ?''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      const OrdersCompanion(
        isDraftOrder: Value(0),
        draftOrderTime: Value.absent()
      ),
    );
  }

  @override
  Future<List<Map<String, dynamic>>> getDrafts() async {
    try {
      final result = await db.customSelect(
          '''
      SELECT
        o.order_id,
        CASE
          WHEN o.table_number IS NOT NULL AND o.table_number <> '' THEN o.table_number
          WHEN o.meal_voucher IS NOT NULL AND o.meal_voucher <> '' THEN o.meal_voucher
          ELSE NULL
        END AS cartCode,
        o.pay_amount,
        o.create_time,
        oi.commodity_name,
        oi.quantity
      FROM
        orders o
      LEFT JOIN order_item oi ON o.order_id = oi.order_id AND oi.parent_order_item_id = 0
      WHERE
        o.is_draft_order = 1
      ORDER BY
        oi.create_time ASC
      '''
      ).get();

      final Map<int, Map<String, dynamic>> draftMap = {};

      for (final row in result) {
        final orderId = row.read<int>('order_id');

        if (!draftMap.containsKey(orderId)) {
          draftMap[orderId] = {
            'orderId': orderId,
            'cartCode': row.readNullable<String>('cartCode') ?? '',
            'totalPrice': row.read<double>('pay_amount'),
            'createTime': row.read<int>('create_time'),
            'items': []
          };
        }

        final itemName = row.readNullable<String>('commodity_name');
        final quantity = row.readNullable<int>('quantity');

        if (itemName != null && quantity != null) {
          (draftMap[orderId]!['items'] as List).add({
            'commodityName': itemName,
            'quantity': quantity,
          });
        }
      }

      return draftMap.values.toList();
    } catch (e, stackTrace) {
      print('getDrafts failed: $e');
      print(stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> setDiningMethod(int orderId, int method) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }

    // 校验取值
    if (method < 1 || method > 4) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        diningMethod: Value(method),
      ),
    );
  }

  @override
  Future<void> setDiningNumber(int orderId, int number) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }

    // 校验取值
    if (number <= 0 || number > 999) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        diningNumber: Value(number),
      ),
    );
  }

  @override
  Future<void> setOrderDiscount(int orderId, int discountType,double discount, String reason) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }

    if (discountType != 1 && discountType != 2) {
      throw Exception(MessageKey.invalidParam);
    }

    if (discountType == 1 && (discount <= 0 || discount >= 1)) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    if (discountType == 2 && (discount <= 0)) {
      throw Exception(MessageKey.invalidParam);
    }

    if (reason.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        kpDiscountType: Value(discountType),
        kpDiscountAmount: Value(discount),
        kpDiscountReason: Value(reason)
      ),
    );
  }

  @override
  Future<void> setOrderRemark(int orderId, String remark) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }

    if (remark.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        remark: Value(remark),
      ),
    );
  }

  @override
  Future<bool> clearCart(int orderId) async {
    try {
      return await db.transaction(() async {
        final updatedItems = await (db.update(db.orderItem)
          ..where((tbl) => tbl.orderId.equals(orderId)))
            .write(const OrderItemCompanion(
          deleted: Value(1),
        ));

        final updatedPractices = await (db.update(db.orderItemPractice)
          ..where((tbl) => tbl.orderId.equals(orderId)))
            .write(const OrderItemPracticeCompanion(
          deleted: Value(1),
        ));

        return updatedItems > 0 || updatedPractices > 0;
      });
    } catch (e, st) {
      print('clearCart failed: $e\n$st');
      rethrow;
    }
  }

  @override
  Future<void> setOrderItemQuantity(int orderItemId, double quantity) async {
    final orderItem = await db.customSelect(
      '''
    SELECT *
    FROM order_item
    WHERE order_item_id = ?
    ''',
      variables: [Variable.withInt(orderItemId)],
    ).getSingleOrNull();

    if (orderItem == null) {
      throw Exception(MessageKey.invalidParam);
    }
    if (quantity <= 0) {
      throw Exception(MessageKey.invalidParam);
    }
    // 执行更新
    await (db.update(db.orderItem)
      ..where((tbl) => tbl.orderItemId.equals(orderItemId)))
        .write(
      OrderItemCompanion(
        quantity: Value(quantity),
      ),
    );
  }

  @override
  Future<void> setOrderMealVoucher(int orderId, String mealVoucher) async {
    final order = await db.customSelect(
      '''
    SELECT *
    FROM orders
    WHERE order_id = ?
    ''',
      variables: [Variable.withInt(orderId)],
    ).getSingleOrNull();

    if (order == null) {
      throw Exception(MessageKey.invalidParam);
    }

    if (mealVoucher.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await (db.update(db.orders)
      ..where((tbl) => tbl.orderId.equals(orderId)))
        .write(
      OrdersCompanion(
        mealVoucher: Value(mealVoucher),
      ),
    );
  }

  @override
  Future<void> setOrderServiceFee(
      int orderId,
      int tenantId,
      String idStr,
      String? reason,
      ) async {
    try {
      // 1. 拆分ID
      final idList = idStr.isNotEmpty
          ? idStr
          .split(",")
          .map((e) => e.trim())
          .where((e) => e.isNotEmpty)
          .map(int.parse)
          .toSet()
          : <int>{};

      // 2. 开启事务
      await db.transaction(() async {
        // 3. 查询现有附加费
        final existingFees = await (db.select(db.orderAdditionalFee)
          ..where((tbl) =>
          tbl.orderId.equals(orderId) &
          tbl.tenantId.equals(tenantId)))
            .get();

        final existingIds = existingFees.map((e) => e.serviceFeeManagementId).toSet();

        // 4. 分类
        final idsToInsert = idList.difference(existingIds);
        final idsToKeep = idList.intersection(existingIds);
        final idsToDelete = existingIds.difference(idList);

        // 5. 更新启用的(deleted=0)和禁用的(deleted=1)
        if (idsToKeep.isNotEmpty || idsToDelete.isNotEmpty) {
          await db.batch((batch) {
            for (final id in idsToKeep) {
              batch.update(
                db.orderAdditionalFee,
                const OrderAdditionalFeeCompanion(deleted: Value(0)),
                where: (tbl) =>
                tbl.orderId.equals(orderId) &
                tbl.tenantId.equals(tenantId) &
                tbl.serviceFeeManagementId.equals(id),
              );
            }
            for (final id in idsToDelete) {
              batch.update(
                db.orderAdditionalFee,
                const OrderAdditionalFeeCompanion(deleted: Value(1)),
                where: (tbl) =>
                tbl.orderId.equals(orderId) &
                tbl.tenantId.equals(tenantId) &
                tbl.serviceFeeManagementId.equals(id!),
              );
            }
          });
        }

        // 6. 插入新记录
        if (idsToInsert.isNotEmpty) {
          final feeConfigs = await (db.select(db.serviceFeeManagement)
            ..where((tbl) => tbl.serviceFeeManagementId.isIn(idsToInsert)))
              .get();

          if (feeConfigs.isNotEmpty) {
            await db.batch((batch) {
              batch.insertAll(
                db.orderAdditionalFee,
                feeConfigs.map((config) {
                  return OrderAdditionalFeeCompanion.insert(
                    orderAdditionalFeeId: Value(SnowflakeIdGenerator().nextId()),
                    orderId: orderId,
                    tenantId: tenantId,
                    serviceFeeManagementId: Value(config.serviceFeeManagementId),
                    additionalFeeName: config.serviceFeeName,
                    additionalFeeAmount: Value(0), // TODO: 金额逻辑
                    additionalFeeType: 1,
                    createTimezone: 'Asia/Shanghai',
                    modifyTimezone: 'Asia/Shanghai',
                    createTime: DateTime.now().millisecondsSinceEpoch,
                    createAccountId: 0,
                  );
                }).toList(),
              );
            });
          }
        }

        // 7. 更新订单原因
        if (reason != null && reason.isNotEmpty) {
          await (db.update(db.orders)
            ..where((tbl) => tbl.orderId.equals(orderId)))
              .write(OrdersCompanion(
            kpCancelServiceFeeReason: Value(reason),
          ));
        }
      });
    } catch (e, stack) {
      print("setOrderServiceFee 执行异常: $e");
      print(stack);
      // rethrow; // 如需要上抛
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getOrderServiceFee({
    required int orderId,
    required int tenantId,
  }) async {
    try {
      const sql = '''
    SELECT
      sfm.*,
      CASE
        WHEN EXISTS (
          SELECT 1
          FROM order_additional_fee oaf
          WHERE 
            oaf.service_fee_management_id = sfm.service_fee_management_id
            AND oaf.order_id = ?
            AND oaf.tenant_id = ?
            AND oaf.deleted = 0
        ) THEN 1
        ELSE 0
      END AS isCheck
    FROM
      service_fee_management sfm
  ''';

      final list = await db.customSelect(
        sql,
        variables: [
          Variable.withInt(orderId),
          Variable.withInt(tenantId),
        ],
      ).get();

      final data = list.map((row) => {
        'id': row.read<int>('service_fee_management_id'),
        'name': row.readNullable<String>('service_fee_name') ?? '',
        'type': row.readNullable<int>('service_charge_method') ?? 0,
        'number': 1,
        'chargeRate': row.readNullable<double>('service_charge_rate') ?? 0.0,
        'fixedCharge': row.readNullable<double>('service_fixed_charge') ?? 0.0,
        'isCheck': row.read<int>('isCheck'),
      }).toList();

      return data;
    } catch (e, stack) {
      print('getOrderServiceFee 执行异常: $e');
      print(stack);
      rethrow;
    }
  }

  @override
  Future<bool> updateCart(int tenantId, int storeId, int orderId, int productId,
      double quantity, int orderItemId,
      {String? remark,
      double? updatePrice,
      int? discountType,
      double? discount,
      String? discountReason,
      Map<String, dynamic>? productConfigMap,
      List? comboConfig}) async {
    final orderItem = await db.customSelect(
      '''
      SELECT
	      *
      FROM
	    order_item oi
      WHERE
	    oi.order_item_id = ?
	    AND oi.order_id  = ?
	    AND oi.deleted = 0''',
      variables: [Variable.withInt(orderItemId),Variable.withInt(orderId)],
    ).getSingleOrNull();
    if (orderItem == null) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    ///1、如果是修改商品
    if (productConfigMap != null) {
      ///1、查询orderItem数据
      final int commoditySkuId = productConfigMap['commoditySkuId'];
      final orderItemId = orderItem.read<int>('order_item_id');
      /// 查询原商品的做法数据
      final originPracticeIds = await (db.select(db.orderItemPractice)
        ..where((tbl) =>
        tbl.orderId.equals(orderId) &
        tbl.orderItemId.equals(orderItemId) &
        tbl.deleted.equals(0)))
          .map((row) => row.orderItemPracticeId)
          .get();
      /// 查询原商品的加料数据
      final originAddonsId = await (db.select(db.orderItem)
        ..where((tbl) =>
        tbl.parentOrderItemId.equals(orderItemId) &
        tbl.deleted.equals(0)))
          .map((row) => row.orderItemId)
          .get();

      List<OrderItemPracticeCompanion> practiceList = [];
      final List<OrderItemCompanion> addonsList = [];
      ///2、增加做法数据
      final List<Map<String, dynamic>> practiceConfigList =
      (productConfigMap['practiceConfigList'] as List<dynamic>? ?? []).cast<Map<String, dynamic>>();
      if (practiceConfigList.isNotEmpty) {
        practiceList = await getOrderPracticeItem(
            orderId, orderItemId, tenantId, practiceConfigList);
      }

      ///3、增加加料数据
      final List<Map<String, dynamic>> addonsConfigList =
      (productConfigMap['addonsConfigList'] as List<dynamic>? ?? []).cast<Map<String, dynamic>>();
      if (addonsConfigList.isNotEmpty) {
        for (var item in addonsConfigList) {
          final addonsOrderIemId = SnowflakeIdGenerator().nextId();
          final unitQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
          final addonsItem = await getOrderItem(
              addonsOrderIemId,
              orderId,
              storeId,
              item["addonsCommodityId"],
              tenantId,
              unitQuantity,
              remark:item["remark"],
              parentOrderItemId: orderItemId);
          addonsList.add(addonsItem);
        }
      }

      try {
        await db.transaction(() async {
          await db.batch((batch) {
            batch.update(
              db.orderItem,
              OrderItemCompanion(
                quantity: Value(quantity),
                remark:remark != null ? Value(remark) : Value(orderItem.readNullable<String>('remark')),
                price: updatePrice != null ?  Value(updatePrice) :Value(orderItem.read<double>('price')),
                kpDiscountType: discountType != null ? Value(discountType) : Value(orderItem.readNullable<int>('kp_discount_type')),
                kpDiscountAmount: discount != null ? Value(discount) : Value(orderItem.readNullable<double>('kp_discount_amount')),
                kpDiscountReason: discountReason != null ? Value(discountReason) : Value(orderItem.readNullable<String>('kp_discount_reason')),
                commoditySkuId: Value(commoditySkuId)
              ),
              where: (tbl) => tbl.orderItemId.equals(orderItemId),
            );
            if (originPracticeIds.isNotEmpty) {
              for (final id in originPracticeIds) {
                batch.update(
                  db.orderItemPractice,
                  const OrderItemPracticeCompanion(
                    deleted: Value(1),
                  ),
                  where: (tbl) => tbl.orderItemPracticeId.equals(id),
                );
              }
            }
            if (originAddonsId.isNotEmpty) {
              for (final id in originAddonsId) {
                batch.update(
                  db.orderItem,
                  const OrderItemCompanion(
                    deleted: Value(1),
                  ),
                  where: (tbl) => tbl.orderItemId.equals(id),
                );
              }
            }
            if (practiceList.isNotEmpty) {
              batch.insertAll(db.orderItemPractice, practiceList);
            }
            if (addonsList.isNotEmpty) {
              batch.insertAll(db.orderItem, addonsList);
            }
          });
        });
        return true;
      } catch (e, st) {
        print(e);
        return false;
      }
    } else {
      /// 查询原套餐的商品数据
      final originProductIds = await (db.select(db.orderItem)
        ..where((tbl) =>
        tbl.parentOrderItemId.equals(orderItemId) &
        tbl.deleted.equals(0)))
          .map((row) => row.orderItemId)
          .get();

      /// 查询原商品的做法数据
      final originPracticeIds = await (db.select(db.orderItemPractice)
        ..where((tbl) =>
        tbl.orderId.equals(orderId) &
        tbl.orderItemId.isIn(originProductIds) &
        tbl.deleted.equals(0)))
          .map((row) => row.orderItemPracticeId)
          .get();
      /// 查询原商品的加料数据
      final originAddonsId = await (db.select(db.orderItem)
        ..where((tbl) =>
        tbl.parentOrderItemId.isIn(originProductIds) &
        tbl.deleted.equals(0)))
          .map((row) => row.orderItemId)
          .get();

      ///2、增加套餐商品数据到orderItem,这时候parentOrderItemId是套餐orderItem的id
      /// 所有套餐商品数组
      final List<OrderItemCompanion> comboOrderItemList = [];
      /// 所有的套餐商品做法数组
      final List<OrderItemPracticeCompanion> orderPracticeList = [];
      /// 所有的套餐加料做法数组
      final List<OrderItemCompanion> orderAddonsList = [];
      final comboList = (comboConfig as List)
          .map((e) => e as Map<String, dynamic>)
          .toList();
      for (var item in comboList) {
        /// 拼接套餐商品数据
        final orderItemId = SnowflakeIdGenerator().nextId();
        final productQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
        final comboOrderItem = await getOrderItem(
            orderItemId,
            orderId,
            storeId,
            item["commodityId"],
            tenantId,
            productQuantity,
            remark: item["remark"],
            comboGroupId:item["comboGroupingId"],
            parentOrderItemId:orderItemId);
        comboOrderItemList.add(comboOrderItem);

        /// 拼接套餐商品做法数据
        final practiceListRaw = item["practiceConfigList"];
        List<Map<String, dynamic>>? practiceList;
        if (practiceListRaw != null && practiceListRaw is List) {
          practiceList = practiceListRaw.map((e) => e as Map<String, dynamic>).toList();
        }
        if (practiceList != null &&
            practiceList.isNotEmpty) {
          final orderPractices = await getOrderPracticeItem(
              orderId, orderItemId, tenantId, practiceList);
          orderPracticeList.addAll(orderPractices);
        }

        /// 拼接套餐商品加料数据
        final addonsListRaw = item["addonsConfigList"];
        List<Map<String, dynamic>>? addonsList;
        if (addonsListRaw != null && addonsListRaw is List) {
          addonsList = addonsListRaw.map((e) => e as Map<String, dynamic>).toList();
        }
        if (addonsList != null &&
            addonsList.isNotEmpty) {
          for (var item in addonsList) {
            final addonsOrderItemId = SnowflakeIdGenerator().nextId();
            final addonsQuantity = (item["unitQuantity"] as num?)?.toDouble() ?? 0.0;
            final addonsItem = await getOrderItem(
                addonsOrderItemId,
                orderId,
                storeId,
                item["addonsCommodityId"],
                tenantId,
                addonsQuantity,
                remark:item["remark"],
                parentOrderItemId:orderItemId);
            orderAddonsList.add(addonsItem);
          }
        }
      }
      try {
        await db.transaction(() async {
          await db.batch((batch) {
            batch.update(
              db.orderItem,
              OrderItemCompanion(
                  quantity: Value(quantity),
                  remark:remark != null ? Value(remark) : Value(orderItem.readNullable<String>('remark')),
                  price: updatePrice != null ?  Value(updatePrice) :Value(orderItem.read<double>('price')),
                  kpDiscountType: discountType != null ? Value(discountType) : Value(orderItem.readNullable<int>('kp_discount_type')),
                  kpDiscountAmount: discount != null ? Value(discount) : Value(orderItem.readNullable<double>('kp_discount_amount')),
                  kpDiscountReason: discountReason != null ? Value(discountReason) : Value(orderItem.readNullable<String>('kp_discount_reason')),
              ),
              where: (tbl) => tbl.orderItemId.equals(orderItemId),
            );
            if (originProductIds.isNotEmpty) {
              for (final id in originProductIds) {
                batch.update(
                  db.orderItem,
                  const OrderItemCompanion(
                    deleted: Value(1),
                  ),
                  where: (tbl) => tbl.orderItemId.equals(id),
                );
              }
            }
            if (originPracticeIds.isNotEmpty) {
              for (final id in originPracticeIds) {
                batch.update(
                  db.orderItemPractice,
                  const OrderItemPracticeCompanion(
                    deleted: Value(1),
                  ),
                  where: (tbl) => tbl.orderItemPracticeId.equals(id),
                );
              }
            }
            if (originAddonsId.isNotEmpty) {
              for (final id in originAddonsId) {
                batch.update(
                  db.orderItem,
                  const OrderItemCompanion(
                    deleted: Value(1),
                  ),
                  where: (tbl) => tbl.orderItemId.equals(id),
                );
              }
            }
            if (comboOrderItemList.isNotEmpty) {
              batch.insertAll(db.orderItem, comboOrderItemList);
            }
            if (orderPracticeList.isNotEmpty) {
              batch.insertAll(db.orderItemPractice, orderPracticeList);
            }
            if (orderAddonsList.isNotEmpty) {
              batch.insertAll(db.orderItem, orderAddonsList);
            }
          });
        });
        return true; // 成功
      } catch (e, st) {
        print('❌ 插入套餐到购物车失败: $e\n$st');
        return false;
      }
    }

    return true;
  }

  @override
  Future<void> setOrderItemDiscount(String orderItemIdStr, int discountType, double discount, String reason) async {
    final idList = orderItemIdStr
        .split(',')
        .map((e) => int.tryParse(e.trim()))
        .whereType<int>()
        .toList();

    if (idList.isEmpty) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    final result = await db.customSelect(
      '''
    SELECT order_item_id FROM order_item 
    WHERE order_item_id IN (${List.filled(idList.length, '?').join(',')})
    AND deleted = 0
    ''',
      variables: idList.map((id) => Variable.withInt(id)).toList(),
    ).get();

    if (result.isEmpty) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    if (discountType != 1 && discountType != 2) {
      throw Exception(MessageKey.invalidParam);
    }

    if (discountType == 1 && (discount <= 0 || discount >= 1)) {
      throw ApiIntranetException(MessageKey.invalidParam);
    }

    if (discountType == 2 && (discount <= 0)) {
      throw Exception(MessageKey.invalidParam);
    }

    if (reason.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }

    // 执行更新
    await db.transaction(() async {
      await (db.update(db.orderItem)
        ..where((tbl) => tbl.orderItemId.isIn(idList)))
          .write(
        OrderItemCompanion(
          kpDiscountType: Value(discountType),
          kpDiscountAmount: Value(discount),
          kpDiscountReason: Value(reason),
        ),
      );
    });
  }
}