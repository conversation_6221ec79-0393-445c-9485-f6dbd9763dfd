import 'package:kpos/common/services/networking/remote_service/api_endpoint.dart';

enum CartEndpoint implements ApiEndpoint {

  getCart("/get/cart"),

  deleteOrderItem("/delete/order/item"),

  getDiningStyleList("/dinning/style"),
  createOrder("/create/order"),

  /// 加入购物车
  addCart("/add/cart"),
  /// 修改购物车
  updateCart("/update/cart"),

  /// 清空购物车
  clearCart("/clear/cart"),

  /// 存单
  saveDraft("/save/draft/order"),

  /// 取单
  deleteDraft("/delete/draft/order"),

  /// 获取取单数据
  getDrafts("/get/draft/orders"),

  /// 设置就餐方式
  setDiningMethod("/set/order/dining/method"),

  /// 设置就餐人数
  setDiningNumber("/set/order/dining/number"),

  /// 设置订单备注
  setOrderRemark("/set/order/remark"),

  /// 设置订单折扣
  setOrderDiscount("/set/order/discount"),

  /// 设置凭证号
  setOrderMealVoucher("/set/order/meal/voucher"),

  /// 设置商品价格
  setOrderItemPrice("/set/order/item/price"),

  /// 设置商品折扣
  setOrderItemDiscount("/set/order/item/discount"),

  /// 设置购物车商品数量
  setOrderItemQuantity("/set/order/item/quantity"),

  /// 设置订单服务费
  setOrderServiceFee("/set/order/service/fee"),

  /// 获取订单服务费
  getOrderServiceFee("/get/order/service/fee");


  const CartEndpoint(this.path);

  @override
  final String path;

}