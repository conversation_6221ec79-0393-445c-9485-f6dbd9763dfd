import 'dart:convert';
import 'dart:ui';

import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/language_settings_service/language_settings_service.dart';
import 'package:kpos/common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import 'package:kpos/common/services/networking/constants/api_intranet/api_intranet_response_code.dart';
import 'package:kpos/common/services/networking/constants/headers_key.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_exception.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_localization.dart';
import 'package:kpos/common/services/networking/intranet_service/api_intranet_response.dart';
import 'package:kpos/common/services/web_socket/web_socket_server_service.dart';
import 'package:kpos/features/cart/data/cart_local_repository.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import '../../store/application/store_service.dart';

part 'cart_server_service.g.dart';

class CartServerService {
  final Ref _ref;
  CartServerService({required Ref ref}) : _ref = ref;

  Locale get currentLocale => _ref.read(languageSettingsServiceProvider).currentLocale;

  Future<Response> createOrder(Request request) async {
    try {
      final payload = await request.readAsString();
      Map<String, dynamic> data = {};
      if (payload.isNotEmpty) {
        try {
          final decoded = jsonDecode(payload);
          if (decoded is Map<String, dynamic>) {
            data = decoded;
          } else {
            return ApiIntranetResponse.failure(
                message:Localization.locale(MessageKey.invalidParam,
                    locale: currentLocale));
          }
        } catch (e) {
          return ApiIntranetResponse.failure(message:'Invalid JSON format : ${e.toString()}');
        }
      }

      // 获取租户ID
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }

      // 获取绑定门店
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;

      // 创建OrderLocalRepository
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));

      // 调用创建订单
      final result = await cartLocal.createOrder(
        tenantId,
        boundStore.storeId,
        boundStore.storeName,
        boundStore.brandId,
        boundStore.brandName,
        data["tableNumber"] ?? '',
      );
      return ApiIntranetResponse.success({"orderId": result});
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> addCart(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["productId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["quantity"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["productConfig"] == null && data["comboConfig"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final updatePrice = (data["updatePrice"] as num?)?.toDouble();
      final discountType = data["discountType"] as int?;
      final discount = (data["discount"] as num?)?.toDouble();
      final discountReason = data["discountReason"]?.toString();
      final orderLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final quantity = (data["quantity"] as num?)?.toDouble() ?? 0.0;
      final result = await orderLocal.addCart(
          tenantId,
          boundStore.storeId,
          data["orderId"],
          data["productId"],
          quantity,
          remark: data["remark"] ?? '',
          updatePrice: updatePrice,
          discountType: discountType,
          discount: discount,
          discountReason: discountReason,
          productConfigMap: data["productConfig"],
          comboConfig:data["comboConfig"]);
      if (result) {
        return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> updateCart(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["productId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["quantity"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["productConfig"] == null && data["comboConfig"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final updatePrice = data["updatePrice"] != null ? (data["updatePrice"] as num).toDouble() : null;
      final discountType = data["discountType"] as int?;
      final discount = data["discount"] != null ? (data["discount"] as num).toDouble() : null;
      final discountReason = data["discountReason"] as String?;
      final quantity = (data["quantity"] as num?)?.toDouble() ?? 0.0;
      final orderLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await orderLocal.updateCart(
          tenantId,
          boundStore.storeId,
          data["orderId"],
          data["productId"],
          quantity,
          data["orderItemId"],
          remark: data["remark"] ?? '',
          updatePrice: updatePrice,
          discountType: discountType,
          discount: discount,
          discountReason: discountReason,
          productConfigMap: data["productConfig"],
          comboConfig:data["comboConfig"]);
      if (result) {
        return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getCart(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.getCart(data["orderId"]);
      if (result.isNotEmpty) {
        return ApiIntranetResponse.success(result);
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> clearCart(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      await cartLocal.clearCart(data["orderId"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> saveDraft(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.saveDraft(data["orderId"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> deleteDraft(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.deleteDraft(data["orderId"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> deleteCart(Request request) async {
    try {
      final db = _ref.watch(databaseProvider);
      final body = await request.readAsString();
      final clientId = request.headers[HeadersKey.clientId] ?? "";
      final Map<String, dynamic> params = jsonDecode(body);
      await db.carts.deleteWhere((c) => c.id.equals(params['cart_id']));
      _ref.read(webSocketServerServiceProvider).sendAllMessage("delete_cart",clientId);
      return ApiIntranetResponse.success("operate success");
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getDraftOrders(Request request) async {
    try {
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.getDrafts();
      return ApiIntranetResponse.success(result);
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getDiningStyleList(Request request) async {
    try {
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final list = await cartLocal.getDiningStyleList();
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setDiningMethod(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["method"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setDiningMethod(data["orderId"],data["method"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setDiningNumber(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["number"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setDiningNumber(data["orderId"],data["number"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderDiscount(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      _ref.read(currentLocaleProvider);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["discountType"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["discount"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["reason"] == null || (data["reason"] as String).trim().isEmpty) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setOrderDiscount(data["orderId"],data["discountType"],data["discount"],data["reason"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderRemark(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["remark"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setOrderRemark(data["orderId"],data["remark"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderMealVoucher(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["mealVoucher"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setOrderMealVoucher(data["orderId"],data["mealVoucher"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderItemQuantity(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderItemId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["quantity"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final result = await cartLocal.setOrderItemQuantity(data["orderItemId"],(data["quantity"] as num).toDouble());
      return ApiIntranetResponse.success('success');
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getOrderServiceFee(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      final list = await cartLocal.getOrderServiceFee(orderId: data["orderId"], tenantId: tenantId);
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderServiceFee(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["idStr"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      await cartLocal.setOrderServiceFee(data["orderId"], tenantId, data["idStr"],data["reason"]);
      return ApiIntranetResponse.success('success');
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> setOrderItemDiscount(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      _ref.read(currentLocaleProvider);
      if (data["orderItemIdStr"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["discountType"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["discount"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["reason"] == null || (data["reason"] as String).trim().isEmpty) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final cartLocal = CartLocalRepository(_ref.watch(databaseProvider));
      await cartLocal.setOrderItemDiscount(data["orderItemIdStr"],data["discountType"],data["discount"],data["reason"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

}

@riverpod
CartServerService cartServerService(Ref ref) {
  return CartServerService(ref: ref);
}