import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/cart/data/cart_intranet_repository.dart';
import 'package:kpos/features/cart/domain/create_order_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'order_creation_service.g.dart';

class OrderCreationService {
  final Ref _ref;

  OrderCreationService({required Ref ref}) : _ref = ref;

  /// 创建订单
  /// [tableNumber] 桌号 非必填
  /// 返回创建的订单信息
  Future<CreateOrderItem> createOrder({String? tableNumber}) async {
    try {
      final createOrderItem = await _ref.read(cartIntranetRepositoryProvider).createOrder(
            tableNumber: tableNumber,
          );

      // 创建订单成功后，保存订单ID到本地存储
      await _saveCurrentOrderId(createOrderItem.orderId);
      return createOrderItem;
    } catch (e) {
      // 创建订单失败
      // await clearCurrentOrderId();
      rethrow;
    }
  }

  /// 保存当前订单ID到本地存储
  Future<void> _saveCurrentOrderId(int orderId) async {
    final storageService = _ref.read(keyValueStorageServiceProvider);
    await storageService.setOrderId(orderId);
    print("保存订单 Id: $orderId");
    _ref.read(currentOrderIdProvider.notifier).set(orderId);
  }

  /// 获取当前订单ID
  Future<int?> getCurrentOrderId() async{
    final storageService = _ref.read(keyValueStorageServiceProvider);
    int? id = await storageService.getOrderId();
    print("获取订单 Id: $id");
    return id;
  }

  /// 清除当前订单ID
  Future<void> clearCurrentOrderId() async {
    final storageService = _ref.read(keyValueStorageServiceProvider);
    await storageService.setOrderId(null);
    _ref.read(currentOrderIdProvider.notifier).set(null);
  }

  /// 完成订单（清除订单ID）
  Future<void> completeOrder() async {
    await clearCurrentOrderId();
  }
}

@riverpod
OrderCreationService orderCreationService(Ref ref) {
  return OrderCreationService(ref: ref);
}

///当前订单id
///final id = ref.watch(currentOrderIdProvider);
@Riverpod(keepAlive: true)
class CurrentOrderId extends _$CurrentOrderId {
  @override
  int? build() => null;

  void set(int? value) {
    state = value;
  }
}