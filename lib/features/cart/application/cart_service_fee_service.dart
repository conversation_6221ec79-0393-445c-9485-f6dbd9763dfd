import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/store/domain/service_fee_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/cart_intranet_repository.dart';

part 'cart_service_fee_service.g.dart';

class CartServiceFeeService {
  final CartIntranetRepository _repository;

  CartServiceFeeService({required CartIntranetRepository repository})
      : _repository = repository;

  Future<List<ServiceFeeItem>> getServiceFeeList({required int orderId}) async {
    return await _repository.getServiceFeeItemList(orderId: orderId);
  }

  Future<ServiceFeeItem?> findServiceFeeById(int id, {required int orderId}) async {
    final serviceFees = await getServiceFeeList(orderId: orderId);
    try {
      return serviceFees.firstWhere((serviceFee) => serviceFee.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<ServiceFeeItem?> findServiceFeeByName(String name, {required int orderId}) async {
    final serviceFees = await getServiceFeeList(orderId: orderId);
    try {
      return serviceFees.firstWhere((serviceFee) => serviceFee.name == name);
    } catch (e) {
      return null;
    }
  }

  Future<List<ServiceFeeItem>> getProportionalServiceFees({required int orderId}) async {
    final serviceFees = await getServiceFeeList(orderId: orderId);
    return serviceFees.where((serviceFee) => serviceFee.type == 1).toList();
  }

  Future<List<ServiceFeeItem>> getFixedServiceFees({required int orderId}) async {
    final serviceFees = await getServiceFeeList(orderId: orderId);
    return serviceFees.where((serviceFee) => serviceFee.type == 2).toList();
  }

  Future<void> setOrderServiceFee({required int orderId, required String idStr, String? reason}) async {
    await _repository.setOrderServiceFee(orderId: orderId, idStr: idStr, reason: reason);
  }
}

@riverpod
CartServiceFeeService cartServiceFeeService(Ref ref) {
  return CartServiceFeeService(
    repository: ref.watch(cartIntranetRepositoryProvider),
  );
}

@riverpod
Future<List<ServiceFeeItem>> cartServiceFeeList(CartServiceFeeListRef ref, {required int orderId}) {
  final service = ref.watch(cartServiceFeeServiceProvider);
  return service.getServiceFeeList(orderId: orderId);
} 