import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_loading.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/cart/data/cart_intranet_repository.dart';
import 'package:kpos/features/cart/domain/cart_add_request.dart';
import 'package:kpos/features/cart/domain/cart_order_item.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/cart/domain/dining_style_item.dart';
import 'package:kpos/features/cart/domain/order_draft_item.dart';
import 'package:kpos/features/cart/presentation/cart_action/cart_action_view.dart';
import 'package:kpos/features/cart/presentation/cart_controller.dart';
import 'package:kpos/features/cart/presentation/cart_product_tags.dart';
import 'package:kpos/features/cart/presentation/cart_screen.dart';
import 'package:kpos/features/order/data/order_intranet_repository.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'cart_service.g.dart';

class CartService {
  final Ref _ref;
  final OrderCreationService _orderCreationService;

  CartService({required Ref ref})
      : _ref = ref,
        _orderCreationService = ref.read(orderCreationServiceProvider);

  //示例数据
  CartOrderItem? cartItem;

  List<CartProductItem> cartItemList = [];
  
  // 模拟第二次订单数据
  List<CartProductItem> get secondCartItemList => [];
  
  // 模拟第三次订单数据
  List<CartProductItem> get thirdCartItemList => [];


  //服务费
  double serviceFee = 0.0;

  ///初始化购物车数据
  Future<void> initData({
    bool isTableOrdering = false,
    String? tableNumber,
    int? seatedNumber,
    int? orderId,
  }) async {
    // 初始化已存单数据
    await getOrderDrafts();

    // 检查是否已有订单
    if (orderId == null) {
      // 如果没有订单ID，创建新订单
      debugPrint("没有活跃订单，创建新订单");
      await createOrderAndInitializeCart(
        tableNumber: isTableOrdering ? tableNumber : null,
      );

      if (seatedNumber != null && seatedNumber > 0) {
        _ref.read(peopleCountStateProvider.notifier).set(seatedNumber);
        await updateDiningNumber(seatedNumber);
      }
    } else {
      debugPrint("已有活跃订单 #$orderId，检查是否需要创建新订单");
      
      try {
        // 获取当前订单数据
        final cart = await _ref.read(cartIntranetRepositoryProvider).getCart(orderId);
        
        // 需要根据两个条件判断是否创建新订单:
        // 1. 如果是桌台点餐模式，并且当前订单已有tableNumber不为空
        // 2. 如果桌台点餐传入的tableNumber与当前订单不同
        bool shouldCreateNewOrder = false;
        
        if (isTableOrdering && tableNumber != null) {
          // 如果当前订单没有桌台号，或者桌台号与传入的不同，需要创建新订单
          if (cart.tableNumber.isEmpty || cart.tableNumber != tableNumber) {
            debugPrint("当前订单桌台号(${cart.tableNumber})与新桌台号($tableNumber)不同，创建新订单");
            shouldCreateNewOrder = true;
          } else {
            debugPrint("桌台号相同，继续使用当前订单");
          }
        }
        
        if (shouldCreateNewOrder) {
          // 创建新订单
          await createOrderAndInitializeCart(
            tableNumber: tableNumber,
          );

          if (seatedNumber != null && seatedNumber > 0) {
            _ref.read(peopleCountStateProvider.notifier).set(seatedNumber);
            await updateDiningNumber(seatedNumber);
          }
        } else {
          // 使用现有订单
          cartItem = cart;
          cartItemList = List<CartProductItem>.from(cart.cartItemList);
          
          // 使用现有订单时，根据订单状态设置就餐方式
          final hasTableNumber = cart.tableNumber.isNotEmpty;
          if (hasTableNumber) {
            _ref.read(diningTypeStateProvider.notifier).set(DiningType.dineIn);
          } else {
            _ref.read(diningTypeStateProvider.notifier).set(DiningType.selfPickup);
          }
          
          // 使用订单中的就餐人数，如果没有则使用传入的参数，或默认为1
          final diningNumber = cart.diningNumber > 0 ? cart.diningNumber : (seatedNumber ?? 1);
          _ref.read(peopleCountStateProvider.notifier).set(diningNumber);
          
          // 如果传入了新的就餐人数且与订单中的不同，则更新到服务器
          if (seatedNumber != null && seatedNumber > 0 && seatedNumber != cart.diningNumber) {
            await updateDiningNumber(seatedNumber);
          }
        }
        
      } catch (e) {
        debugPrint("获取购物车数据失败，创建新订单: $e");
        await createOrderAndInitializeCart(
          tableNumber: isTableOrdering ? tableNumber : null,
        );

        if (seatedNumber != null && seatedNumber > 0) {
          _ref.read(peopleCountStateProvider.notifier).set(seatedNumber);
          await updateDiningNumber(seatedNumber);
        }
      }
    }
  }

  /// 创建订单并初始化购物车
  /// [tableNumber] 桌号（可选）
  Future<int> createOrderAndInitializeCart({String? tableNumber}) async {
    try {
      // 清空现有购物车数据
      cartItemList.clear();
      serviceFee = 0.0;
      cartItem = null;
      
      // 清空选中状态
      _ref.read(cartSelectedItemsProvider.notifier).set([]);
      
      // 创建订单
      final createOrderItem =
          await _orderCreationService.createOrder(tableNumber: tableNumber);

      print("订单创建成功，订单ID: ${createOrderItem.orderId}");
      print("购物车已初始化，可以开始添加商品");
      
      // 重置就餐方式 - 桌台点餐为堂食，直接点餐为自取
      final isTableOrdering = tableNumber != null && tableNumber.isNotEmpty;
      final defaultDiningType = isTableOrdering ? DiningType.dineIn : DiningType.selfPickup;
      _ref.read(diningTypeStateProvider.notifier).set(defaultDiningType);
      
      // 重置用餐人数为默认值1
      _ref.read(peopleCountStateProvider.notifier).set(1);
      
      // 创建订单后更新就餐人数到服务器
      await updateDiningNumber(1);
      
      // 刷新相关 Provider
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);

      return createOrderItem.orderId;
    } catch (e) {
      print("创建订单失败: $e");
      rethrow;
    }
  }

  /// 添加单商品到购物车（服务器）
  /// [productId] 商品ID
  /// [quantity] 数量
  /// [remark] 备注
  /// [productConfig] 商品配置
  Future<void> addSingleProductToCartServer({
    required int productId,
    required num quantity,
    String? remark,
    required ProductConfig productConfig,
    String? tableNumber,
    int? discountType,
    double? discount,
    String? discountReason,
    double? updatePrice,
  }) async {
    int? orderId = _ref.read(currentOrderIdProvider);
    orderId ??= await createOrderAndInitializeCart(tableNumber: tableNumber);
    debugPrint("正在向订单 #$orderId 添加单商品 #$productId");
    try {
      final request = CartAddRequest.singleProduct(
        orderId: orderId,
        productId: productId,
        quantity: quantity,
        remark: remark,
        productConfig: productConfig,
      );
      await _ref.read(cartIntranetRepositoryProvider).addToCart(request);
      debugPrint("单商品已成功添加到购物车");
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
      _ref.invalidate(productControllerProvider);
    } catch (e) {
      debugPrint("添加单商品到购物车失败: $e");
      // rethrow;
    }
  }

  /// 添加套餐到购物车（服务器）
  /// [productId] 套餐ID
  /// [quantity] 数量
  /// [remark] 备注
  /// [comboConfig] 套餐配置
  Future<void> addComboToCartServer({
    required int productId,
    required int quantity,
    String? remark,
    required List<ComboConfig> comboConfig,
    String? tableNumber,
    int? discountType,
    double? discount,
    String? discountReason,
    double? updatePrice,
  }) async {
    int? orderId = await _orderCreationService.getCurrentOrderId();
    orderId ??= await createOrderAndInitializeCart(tableNumber: tableNumber);
    debugPrint("正在向订单 #$orderId 添加套餐 #$productId");

    try {
      final request = CartAddRequest.combo(
        orderId: orderId,
        productId: productId,
        quantity: quantity,
        remark: remark,
        comboConfig: comboConfig,
      );
      await _ref.read(cartIntranetRepositoryProvider).addToCart(request);
      debugPrint("套餐已成功添加到购物车");
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
      _ref.invalidate(productControllerProvider);
    } catch (e) {
      debugPrint("添加套餐到购物车失败: $e");
      rethrow;
    }
  }

  /// 完成订单并清空购物车
  Future<void> completeOrderAndClearCart() async {
    final orderId = await _orderCreationService.getCurrentOrderId();
    if (orderId == null) {
      throw Exception("没有活跃的订单");
    }
    debugPrint("正在完成订单 #$orderId");

    try {
      // 这里可以添加提交订单到服务器的逻辑
      // await _ref.read(cartIntranetRepositoryProvider).submitOrder(orderId);

      // 完成订单，清除订单ID
      await _orderCreationService.completeOrder();

      // 清空购物车
      await clearCart();

      print("订单 #$orderId 已完成，购物车已清空");
    } catch (e) {
      print("完成订单失败: $e");
      rethrow;
    }
  }

  //取单
  Future<void> pickUpOrder(OrderDraftItem item) async {
    debugPrint("开始取单，订单ID: ${item.orderId}");
    await _ref.read(cartIntranetRepositoryProvider).pickDraft(item.orderId);
    final cart = await _ref.read(cartIntranetRepositoryProvider).getCart(item.orderId);
    cartItem = cart;
    cartItemList = List<CartProductItem>.from(cart.cartItemList);
    
    debugPrint("取单后购物车数据，共 ${cartItemList.length} 个商品");
    
    // 设置当前订单ID并保存到本地存储
    final storageService = _ref.read(keyValueStorageServiceProvider);
    await storageService.setOrderId(item.orderId);
    _ref.read(currentOrderIdProvider.notifier).set(item.orderId);
    
    _ref.invalidate(cartPriceProvider);
    _ref.invalidate(cartControllerProvider);
    
    // 刷新已存单列表
    await getOrderDrafts();
  }

  //挂单
  Future<void> holdOrder(BuildContext ctx) async {
    String toastText = ctx.locale.orderSavedSuccess;
    if (cartItem != null) {
      await _ref.read(cartIntranetRepositoryProvider).saveDraft(cartItem!.orderId);
      cartItemList.clear();
      serviceFee = 0.0;
      
      // 重置餐牌号
      cartItem = cartItem?.copyWith(mealVoucher: '');
      
      _ref.read(orderCreationServiceProvider).clearCurrentOrderId();
      _ref.read(cartSelectedItemsProvider.notifier).set([]);
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);

      // 根据点餐方式重置就餐方式：桌台点餐为堂食，直接点餐为自取
      final isTableOrdering = cartItem?.tableNumber != null && cartItem!.tableNumber.isNotEmpty;
      final defaultDiningType = isTableOrdering ? DiningType.dineIn : DiningType.selfPickup;
      _ref.read(diningTypeStateProvider.notifier).set(defaultDiningType);
      _ref.read(peopleCountStateProvider.notifier).set(1);

      KPToast.show(content: toastText);
      
      // 刷新已存单列表
      await getOrderDrafts();
    } else {
      // 如果没有 cartItem，直接清空购物车
      cartItemList.clear();
      serviceFee = 0.0;
      _ref.read(orderCreationServiceProvider).clearCurrentOrderId();
      _ref.read(cartSelectedItemsProvider.notifier).set([]);
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
      
      // 重置就餐方式和就餐人数（默认为直接点餐）
      _ref.read(diningTypeStateProvider.notifier).set(DiningType.selfPickup);
      _ref.read(peopleCountStateProvider.notifier).set(1);
    }
  }

  ///获取已存单数据
  Future<void> getOrderDrafts() async {
    try {
      final drafts = await _ref.read(cartIntranetRepositoryProvider).getOrderDraftList();
      _ref.read(orderDraftsProvider.notifier).set(drafts);
      debugPrint("已存单数据获取成功，共 ${drafts.length} 条记录");
    } catch (e) {
      debugPrint("获取已存单数据失败: $e");
    }
  }

  Future<void> deleteCartProduct(int orderItemId) async {
    await _ref.read(cartIntranetRepositoryProvider).deleteCartProduct(orderItemId);
    _ref.invalidate(cartControllerProvider);
    _ref.invalidate(cartPriceProvider);
  }

  //清空购物车
  Future<void> clearCart() async {
    int? orderId = _ref.watch(currentOrderIdProvider);
    if (orderId != null) {
      cartItemList.clear();
      serviceFee = 0.0;
      await _ref.read(cartIntranetRepositoryProvider).clearCart(orderId);
      _ref.read(orderCreationServiceProvider).clearCurrentOrderId();
      _ref.read(cartSelectedItemsProvider.notifier).set([]);
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
    }
  }

  //修改订单备注
  Future<void> updateOrderNotes(String notes) async {
    final orderId = await _orderCreationService.getCurrentOrderId();
    if (orderId != null) {
      await _ref
          .read(cartIntranetRepositoryProvider)
          .setOrderRemark(orderId: orderId, remark: notes);
      cartItem = cartItem?.copyWith(remark: notes);
      _ref.invalidate(cartPriceProvider);
    }
  }

  double curDiscount = 0.0;

  //设置折扣
  Future<void> setDiscount(double discount, int discountType, String reason) async {
    if (discount < 0) {
      throw Exception("折扣不能为负数");
    }
    final orderId = await _orderCreationService.getCurrentOrderId();
    if (orderId != null) {
      await _ref.read(cartIntranetRepositoryProvider).setOrderDisCount(
            orderId: orderId,
            discount: discount,
            reason: reason,
            discountType: discountType,
          );
      curDiscount = discount;
      _ref.invalidate(cartPriceProvider);
    }
  }

  //修改服务费
  Future<void> updateServiceFee(double fee) async {
    serviceFee = fee;
    _ref.invalidate(cartPriceProvider);
  }

  /// 更新餐牌号
  /// [mealVoucher] 餐牌号
  Future<void> updateMealVoucher(String mealVoucher, BuildContext? context) async {
    int? orderId = await _orderCreationService.getCurrentOrderId();
    String emptyText = '';
    if (context != null&&context.mounted) {
      emptyText = context.locale.pickUpNumberEmptyTip;
    }

    if(mealVoucher.isEmpty){
      KPToast.show(content: emptyText);
      return;
    }

    KPLoading.show();
    try {
      // 如果没有订单 ID，先创建订单
      if (orderId == null) {
        debugPrint("更新餐牌号：没有订单ID，先创建订单");
        final createOrderItem = await createOrderAndInitializeCart();
        orderId = createOrderItem;
      }
      
      debugPrint("更新餐牌号: $mealVoucher, 订单ID: $orderId");
      await _ref.read(cartIntranetRepositoryProvider).setMealVoucher(
        orderId: orderId,
        mealVoucher: mealVoucher,
      );
      
      // 更新本地数据
      cartItem = cartItem?.copyWith(mealVoucher: mealVoucher);
      _ref.invalidate(cartPriceProvider);
      debugPrint("餐牌号更新成功: $mealVoucher");
    } catch (e) {
      debugPrint("更新餐牌号失败: $e");
    } finally {
      KPLoading.dismiss();
    }
  }

  //修改某个商品或套餐的数量
  Future<void> updateCartItemQuantity(int index, double quantity) async {
    if (index < 0 || index >= cartItemList.length) return;
    
    // 确保数量在有效范围内
    if (quantity < 1 || quantity > 999) {
      debugPrint("数量超出范围: $quantity，有效范围: 1-999");
      return;
    }
    
    CartProductItem item = cartItemList[index];
    // KPLoading.show();
    try {
      // 调用服务器接口更新数量
      await _ref.read(cartIntranetRepositoryProvider).setCartItemQuantity(
        orderItemId: item.orderItemId,
        quantity: quantity.toDouble(),
      );
      
      // 更新本地数据
      cartItemList[index] = item.copyWith(quantity: quantity);
      
      // 同时更新 cartItem 中的对应商品
      if (cartItem != null) {
        final updatedCartItemList = cartItem!.cartItemList.map((cartItem) {
          if (cartItem.orderItemId == item.orderItemId) {
            return cartItem.copyWith(quantity: quantity);
          }
          return cartItem;
        }).toList();
        
        cartItem = cartItem!.copyWith(cartItemList: updatedCartItemList);
      }
      _ref.invalidate(productControllerProvider);
      debugPrint("商品数量更新成功: ${item.commodityName} -> $quantity");
    } catch (e) {
      debugPrint("更新商品数量失败: $e");
      // 可以选择重新获取购物车数据来同步状态
      // await getCartItemsFromServer(cartItem?.orderId);
    } finally {
      _ref.invalidate(cartControllerProvider);
      // 无论成功还是失败，确保关闭加载框
      // KPLoading.dismiss();
    }
  }

  //批量操作
  Future<void> batchAction(CartBatchActionMark mark) async {
    List<int> selectedItems = _ref.read(cartSelectedItemsProvider);
    print("选中项------$selectedItems");
    CartItemStatus? itemStatus;
    switch (mark) {
      case CartBatchActionMark.dishesServed:
        itemStatus = CartItemStatus.dishesServed;
        break;
      case CartBatchActionMark.returnDish:
        itemStatus = CartItemStatus.returnDish;
        break;
      case CartBatchActionMark.packaging:
        itemStatus = CartItemStatus.packaging;
        break;
      case CartBatchActionMark.urgeDishes:
        itemStatus = CartItemStatus.urgeDishes;
        break;
      default:
        break;
    }
    
    if (itemStatus != null) {
      // 批量更新所有选中的商品
      for (int i = 0; i < selectedItems.length; i++) {
        if (selectedItems[i] >= 0 && selectedItems[i] < cartItemList.length) {
          final item = cartItemList[selectedItems[i]];
          
          // 调用后端接口更新状态
          try {
            // 根据不同状态调用不同接口
            switch (itemStatus.value) {
              case 1: // CartItemStatus.dishesServed.value
                await _ref.read(orderIntranetRepositoryProvider).servedOrderItem(
                  orderItemId: item.orderItemId,
                  status: 1 // 1=划菜
                );
                // 更新本地状态
                cartItemList[selectedItems[i]] = item.copyWith(isServed: 1);
                break;
                
              case 2: // CartItemStatus.urgeDishes.value  
                await _ref.read(orderIntranetRepositoryProvider).urgeOrderItem(
                  orderItemId: item.orderItemId,
                  status: 1 // 1=催菜
                );
                // 更新本地状态
                final currentTime = DateTime.now().millisecondsSinceEpoch;
                cartItemList[selectedItems[i]] = item.copyWith(isUrge: 1, urgeTime: currentTime);
                break;
                
              case 3: // CartItemStatus.packaging.value
                // 暂不处理
                break;
                
              case 6: // CartItemStatus.returnDish.value
                await _ref.read(orderIntranetRepositoryProvider).returnOrderItem(
                  orderItemId: item.orderItemId,
                  status: 1, // 1=退菜
                );
                // 更新本地状态
                cartItemList[selectedItems[i]] = item.copyWith(isReturn: 1);
                break;
                
              default:
                debugPrint("未知状态: ${itemStatus.value}");
                continue;
            }
          } catch (e) {
            debugPrint("批量更新状态失败：${item.orderItemId} - $e");
          }
        }
      }
    }
    
    // 清空选中项
    _ref.read(cartSelectedItemsProvider.notifier).set([]);
    
    // 刷新UI
    _ref.invalidate(cartControllerProvider);
    _ref.invalidate(cartPriceProvider);
  }

  /// 更新用餐人数
  Future<void> updateDiningNumber(int newValue) async {
    if (newValue < 1 || newValue > 999) return;
    final orderId = await _orderCreationService.getCurrentOrderId();
    if (orderId != null) {
      try {
        await _ref.read(cartIntranetRepositoryProvider).setDiningNumber(orderId: orderId, number: newValue);
        _ref.read(peopleCountStateProvider.notifier).set(newValue);
      } catch (e) {
        debugPrint("更新用餐人数失败: $e");
      }
    }
  }

  /// 更新单个购物车商品状态（status）
  /// @param orderItemId 要更新的商品ID
  /// @param itemStatus 目标状态标记
  Future<void> updateCartItemStatus(int orderItemId, int itemStatus,{String? reason}) async {
    try {
      // 根据不同状态调用不同接口
      switch (itemStatus) {
        case 1: // CartItemStatus.dishesServed.value
          await _ref.read(orderIntranetRepositoryProvider).servedOrderItem(
            orderItemId: orderItemId,
            status: 1 // 1=划菜
          );
          // 更新本地状态
          cartItemList = [
            for (final item in cartItemList)
              item.orderItemId == orderItemId 
                  ? item.copyWith(isServed: 1)
                  : item
          ];
          break;
        case 2: // CartItemStatus.urgeDishes.value  
          await _ref.read(orderIntranetRepositoryProvider).urgeOrderItem(
            orderItemId: orderItemId,
            status: 1 // 1=催菜
          );
          // 更新本地状态
          final currentTime = DateTime.now().millisecondsSinceEpoch;
          cartItemList = [
            for (final item in cartItemList)
              item.orderItemId == orderItemId 
                  ? item.copyWith(isUrge: 1, urgeTime: currentTime)
                  : item
          ];
          break;
        case 6: // CartItemStatus.returnDish.value
          await _ref.read(orderIntranetRepositoryProvider).returnOrderItem(
            orderItemId: orderItemId,
            status: 1, // 1=退菜
            reason: reason,
          );
          // 更新本地状态
          cartItemList = [
            for (final item in cartItemList)
              item.orderItemId == orderItemId 
                  ? item.copyWith(isReturn: 1)
                  : item
          ];
          break;
        default:
          debugPrint("未知状态: $itemStatus");
          return;
      }
      
      // 通知UI刷新
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
    } catch (e) {
      debugPrint("更新购物车商品状态失败: $e");
    }
  }

  /// 取消退菜
  Future<void> cancelReturnDish(int orderItemId,{String? reason}) async {
    try {
      // 调用接口取消退菜
      await _ref.read(orderIntranetRepositoryProvider).returnOrderItem(
        orderItemId: orderItemId,
        status: 2,// 2=取消退菜
        reason: reason,
      );
      
      // 更新本地cartItemList
      cartItemList = [
        for (final item in cartItemList)
          item.orderItemId == orderItemId ? item.copyWith(isReturn: 0) : item
      ];
      
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
    } catch (e) {
      debugPrint("取消退菜失败: $e");
    }
  }

  /// 取消划菜
  Future<void> cancelDishesServed(int orderItemId) async {
    try {
      // 调用接口取消划菜
      await _ref.read(orderIntranetRepositoryProvider).servedOrderItem(
        orderItemId: orderItemId,
        status: 2 // 2=取消划菜
      );
      
      // 更新本地cartItemList
      cartItemList = [
        for (final item in cartItemList)
          item.orderItemId == orderItemId ? item.copyWith(isServed: 0) : item
      ];
      
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
    } catch (e) {
      debugPrint("取消划菜失败: $e");
    }
  }

  /// 取消催菜
  Future<void> cancelUrgeDishes(int orderItemId) async {
    try {
      // 调用接口取消催菜
      await _ref.read(orderIntranetRepositoryProvider).urgeOrderItem(
        orderItemId: orderItemId,
        status: 2 // 2=取消催菜
      );
      
      // 更新本地cartItemList
      cartItemList = [
        for (final item in cartItemList)
          item.orderItemId == orderItemId 
            ? item.copyWith(isUrge: 0)
            : item
      ];
      
      _ref.invalidate(cartPriceProvider);
      _ref.invalidate(cartControllerProvider);
    } catch (e) {
      debugPrint("取消催菜失败: $e");
    }
  }
  

}

@riverpod
CartService cartService(Ref ref) {
  return CartService(ref: ref);
}

@riverpod
class ShowCartBatchAction extends _$ShowCartBatchAction {
  @override
  bool build() => false;

  // 切换购物车批量操作显示状态
  void change() {
    state = !state;
  }

  // 设置显示状态
  void set(bool value) {
    state = value;
  }
}

@Riverpod(keepAlive: true)
class CartSelectedItems extends _$CartSelectedItems {
  @override
  List<int> build() => [];

  // 设置显示状态
  void set(List<int> value) {
    state = value;
  }
}

@Riverpod(keepAlive: true)
class DiningStyles extends _$DiningStyles {
  @override
  List<DiningStyleItem> build() => [];

  void set(List<DiningStyleItem> list) {
    state = list;
  }
}

@Riverpod(keepAlive: true)
class DiningTypeState extends _$DiningTypeState {
  @override
  DiningType build() => DiningType.selfPickup;

  void set(DiningType type) {
    state = type;
  }
}

@Riverpod(keepAlive: true)
class PeopleCountState extends _$PeopleCountState {
  @override
  int build() => 2;

  void set(int count) {
    // 确保最小值为1
    state = count < 1 ? 1 : count;
  }
}

@Riverpod(keepAlive: true)
class TableOrderingState extends _$TableOrderingState {
  @override
  bool build() => false;

  void set(bool value) {
    state = value;
  }
}

@Riverpod(keepAlive: true)
class SelectedTableTitleState extends _$SelectedTableTitleState {
  @override
  String build() => '';

  void set(String title) {
    state = title;
  }
}

@Riverpod(keepAlive: true)
class LastPickUpNumberState extends _$LastPickUpNumberState {
  @override
  String build() => '';

  void set(String value) {
    state = value;
  }
}

final orderDraftsProvider = StateNotifierProvider<OrderDraftsNotifier, List<OrderDraftItem>>((ref) {
  return OrderDraftsNotifier();
});

class OrderDraftsNotifier extends StateNotifier<List<OrderDraftItem>> {
  OrderDraftsNotifier() : super([]);

  void set(List<OrderDraftItem> drafts) {
    state = drafts;
  }
}


