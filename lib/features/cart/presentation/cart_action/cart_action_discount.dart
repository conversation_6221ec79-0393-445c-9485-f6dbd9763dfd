import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/kp_remark.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/domain/discount_item.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';
import 'package:kpos/features/store/application/store_prepared_reason_service.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';

import 'cart_custom_discount.dart';

class CartActionDiscount extends ConsumerStatefulWidget {
  final VoidCallback onClose;

  const CartActionDiscount({super.key, required this.onClose});

  @override
  ConsumerState createState() => _CartActionDiscountState();
}

class _CartActionDiscountState extends ConsumerState<CartActionDiscount>
    with WidgetsBindingObserver {
  final _discountFormKey = GlobalKey<FormBuilderState>();
  final _reasonFormKey = GlobalKey<FormBuilderState>();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _reasonEditController = TextEditingController();

  List<String> selectedReasonValues = [];
  DiscountItem? selectedDiscountItem;

  final reasonEmptyNotifier = ValueNotifier(false);

  ValueNotifier<String> customDiscount = ValueNotifier<String>('');

  final setDiscountLoadingProvider = StateProvider<bool>((ref) => false);

  ///折扣options列表-假数据
  ///DiscountItem的type表示折扣类型，1=百分比折扣，2=固定金额折扣，3=自定义
  final List<DiscountItem> _discountItems = [
    const DiscountItem(type: 1, discount: 5),
    const DiscountItem(type: 1, discount: 80),
    const DiscountItem(type: 1, discount: 70),
    const DiscountItem(type: 1, discount: 50),
    const DiscountItem(type: 2, discount: 5),
    const DiscountItem(type: 2, discount: 10),
    const DiscountItem(type: 2, discount: 20),
    const DiscountItem(type: 3, discount: 0),
    const DiscountItem(type: 1, discount: 100),
  ];

  double _keyboardHeight = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    if (bottomInset == 0 && _keyboardHeight > 0) {
      if (selectedReasonValues.isEmpty && _reasonEditController.text.trim().isEmpty) {
        reasonEmptyNotifier.value = true;
      } else {
        reasonEmptyNotifier.value = false;
      }
    } else if (bottomInset > 0 && _keyboardHeight == 0) {
      // 键盘弹出
    }
    _keyboardHeight = bottomInset;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 数据回显逻辑
    if (selectedDiscountItem == null &&
        selectedReasonValues.isEmpty &&
        _reasonEditController.text.isEmpty) {
      final cartItem = ref.read(cartServiceProvider).cartItem;
      if (cartItem != null) {
        final discount = cartItem.discount;
        final discountType = cartItem.discountType;

        final match = _discountItems.firstWhere(
          (item) =>
              item.type == discountType &&
              ((item.type == 1 && (item.discount / 100).toStringAsFixed(2) == discount.toStringAsFixed(2)) ||
               (item.type == 2 && item.discount.toStringAsFixed(2) == discount.toStringAsFixed(2))),
          orElse: () => DiscountItem(type: discountType, discount: discountType == 1 ? discount * 100 : discount),
        );

        // 判断是否为自定义
        final isCustom = !_discountItems.any((item) =>
          item.type == discountType &&
          ((item.type == 1 && (item.discount / 100).toStringAsFixed(2) == discount.toStringAsFixed(2)) ||
           (item.type == 2 && item.discount.toStringAsFixed(2) == discount.toStringAsFixed(2)))
        );

        if (isCustom) {
          // 选中自定义入口（type 3）
          selectedDiscountItem = _discountItems.firstWhere((item) => item.type == 3);
          // 只有当折扣不为0时才设置customDiscount值
          if (discount > 0.001) {
            customDiscount.value = discountType == 1 ? (discount * 100).toString() : "-${discount.toString()}";
          } else {
            customDiscount.value = '';
          }
        } else {
          selectedDiscountItem = match;
          customDiscount.value = '';
        }

        // 原因回显同前
        final reason = cartItem.discountReason ?? '';
        if (reason.isNotEmpty) {
          final cache = ref.read(storePreparedReasonCacheProvider);
          final reasonTags = cache.getDiscountReasons()?.map((e) => e.preparedReasonName).toList() ?? [];
          final parts = reason.split(';');
          final tags = <String>[];
          final custom = <String>[];
          for (final part in parts) {
            if (reasonTags.contains(part)) {
              tags.add(part);
            } else if (part.trim().isNotEmpty) {
              custom.add(part);
            }
          }
          selectedReasonValues = tags;
          _reasonEditController.text = custom.join(';');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Container(
        height: double.maxFinite,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: const BoxDecoration(
          color: KPColors.fillGrayLightLightest,
          border: Border(
            top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
            right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTop(),
            const SizedBox(height: 24),
            //售价
            Text(
              'Sales123',
              style: KPFontStyle.headingMedium.copyWith(color: KPColors.textGrayPrimary),
            ),
            const SizedBox(height: 8),
            Text(
              'S\$21.50',
              style: KPFontStyle.amountMedium.copyWith(
                color: KPColors.textGrayPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Text(
                  context.locale.discount,
                  style:
                      KPFontStyle.headingXSmall.copyWith(color: KPColors.textGrayPrimary),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    context.locale.noDiscountForAddOns,
                    style:
                        KPFontStyle.bodySmall.copyWith(color: KPColors.textGraySecondary),
                  ),
                ),
                Text(
                  context.locale.clear,
                  style: const TextStyle(
                    fontSize: 14,
                    color: KPColors.textBrandDefault,
                    fontWeight: FontWeight.w700,
                  ),
                ).onTap(() {
                  _discountFormKey.currentState?.reset();
                }),
              ],
            ),
            const SizedBox(height: 8),
            //折扣选项
            _buildDiscount(),
            const SizedBox(height: 24),
            //原因
            Expanded(
              child: _buildReasonSection(),
            ),
            //下方分割线
            Container(
              height: 1,
              color: KPColors.borderGrayLightBase,
              margin: const EdgeInsets.only(bottom: 12),
            ),
            //按钮
            _buildBottom(),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return Row(
      children: [
        Text(
          context.locale.orderDiscount,
          style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
        ),
        const Expanded(child: SizedBox()),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(
            Icons.close,
            size: 24,
          ),
        ),
      ],
    );
  }

  void showNumberInputDialog(Function(DiscountItem) onChange) {
    showDialog(
      context: context,
      builder: (context) {
        return const CartCustomDiscount();
      },
    ).then((value) {
      if(value==null)return;
      value as DiscountItem;
      // 只有当折扣值大于0时才显示
      if (value.discount > 0.001) {
        customDiscount.value = value.type==1? value.discount.toString():"-${value.discount.toString()}";
      } else {
        customDiscount.value = '';
      }
      onChange(value);
    });
  }

  //获取自定义折扣的文字
  String getCustomDiscountText(String discount) {
    if (discount.isEmpty) return '';
    
    // 处理金额减免情况
    if (discount.startsWith('-')) {
      // 解析数值
      double? value = double.tryParse(discount.substring(1));
      // 如果值为0或接近0，不显示
      if (value == null || value < 0.01) return '';
      return ' (-S\$${discount.substring(1)})';
    }
    
    // 处理百分比情况
    double? value = double.tryParse(discount);
    // 如果值为0或接近0，不显示
    if (value == null || value < 0.01) return '';
    return ' ($discount%)';
  }

  Widget _buildDiscount() {
    return FormBuilder(
      key: _discountFormKey,
      child: FormBuilderField<DiscountItem>(
        name: 'discount_option',
        builder: (FormFieldState<DiscountItem> field) {
          return Wrap(
            spacing: 8,
            runSpacing: 4,
            children: _discountItems.map((item) {
              final isSelected = field.value == item;
              String label;
              switch (item.type) {
                case 1:
                  if(item.discount>=100){
                    label = 'Free';
                  } else {
                    label = '${item.discount.toStringAsFixed(0)}% off';
                  }
                  break;
                case 2:
                  label = '-S\$${item.discount.toStringAsFixed(0)}';
                  break;
                case 3:
                  label = 'Custom';
                  break;
                default:
                  label = '';
              }
              return ChoiceChip(
                label: ValueListenableBuilder(
                  valueListenable: customDiscount,
                  builder: (context, discount, _) {
                    return Text(
                      label + (item.type == 3 ? getCustomDiscountText(discount) : ''),
                      style: KPFontStyle.headingXSmall.copyWith(
                        color: isSelected
                            ? KPColors.textBrandDefault
                            : KPColors.textGrayPrimary,
                      ),
                    );
                  },
                ),
                selected: isSelected,
                onSelected: (selected) {
                  if (item.type == 3) {
                    // 如果是自定义选项
                    if (isSelected && customDiscount.value.isNotEmpty) {
                      // 已选中且有自定义值，再次点击时取消选择
                      field.didChange(null);
                      selectedDiscountItem = null;
                      customDiscount.value = '';
                    } else {
                      // 未选中或没有自定义值时，显示输入弹窗
                      showNumberInputDialog((value) {
                        field.didChange(item);
                        selectedDiscountItem = item.copyWith(type: value.type, discount: value.discount);
                      });
                    }
                  } else {
                    field.didChange(selected ? item : null);
                    selectedDiscountItem = selected ? item : null;
                    if (selected) {
                      customDiscount.value = '';
                    }
                  }
                },
                backgroundColor: const Color(0xFFF4F5F7),
                selectedColor: KPColors.fillBrandLightest,
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    color: isSelected ? KPColors.borderBrandDefault : Colors.transparent,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                showCheckmark: false,
              );
            }).toList(),
          );
        },
      ),
    );
  }

  Widget _buildReasonWidget(List<String> reasons) {
    return ValueListenableBuilder(
        valueListenable: reasonEmptyNotifier,
        builder: (context, isEmpty, _) {
          return KPRemark(
            remarkFormKey: _reasonFormKey,
            isContentEmpty: isEmpty,
            hintText: context.locale.reason,
            title: context.locale.reason,
            remarkList: reasons,
            isShowAsterisk: true,
            formBuilderFieldName: 'discount_reasons',
            controller: _reasonEditController,
            focusNode: _focusNode,
            onRemarkChanged: (List<String> remark) {
              selectedReasonValues = remark;
              if (selectedReasonValues.isNotEmpty) {
                reasonEmptyNotifier.value = false;
              }
            },
            onTextFieldTap: () {
              reasonEmptyNotifier.value = false;
            },
            inputErrorText: context.locale.discountReasonInputTip,
          );
        });
  }

  Widget _buildReasonSection() {
    final cache = ref.read(storePreparedReasonCacheProvider);
    final discountReasons = cache.getDiscountReasons();

    if (discountReasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: discountReasonsProvider,
        dataBuilder: (context, ref, data) {
          return SingleChildScrollView(
              child: _buildReasonWidget(data.map((e) => e.preparedReasonName).toList()));
        },
      );
    } else {
      // 使用缓存数据
      return SingleChildScrollView(
          child: _buildReasonWidget(
              discountReasons.map((e) => e.preparedReasonName).toList()));
    }
  }

  Widget _buildBottom() {
    final isLoading = ref.watch(setDiscountLoadingProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 106,
          height: 48,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(KPRadius.l),
            color: KPColors.fillGrayLightLighter,
          ),
          child: Text(
            context.locale.cancelCaps,
            style: const TextStyle(
              color: KPColors.textGrayPrimary,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
          ),
        ).onTap(() {
          widget.onClose();
        }),
        const SizedBox(width: 16),
        KPLoadingButton(
          onPressed: () async {
            if (selectedDiscountItem != null) {
              ref.read(setDiscountLoadingProvider.notifier).state = true;
              String reason = '';
              if (selectedReasonValues.isNotEmpty) {
                reason = selectedReasonValues.join(';');
              }
              if (_reasonEditController.text.trim().isNotEmpty) {
                reason += (reason.isNotEmpty ? ';' : '') + _reasonEditController.text.trim();
              }
              int type = selectedDiscountItem!.type;
              try {
                await ref.read(cartServiceProvider).setDiscount(
                  type == 1 ? selectedDiscountItem!.discount / 100 : selectedDiscountItem!.discount,
                  type,
                  reason,
                );
              } catch (e) {
                // 可选：弹出错误提示
              } finally {
                widget.onClose();
                ref.read(setDiscountLoadingProvider.notifier).state = false;
              }
            }
          },
          text: context.locale.confirmCaps,
          isLoading: isLoading,
          backgroundColor: KPColors.fillGrayDarDarkest,
          textColor: KPColors.textGrayInverse,
          width: 106,
          height: 48,
        ),
      ],
    );
  }
}
