import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/kp_remark.dart';
import 'package:kpos/common/components/kp_toast.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';
import 'package:kpos/features/store/application/store_prepared_reason_service.dart';
import 'package:kpos/features/store/domain/service_fee_item.dart';
import '../../application/cart_service_fee_service.dart';

class CartActionServiceFee extends ConsumerStatefulWidget {
  final VoidCallback onClose;

  const CartActionServiceFee({super.key, required this.onClose});

  @override
  ConsumerState createState() => _CartActionServiceFeeState();
}

class _CartActionServiceFeeState extends ConsumerState<CartActionServiceFee>
    with WidgetsBindingObserver {
  final _feeFormKey = GlobalKey<FormBuilderState>();
  final _reasonFormKey = GlobalKey<FormBuilderState>();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _reasonEditController = TextEditingController();

  List<String> selectedReasonValues = [];
  List<int> selectedFeeValues = [];

  final reasonEmptyNotifier = ValueNotifier(false);
  double _keyboardHeight = 0;

  // 服务费列表 - 从服务器获取
  List<ServiceFeeItem> _serviceFeeList = [];
  List<int> _initialSelectedItems = [];

  // 获取初始选中的服务费索引 -根据isCheck==1
  List<int> _getInitialSelectedItems(List<ServiceFeeItem> serviceFees) {
    return serviceFees.asMap().entries
        .where((entry) => entry.value.isCheck == 1)
        .map((entry) => entry.key)
        .toList();
  }

  ValueNotifier<bool> isShowReason = ValueNotifier<bool>(false);

  final _loadingProvider = StateProvider<bool>((ref) => false);

  // void _resetServiceFeeState() {
  //   _serviceFeeList = [];
  //   _initialSelectedItems = [];
  //   selectedFeeValues = [];
  //   selectedReasonValues = [];
  //   _reasonEditController.clear();
  //   isShowReason.value = false;
  //   reasonEmptyNotifier.value = false;
  //   final orderId = ref.read(currentOrderIdProvider);
  //   if (orderId != null) {
  //     ref.invalidate(cartServiceFeeListProvider(orderId: orderId));
  //   }
  // }

  Future<void> _submitForm() async {
    if (_feeFormKey.currentState?.saveAndValidate() ?? false) {
      print('已选择的服务费：$selectedFeeValues');
      print('已选择的取消原因：$selectedReasonValues');
      if (isShowReason.value &&
          selectedReasonValues.isEmpty &&
          _reasonEditController.text.trim().isEmpty) {
        reasonEmptyNotifier.value = true;
        // KPToast.show(content: context.locale.inputCancelReasonTip);
        return;
      }
      List<ServiceFeeItem> selectedItems = _serviceFeeList
          .asMap()
          .entries
          .where((entry) => selectedFeeValues.contains(entry.key))
          .map((entry) => entry.value)
          .toList();
      // final total = selectedItems.fold<double>(
      //     0, (sum, item) => sum + _calculateServiceFeeAmount(item));
      // ref.read(cartServiceProvider).updateServiceFee(total);

      final orderId = ref.read(currentOrderIdProvider);
      if (orderId != null) {
        final idStr = selectedItems.map((e) => e.id.toString()).join(',');
        String reason = '';
        if (isShowReason.value) {
          if (selectedReasonValues.isNotEmpty) {
            reason = selectedReasonValues.join(';');
          }
          if (_reasonEditController.text.trim().isNotEmpty) {
            reason += (reason.isNotEmpty ? ';' : '') + _reasonEditController.text.trim();
          }
        }
        await ref.read(cartServiceFeeServiceProvider).setOrderServiceFee(
          orderId: orderId,
          idStr: idStr,
          reason: reason.isNotEmpty ? reason : null,
        );
        if(mounted){
          KPToast.show(content: context.locale.setFeeSuccess);
        }
      }

      widget.onClose();
    }
  }

  // 计算服务费金额
  double _calculateServiceFeeAmount(ServiceFeeItem item) {
    if (item.type == 1) {
      // 按比例收取
      return item.chargeRate;
    } else {
      // 固定费用收取
      return item.fixedCharge;
    }
  }

  bool listEquality(List a, List b) {
    a.sort();
    b.sort();
    bool isEqual = a.length == b.length && a.every((v) => b[a.indexOf(v)] == v);
    return isEqual;
  }

  void onCheckBoxChanged(FormFieldState<List<int>> field, int index, bool checked) {
    final current = List<int>.from(field.value ?? []);
    if (checked) {
      // 取消选中
      current.remove(index);
    } else {
      // 选中
      current.add(index);
    }
    field.didChange(current);
    selectedFeeValues = current;

    // 判断是否有初始选中项被取消，控制取消原因区域显示
    final anyDeselected = _initialSelectedItems.any((i) => !current.contains(i));
    isShowReason.value = anyDeselected;
  }

  // 初始化服务费数据
  void _initializeServiceFeeData(List<ServiceFeeItem> serviceFees) {
    _serviceFeeList = serviceFees;
    _initialSelectedItems = _getInitialSelectedItems(serviceFees);
    selectedFeeValues = List.from(_initialSelectedItems);
  }

  @override
  void initState() {
    super.initState();
    print("asdgasdg");
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    if (bottomInset == 0 && _keyboardHeight > 0) {
      if (isShowReason.value &&
          selectedReasonValues.isEmpty &&
          _reasonEditController.text.trim().isEmpty) {
        reasonEmptyNotifier.value = true;
      } else {
        reasonEmptyNotifier.value = false;
      }
    } else if (bottomInset > 0 && _keyboardHeight == 0) {
      // 键盘弹出
    }
    _keyboardHeight = bottomInset;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _focusNode.dispose();
    _reasonEditController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Container(
        height: double.maxFinite,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: const BoxDecoration(
          color: KPColors.fillGrayLightLightest,
          border: Border(
            top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
            right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          ),
        ),
        child: FormBuilder(
          key: _feeFormKey,
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTop(),
                    const SizedBox(height: 14),
                    //表格
                    Flexible(child: _buildForm()),
                    const SizedBox(height: 24),
                    //原因填写
                    ValueListenableBuilder(
                      valueListenable: isShowReason,
                      builder: (ctx, isShow, _) {
                        return Visibility(
                          visible: isShow,
                          child: _buildReasonSection(),
                        );
                      },
                    ),
                  ],
                ),
              ),
              //下方分割线
              Container(
                height: 1,
                color: KPColors.borderGrayLightBase,
                margin: const EdgeInsets.only(bottom: 12),
              ),
              //按钮
              _buildBottom(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    final orderId = ref.read(currentOrderIdProvider); // 获取当前订单ID
    if(orderId==null) return const SizedBox();
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 24),
      child: KPAsyncValueWidget<List<ServiceFeeItem>>(
        asyncValueProvider: cartServiceFeeListProvider(orderId: orderId),
        dataBuilder: (context, ref, serviceFees) {
          // 初始化服务费数据
          if (_serviceFeeList.isEmpty) {
            _initializeServiceFeeData(serviceFees);
          }
          
          return FormBuilderField<List<int>>(
            name: 'selected_fees',
            initialValue: _initialSelectedItems, //初始化勾选的索引
            builder: (field) {
              return SingleChildScrollView(
                child: Column(
                  children: List.generate(_serviceFeeList.length, (index) {
                    final item = _serviceFeeList[index];
                    final checked = field.value?.contains(index) ?? false;
                    return InkWell(
                      onTap: () {
                        onCheckBoxChanged(field, index, checked);
                      },
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: checked,
                                onChanged: (val) {
                                  onCheckBoxChanged(field, index, checked);
                                },
                              ),
                              Expanded(child: _buildListItem(item)),
                            ],
                          ),
                          Container(
                            height: 1,
                            color: KPColors.borderGrayLightBase,
                            margin: const EdgeInsets.only(left: 16),
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              );
            },
          );
        },
      ),
    );
  }

  // 构建列表项
  Widget _buildListItem(ServiceFeeItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                _formatServiceFeeName(item),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w800,
                  color: KPColors.textGrayPrimary,
                ),
              ),
            ),
          ),
          // 数量
          Expanded(
            flex: 1,
            child: Text(
              "x${item.number}",
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: KPColors.textGrayPrimary,
              ),
            ),
          ),
          // 金额
          Expanded(
            flex: 1,
            child: Text(
              "S\$${_formatServiceFeeAmount(item)}",
              textAlign: TextAlign.right,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: KPColors.textGrayPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 格式化服务费名称显示
  String _formatServiceFeeName(ServiceFeeItem item) {
    if (item.type == 1) {
      // 按比例收取，在名字后面加上百分比
      double rate = item.chargeRate;
      // 判断是否为整数
      String rateStr = rate.truncateToDouble() == rate
          ? rate.toStringAsFixed(0)
          : rate.toStringAsFixed(2);
      return "${item.name}($rateStr%)";
    } else {
      // 固定费用收取，只显示名字
      return item.name;
    }
  }

  // 格式化服务费金额显示
  String _formatServiceFeeAmount(ServiceFeeItem item) {
    if (item.type == 1) {
      // todo 按比例收取，需要获取订单总价乘以这个比例
      return "1.88";
    } else {
      // 固定费用收取，显示固定金额
      return item.fixedCharge.toStringAsFixed(2);
    }
  }

  Widget _buildReasonWidget(List<String> reasonTags) {
    return ValueListenableBuilder(
        valueListenable: reasonEmptyNotifier,
        builder: (context, isEmpty, _) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: KPRemark(
              remarkFormKey: _reasonFormKey,
              isContentEmpty: isEmpty,
              hintText: context.locale.reason,
              title: context.locale.reason,
              remarkList: reasonTags,
              isShowAsterisk: true,
              formBuilderFieldName: 'fee_reason_tags',
              controller: _reasonEditController,
              focusNode: _focusNode,
              onRemarkChanged: (List<String> v) {
                selectedReasonValues = v;
                if (selectedReasonValues.isNotEmpty) {
                  reasonEmptyNotifier.value = false;
                }
              },
              onTextFieldTap: () {
                reasonEmptyNotifier.value = false;
              },
              inputErrorText: context.locale.inputCancelReasonTip,
            ),
          );
        });
  }

  Widget _buildTop() {
    return Row(
      children: [
        const SizedBox(width: 24),
        Text(
          context.locale.serviceFee,
          style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
        ),
        const Expanded(child: SizedBox()),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(
            Icons.close,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildBottom() {
    final isLoading = ref.watch(_loadingProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 106,
          height: 48,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(KPRadius.l),
            color: KPColors.fillGrayLightLighter,
          ),
          child: Text(
            context.locale.cancelCaps,
            style: const TextStyle(
              color: KPColors.textGrayPrimary,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
          ),
        ).onTap(() {
          widget.onClose();
        }),
        const SizedBox(width: 16),
        KPLoadingButton(
          onPressed: () async {
            ref.read(_loadingProvider.notifier).state = true;
            try {
              await _submitForm();
            } catch (e) {
              // 可选：弹出错误提示
            } finally {
              widget.onClose();
              ref.read(_loadingProvider.notifier).state = false;
            }
          },
          text: context.locale.confirmCaps,
          isLoading: isLoading,
          backgroundColor: KPColors.fillGrayDarDarkest,
          textColor: KPColors.textGrayInverse,
          width: 106,
          height: 48,
        ),
        const SizedBox(width: 24),
      ],
    );
  }

  Widget _buildReasonSection() {
    final cache = ref.read(storePreparedReasonCacheProvider);
    final serviceFeeCancelReasons = cache.getServiceFeeCancelReasons();
    
    if (serviceFeeCancelReasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: serviceFeeCancelReasonsProvider,
        dataBuilder: (context, ref, data) {
          return _buildReasonWidget(
            data.map((e) => e.preparedReasonName).toList(),
          );
        },
      );
    } else {
      // 使用缓存数据
      return _buildReasonWidget(
        serviceFeeCancelReasons.map((e) => e.preparedReasonName).toList(),
      );
    }
  }
}
