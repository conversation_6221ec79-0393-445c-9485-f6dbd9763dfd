import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/cart/domain/discount_item.dart';

class CartCustomDiscount extends ConsumerStatefulWidget {
  const CartCustomDiscount({super.key});

  @override
  ConsumerState createState() => _CartCustomDiscountState();
}

class _CartCustomDiscountState extends ConsumerState<CartCustomDiscount> with SingleTickerProviderStateMixin {

  late final TabController tabController;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.only(left: 24, right: 24, top: 24),
          width: 480,
          height: 554,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.locale.customDiscount,
                style: KPFontStyle.headingXLarge.copyWith(color: KPColors.textGrayPrimary),
              ),
              const SizedBox(height: 24),
              ExtendedTabBar(
                isScrollable: true,
                labelPadding: const EdgeInsets.only(left: 0, right: 32),
                indicatorColor: KPColors.textBrandDefault,
                indicatorSize: TabBarIndicatorSize.label,
                splashBorderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                indicatorWeight: 3,
                dividerColor: Colors.transparent,
                labelColor: KPColors.textBrandDefault,
                labelStyle: const TextStyle(color: KPColors.textBrandDefault, fontSize: 14, fontWeight: FontWeight.w500),
                unselectedLabelColor: Colors.black,
                unselectedLabelStyle: const TextStyle(color: Colors.black, fontSize: 14, fontWeight: FontWeight.w500),
                scrollDirection: Axis.horizontal,
                tabs: [
                  ExtendedTab(
                    height: 34,
                    text: '${context.locale.discount} (%)',
                  ),
                  ExtendedTab(
                    height: 34,
                    text: context.locale.reductionAmount,
                  ),
                ],
                controller: tabController,
              ),
              Container(
                width: double.maxFinite,
                height: .5,
                color: KPColors.borderGrayLightDarkest,
              ),
              const SizedBox(height: 10),
              Expanded(
                child: ExtendedTabBarView(controller: tabController,children: [
                  Container(
                    margin: const EdgeInsets.only(top: 16),
                    child: KPNumberKeyboard(
                      minValue: 1,
                      maxValue: 100,
                      onConfirmPressed: (value) {
                        if(value==null) return;
                        if(value>=1&&value<=100){
                          context.pop(DiscountItem(type: tabController.index+1,discount: value));
                        }
                      },
                      buttonWidth: 138,
                      buttonHeight: 54,
                      buttonSpacing: 8,
                      showDot: true,
                      title: '${context.locale.discount} (%)',
                      onCancelPressed: () {},
                    ),
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 16),
                    child: KPNumberKeyboard(
                      minValue: 0.01,
                      maxValue: 100,
                      onConfirmPressed: (value) {
                        if(value==null) return;
                        context.pop(DiscountItem(type: tabController.index+1,discount: value));
                      },
                      buttonWidth: 138,
                      buttonHeight: 54,
                      buttonSpacing: 8,
                      showDot: true,
                      title: context.locale.reductionAmount,
                      onCancelPressed: () {},
                    ),
                  ),
                ],),
              ),

            ],
          ),
        ),
      ],
    );
  }
}
