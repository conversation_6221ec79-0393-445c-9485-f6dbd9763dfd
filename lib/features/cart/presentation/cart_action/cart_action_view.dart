import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/auth/presentation/re_login_screen_controller.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/cart/presentation/cart_action/index.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

enum CartActionPageMark {
  main(value: 0),
  fee(value: 1),
  notes(value: 2),
  discount(value: 3),
  reprint(value: 4),
  batch(value: 5),
  clear(value: 6),
  cancelOrder(value: 7);

  const CartActionPageMark({
    required this.value,
  });

  final int value;

  static CartActionPageMark markToPageType(int value) {
    return CartActionPageMark.values.firstWhere(
          (e) => e.value == value,
      orElse: () => CartActionPageMark.main,
    );
  }
}

class OrderActionConfig {
  String icon;
  String Function(BuildContext context) title;
  int type = 0;
  CartActionPageMark mark = CartActionPageMark.main;

  OrderActionConfig({
    ///icon svg图标
    required this.icon,
    ///title 标题
    required this.title,

    ///type 0: 通用, 1: 直接点餐，2：桌台点餐
    required this.type,

    ///mark 要显示的二级页面标记，主页面为 0
    required this.mark,
  });
}

enum CartBatchActionMark {
  unknown(value: 0),
  dishesServed(value: 1),
  discount(value: 2),
  staff(value: 3),
  urgeDishes(value: 4),
  packaging(value: 5),
  returnDish(value: 6);

  const CartBatchActionMark({
    required this.value,
  });

  final int value;

  static CartBatchActionMark markToAction(int value) {
    return CartBatchActionMark.values.firstWhere(
          (e) => e.value == value,
      orElse: () => CartBatchActionMark.unknown,
    );
  }
}

class BatchActionConfig {
  String Function(BuildContext context) title;
  CartBatchActionMark batchMark;

  BatchActionConfig({
    required this.title,
    required this.batchMark,
  });
}

class CartActionView extends ConsumerStatefulWidget {
  final bool isTableOrdering;

  const CartActionView({super.key, required this.isTableOrdering});

  @override
  ConsumerState createState() => _CartActionViewState();
}

class _CartActionViewState extends ConsumerState<CartActionView> {
  final List<OrderActionConfig> _actionConfigs = [
    OrderActionConfig(
      icon: Assets.images.iconServiceFee.path,
      title: (context) => context.locale.serviceFee,
      type: 2,
      mark: CartActionPageMark.fee,
    ),
    OrderActionConfig(
      icon: Assets.images.iconOrdersNotes.path,
      title: (context) => context.locale.ordersNotes,
      type: 0,
      mark: CartActionPageMark.notes,
    ),
    OrderActionConfig(
      icon: Assets.images.iconDiscount.path,
      title: (context) => context.locale.discount,
      type: 0,
      mark: CartActionPageMark.discount,
    ),
    // OrderActionConfig(
    //   icon: Assets.images.iconStreamlineServing.path,
    //   title: (context) => context.locale.commissionStaff,
    //   type: 0,
    //   mark: CartActionPageMark.staff,
    // ),
    OrderActionConfig(
      icon: Assets.images.iconReprint.path,
      title: (context) => context.locale.reprint,
      type: 0,
      mark: CartActionPageMark.reprint,
    ),
    OrderActionConfig(
      icon: Assets.images.iconBatchAction.path,
      title: (context) => context.locale.batchAction,
      type: 0,
      mark: CartActionPageMark.batch,
    ),
    OrderActionConfig(
      icon: Assets.images.iconClearCart.path,
      title: (context) => context.locale.clearCart,
      type: 0,
      mark: CartActionPageMark.clear,
    ),
    OrderActionConfig(
      icon: Assets.images.iconCancelOrder.path,
      title: (context) => context.locale.cancelOrder,
      type: 0,
      mark: CartActionPageMark.cancelOrder,
    ),
  ];

  List<OrderActionConfig> get actionsList {
    //桌台点餐的操作列表
    if (widget.isTableOrdering) {
      return _actionConfigs.where((e) => e.type == 2 || e.type == 0).toList();
    }
    //直接点餐的操作列表
    return _actionConfigs.where((e) => e.type == 1 || e.type == 0).toList();
  }

  final List<BatchActionConfig> batchActionConfigs = [
    BatchActionConfig(
      title: (context) => context.locale.dishesServed,
      batchMark: CartBatchActionMark.dishesServed,
    ),
    BatchActionConfig(
      title: (context) => context.locale.discount,
      batchMark: CartBatchActionMark.discount,
    ),
    BatchActionConfig(
      title: (context) => context.locale.commissionStaff,
      batchMark: CartBatchActionMark.staff,
    ),
    BatchActionConfig(
      title: (context) => context.locale.urgeDishes,
      batchMark: CartBatchActionMark.urgeDishes,
    ),
    BatchActionConfig(
      title: (context) => context.locale.packaging,
      batchMark: CartBatchActionMark.packaging,
    ),
    BatchActionConfig(
      title: (context) => context.locale.returnDish,
      batchMark: CartBatchActionMark.returnDish,
    ),
  ];

  ValueNotifier<int> pageMarkNotifier = ValueNotifier<int>(0);
  List<int> markStack = [0];

  final selectStaffNotifier = ValueNotifier("");

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: pageMarkNotifier,
      builder: (ctx, mark, _) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: mark == CartActionPageMark.main.value || mark == CartActionPageMark.batch.value ? 280 : 560,
          child: _buildViewByMark(mark),
        );
      },
    );
  }

  void changePageMark(int mark) {
    if(mark==CartActionPageMark.fee.value){
      //点击服务费时，先判断是否存在订单
      int? orderId = ref.read(currentOrderIdProvider);
      if(orderId==null){
        //没有订单时，提示用户
        KPToast.show(content: context.locale.addProductFirst);
        return;
      }
    }
    if (markStack.isEmpty || markStack.last != mark) {
      markStack.add(mark);
      pageMarkNotifier.value = mark;
      if(mark==CartActionPageMark.batch.value){
        //批量操作时，通知购物车显示批量操作
        ref.read(showCartBatchActionProvider.notifier).set(true);
      }
    }
  }

  ///返回上一级视图
  void backToPreView() {
    if (markStack.length > 1) {
      if(markStack.last==CartActionPageMark.batch.value){
        //从批量操作返回时，通知购物车隐藏批量操作
        ref.read(showCartBatchActionProvider.notifier).set(false);
      }
      markStack.removeLast();
      final lastMark = markStack.last;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        pageMarkNotifier.value = lastMark;
      });
    }
  }

  ///根据标记返回不同的二级视图
  Widget _buildViewByMark(int mark) {
    switch (CartActionPageMark.markToPageType(mark)) {
      case CartActionPageMark.fee:
        //服务费
        return CartActionServiceFee(
          onClose: () {
            backToPreView();
          },
        );
      case CartActionPageMark.notes:
        //订单备注
        return CartActionOrderNotes(onClose: () {
          backToPreView();
        });
      case CartActionPageMark.discount:
        //折扣
        return CartActionDiscount(onClose: () {
          backToPreView();
        });
      case CartActionPageMark.reprint:
        //重打印
        return Container();
      case CartActionPageMark.batch:
        //批量操作
        return _buildBatchAction();
      case CartActionPageMark.cancelOrder:
        //取消订单
        return CartActionCancelOrder(onClose: () {
          backToPreView();
          KPSlidePopup.dismissFromTarget();
        });
      case CartActionPageMark.clear:
      default:
        return _buildMain();
    }
  }

  ///主视图
  Widget _buildMain() {
    return Container(
      height: double.maxFinite,
      decoration: const BoxDecoration(
        color: KPColors.fillGrayLightLightest,
        border: Border(
          top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(actionsList.length, (index) {
            final action = actionsList[index];
            return Column(
              children: [
                ListTile(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  leading: SvgPicture.asset(
                    action.icon,
                    width: 24,
                    height: 24,
                  ),
                  title: Text(
                    action.title(context),
                    style: KPFontStyle.bodyLarge.copyWith(
                        color: action.mark == CartActionPageMark.clear||action.mark == CartActionPageMark.cancelOrder
                            ? KPColors.textRedDefault
                            : KPColors.textGrayPrimary),
                  ),
                  onTap: () {
                    if (action.mark == CartActionPageMark.clear) {
                      //清空购物车
                      KPDialog.showAlertDialog(
                        context: context,
                        title: context.locale.reminder,
                        content: context.locale.clearCartTip,
                        rightBtnTitle: context.locale.clear,
                        rightBtnBgColor: KPColors.fillRedNormal,
                        rightAction: () {
                          if (context.mounted) {
                            context.pop();
                            KPSlidePopup.dismissFromTarget();
                            ref.read(cartServiceProvider).clearCart();
                          }
                        },
                      );
                    } else if(action.mark == CartActionPageMark.reprint){
                      //重打印先关闭
                      return;
                    }else{
                      changePageMark(action.mark.value);
                    }
                  },
                ),
                if (index != actionsList.length - 1)
                  const Divider(
                    height: 1,
                    color: KPColors.borderGrayLightBase,
                  ),
              ],
            );
          }),
        ),
      ),
    );
  }

  ///批量操作
  Widget _buildBatchAction() {
    return Container(
      height: double.maxFinite,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: const BoxDecoration(
        color: KPColors.fillGrayLightLightest,
        border: Border(
          top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const SizedBox(width: 24),
              Text(
                context.locale.batchAction,
                style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
              ),
              const Expanded(child: SizedBox()),
              IconButton(
                onPressed: backToPreView,
                icon: const Icon(
                  Icons.close,
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemCount: batchActionConfigs.length,
              itemBuilder: (context,index){
                BatchActionConfig config = batchActionConfigs[index];
                return Column(
                  children: [
                    Container(
                      height: 56,
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      alignment: Alignment.centerLeft,
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          Text(
                            config.title(context),
                            style: KPFontStyle.bodyLarge.copyWith(
                              color: config.batchMark == CartBatchActionMark.returnDish
                                  ? KPColors.textRedDefault
                                  : KPColors.textGrayPrimary,
                            ),
                          ),
                          if(config.batchMark==CartBatchActionMark.staff)
                          Expanded(
                            child: ValueListenableBuilder(
                                valueListenable: selectStaffNotifier,
                                builder: (context, name, _) {
                                  return Row(
                                    children: [
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(name,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                color: context.theme.grayColor2)),
                                      ),
                                      LayoutBuilder(builder: (context, constraints) {
                                        return GestureDetector(
                                          onTap: (){
                                            KPPopover.showPopover(
                                                context: context,
                                                width: 320,
                                                height: 240,
                                                placement:
                                                TDPopoverPlacement.bottomRight,
                                                contentWidget:
                                                _buildStaffView(context, ref));
                                          },
                                          child: Assets.images.iconArrowRight
                                              .svg(width: 24, height: 24),
                                        );
                                      }),
                                    ],
                                  );
                                },),
                          ),
                        ],
                      ),
                    ).onTap((){
                       print("点击了批量操作：${config.title(context)}");
                      _batchActionOnTap(config.batchMark);
                    }),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24),
                      child: Divider(
                        height: 1,
                        color: KPColors.borderGrayLightBase,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  ///批量操作点击事件
  void _batchActionOnTap(CartBatchActionMark batchMark) {
    List<int> selectedItems = ref.read(cartSelectedItemsProvider);
    switch (batchMark) {
      case CartBatchActionMark.dishesServed:
        //划菜
        if(selectedItems.isEmpty) return;
        backToPreView();
        ref.read(cartServiceProvider).batchAction(batchMark);
        break;
      case CartBatchActionMark.discount:
        //折扣
        changePageMark(3);
        break;
      case CartBatchActionMark.staff:
        //提成人
        break;
      case CartBatchActionMark.urgeDishes:
        //催菜
        if(selectedItems.isEmpty) return;
        backToPreView();
        ref.read(cartServiceProvider).batchAction(batchMark);
        break;
      case CartBatchActionMark.packaging:
        //打包
        backToPreView();
        if(selectedItems.isEmpty) return;
        ref.read(cartServiceProvider).batchAction(batchMark);
        break;
      case CartBatchActionMark.returnDish:
        //退菜
        if(selectedItems.isEmpty) return;
        backToPreView();
        ref.read(cartServiceProvider).batchAction(batchMark);
        break;
      default:
        break;
    }
  }

  Widget _buildStaffView(BuildContext context,WidgetRef ref) {
    return KPAsyncValueWidget(
        asyncValueProvider: reLoginScreenControllerProvider,
        dataBuilder: (ct,ref,data) {
          return ListView.separated(
              padding: EdgeInsets.zero,
              itemCount: data.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                indent: 40,
                color: Colors.grey[300],
              ),
              itemBuilder: (context, index) {
                final item = data[index];
                return GestureDetector(
                  onTap: () {
                    selectStaffNotifier.value = item.employeeName;
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12,vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 36,
                              height: 36,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: context.theme.brandNormalColor,
                                borderRadius: BorderRadius.circular(60),
                              ),
                              child: Text(
                                item.employeeName.substring(0,2),
                                style: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(item.employeeName,maxLines: 1,overflow: TextOverflow.ellipsis),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: context.theme.grayColor1),
                          child:Text(item.roleName,style: TextStyle(fontSize: 10),),
                        )
                      ],
                    ),
                  ),
                );
              });
        });
  }
}
