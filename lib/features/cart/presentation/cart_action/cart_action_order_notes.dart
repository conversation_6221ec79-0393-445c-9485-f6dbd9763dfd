import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/store/application/store_prepared_reason_service.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';

class CartActionOrderNotes extends ConsumerStatefulWidget {
  final VoidCallback onClose;
  const CartActionOrderNotes({super.key,required this.onClose});

  @override
  ConsumerState createState() => _CartActionOrderNotesState();
}

class _CartActionOrderNotesState extends ConsumerState<CartActionOrderNotes> {

  final _formKey = GlobalKey<FormBuilderState>();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _editController = TextEditingController();

  List<String> selectedTags = [];
  final _loadingProvider = StateProvider<bool>((ref) => false);

  @override
  void dispose() {
    _focusNode.dispose();
    _editController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    //回显逻辑
    if (_editController.text.isEmpty && selectedTags.isEmpty) {
      final cartItem = ref.read(cartServiceProvider).cartItem;
      final remark = cartItem?.remark ?? '';
      if (remark.isNotEmpty) {
        // 获取标签库
        final cache = ref.read(storePreparedReasonCacheProvider);
        final notesTags = cache.getRemarkReasons()?.map((e) => e.preparedReasonName).toList() ?? [];
        // 分割备注
        final parts = remark.split(';');
        final tags = <String>[];
        final custom = <String>[];
        for (final part in parts) {
          if (notesTags.contains(part)) {
            tags.add(part);
          } else if (part.trim().isNotEmpty) {
            custom.add(part);
          }
        }
        selectedTags = tags;
        _editController.text = custom.join(';');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Container(
        height: double.maxFinite,
        padding: const EdgeInsets.symmetric(horizontal: 24,vertical: 12),
        decoration: const BoxDecoration(
          color: KPColors.fillGrayLightLightest,
          border: Border(
            top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
            right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTop(),
            //备注
            Expanded(child: _buildNotesWidgetWithCache()),
            //下方分割线
            Container(
              height: 1,
              color: KPColors.borderGrayLightBase,
              margin: const EdgeInsets.only(bottom: 12),
            ),
            //按钮
            _buildBottom(),
          ],
        ),
      ),
    );

  }

  Widget _buildTop() {
    return Row(
      children: [
        Text(
          context.locale.ordersNotes,
          style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
        ),
        const Expanded(child: SizedBox()),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(
            Icons.close,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildNotesWidgetWithCache() {
    // 直接从缓存获取数据
    final cache = ref.read(storePreparedReasonCacheProvider);
    final remarkReasons = cache.getRemarkReasons();
    if (remarkReasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: remarkReasonsProvider,
        dataBuilder: (context, ref, data) {
          return _buildNotesWidget(data.map((e) => e.preparedReasonName).toList());
        },
      );
    }
    
    // 如果缓存中有数据，直接使用（无网络请求）
    return _buildNotesWidget(remarkReasons.map((e) => e.preparedReasonName).toList());
  }

  Widget _buildNotesWidget(List<String> notesTags) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormBuilder(
            key: _formKey,
            child: FormBuilderField<List<String>>(
              name: 'orders_notes',
              initialValue: selectedTags,
              builder: (FormFieldState<List<String>> field) {
                return Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: notesTags.map((note) {
                    final isSelected = field.value?.contains(note) ?? false;
                    return ChoiceChip(
                      label: Text(
                        note,
                        style: KPFontStyle.headingXSmall.copyWith(
                          color: isSelected
                              ? KPColors.textBrandDefault
                              : KPColors.textGrayPrimary,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        final currentValue = List<String>.from(field.value ?? []);
                        if (selected) {
                          currentValue.add(note);
                        } else {
                          currentValue.remove(note);
                        }
                        field.didChange(currentValue);
                        selectedTags = currentValue;
                      },
                      backgroundColor: Colors.white,
                      selectedColor: KPColors.fillBrandLightest,
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          color: isSelected
                              ? KPColors.borderBrandDefault
                              : KPColors.borderGrayLightDark,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      showCheckmark: false,
                    );
                  }).toList(),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            focusNode: _focusNode,
            controller: _editController,
            maxLength: 64,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            //不显示字符计数器
            buildCounter: (
                BuildContext context, {
                  required int currentLength,
                  required bool isFocused,
                  int? maxLength,
                }) =>
            null,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderSide:
                const BorderSide(color: KPColors.borderGrayLightDarkest, width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide:
                const BorderSide(color: KPColors.borderBrandDefault, width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              hintText: context.locale.customNotes,
              hintStyle: KPFontStyle.headingXSmall.copyWith(
                color: KPColors.textGraySecondary,
              ),
            ),
            //不允许输入表情
            inputFormatters: [NoEmojiInputFormatter()],
          ),
        ],
      ),
    );
  }

  Widget _buildBottom() {
    final isLoading = ref.watch(_loadingProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 106,
          height: 48,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(KPRadius.l),
            color: KPColors.fillGrayLightLighter,
          ),
          child: Text(
            context.locale.cancelCaps,
            style: const TextStyle(
              color: KPColors.textGrayPrimary,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
          ),
        ).onTap(() {
          widget.onClose();
        }),
        const SizedBox(width: 16),
        KPLoadingButton(
          onPressed: () async {
            ref.read(_loadingProvider.notifier).state = true;
          String notes = '';
          if(selectedTags.isNotEmpty){
            notes = selectedTags.join(';');
          }
          if(_editController.text.trim().isNotEmpty){
            notes += (notes.isNotEmpty ? ';' : '') + _editController.text.trim();
          }
            try {
              await ref.read(cartServiceProvider).updateOrderNotes(notes);
            } catch (e) {
              // 可选：弹出错误提示
            } finally {
              widget.onClose();
              ref.read(_loadingProvider.notifier).state = false;
            }
          },
          text: context.locale.confirmCaps,
          isLoading: isLoading,
          backgroundColor: KPColors.fillGrayDarDarkest,
          textColor: KPColors.textGrayInverse,
          width: 106,
          height: 48,
        ),
      ],
    );
  }
}
