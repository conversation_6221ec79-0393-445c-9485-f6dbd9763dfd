import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/kp_remark.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/store/application/store_prepared_reason_cache.dart';
import 'package:kpos/features/store/application/store_prepared_reason_service.dart';
import 'package:oktoast/oktoast.dart';

class CartActionCancelOrder extends ConsumerStatefulWidget {
  final VoidCallback onClose;

  const CartActionCancelOrder({super.key, required this.onClose});

  @override
  ConsumerState createState() => _CartActionCancelOrderState();
}

class _CartActionCancelOrderState extends ConsumerState<CartActionCancelOrder> {
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _reasonEditController = TextEditingController();

  final _reasonEmptyNotifier = ValueNotifier(false);
  final _reasonFormKey = GlobalKey<FormBuilderState>();

  List<String> _selectedReasonValues = [];

  final cancelOrderLoadingProvider = StateProvider<bool>((ref) => false);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
      },
      child: Stack(
        children: [
          Container(
            height: double.maxFinite,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: const BoxDecoration(
              color: KPColors.fillGrayLightLightest,
              border: Border(
                top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
                right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
              ),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTop(),
                  Container(
                    width: double.maxFinite,
                    margin: const EdgeInsets.only(top: 14, bottom: 24),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 13),
                    decoration: BoxDecoration(
                      color: KPColors.fillBrandLightest,
                      borderRadius: BorderRadius.circular(KPRadius.l),
                    ),
                    child: Row(
                      children: [
                        Assets.images.iconBrandWarning.svg(
                          width: 24,
                          height: 24,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            context.locale.cancelOrderTip,
                            style: KPFontStyle.bodyMedium.copyWith(
                              color: KPColors.textGrayPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  //原因
                  _buildReasonSection(),
                  const SizedBox(height: 36),
                  Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        margin: const EdgeInsets.only(left: 16, right: 16),
                        decoration: BoxDecoration(
                          color: KPColors.fillGrayDarkLightest,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 18,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        context.locale.printWithdrawOrder,
                        style: KPFontStyle.bodyLarge
                            .copyWith(color: KPColors.textGrayPrimary),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            child: Container(
              width: 560,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  //下方分割线
                  Container(
                    height: 1,
                    color: KPColors.borderGrayLightBase,
                    margin: const EdgeInsets.only(bottom: 12),
                  ),
                  //按钮
                  _buildBottom(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReasonSection() {
    //todo 接口出来后替换为取消订单原因
    final cache = ref.read(storePreparedReasonCacheProvider);
    final reasons = cache.getDiscountReasons();

    if (reasons == null) {
      // 如果缓存中没有数据，使用异步加载（首次访问）
      return KPAsyncValueWidget(
        asyncValueProvider: discountReasonsProvider,
        dataBuilder: (context, ref, data) {
          return SingleChildScrollView(
              child: _buildReasonWidget(data.map((e) => e.preparedReasonName).toList()));
        },
      );
    } else {
      // 使用缓存数据
      return SingleChildScrollView(
          child: _buildReasonWidget(reasons.map((e) => e.preparedReasonName).toList()));
    }
  }

  Widget _buildReasonWidget(List<String> reasons) {
    return ValueListenableBuilder(
        valueListenable: _reasonEmptyNotifier,
        builder: (context, isEmpty, _) {
          return KPRemark(
            remarkFormKey: _reasonFormKey,
            isContentEmpty: isEmpty,
            hintText: context.locale.reason,
            title: context.locale.reason,
            remarkList: reasons,
            isShowAsterisk: true,
            formBuilderFieldName: 'cancel_order_reasons',
            controller: _reasonEditController,
            focusNode: _focusNode,
            onRemarkChanged: (List<String> remark) {
              _selectedReasonValues = remark;
              if (_selectedReasonValues.isNotEmpty) {
                _reasonEmptyNotifier.value = false;
              }
            },
            onTextFieldTap: () {
              _reasonEmptyNotifier.value = false;
            },
            inputErrorText: context.locale.inputCancelReasonTip,
          );
        });
  }

  Widget _buildBottom() {
    final isLoading = ref.watch(cancelOrderLoadingProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          width: 106,
          height: 48,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(KPRadius.l),
            color: KPColors.fillGrayLightLighter,
          ),
          child: Text(
            context.locale.cancelCaps,
            style: const TextStyle(
              color: KPColors.textGrayPrimary,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
          ),
        ).onTap(() {
          widget.onClose();
        }),
        const SizedBox(width: 16),
        KPLoadingButton(
          onPressed: () async {
            ref.read(cancelOrderLoadingProvider.notifier).state = true;
            String reason = '';
            if (_selectedReasonValues.isNotEmpty) {
              reason = _selectedReasonValues.join(';');
            }
            if (_reasonEditController.text.trim().isNotEmpty) {
              reason +=
                  (reason.isNotEmpty ? ';' : '') + _reasonEditController.text.trim();
            }
            try {
              //todo 调用取消订单接口
              debugPrint("取消订单原因: $reason");
            } catch (e) {
              // 可选：弹出错误提示
            } finally {
              KPToast.show(
                content: context.locale.cancelOrderSuccess,
                isGreen: true,
                position: ToastPosition.bottom,
              );
              widget.onClose();
              ref.read(cancelOrderLoadingProvider.notifier).state = false;
            }
          },
          text: context.locale.cancelOrder,
          isLoading: isLoading,
          backgroundColor: KPColors.fillRedNormal,
          textColor: KPColors.textGrayInverse,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          height: 48,
        ),
      ],
    );
  }

  Widget _buildTop() {
    return Row(
      children: [
        Text(
          context.locale.cancelOrder,
          style: KPFontStyle.headingLarge.copyWith(color: KPColors.textGrayPrimary),
        ),
        const Expanded(child: SizedBox()),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(
            Icons.close,
            size: 24,
          ),
        ),
      ],
    );
  }
}
