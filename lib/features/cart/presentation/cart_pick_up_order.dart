import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/constant/app_sizes.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/utils/common_util.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/domain/order_draft_item.dart';
import 'package:kpos/features/cart/domain/order_draft_product_item.dart';
import 'package:kpos/features/cart/presentation/cart_controller.dart';

class CartPickUpOrder extends ConsumerStatefulWidget {
  const CartPickUpOrder({super.key});

  @override
  ConsumerState createState() => _CartPickUpOrderState();
}

class _CartPickUpOrderState extends ConsumerState<CartPickUpOrder> {

  final _formKey = GlobalKey<FormBuilderState>();

  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    //获取取单列表并排序
    List<OrderDraftItem> orderList = List.from(ref.watch(orderDraftsProvider));
    return Container(
      width: 480,
      height: 546,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: KPColors.fillGrayLightLightest,
        borderRadius: BorderRadius.circular(KPRadius.xxl),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.locale.pickUpOrder,
            style: KPFontStyle.headingLarge.copyWith(color: const Color(0xFF1D1B20)),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: FormBuilder(
              key: _formKey,
              child: FormBuilderField<int>(
                name: 'cart_pick_up_order',
                initialValue: 0,
                builder: (field){
                  return ListView.separated(
                    itemCount: orderList.length,
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Radio<int>(
                            value: index,
                            groupValue: field.value,
                            fillColor: WidgetStateProperty.resolveWith<Color>(
                                  (states) {
                                if (states.contains(WidgetState.selected)) {
                                  return KPColors.iconBrandDefault;
                                }
                                return KPColors.iconGrayTertiary;
                              },
                            ),
                            onChanged: (val) {
                              field.didChange(index);
                              selectedIndex = index;
                            },
                          ),
                          const SizedBox(width: 16),
                          Expanded(child: _buildItem(context,index,orderList).onTap((){
                            field.didChange(index);
                            selectedIndex = index;
                          })),
                        ],
                      );
                    },
                    separatorBuilder: (context,index){
                      return Container(
                        width: double.maxFinite,
                        height: 1,
                        margin: const EdgeInsets.only(top: 12),
                        color: KPColors.borderGrayLightBase,
                      );
                    },
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: context.pop,
                  child: Container(
                    height: 48,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: const Color(0xFFF3F3F4)),
                    child: Text(
                      context.locale.cancelCaps,
                      style: KPFontStyle.headingSmall
                          .copyWith(color: KPColors.textGrayPrimary),
                    ),
                  ),
                ),
              ),
              gapW12,
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    await ref.read(cartServiceProvider).pickUpOrder(orderList[selectedIndex]);
                    if (mounted) {
                    context.pop();
                    }
                  },
                  child: Container(
                    height: 48,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        color: const Color(0xFF20232B)),
                    child: Text(
                      context.locale.confirmCaps,
                      style: KPFontStyle.headingSmall
                          .copyWith(color: KPColors.textGrayInverse),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItem(BuildContext context, int index,List<OrderDraftItem> orderList) {
    OrderDraftItem item = orderList[index];
    List<OrderDraftProductItem> dataList = item.items;
    final showList = dataList.take(3).toList();
    final hasMore = dataList.length > 3;
    //todo 计算总数
    final totalCount = 3;
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //title
          Row(
            children: [
              Expanded(
                child: Text(
                  '#${item.cartCode}',
                  style: KPFontStyle.headingMedium.copyWith(color: KPColors.textGrayPrimary),
                ),
              ),
              Text(
                'S\$${item.totalPrice}',
                style: KPFontStyle.headingMedium.copyWith(color: KPColors.textGrayPrimary),
              ),
            ],
          ),
          const SizedBox(height: 4),
          //时间
          Row(
            children: [
              Expanded(
                child: Text(
                  CommonUtil.formatTimestamp(item.createTime),
                  style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                ),
              ),
              Text(
                context.locale.totalItems(totalCount),
                style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // 商品列表(最多显示3个，多于3个显示省略号)
          IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  width: 1,
                  margin: const EdgeInsets.only(right: 12),
                  color: KPColors.borderGrayLightBase,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...showList.map((item) => _productItem(item.commodityName, item.quantity)),
                      if (hasMore)
                        Text(
                          '...',
                          style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _productItem(String title, int num) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
          ),
        ),
        Text(
          "×$num",
          style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
        ),
      ],
    );
  }
}
