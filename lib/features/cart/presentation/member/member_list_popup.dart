import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/cart/presentation/member/add_member_popup.dart';

import '../../../../assets/assets.gen.dart';
import '../../../../common/components/no_emoji_input_formatter.dart';
import '../../../../common/components/popup/kp_slide_popup.dart';

class MemberChangedResult {
  final Member member;
  final bool isAddedMember;

  MemberChangedResult(this.member, this.isAddedMember);
}

class MemberListPopup extends StatefulWidget {
  final Function(MemberChangedResult) memberChangedResult;

  const MemberListPopup({super.key, required this.memberChangedResult});

  @override
  State<MemberListPopup> createState() => _MemberListPopupState();
}

class _MemberListPopupState extends State<MemberListPopup> {
  final FocusNode textInputFocusNode = FocusNode();
  final TextEditingController editTextController = TextEditingController();
  ValueNotifier<List<Member>> filteredMembers = ValueNotifier([]);
  bool isSearched = false;

  final memberList = [
    Member(
      name: "Alenwgwgwgwgwgwgwgwgwgwgwgwgwgwgwgwgw Wu",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 1,
    ),
    Member(
      name: "Alen Wu2222",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 2,
    ),
    Member(
      name: "Ankui",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 2,
    ),
    Member(
      name: "Mark",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 1,
    ),
    Member(
      name: "Jerry",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 1,
    ),
    Member(
      name: "Alen Wu",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 1,
    ),
    Member(
      name: "Alen Wu2222",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 0,
    ),
    Member(
      name: "Ankui",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 2,
    ),
    Member(
      name: "Mark",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 0,
    ),
    Member(
      name: "Jerry",
      email: "<EMAIL>",
      phone: "1234567890",
      sex: 1,
    ),
  ];

  @override
  void dispose() {
    textInputFocusNode.dispose();
    editTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.maxFinite,
      padding: const EdgeInsets.only(
        top: KPSpacing.l,
        left: KPSpacing.xxxl,
        right: KPSpacing.xxxl,
      ),
      decoration: const BoxDecoration(
        color: KPColors.fillGrayLightLightest,
        border: Border(
          top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
        ),
      ),
      width: 560,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  context.locale.selectMember,
                  style: KPFontStyle.headingLarge.copyWith(
                    color: KPColors.textGrayPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  KPSlidePopup.dismissFromTarget();
                },
                child: Assets.images.iconClose.svg(),
              ),
            ],
          ),
          const SizedBox(height: 14),
          TextField(
            focusNode: textInputFocusNode,
            controller: editTextController,
            textInputAction: TextInputAction.done,
            onSubmitted: (value) {
              if (value.length >= 4) {
                _filterMembers(value);
              } else {
                setState(() {
                  filteredMembers.value = [];
                });
              }
            },
            maxLength: 99,
            buildCounter: (
              BuildContext context, {
              required int currentLength,
              required bool isFocused,
              int? maxLength,
            }) =>
                null,
            decoration: InputDecoration(
              border: _getOutlineInputBorder(
                  color: KPColors.borderGrayLightDarkest),
              focusedBorder:
                  _getOutlineInputBorder(color: KPColors.borderBrandDefault),
              contentPadding: const EdgeInsets.all(KPSpacing.xl),
              hintText: context.locale.searchMemberTip,
              hintStyle: KPFontStyle.headingXSmall.copyWith(
                color: KPColors.textGraySecondary,
              ),
              prefixIcon:
                  const Icon(Icons.search, color: KPColors.textGraySecondary),
              suffixIcon: editTextController.text.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        editTextController.clear();
                        setState(() {});
                      },
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Assets.images.iconCircleDelete.svg(),
                        ),
                      ),
                    )
                  : null,
            ),
            inputFormatters: [NoEmojiInputFormatter()],
          ),
          const SizedBox(height: KPSpacing.l),
          GestureDetector( child:
          Container(
            padding: const EdgeInsets.all(KPSpacing.xxl),
            decoration: BoxDecoration(
              color: KPColors.fillGrayLightLight,
              border: Border.all(
                color: KPColors.fillGrayLightLight,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(KPRadius.l),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Assets.images.iconAddGray.svg(
                    width: 24, height: 24, color: KPColors.iconGrayPrimary),
                Expanded(
                  child: Center(
                    child: Text(
                      context.locale.addNewMembership,
                      style: KPFontStyle.headingSmall.copyWith(
                        color: KPColors.textGrayPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ],
            )),
            onTap: (){
              KPSlidePopup.showFromTarget(
                context: context,
                contentWidget: AddMemberPopup(searchString: editTextController.text , addedMemberChanged: (member) {
                  widget.memberChangedResult(MemberChangedResult(member, true));
                  Navigator.pop(context);
                }), targetWidth: 360,
                // targetWight: 361,
              );
            },
          ),
          const SizedBox(height: KPSpacing.l),
          Visibility(visible:  filteredMembers.value.isEmpty && isSearched, child: Expanded(child: _buildEmptyWidget(context))),
          Visibility(visible:  filteredMembers.value.isNotEmpty && isSearched, child: Expanded(child: _buildMemberList()))
        ],
      ),
    );
  }
  Widget _buildEmptyWidget(BuildContext context){
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Assets.images.iconSearch.svg(width: 32,height: 32,color: KPColors.iconGrayPrimary),
          Text(context.locale.noResultsFound, style: KPFontStyle.bodyLarge.copyWith(
            color: KPColors.textGrayPrimary,
            fontWeight: FontWeight.w400,
          ),)
        ],
      ),
    );
  }

  Widget _buildMemberList() {
  return  ValueListenableBuilder(
        valueListenable: filteredMembers,
        builder: (ctx, filteredMembers, _) {
          return ListView.separated(
            itemCount: filteredMembers.length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              final member = filteredMembers[index];
              Color textColor = KPColors.textGraySecondary;
              Color textBackgroundColor = KPColors.fillGrayLightBase;
              String tag = context.locale.other;
              if (member.sex == 1) {
                textColor = KPColors.textBlueDefault;
                textBackgroundColor = KPColors.textBlueDefault.withValues(alpha: 0.1);
                tag = context.locale.mr;
              } else if (member.sex == 2) {
                textColor = KPColors.textRedDefault;
                textBackgroundColor = KPColors.textRedDefault.withValues(alpha: 0.1);
                tag = context.locale.ms;
              }
              return ListTile(
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Flexible(
                        child: Text(
                          member.name,
                          style: KPFontStyle.bodyLarge.copyWith(
                            fontWeight: FontWeight.w400,
                            color: KPColors.textGrayPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                    const SizedBox(width: KPSpacing.m),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: KPSpacing.xs,
                        horizontal: 6,
                      ),
                      decoration: BoxDecoration(
                        color: textBackgroundColor,
                      ),
                      child: Text(
                        tag,
                        style: KPFontStyle.bodySmall.copyWith(
                          fontWeight: FontWeight.w400,
                          color: textColor,
                        ),
                      ),
                    )
                  ],
                ),
                subtitle: Text(member.email,
                    style: KPFontStyle.bodyMedium.copyWith(
                      fontWeight: FontWeight.w400,
                      color: KPColors.textGraySecondary,
                    )),
                onTap: () {
                  widget.memberChangedResult(MemberChangedResult(member, false));
                  Navigator.pop(context);
                },
              );
            },
            separatorBuilder: (context, index) {
              return const Divider(
                height: 1,
                thickness: 1,
                color: KPColors.borderGrayLightBase,
                indent: 16,
                endIndent: 16,
              );
            },
          );
        });
  }

  void _filterMembers(String query) {
    setState(() {
      filteredMembers.value = memberList.where((member) {
        return member.name.toLowerCase().contains(query.toLowerCase()) ||
            member.email.toLowerCase().contains(query.toLowerCase()) ||
            member.phone.contains(query);
      }).toList();
      isSearched = true;
    });
  }

  OutlineInputBorder _getOutlineInputBorder(
      {required Color color, double borderWith = 1, double borderRadius = 4}) {
    return OutlineInputBorder(
      borderSide: BorderSide(color: color, width: borderWith),
      borderRadius: BorderRadius.circular(borderRadius),
    );
  }
}

class Member {
  final String name;
  final String email;
  final String phone;
  final int sex;

  Member({
    required this.name,
    required this.email,
    required this.phone,
    required this.sex,
  });
}
