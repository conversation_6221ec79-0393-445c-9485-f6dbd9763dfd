import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../../assets/assets.gen.dart';
import '../../../../common/components/form_fields/kp_text_field.dart';
import '../../../../common/components/kp_date_picker.dart';
import '../../../../common/components/kp_drop_down_menus.dart';
import '../../../../common/components/no_emoji_input_formatter.dart';
import 'member_list_popup.dart';

enum AreaCodeType {
  malaysia(areaCode: "+60", area: '马来西亚'),
  australia(areaCode: "+61", area: '澳大利亚'),
  singapore(areaCode: "+65", area: '新加坡'),
  japan(areaCode: "+81", area: '日本'),
  hongKong(areaCode: "+852", area: '香港');

  const AreaCodeType({
    required this.areaCode,
    required this.area,
  });

  static AreaCodeType getAreaCodeTypeByAreaCode(String areaCode) {
    return AreaCodeType.values.firstWhere((e) => e.areaCode == areaCode);
  }

  String get displayText => "$areaCode $area";

  static AreaCodeType? fromCode(String code) {
    try {
      return values.firstWhere((e) => e.areaCode == code);
    } catch (e) {
      return null;
    }
  }

  String get areaCodeOnly => areaCode;

  final String areaCode;
  final String area;
}

class AddMemberPopup extends StatefulWidget {
  final String? searchString;
  final Function(Member) addedMemberChanged;

  const AddMemberPopup(
      {super.key,
      required this.searchString,
      required this.addedMemberChanged});

  @override
  State<AddMemberPopup> createState() => _AddMemberPopupState();
}

class _AddMemberPopupState extends State<AddMemberPopup> {
  final FocusNode firstNameInputFocusNode = FocusNode();
  final TextEditingController firstNameTextController = TextEditingController();

  final FocusNode lastNameInputFocusNode = FocusNode();
  final TextEditingController lastNameTextController = TextEditingController();

  final FocusNode emailInputFocusNode = FocusNode();
  final TextEditingController emailTextController = TextEditingController();

  final FocusNode phoneInputFocusNode = FocusNode();
  final TextEditingController phoneTextController = TextEditingController();
  String _selectedOption = "";
  String errorMessage = '';
  final ValueNotifier<AreaCodeType?> _defaultAreaCode = ValueNotifier(null);

  final ValueNotifier<bool> _isAgree = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    if (widget.searchString != null) {
      firstNameTextController.text = widget.searchString!;
    }
    _setDefaultAreaCode();
  }

  void _setDefaultAreaCode() {
    final timeZone = DateTime.now().timeZoneName;

    final timeZoneToAreaCode = {
      'Australia/Sydney': '+61', // 澳大利亚
      'Australia/Melbourne': '+61',
      'Asia/Hong_Kong': '+852', // 中国香港特别行政区
      'Asia/Tokyo': '+81', // 日本
      'Asia/Kuala_Lumpur': '+60', // 马来西亚
      'Asia/Singapore': '+65', // 新加坡
      'America/New_York': '+1', // 美国东部
      'America/Chicago': '+1', // 美国中部
      'America/Denver': '+1', // 美国山地
      'America/Los_Angeles': '+1', // 美国太平洋
      'Europe/London': '+44', // 英国
      'Europe/Paris': '+33', // 法国
      'Asia/Shanghai': '+86', // 中国
    };

    final supportedCodes = ['+60', '+61', '+65', '+81', '+852'];

    final code = timeZoneToAreaCode[timeZone];
    if (code != null && supportedCodes.contains(code)) {
      _defaultAreaCode.value = AreaCodeType.getAreaCodeTypeByAreaCode(code);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_selectedOption.isEmpty) {
      _selectedOption = context.locale.mr;
    }
  }

  @override
  void dispose() {
    firstNameInputFocusNode.dispose();
    firstNameTextController.dispose();
    lastNameTextController.dispose();
    lastNameTextController.dispose();
    emailTextController.dispose();
    emailInputFocusNode.dispose();
    phoneTextController.dispose();
    phoneInputFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: double.maxFinite,
        padding: const EdgeInsets.only(
          top: KPSpacing.l,
          left: KPSpacing.xxxl,
          right: KPSpacing.xxxl,
        ),
        decoration: const BoxDecoration(
          color: KPColors.fillGrayLightLightest,
          border: Border(
            top: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
            right: BorderSide(color: KPColors.borderGrayLightBase, width: 1),
          ),
        ),
        width: 560,
        child: Stack(children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        context.locale.addNewMembership,
                        style: KPFontStyle.headingLarge.copyWith(
                          color: KPColors.textGrayPrimary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Assets.images.iconClose.svg(),
                    ),
                  ],
                ),
                const SizedBox(height: KPSpacing.xxxl),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: KPTextField(
                        controller: firstNameTextController,
                        focusNode: firstNameInputFocusNode,
                        labelText: context.locale.firstName,
                        hintText: context.locale.enterFirstName,
                        inputFormatters: [NoEmojiInputFormatter()],
                        isRequired: true,
                        maxLength: 22,
                        errorTextBuilder: (value) {
                          if (value == null || value.isEmpty) {
                            return context.locale.enterFirstName;
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: KPSpacing.l),
                    Expanded(
                      flex: 1,
                      child: KPTextField(
                        controller: lastNameTextController,
                        focusNode: lastNameInputFocusNode,
                        labelText: context.locale.lastName,
                        hintText: context.locale.enterLastName,
                        inputFormatters: [NoEmojiInputFormatter()],
                        isRequired: true,
                        errorTextBuilder: (value) {
                          if (value == null || value.isEmpty) {
                            return context.locale.enterLastName;
                          }
                          return null;
                        },
                      ),
                    )
                  ],
                ),
                const SizedBox(height: KPSpacing.xxxl),
                Row(
                  children: [
                    Expanded(
                      child: _tag(context.locale.mr),
                    ),
                    const SizedBox(width: KPSpacing.m),
                    Expanded(
                      child: _tag(context.locale.ms),
                    ),
                    const SizedBox(width: KPSpacing.m),
                    Expanded(
                      child: _tag(context.locale.other),
                    ),
                  ],
                ),
                const SizedBox(height: KPSpacing.xxxl),
                KPTextField(
                  controller: emailTextController,
                  focusNode: emailInputFocusNode,
                  labelText: context.locale.email,
                  hintText: context.locale.enterEmail,
                  keyboardType: TextInputType.emailAddress,
                  isRequired: true,
                  maxLength: 100,
                  errorTextBuilder: (value) {
                    if (value == null || value.isEmpty) {
                      return context.locale.enterEmail;
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return context.locale.emailErrorTip;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: KPSpacing.xxxl),
                Row(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: _defaultAreaCode,
                      builder: (ctx, defaultAreaCode, _) {
                        return KPDropDownMenus<AreaCodeType>(
                          items: AreaCodeType.values,
                          displayText: (type) => type.displayText,
                          value: defaultAreaCode,
                          textStyle: KPFontStyle.bodyLarge.copyWith(
                            color: KPColors.textGrayPrimary,
                            fontWeight: FontWeight.w400,
                          ),
                          onChanged: (type) {
                            if (type != null) {
                              setState(() {
                                _defaultAreaCode.value = type;
                              });
                            }
                          },
                          selectedTextBuilder: (type) => type.areaCodeOnly,
                          width: 110,
                          backgroundColor: Colors.white,
                        );
                      },
                    ),
                    const SizedBox(width: KPSpacing.m),
                    Expanded(
                      flex: 2,
                      child: KPTextField(
                        labelText: context.locale.phoneNumber,
                        hintText: context.locale.enterPhoneNumberTip,
                        keyboardType: TextInputType.phone,
                        controller: phoneTextController,
                        focusNode: phoneInputFocusNode,
                        maxLength: 20,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: KPSpacing.xxxl),
                KPDatePicker(
                  labelText: context.locale.birthday,
                  hintText: context.locale.selectBirthday,
                  unselectedBorderColor: const Color(0xFFDCDDE1),
                  suffixIcon: const Icon(Icons.arrow_drop_down),
                  onConfirm: (date) {
                    print('选择的日期: $date');
                  },
                ),
                const SizedBox(height: KPSpacing.xxxl),
                // MultiSelectDropdown1(
                //   title: "选择性别",
                //   items: const ['男', '女', '其他'],
                //   selectedItems: _selectedGenders,
                //   onChanged: (selected) {
                //     setState(() {
                //       _selectedGenders = selected;
                //     });
                //   },
                //   allowSearch: true,
                //   // 启用搜索功能
                //   primaryColor: Colors.blue,
                //   // 自定义主题色
                //   menuHeight: 300, // 自定义菜单高度
                // ),
                // const SizedBox(height: KPSpacing.m),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: "Membership Type",
                  ),
                  items: const [
                    DropdownMenuItem(value: "regular", child: Text("Regular")),
                    DropdownMenuItem(value: "premium", child: Text("Premium")),
                  ],
                  onChanged: (value) {},
                ),
                const SizedBox(height: KPSpacing.xxxl),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                        width: 24,
                        height: 24,
                        child: Padding(
                            padding: const EdgeInsets.all(4),
                            child: Checkbox(
                              activeColor: KPColors.iconGrayTertiary,
                              fillColor: WidgetStateProperty.all(
                                  KPColors.iconGrayTertiary),
                              checkColor: Colors.white,
                              onChanged: null,
                              value: true,
                            ))),
                    const SizedBox(width: 4),
                    Text(
                      context.locale.pDPATip,
                      style: KPFontStyle.bodyLarge.copyWith(
                        fontWeight: FontWeight.w400,
                        color: KPColors.textGrayPrimary,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: KPSpacing.xxxl),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        _isAgree.value = !_isAgree.value;
                      },
                      child: ValueListenableBuilder(
                          valueListenable: _isAgree,
                          builder: (ct, isAgree, _) {
                            return SizedBox.square(
                              dimension: 24,
                              child: Checkbox(
                                  value: isAgree,
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  onChanged: (value) {
                                    _isAgree.value = value!;
                                  }),
                            );
                          }),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.locale.memberConsentsToReceiveMarketing,
                      style: KPFontStyle.bodyLarge.copyWith(
                        fontWeight: FontWeight.w400,
                        color: KPColors.textGrayPrimary,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 100),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomView(),
          ),
        ]));
  }

  Widget _tag(String text) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedOption = text;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: _selectedOption == text
                ? KPColors.textBrandDefault
                : KPColors.fillGrayLightLight,
          ),
          borderRadius: BorderRadius.circular(KPRadius.m),
          color: _selectedOption == text
              ? KPColors.fillBrandLightest
              : KPColors.fillGrayLightLight,
        ),
        child: Center(
          child: Text(
            text,
            style: KPFontStyle.bodyLarge.copyWith(
              fontWeight: FontWeight.w400,
              color: _selectedOption == text
                  ? KPColors.textBrandDefault
                  : KPColors.textGrayPrimary,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomView() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color:  KPColors.borderGrayLightBase,
            width: 1.0,
          ),
        ),
      ),
      padding: const EdgeInsets.all(KPSpacing.xl),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
              child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(KPRadius.l),
              color: KPColors.fillGrayLightLight,
            ),
            padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxl, vertical: KPSpacing.l),
            child: Text(context.locale.cancel, style: KPFontStyle.headingSmall.copyWith(
                fontWeight: FontWeight.w700, color: KPColors.textGrayPrimary)),
          )
          ),
          const SizedBox(width: KPSpacing.l),
          GestureDetector(
              onTap: () {
                Navigator.pop(context);
                widget.addedMemberChanged(
                  Member(name: "${firstNameTextController.text} ${lastNameTextController.text}", email: "<EMAIL>", phone: phoneTextController.text, sex: 1)
                );

              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(KPRadius.l),
                  color: KPColors.fillGrayDarDarkest,
                ),
                padding: const EdgeInsets.symmetric(horizontal: KPSpacing.xxl, vertical: KPSpacing.l),
                child: Text(context.locale.confirm, style: KPFontStyle.headingSmall.copyWith(
                    fontWeight: FontWeight.w700, color: KPColors.textGrayInverse)),
              )
          ),
        ],
      ),
    );
  }
}

