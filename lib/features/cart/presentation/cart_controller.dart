import 'package:flutter/material.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/cart/data/cart_intranet_repository.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/cart/presentation/cart_screen.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'cart_controller.g.dart';

@riverpod
class CartController extends _$CartController {
  @override
  FutureOr<List<CartProductItem>> build() async {
    return _fetchCartItems();
  }

  Future<List<CartProductItem>> _fetchCartItems() async {
    final cartService = ref.read(cartServiceProvider);
    final orderCreationService = ref.read(orderCreationServiceProvider);
    
    // 获取当前订单ID
    int? orderId = await orderCreationService.getCurrentOrderId();
    debugPrint("CartController 获取到订单ID: $orderId");
    ref.read(currentOrderIdProvider.notifier).set(orderId);
    
    if (orderId == null) {
      debugPrint("没有活跃的订单");
      cartService.cartItemList.clear();
      return [];
    } else {
      // 获取购物车数据
      await _getCartItemsFromServer(orderId);
      debugPrint("CartController 返回购物车数据，共 ${cartService.cartItemList.length} 个商品");
      return List<CartProductItem>.from(cartService.cartItemList);
    }
  }

  /// 获取购物车列表（从服务器）
  /// 需要先有活跃的订单
  Future<void> _getCartItemsFromServer(int orderId) async {
    final cartService = ref.read(cartServiceProvider);
    debugPrint("正在获取订单 #$orderId 的购物车列表");
    
    try {
      final item = await ref.read(cartIntranetRepositoryProvider).getCart(orderId);
      debugPrint("购物车列表获取成功，共 ${item.cartItemList.length} 个商品");
      
      cartService.cartItem = item;
      cartService.cartItemList = List<CartProductItem>.from(item.cartItemList);
      
      // 同步更新用餐方式
      final diningType = DiningType.getTypeByValue(item.diningMethod);
      ref.read(diningTypeStateProvider.notifier).set(diningType);

      // 同步更新用餐人数
      ref.read(peopleCountStateProvider.notifier).set(item.diningNumber);

      //同步更新是否桌台点餐 以及桌台点餐下的当前桌台号
      if (item.tableNumber.isNotEmpty) {
        ref.read(tableOrderingStateProvider.notifier).set(true);
        ref.read(selectedTableTitleStateProvider.notifier).set(item.tableNumber);
      }
    } catch (e) {
      debugPrint("获取购物车列表失败: $e");
      cartService.cartItemList.clear();
    }
  }

  /// 刷新购物车数据
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = AsyncData(await _fetchCartItems());
  }
}
