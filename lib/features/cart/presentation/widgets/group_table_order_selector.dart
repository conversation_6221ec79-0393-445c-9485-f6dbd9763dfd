import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';

final groupOrderModeProvider = StateProvider.autoDispose<int>((ref) => 0);
final groupOrderSelectedTablesProvider =
    StateProvider.autoDispose<Set<int>>((ref) => {-1});

/// 联台下单弹窗组件
class GroupTableOrderSelector extends ConsumerStatefulWidget {
  final List<TableItem> tableList;
  final String curTableTitle;

  const GroupTableOrderSelector({super.key, required this.tableList,required this.curTableTitle});

  @override
  ConsumerState<GroupTableOrderSelector> createState() => _GroupTableOrderSelectorState();
}

class _GroupTableOrderSelectorState extends ConsumerState<GroupTableOrderSelector> {

  @override
  Widget build(BuildContext context) {
    final selectedMode = ref.watch(groupOrderModeProvider);
    final selectedTables = ref.watch(groupOrderSelectedTablesProvider);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.locale.confirmSendOrder,
            style: KPFontStyle.headingLarge.copyWith(color: const Color(0xFF1D1B20)),
          ),
          const SizedBox(height: 16),
          Text(
            context.locale
                .confirmSendOrderTip(widget.tableList.map((e) => e.tableTitle).where((t)=>t!=widget.curTableTitle).join('、')),
            style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
          ),
          const SizedBox(height: 24),
          // 单选模式
          RadioListTile<int>(
            value: 0,
            groupValue: selectedMode,
            activeColor: KPColors.fillBrandNormal,
            contentPadding: EdgeInsets.zero,
            onChanged: (v) => ref.read(groupOrderModeProvider.notifier).state = v!,
            title: Text(
              context.locale.orderForThisTableOnly,
              style: KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          RadioListTile<int>(
            value: 1,
            groupValue: selectedMode,
            activeColor: KPColors.fillBrandNormal,
            contentPadding: EdgeInsets.zero,
            onChanged: (v) => ref.read(groupOrderModeProvider.notifier).state = v!,
            title: Text(
              context.locale.orderForAllGroup(widget.tableList.length),
              style: KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          RadioListTile<int>(
            value: 2,
            groupValue: selectedMode,
            activeColor: KPColors.fillBrandNormal,
            contentPadding: EdgeInsets.zero,
            onChanged: (v) => ref.read(groupOrderModeProvider.notifier).state = v!,
            title: Text(
              context.locale.customizeTablesToOrderFor,
              style: KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
          ),
          // 网格多选
          if (selectedMode == 2)
            Padding(
              padding: const EdgeInsets.only(top: 16.0, left: 50),
              child: LayoutBuilder(
                builder: (context, constraints) {
                  int crossAxisCount = 3;
                  double spacing = 8;
                  double aspectRatio = 1.5625;
                  int rowCount = (widget.tableList.length / crossAxisCount).ceil();
                  double totalSpacing = (crossAxisCount - 1) * spacing;
                  double itemWidth =
                      (constraints.maxWidth - totalSpacing) / crossAxisCount;
                  double itemHeight = itemWidth / aspectRatio;
                  double totalHeight = rowCount * itemHeight + (rowCount - 1) * spacing;
                  double gridHeight = totalHeight > 302 ? 302 : totalHeight;
                  return SizedBox(
                    height: gridHeight,
                    child: GridView.builder(
                      physics: totalHeight > 302
                          ? const AlwaysScrollableScrollPhysics()
                          : const NeverScrollableScrollPhysics(),
                      itemCount: widget.tableList.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: spacing,
                        crossAxisSpacing: spacing,
                        childAspectRatio: aspectRatio,
                      ),
                      itemBuilder: (context, index) {
                        final table = widget.tableList[index];
                        final isCurTable = table.tableTitle == widget.curTableTitle;
                        final isSelected = isCurTable || selectedTables.contains(index);
                        final isEnabled = !isCurTable;
                        final isUnpaid = table.state == TableState.unpaid.value;
                        return Stack(
                          children: [
                            // 右侧内容卡片
                            Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(
                                  color: KPColors.borderGrayLightDarkest,
                                  width: 1,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  isCurTable
                                      ? Container(
                                          width: 20,
                                          height: 20,
                                          margin: const EdgeInsets.only(left: 16, right: 16),
                                          decoration: BoxDecoration(
                                            color: KPColors.fillGrayDarkLightest,
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: const Icon(
                                            Icons.check,
                                            size: 18,
                                            color: Colors.white,
                                          ),
                                        )
                                      : Checkbox(
                                          value: isSelected,
                                          onChanged: isEnabled
                                              ? (v) {
                                                  final notifier = ref.read(groupOrderSelectedTablesProvider.notifier);
                                                  final newSet = {...selectedTables};
                                                  if (v == true) {
                                                    newSet.add(index);
                                                  } else {
                                                    newSet.remove(index);
                                                  }
                                                  notifier.state = newSet;
                                                }
                                              : null,
                                          activeColor: KPColors.fillBrandNormal,
                                        ),
                                  Flexible(
                                    child: Text(
                                      table.tableTitle,
                                      maxLines: 3,
                                      style: KPFontStyle.headingSmall.copyWith(
                                        color: KPColors.textGrayPrimary,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // 左侧彩色条
                            Container(
                              width: 6,
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: isUnpaid
                                    ? TableState.unpaid.color
                                    : TableState.pendingOrder.color,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(8),
                                  bottomLeft: Radius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          const SizedBox(height: 24),
          //按钮
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: KPColors.fillGrayLightLighter,
                    borderRadius: BorderRadius.circular(KPRadius.l),
                  ),
                  child: Text(
                    context.locale.cancelCaps,
                    style: KPFontStyle.headingSmall
                        .copyWith(color: KPColors.textGrayPrimary),
                  ),
                ).onTap(() {
                  context.pop();
                }),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: KPColors.fillGrayDarDarkest,
                    borderRadius: BorderRadius.circular(KPRadius.l),
                  ),
                  child: Text(
                    selectedMode == 2
                        ? context.locale.batchSendOrder
                        : context.locale.sendOrder,
                    style: KPFontStyle.headingSmall.copyWith(
                      color: KPColors.textGrayInverse,
                    ),
                  ),
                ).onTap(() {
                  //todo 联台的下单逻辑
                  context.pop();
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }
}