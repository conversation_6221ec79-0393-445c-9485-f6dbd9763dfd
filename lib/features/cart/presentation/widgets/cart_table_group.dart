import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:path/path.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class CartTableGroup extends ConsumerStatefulWidget {
  final TableGroupItem tableGroup;
  final ValueChanged<String> onTableSelected;
  final String selectedTableTitle;
  const CartTableGroup({super.key,required this.tableGroup,required this.onTableSelected,required this.selectedTableTitle});

  @override
  ConsumerState createState() => _CartTableGroupState();
}

class _CartTableGroupState extends ConsumerState<CartTableGroup> {

  //是否正在选中查看联台按钮
  final isTableGroupSelected = StateProvider<bool>((ref) => false);

  @override
  Widget build(BuildContext context) {
    bool isOpen = ref.watch(isTableGroupSelected);
    return LayoutBuilder(builder: (ctx, constraints) {
      return GestureDetector(
        onTap: () {
          ref.read(isTableGroupSelected.notifier).state = true;
          KPPopover.showPopover(
            context: context,
            width: 200,
            height: (widget.tableGroup.tableList.length<4)? (widget.tableGroup.tableList.length+1)*55:220,
            contentWidget: _buildPopoverContent(context),
            overlayColor: Colors.transparent,
            placement: TDPopoverPlacement.bottomLeft,
            onComplete: (v){
              ref.read(isTableGroupSelected.notifier).state = false;
            }
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color:isOpen? KPColors.fillGrayLightDark: KPColors.fillGrayLightLightest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              SvgPicture.asset(
                Assets.images.iconTableLink.path,
                width: 18,
                height: 18,
                color: KPColors.iconGrayPrimary,
              ),
              const SizedBox(width: 4),
              Text(
                widget.tableGroup.groupName,
                style: KPFontStyle.headingXSmall.copyWith(
                  color: KPColors.textGrayPrimary,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildPopoverContent(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: KPColors.fillGrayLightLightest,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12,vertical: 16),
            alignment: Alignment.centerLeft,
            child: Text(
              '${widget.tableGroup.groupName}(${context.locale.tablesWithCount(widget.tableGroup.tableList.length)})',
              style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
            ),
          ),
          // 可滚动列表区域
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: widget.tableGroup.tableList.length,
              itemBuilder: (context, index) {
                final tableName = widget.tableGroup.tableList[index].tableTitle;
                final isSelected = tableName == widget.selectedTableTitle;
                return GestureDetector(
                  onTap: () {
                    widget.onTableSelected(tableName);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12,vertical: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? KPColors.fillBrandLightest : null,
                    ),
                    child: Row(
                      children: [
                        // 左侧序号
                        Expanded(
                          child: Text(
                            tableName,
                            style: KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
                          ),
                        ),
                        // 右侧箭头
                        Visibility(
                          visible: isSelected,
                          child: const Icon(
                            Icons.check,
                            size: 16,
                            color: KPColors.iconBrandDefault,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
