import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/constant/app_sizes.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/utils/common_util.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/cart/data/cart_intranet_repository.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/cart/domain/dining_style_item.dart';
import 'package:kpos/features/cart/presentation/cart_controller.dart';
import 'package:kpos/features/cart/presentation/cart_pick_up_order.dart';
import 'package:kpos/features/cart/presentation/member/member_list_popup.dart';
import 'package:kpos/features/cart/presentation/widgets/index.dart';
import 'package:kpos/features/product/domain/product_spec_group.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:kpos/common/components/kp_loading.dart';
import 'cart_action/cart_action_view.dart';
import 'cart_product_tags.dart';

import '../../../assets/assets.gen.dart';
import '../../product/domain/product_item.dart';
import '../../product/presentation/product_detail_controller.dart';
import '../../product/presentation/providers/editing_cart_item_provider.dart';
import '../../product/presentation/spec/product_spec_screen.dart';

// 记录当前打开的商品弹窗ID
final activePopupItemIdProvider = StateProvider<String?>((ref) => null);

// 历史订单展开/收起状态
final historicalOrdersExpandedProvider = StateProvider<bool>((ref) => false);

enum DiningType {
  unknown(value: 0),

  ///堂食
  dineIn(value: 1),

  ///自提
  selfPickup(value: 2),

  ///外带
  takeAway(value: 3),

  ///外卖
  delivery(value: 4);

  const DiningType({
    required this.value,
  });

  static DiningType getTypeByValue(int value) {
    return DiningType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => DiningType.unknown,
    );
  }

  static String valueToLabel(int value, BuildContext ctx) {
    switch (value) {
      case 1:
        return KPLocale.of(ctx).dineIn;
      case 2:
        return KPLocale.of(ctx).delivery;
      case 3:
        return KPLocale.of(ctx).selfPickup;
      case 4:
        return KPLocale.of(ctx).takeAway;
      default:
        return '';
    }
  }

  final int value;
}

enum CartItemMark {
  unknown(value: 0),
  dishesServed(value: 1),
  urgeDishes(value: 2),
  packaging(value: 3),
  //等叫
  onHold(value: 4),
  //叫起
  requestService(value: 5),
  returnDish(value: 6);

  const CartItemMark({
    required this.value,
  });

  static CartItemMark getTypeByValue(int value) {
    return CartItemMark.values.firstWhere(
      (e) => e.value == value,
      orElse: () => CartItemMark.unknown,
    );
  }

  final int value;
}

class CartScreen extends ConsumerStatefulWidget {
  final TableItem? table;
  final TableGroupItem? tableGroup;

  const CartScreen({super.key, this.table, this.tableGroup});

  @override
  ConsumerState createState() => _CartScreenState();
}

class _CartScreenState extends ConsumerState<CartScreen> with WidgetsBindingObserver {
  // 在文件顶部添加编辑状态 provider(用于商品详情回显做规格的修改)
  // final editingCartItemProvider = StateProvider<CartProductItem?>((ref) => null);

  final pickUpNumberController = TextEditingController();

  final editTableNameNotifier = ValueNotifier(false);

  late FocusNode focusNode;

  final GlobalKey _bundleOffKey = GlobalKey();
  final GlobalKey _bundleTaxKey = GlobalKey();

  double _keyboardHeight = 0;

  //是否正在选中修改用餐人数按钮
  final isPeopleCountSelected = StateProvider<bool>((ref) => false);

  //退菜确认勾选 Provider
  final cancelReturnConfirmProvider = StateProvider<bool>((ref) => false);

  final _formKey = GlobalKey<FormBuilderState>();

  ValueNotifier<int> popoverIndex = ValueNotifier(-1);

  //当前点击修改数量的 tag 的 index
  ValueNotifier<int> activeNumberIndex = ValueNotifier(-1);

  // 滑动打开cell索引的Provider
  final openedSwipeIndexProvider = StateProvider<int?>((ref) => null);

  // 记录是否已弹过气泡提示
  final ValueNotifier<bool> hasShownProductTip = ValueNotifier(false);

  // 记录餐牌号输入框是否交互过
  final ValueNotifier<bool> hasInteractedWithPickUpNumber = ValueNotifier(false);

  // 记录用户是否实际输入过内容 (只有用户输入过内容，然后清空才显示错误)
  final ValueNotifier<bool> hasActuallyTyped = ValueNotifier(false);

  // 记录上一次的订单ID
  String? _lastOrderId;

  bool? isAllSelected(int len, int total) {
    if (len != 0 && len != total) {
      return null;
    } else if (len == total) {
      return true;
    } else {
      return false;
    }
  }

  void onCheckBoxTap(FormFieldState<List<int>> field, int index, bool checked) {
    final current = List<int>.from(field.value ?? []);
    if (checked) {
      current.remove(index);
    } else {
      current.add(index);
    }
    field.didChange(current);
    ref.read(cartSelectedItemsProvider.notifier).set(List.from(current));
  }

  void _getDingingType() async {
    List<DiningStyleItem> diningTypeList =
        await ref.read(cartIntranetRepositoryProvider).getDiningStyleItemList();
    ref.read(diningStylesProvider.notifier).set(diningTypeList);
  }

  @override
  void initState() {
    super.initState();
    //获取就餐方式
    _getDingingType();
    focusNode = FocusNode();
    focusNode.addListener(_onPickUpFocusChange);
    WidgetsBinding.instance.addObserver(this);
    Future(() async {
      // 首先检查是否有订单ID
      final orderId = await ref.read(orderCreationServiceProvider).getCurrentOrderId();
      
      // 如果是桌台点餐，设置相关状态
      if (widget.table != null) {
        ref.read(tableOrderingStateProvider.notifier).set(true);
        ref
            .read(selectedTableTitleStateProvider.notifier)
            .set(widget.table?.tableTitle ?? '');
      } else if (orderId == null) {
        // 如果没有订单ID且不是桌台点餐，确保清除桌台相关状态
        ref.read(tableOrderingStateProvider.notifier).set(false);
        ref.read(selectedTableTitleStateProvider.notifier).set('');
      }
      
      await ref.read(cartServiceProvider).initData(
            isTableOrdering: ref.read(tableOrderingStateProvider),
            tableNumber: widget.table?.tableTitle,
            seatedNumber: widget.table?.seatedNumber,
            orderId: orderId,
          );

      // 获取完数据后，检查非桌台点餐且餐牌号为空的情况，自动对焦
      if (mounted && !ref.read(tableOrderingStateProvider)) {
        final pickUpNumber = ref.read(cartServiceProvider).cartItem?.mealVoucher ?? '';
        if (pickUpNumber.isEmpty) {
          editTableNameNotifier.value = true;
          // 重置标记，确保自动对焦不会标记为已输入
          hasInteractedWithPickUpNumber.value = false;
          hasActuallyTyped.value = false;
          focusNode.requestFocus();
        }
        // 初始化设置 lastPickUpNumberStateProvider
        ref.read(lastPickUpNumberStateProvider.notifier).set(pickUpNumber);
      }
    });
  }

  void _onPickUpFocusChange() async {
    if (focusNode.hasFocus) {
      // 有焦点时不进行操作，实际输入在 onChanged 中处理
    } else if (!focusNode.hasFocus) {
      final value = pickUpNumberController.text;
      final context = rootNavigatorKey.currentContext;

      // 与购物车中实际存储的餐牌号比较，而不是与 provider 比较
      final cartPickUpNumber = ref.read(tableOrderingStateProvider)
          ? ref.read(cartServiceProvider).cartItem?.tableNumber ?? ''
          : ref.read(cartServiceProvider).cartItem?.mealVoucher ?? '';

      if (value != cartPickUpNumber) {
        await ref.read(cartServiceProvider).updateMealVoucher(value, context);
      }
      editTableNameNotifier.value = false;
    }
  }

  @override
  void dispose() {
    pickUpNumberController.dispose();
    focusNode.removeListener(_onPickUpFocusChange);
    focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    if (bottomInset == 0 && _keyboardHeight > 0) {
      // 根据是否桌台点餐获取对应的餐牌号
      String number = '';
      if (ref.read(tableOrderingStateProvider)) {
        number = ref.read(cartServiceProvider).cartItem?.tableNumber ?? '';
      } else {
        number = ref.read(cartServiceProvider).cartItem?.mealVoucher ?? '';
      }
      if (number.isNotEmpty) {
        editTableNameNotifier.value = false;
      }
    } else if (bottomInset > 0 && _keyboardHeight == 0) {
      // 键盘刚刚弹出
      print("键盘弹出");
    }
    _keyboardHeight = bottomInset;
  }

  @override
  Widget build(BuildContext context) {
    // 监听批量操作显示状态，关闭时清空选中
    ref.listen<bool>(showCartBatchActionProvider, (prev, next) {
      if (prev == true && next == false) {
        // 清空选中
        _formKey.currentState?.fields['cart_list']?.reset();
      }
    });

    // 监听订单ID变化，订单ID变化时重置提示状态
    final currentOrderId = ref.watch(currentOrderIdProvider)?.toString();
    if (currentOrderId != _lastOrderId) {
      _lastOrderId = currentOrderId;
      hasShownProductTip.value = false;
    }

    final isTableOrdering = ref.watch(tableOrderingStateProvider);
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false, // 设置为false，确保键盘弹出时不会挤压内容
        backgroundColor: Colors.white,
        body: LayoutBuilder(
          builder: (context, constraints) {
            final maxTopViewHeight = constraints.maxHeight * 0.4;
            final isBatchAction = ref.watch(showCartBatchActionProvider);
            return GestureDetector(
              onTap: () {
                if (focusNode.hasFocus) {
                  focusNode.unfocus();
                }
              },
              child: Container(
                padding: EdgeInsets.only(top: isBatchAction ? 0 : 20, bottom: 20),
                decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Color(0xFFEEEEF0), width: 1)),
                ),
                child: FormBuilder(
                  key: _formKey,
                  child: Column(
                    children: [
                      //顶部组件，批量操作时不显示
                      Visibility(
                        visible: !isBatchAction,
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxHeight: maxTopViewHeight,
                          ),
                          child: SingleChildScrollView(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              child: _buildTopView(context, isTableOrdering),
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                          visible: !isBatchAction, child: const SizedBox(height: 8)),
                      Expanded(child: _buildListView(context, isBatchAction)),
                      //促销活动提示区域
                      // _buildActivity(),
                      Visibility(visible: !isBatchAction, child: _buildBottomView(ref)),
                      //按钮
                      _buildButtonRow(isTableOrdering),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTopView(BuildContext context, bool isTableOrdering) {
    // 根据是否桌台点餐选择不同的餐牌号字段
    String pickUpNumber = '';
    if (ref.read(tableOrderingStateProvider)) {
      // 桌台点餐使用 tableNumber
      pickUpNumber = ref.watch(cartServiceProvider).cartItem?.tableNumber ?? '';
    } else {
      // 非桌台点餐使用 mealVoucher
      pickUpNumber = ref.watch(cartServiceProvider).cartItem?.mealVoucher ?? '';
      Future(() {
        ref.read(lastPickUpNumberStateProvider.notifier).set(pickUpNumber);
      });
    }

    List<DiningStyleItem> diningStyleList = ref.watch(diningStylesProvider);
    List<DiningType> diningTypeList = [
      DiningType.dineIn,
      DiningType.selfPickup,
      DiningType.delivery,
      DiningType.takeAway
    ];
    if (diningStyleList.isNotEmpty) {
      diningTypeList = diningStyleList
          .map((item) => DiningType.getTypeByValue(item.saleType))
          .toList();
    }
    String selectedTableTitle = ref.watch(selectedTableTitleStateProvider);
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (isTableOrdering)
              Row(
                children: [
                  Text(
                    selectedTableTitle,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(width: 4),
                  if (widget.tableGroup != null)
                    //联台标识/按钮
                    CartTableGroup(
                      tableGroup: widget.tableGroup!,
                      onTableSelected: (tableName) {
                        ref.read(selectedTableTitleStateProvider.notifier).set(tableName);
                      },
                      selectedTableTitle: selectedTableTitle,
                    ),
                ],
              ),
            if (!isTableOrdering)
              ValueListenableBuilder(
                valueListenable: editTableNameNotifier,
                builder: (ct, isEdit, _) {
                  return isEdit
                      ? _buildPickUpNumberView()
                      : SizedBox(
                          height: 32,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                '#$pickUpNumber',
                                style: KPFontStyle.headingXLarge
                                    .copyWith(color: KPColors.textGrayPrimary),
                              ),
                              const SizedBox(width: 8),
                              GestureDetector(
                                onTap: () {
                                  editTableNameNotifier.value = true;
                                  pickUpNumberController.text = pickUpNumber;
                                  // 重置标记
                                  hasInteractedWithPickUpNumber.value = false;
                                  hasActuallyTyped.value = false;
                                  focusNode.requestFocus();
                                },
                                child: Assets.images.iconEdit.svg(width: 20, height: 20),
                              ),
                            ],
                          ),
                        );
                },
              ),
            TextButton(
              onPressed: () {
                KPSlidePopup.showFromTarget(
                  context: context,
                  contentWidget: CartActionView(isTableOrdering: widget.table != null),
                  targetWidth: 360,
                  onMaskTap: () {
                    ref.read(showCartBatchActionProvider.notifier).set(false);
                  },
                );
              },
              child: Text(
                context.locale.action,
                style: TextStyle(
                  color: context.theme.brandNormalColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LayoutBuilder(builder: (popoverContext, _) {
          return SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  //选择就餐方式
                  Consumer(
                    builder: (ctx, ref, _) {
                      final selectedDiningType = ref.watch(diningTypeStateProvider);
                      return KPDropDownMenu<DiningType>(
                        items: diningTypeList,
                        itemText: (type) => DiningType.valueToLabel(type.value, context),
                        value: selectedDiningType,
                        itemTextStyle: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF323843),
                          fontWeight: FontWeight.w500,
                        ),
                        onChanged: (type) async {
                          if (type != null) {
                            final orderId = await ref
                                .read(orderCreationServiceProvider)
                                .getCurrentOrderId();
                            if (orderId != null) {
                              await ref
                                  .read(cartIntranetRepositoryProvider)
                                  .setDiningMethod(orderId: orderId, method: type.value);
                              ref.read(diningTypeStateProvider.notifier).set(type);
                            }
                          }
                        },
                        menuMaxHeight: 340,
                        menuWidth: 200,
                        dropdownColor: Colors.white,
                      );
                    },
                  ),
                  const SizedBox(width: 6),
                  Consumer(
                    builder: (ctx, ref, _) {
                      final peopleCount = ref.watch(peopleCountStateProvider);
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        height: 32,
                        decoration: BoxDecoration(
                            color: ref.watch(isPeopleCountSelected)
                                ? KPColors.borderGrayLightDark
                                : null,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: KPColors.borderGrayLightDark, width: 1)),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Assets.images.iconUsers.svg(),
                            const SizedBox(width: 8),
                            Text(
                              "$peopleCount",
                              style: KPFontStyle.headingXSmall,
                            ),
                          ],
                        ),
                      ).onTap(() {
                        Future.microtask(() {
                          ref.read(isPeopleCountSelected.notifier).state = true;
                        });
                        KPPopover.showNumberKeyboard(
                          context: popoverContext,
                          title: context.locale.numberOfCovers,
                          hintText: KPLocale.of(context).enterNumOfPeople,
                          isAutoClamp: true,
                          initValue: peopleCount.toDouble(),
                          onConfirmPressed: (value) async {
                            Future.microtask(() {
                              ref.read(isPeopleCountSelected.notifier).state = false;
                            });
                            int newValue = (value ?? 1).toInt();
                            await ref
                                .read(cartServiceProvider)
                                .updateDiningNumber(newValue);
                          },
                        );
                      });
                    },
                  ),
                  const SizedBox(width: 6),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    height: 32,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE7E8EB), width: 1)),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Assets.images.iconCrown.svg(),
                        const SizedBox(width: 8),
                        Text(
                          context.locale.members,
                          style:
                              const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ).onTap(() {
                    KPSlidePopup.showFromTarget(
                      context: context,
                      contentWidget: MemberListPopup(
                        memberChangedResult: (result) {},
                      ),
                      targetWidth: 360,
                    );
                  }),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildPickUpNumberView() {
    // 直接监听 TextEditingController 的值更改
    return Expanded(
      child: ValueListenableBuilder(
          valueListenable: pickUpNumberController,
          builder: (context, textController, _) {
            final currentText = textController.text;
            final hasTyped = hasActuallyTyped.value;

            // 直接使用控制器的当前值判断，确保实时响应
            final shouldShowError = hasTyped && currentText.isEmpty;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 48,
                  padding: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: shouldShowError
                            ? KPColors.textRedDefault
                            : KPColors.textBrandDefault,
                        width: 3.0,
                      ),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.locale.pickUpNumber,
                        style: TextStyle(
                          fontSize: 12,
                          color: shouldShowError
                              ? KPColors.textRedDefault
                              : KPColors.textBrandDefault,
                        ),
                      ),
                      Expanded(
                        child: SizedBox(
                          height: 24,
                          child: TextField(
                            focusNode: focusNode,
                            controller: pickUpNumberController,
                            cursorColor: shouldShowError
                                ? KPColors.textRedDefault
                                : KPColors.textBrandDefault,
                            cursorHeight: 16,
                            cursorWidth: 1,
                            keyboardType: TextInputType.number,
                            onTap: () {
                              // 点击不标记为已输入，只标记交互
                              hasInteractedWithPickUpNumber.value = true;
                            },
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              isDense: true,
                              suffixIcon: shouldShowError
                                  ? const Icon(
                                      Icons.error,
                                      color: KPColors.textRedDefault,
                                      size: 24,
                                    )
                                  : null,
                              suffixIconConstraints: const BoxConstraints(
                                minWidth: 24,
                                minHeight: 24,
                                maxWidth: 32,
                                maxHeight: 32,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 2,
                                horizontal: 0,
                              ),
                            ),
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w400),
                            onSubmitted: (value) async {
                              // 与购物车中实际存储的餐牌号比较，而不是与 provider 比较
                              final cartPickUpNumber = ref
                                      .read(tableOrderingStateProvider)
                                  ? ref.read(cartServiceProvider).cartItem?.tableNumber ??
                                      ''
                                  : ref.read(cartServiceProvider).cartItem?.mealVoucher ??
                                      '';

                              if (value != cartPickUpNumber) {
                                await ref
                                    .read(cartServiceProvider)
                                    .updateMealVoucher(value, context);
                              }
                              editTableNameNotifier.value = false;
                            },
                            onChanged: (v) {
                              // 标记为已交互
                              hasInteractedWithPickUpNumber.value = true;

                              // 实时更新 provider 状态，确保 UI 立即响应
                              ref.read(lastPickUpNumberStateProvider.notifier).set(v);

                              // 只有用户实际输入过非空值，才标记为已实际输入过
                              if (v.isNotEmpty) {
                                hasActuallyTyped.value = true;
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                //错误文字 - 只在已输入过内容且当前为空时显示
                Visibility(
                  visible: shouldShowError,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0, top: 4),
                    child: Text(
                      context.locale.pickUpNumberEmptyTip,
                      style: const TextStyle(
                        fontSize: 12,
                        color: KPColors.textRedDefault,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }),
    );
  }

  Widget _buildListView(BuildContext context, bool isBatchAction) {
    return FormBuilderField<List<int>>(
      name: 'cart_list',
      initialValue: const [],
      builder: (field) {
        return KPAsyncValueWidget<List<CartProductItem>>(
          asyncValueProvider: cartControllerProvider,
          skipLoadingOnRefresh: false,
          dataBuilder: (context, ref, dataList) {
            final openedSwipeIndex = ref.watch(openedSwipeIndexProvider);
            final isHistoricalExpanded = ref.watch(historicalOrdersExpandedProvider);

            // 获取历史订单数据
            final cartService = ref.watch(cartServiceProvider);
            //todo : 这里需要替换成实际的历史订单数
            final historyItemsCount = 0;

            return Column(
              children: [
                //批量操作时显示的全选按钮
                Visibility(
                  visible: isBatchAction,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Row(
                      children: [
                        Checkbox(
                          value: isAllSelected(field.value?.length ?? 0, dataList.length),
                          tristate: true,
                          onChanged: (val) {
                            if ((isAllSelected(
                                    field.value?.length ?? 0, dataList.length) ??
                                true)) {
                              field.reset();
                              ref.read(cartSelectedItemsProvider.notifier).set([]);
                            } else {
                              final all = List.generate(dataList.length, (i) => i);
                              field.didChange(all);
                              ref
                                  .read(cartSelectedItemsProvider.notifier)
                                  .set(List.from(all));
                            }
                          },
                        ),
                        const SizedBox(width: 20),
                        Text(
                          '${context.locale.selectedAll} (${dataList.length})',
                          style: KPFontStyle.headingSmall.copyWith(
                            color: KPColors.textGrayPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Visibility(
                  visible: isBatchAction,
                  child: Container(
                    height: 0.8,
                    color: KPColors.borderGrayLightDark,
                  ),
                ),
                Expanded(
                  child: ListView(
                    // 添加右侧padding以便为滑动操作腾出空间
                    padding: const EdgeInsets.only(right: 16),
                    children: [
                      // 当前订单商品列表（最新的）
                      ...dataList.asMap().entries.map((entry) {
                        final index = entry.key;
                        final item = entry.value;
                        return _buildCartItem(
                            context, item, index, field, isBatchAction, openedSwipeIndex);
                      }),

                      // 如果有历史订单，显示展开/收起按钮
                      if (historyItemsCount > 0)
                        _buildHistoryOrdersToggle(context, historyItemsCount),

                      // 如果展开，显示历史订单列表（带蒙层效果）
                      if (isHistoricalExpanded && historyItemsCount > 0)
                        _buildHistoricalOrdersList(context, [
                          cartService.secondCartItemList,
                          cartService.thirdCartItemList
                        ]),
                    ],
                  ),
                ),
              ],
            );
          },
          emptyBuilder: (ctx, ref) {
            return const SizedBox();
          },
        );
      },
    );
  }

  // 构建历史订单列表（带蒙层效果）
  Widget _buildHistoricalOrdersList(
      BuildContext context, List<List<CartProductItem>> historicalGroups) {
    return Column(
      children: [
        // 动态构建所有历史订单组
        for (final group in historicalGroups) ...[
          // 订单组标题
          _buildHistoryOrderGroupHeader(context, 'Order(2)'),

          // 订单组中的所有商品项
          ...group.map((item) => _buildHistoricalItem(context, item)),
        ],
      ],
    );
  }

  // 构建历史订单组标题
  Widget _buildHistoryOrderGroupHeader(BuildContext context, String title) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          alignment: Alignment.centerLeft,
          child: Text(
            title,
            style: KPFontStyle.headingXSmall.copyWith(
              color: KPColors.textGraySecondary,
            ),
          ),
        ),
        // 半透明蒙层
        Positioned.fill(
          child: Container(
            color: Colors.white.withValues(alpha: 0.35),
          ),
        ),
      ],
    );
  }

  // 构建历史订单项（只读展示，无法操作）
  Widget _buildHistoricalItem(BuildContext context, CartProductItem item) {
    final isCancelled = item.isReturn == 1;

    return Stack(
      children: [
        // 商品项内容
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.only(
                left: 20,
                top: 8,
                bottom: 8,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //数量标签（不可改变）
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 2),
                    child: Text(
                      item.commodityType == 2
                          ? "${item.quantity ?? 0}${item.commodityUnit ?? ''}"
                          : "x${item.quantity?.toInt() ?? 1}",
                      style: KPFontStyle.bodySmall.copyWith(
                        color: KPColors.textGrayPrimary,
                        decoration: isCancelled
                            ? TextDecoration.lineThrough
                            : TextDecoration.none,
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),

                  // 商品名和标签
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 商品名
                        Row(
                          children: [
                            Visibility(
                              visible: item.isServed == 1,
                              child: Container(
                                width: 16,
                                height: 16,
                                margin: const EdgeInsets.only(right: 4),
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Color(0xFF22A06B),
                                ),
                                child: const Icon(Icons.check,
                                    size: 12, color: Colors.white),
                              ),
                            ),
                            Flexible(
                              child: Text(
                                item.commodityName,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w700,
                                  color: isCancelled
                                      ? KPColors.textGrayTertiary
                                      : KPColors.textGrayPrimary,
                                  decoration: isCancelled
                                      ? TextDecoration.lineThrough
                                      : TextDecoration.none,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),

                  // 价格
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "S\$${item.price}",
                        style: KPFontStyle.bodyMedium.copyWith(
                          color: isCancelled
                              ? KPColors.textGrayTertiary
                              : KPColors.textGrayPrimary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                  const SizedBox(width: 20),
                ],
              ),
            ),

            // 内容部分（标签、做法和备注）
            if ((item.practiceList?.isNotEmpty ?? false) ||
                (item.addonsList?.isNotEmpty ?? false) ||
                (item.remark?.isNotEmpty ?? false) ||
                _hasAnyStatus(item))
              Container(
                padding: const EdgeInsets.only(
                  left: 54,
                  right: 20,
                  bottom: 8,
                ),
                color: const Color(0xFFF9F9F9), // 历史订单背景色
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标签
                    if (_hasAnyStatus(item))
                      Container(
                        margin: const EdgeInsets.only(bottom: 4),
                        child: Wrap(
                          spacing: 4,
                          runSpacing: 4,
                          children: _buildTags(item),
                        ),
                      ),

                    //做法和加料
                    if (buildPracticeAndAddonsText(item).isNotEmpty)
                      Text(
                        buildPracticeAndAddonsText(item),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: KPColors.textGrayTertiary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                      ),
                    //备注
                    if (item.remark != null && item.remark!.isNotEmpty)
                      Text(
                        "${context.locale.notes}: ${item.remark}",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: KPColors.textGrayTertiary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),

            // 递归渲染套餐子商品
            if (item.productList != null && item.productList!.isNotEmpty)
              ...item.productList!.map((subItem) {
                // 用同样的方法但子项标记为 isSubItem=true
                return Container(
                  padding: const EdgeInsets.only(left: 20),
                  color: const Color(0xFFF9F9F9), // 历史订单背景色
                  child: _buildHistoricalSubItem(context, subItem, isCancelled),
                );
              }),

            // 分割线
            Container(
              height: 0.8,
              color: KPColors.borderGrayLightDark,
              margin: const EdgeInsets.only(bottom: 4),
            ),
          ],
        ),
        // 半透明蒙层
        Positioned.fill(
          child: Container(
            color: Colors.white.withValues(alpha: 0.35),
          ),
        ),
      ],
    );
  }

  // 历史订单中的子商品项
  Widget _buildHistoricalSubItem(
      BuildContext context, CartProductItem item, bool parentIsCancelled) {
    final isCancelled = parentIsCancelled || (item.isReturn == 1);

    return Stack(
      children: [
        // 子商品内容
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 数量标签
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 2),
                  child: Text(
                    item.commodityType == 2
                        ? "${item.quantity ?? 0}${item.commodityUnit ?? ''}"
                        : "x${item.quantity?.toInt() ?? 1}",
                    style: KPFontStyle.bodySmall.copyWith(
                      color: KPColors.textGrayPrimary,
                      decoration:
                          isCancelled ? TextDecoration.lineThrough : TextDecoration.none,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 商品名
                      Text(
                        item.commodityName,
                        style: KPFontStyle.bodyMedium.copyWith(
                          color: isCancelled
                              ? KPColors.textGrayTertiary.withValues(alpha: 0.8)
                              : KPColors.textGrayTertiary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "S\$${item.price}",
                      style: KPFontStyle.bodyMedium.copyWith(
                        color: isCancelled
                            ? KPColors.textGrayTertiary
                            : KPColors.textGrayPrimary,
                        decoration: isCancelled
                            ? TextDecoration.lineThrough
                            : TextDecoration.none,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                ),
                const SizedBox(width: 20),
              ],
            ),

            // 显示做法、加料等内容
            if ((item.practiceList?.isNotEmpty ?? false) ||
                (item.addonsList?.isNotEmpty ?? false) ||
                (item.remark?.isNotEmpty ?? false))
              Container(
                padding: const EdgeInsets.only(
                  left: 54,
                  right: 20,
                  bottom: 8,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (buildPracticeAndAddonsText(item).isNotEmpty)
                      Text(
                        buildPracticeAndAddonsText(item),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: KPColors.textGrayTertiary.withValues(alpha: 0.8),
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                      ),
                    if (item.remark != null && item.remark!.isNotEmpty)
                      Text(
                        "${context.locale.notes}: ${item.remark}",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: KPColors.textGrayTertiary.withValues(alpha: 0.8),
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
          ],
        ),

        // 半透明蒙层
        Positioned.fill(
          child: Container(
            color: Colors.white.withOpacity(0.35),
          ),
        ),
      ],
    );
  }

  // 检查商品是否有任何状态标签
  bool _hasAnyStatus(CartProductItem item) {
    // 检查各种状态
    return (item.status?.contains(CartItemMark.packaging.value) == true) ||
        (item.isUrge == 1) ||
        (item.status?.contains(CartItemMark.onHold.value) == true) ||
        (item.status?.contains(CartItemMark.requestService.value) == true) ||
        (item.isReturn == 1);
  }

  // 构建单个购物车商品项
  Widget _buildCartItem(BuildContext context, CartProductItem item, int index,
      FormFieldState<List<int>> field, bool isBatchAction, int? openedSwipeIndex,
      {bool isHistorical = false}) {
    final checked = field.value?.contains(index) ?? false;
    return Column(
      children: [
        Container(
          clipBehavior: Clip.hardEdge,
          decoration: const BoxDecoration(),
          child: TDSwipeCell(
            slidableKey: ValueKey(item.orderItemId),
            groupTag: 'cart',
            onChange: (_, bool isOpen) {
              if (isOpen) {
                ref.read(openedSwipeIndexProvider.notifier).state = index;
              } else if (ref.read(openedSwipeIndexProvider) == index) {
                ref.read(openedSwipeIndexProvider.notifier).state = null;
              }
            },
            cell: GestureDetector(
              onTap: () {
                TDSwipeCell.close('cart');
              },
              child: Container(
                color: openedSwipeIndex == index
                    ? KPColors.fillGrayLightLight
                    : isHistorical
                        ? const Color(0xFFF9F9F9) // 历史订单背景色稍微淡一点
                        : Colors.white,
                child: Column(
                  children: [
                    Visibility(visible: isBatchAction, child: const SizedBox(height: 8)),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible: isBatchAction,
                          child: Checkbox(
                            value: checked,
                            onChanged: (val) {
                              onCheckBoxTap(field, index, checked);
                            },
                          ),
                        ),
                        Expanded(
                          child: _buildItemView(
                            context: context,
                            isBatchAction: isBatchAction,
                            index: index,
                            item: item,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            right: TDSwipeCellPanel(
              extentRatio: 0.2,
              children: [
                TDSwipeCellAction(
                  icon: Icons.delete,
                  backgroundColor: Colors.red,
                  onPressed: (ctx) async {
                    await ref.read(cartServiceProvider).deleteCartProduct(item.orderItemId);
                  },
                ),
              ],
            ),
          ),
          ),
        // 分割线
        Container(
          height: 0.8,
          color: KPColors.borderGrayLightDark,
        ),
      ],
    );
  }

  // 构建历史订单展开/收起按钮
  Widget _buildHistoryOrdersToggle(BuildContext context, int historyCount) {
    final isExpanded = ref.watch(historicalOrdersExpandedProvider);
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "${context.locale.orderedDishes}($historyCount)",
            style: KPFontStyle.bodySmall.copyWith(color: KPColors.textGraySecondary),
          ),
          Icon(
            isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: KPColors.textGraySecondary,
            size: 18,
          ),
        ],
      ).onTap(() {
        ref.read(historicalOrdersExpandedProvider.notifier).state = !isExpanded;
      }),
    );
  }

  // 构建单个订单项卡片
  Widget _buildItemView({
    required BuildContext context,
    required bool isBatchAction,
    required int index,
    required CartProductItem item,
    bool isSubItem = false, // 是否为子商品
  }) {
    // todo 这是演示数据，接口联调后删除
    final isCancelled = item.isReturn == 1;
    final activePopupItemId = ref.watch(activePopupItemIdProvider);
    final isActivePopup = activePopupItemId == item.orderItemId.toString();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildProductItemTop(item, index, isBatchAction,
            isSubItem: isSubItem, isCancelled: isCancelled, isActivePopup: isActivePopup),
        // 内容部分
        if ((item.practiceList?.isNotEmpty ?? false) ||
            (item.addonsList?.isNotEmpty ?? false) ||
            (item.remark?.isNotEmpty ?? false))
          Container(
            width: double.maxFinite,
            padding: EdgeInsets.only(
              left: isBatchAction ? 46 : 54,
              right: 20,
              bottom: 8,
            ),
            color: isCancelled||isActivePopup ? KPColors.fillGrayLightLight : Colors.transparent,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //标签
                Container(
                  margin: const EdgeInsets.only(bottom: 4),
                  child: Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: _buildTags(item),
                  ),
                ),
                //做法和加料
                if (buildPracticeAndAddonsText(item).isNotEmpty)
                  Text(
                    buildPracticeAndAddonsText(item),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: isSubItem
                          ? KPColors.textGrayTertiary.withValues(alpha: 0.8)
                          : KPColors.textGrayTertiary,
                      decoration:
                          isCancelled ? TextDecoration.lineThrough : TextDecoration.none,
                    ),
                  ),
                //备注
                if (item.remark != null && item.remark!.isNotEmpty)
                  Text(
                    "${context.locale.notes}: ${item.remark}",
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: isSubItem
                          ? KPColors.textGrayTertiary.withValues(alpha: 0.8)
                          : KPColors.textGrayTertiary,
                      decoration:
                          isCancelled ? TextDecoration.lineThrough : TextDecoration.none,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        // 递归渲染套餐子商品
        if (item.productList != null && item.productList!.isNotEmpty)
          ...item.productList!.asMap().entries.map((entry) {
            final subIndex = entry.key;
            final subItem = entry.value;
            return _buildItemView(
              context: context,
              isBatchAction: isBatchAction,
              index: subIndex,
              item: subItem,
              isSubItem: true,
            );
          }),
      ],
    ).onDoubleTapOrLongPress(() {
      if (isCancelled) {
        _onCancelReturn(item);
      }
    });
  }

  // 合成做法和加料的显示文本
  String buildPracticeAndAddonsText(CartProductItem item) {
    // 做法
    final practiceStr = (item.practiceList ?? [])
        .map((e) => e.price > 0
            ? '${e.commodityPracticeName}（+S\$${e.price.toStringAsFixed(2)}）'
            : e.commodityPracticeName)
        .join(', ');

    // 加料
    String addonsStr = (item.addonsList ?? [])
        .map((e) =>
            '${e.commodityName}（+S\$${e.price.toStringAsFixed(2)}）${e.quantity > 0 ? e.commodityType == 2 ? "${e.quantity}${e.commodityUnit ?? ''}" : "X${e.quantity}" : ''}')
        .join(', ');

    if (practiceStr.isNotEmpty && addonsStr.isNotEmpty) {
      return '$practiceStr, $addonsStr';
    } else if (practiceStr.isNotEmpty) {
      return practiceStr;
    } else {
      return addonsStr;
    }
  }

  ///商品顶部区域
  Widget _buildProductItemTop(CartProductItem item, int index, bool isBatchAction,
      {bool isSubItem = false, bool isCancelled = false, bool isActivePopup = false}) {
    // 商品名行全部左对齐
    return ValueListenableBuilder(
      valueListenable: popoverIndex,
      builder: (ctx, selectedIndex, _) {
        return LayoutBuilder(builder: (popoverContext, constraints) {
          return GestureDetector(
            onTap: () {
              if (isSubItem) return;
              // 首次点击弹气泡，之后直接弹详情
              if (!hasShownProductTip.value) {
                popoverIndex.value = index;
                hasShownProductTip.value = true;
                KPPopover.showPopover(
                  context: popoverContext,
                  content: ctx.locale.clickAgainToModifyProduct,
                  width: 271,
                  showArrow: true,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  theme: TDPopoverTheme.dark,
                  overlayColor: Colors.transparent,
                  placement: TDPopoverPlacement.left,
                  onComplete: (val) {
                    popoverIndex.value = -2;
                  },
                );
                return;
              }
              _onProductItemTap(context, item);
            },
            child: Container(
              padding: EdgeInsets.only(
                left: isBatchAction ? 12 : 20, // 所有商品名行都用 20
                top: 12,
                bottom: 8,
              ),
              color: (isCancelled || isActivePopup)
                  ? KPColors.fillGrayLightLight
                  : Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //数量标签（可改变）
                  ValueListenableBuilder(
                    valueListenable: activeNumberIndex,
                    builder: (context, activeNumber, _) {
                      return Visibility(
                        visible: !isBatchAction && !isSubItem,
                        child: GestureDetector(
                          onTap: () {
                            if (isCancelled) return;
                            activeNumberIndex.value = index;
                            KPPopover.showNumberKeyboard(
                              context: popoverContext,
                              title: context.locale.quantity,
                              hintText: KPLocale.of(context).quantity,
                              offset: -16,
                              showDot: item.commodityType == 2,
                              initValue: item.quantity?.toDouble() ?? 1,
                              onConfirmPressed: (value) async {
                                activeNumberIndex.value = -1;
                                if (item.quantity == value) return;
                                if (value == null) return;
                                if (value < 1 || value > 999) return;
                                await ref
                                    .read(cartServiceProvider)
                                    .updateCartItemQuantity(
                                      index,
                                      value,
                                    );
                              },
                            );
                          },
                          child: Container(
                            constraints: const BoxConstraints(minWidth: 32),
                            alignment: Alignment.center,
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                width: 1,
                                color: activeNumber == index
                                    ? KPColors.borderBrandDefault
                                    : KPColors.borderGrayLightDarkest,
                              ),
                              color: activeNumber == index
                                  ? const Color(0xFFFFF7E6)
                                  : Colors.transparent,
                            ),
                            child: Text(
                              item.commodityType == 2
                                  ? "${item.quantity ?? 0}${item.commodityUnit ?? ''}"
                                  : "x${item.quantity?.toInt() ?? 1}",
                              style: KPFontStyle.bodySmall.copyWith(
                                color: KPColors.textGrayPrimary,
                                decoration: isCancelled
                                    ? TextDecoration.lineThrough
                                    : TextDecoration.none,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  //数量标签（不可改变）
                  Visibility(
                    visible: isBatchAction || isSubItem,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 9, vertical: 2),
                      child: Text(
                        item.commodityType == 2
                            ? "${item.quantity ?? 0}${item.commodityUnit ?? ''}"
                            : "x${item.quantity?.toInt() ?? 1}",
                        style: KPFontStyle.bodySmall.copyWith(
                          color: KPColors.textGrayPrimary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: Row(
                            children: [
                              Visibility(
                                visible: item.isServed == 1,
                                child: Container(
                                  width: 16,
                                  height: 16,
                                  margin: const EdgeInsets.only(right: 4),
                                  alignment: Alignment.center,
                                  decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Color(0xFF22A06B),
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    size: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              // 商品名
                              Flexible(
                                child: Text(
                                  item.commodityName,
                                  style: isSubItem
                                      ? KPFontStyle.bodyMedium.copyWith(
                                          color: isCancelled
                                              ? KPColors.textGrayTertiary
                                              : KPColors.textGrayPrimary,
                                          decoration: isCancelled
                                              ? TextDecoration.lineThrough
                                              : TextDecoration.none,
                                        )
                                      : TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w700,
                                          color: isCancelled
                                              ? KPColors.textGrayTertiary
                                              : KPColors.textGrayPrimary,
                                          decoration: isCancelled
                                              ? TextDecoration.lineThrough
                                              : TextDecoration.none,
                                        ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "S\$${item.price}",
                        style: KPFontStyle.bodyMedium.copyWith(
                          color: isCancelled
                              ? KPColors.textGrayTertiary
                              : KPColors.textGrayPrimary,
                          decoration: isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                  Visibility(
                    visible: isActivePopup && !isSubItem,
                    child: Container(
                      margin: const EdgeInsets.only(top: 1),
                      child: Icon(
                        Icons.keyboard_arrow_right,
                        size: 18,
                        color: isCancelled
                            ? KPColors.textGrayTertiary
                            : KPColors.textGrayPrimary,
                      ),
                    ),
                  ),
                  SizedBox(width: (isActivePopup && !isSubItem) ? 4 : 20),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  List<Widget> _buildTags(CartProductItem item) {
    final widgets = <Widget>[];

    // 打包
    if (item.status?.contains(CartItemMark.packaging.value) == true) {
      widgets.add(Tag.packaging());
    }

    // 催菜
    if (item.isUrge == 1) {
      widgets.add(Tag.urgeDishes(
        time: item.urgeTime != null
            ? CommonUtil.formatTimestamp(item.urgeTime!, pattern: 'HH:mm')
            : null,
      ));
    }

    // 等叫
    if (item.status?.contains(CartItemMark.onHold.value) == true) {
      widgets.add(Tag.onHold());
    }

    // 叫起
    if (item.status?.contains(CartItemMark.requestService.value) == true) {
      widgets.add(Tag.requestService());
    }

    // 退菜
    if (item.isReturn == 1) {
      widgets.add(Tag.returnDish());
    }

    return widgets;
  }

  Widget _buildBottomView(WidgetRef ref) {
    final value = ref.watch(cartPriceProvider);
    String orderNotes = ref.watch(cartServiceProvider).cartItem?.remark ?? '';
    double serviceFee = ref.watch(cartServiceProvider).serviceFee;
    List<CartProductItem> cartItemList =
        ref.watch(cartServiceProvider).cartItem?.cartItemList ?? [];
    return value.when(
      data: (priceMaps) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
              visible: orderNotes.isNotEmpty || serviceFee > 0,
              child: Container(
                width: double.maxFinite,
                height: 1,
                color: KPColors.borderGrayLightBase,
                margin: const EdgeInsets.only(bottom: 8),
              ),
            ),
            Visibility(
              visible: orderNotes.isNotEmpty,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.locale.notes,
                      style: KPFontStyle.bodyMedium.copyWith(
                        color: KPColors.textGrayPrimary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        orderNotes,
                        textAlign: TextAlign.end,
                        style: KPFontStyle.bodyMedium.copyWith(
                          color: KPColors.textGrayPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Visibility(
              visible: serviceFee > 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      context.locale.service,
                      style: KPFontStyle.bodyMedium.copyWith(
                        color: KPColors.textGrayPrimary,
                      ),
                    ),
                    Text(
                      '+ \$$serviceFee',
                      style: KPFontStyle.bodyMedium.copyWith(
                        color: KPColors.textGrayPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            //分割线
            Container(
              width: double.maxFinite,
              height: 1,
              color: KPColors.borderGrayLightBase,
            ),
            Flexible(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                child: ListView.builder(
                    itemCount: priceMaps.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final data = priceMaps[index];
                      if (cartItemList.isEmpty && data['title'] != 'Total') {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${data['title']}',
                              style: _buildBottomStyle(index == priceMaps.length - 1),
                            ),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Visibility(
                                    visible: data['title'] == 'Discount',
                                    child: LayoutBuilder(builder: (ctx, _) {
                                      return Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        child: Tag.activity(
                                          text: context.locale.bundleOff,
                                          showArrow: true,
                                        ),
                                      ).onTap(() {
                                        KPCustomPopover.show(
                                          context: context,
                                          targetContext: ctx,
                                          contentKey: _bundleOffKey,
                                          contentWidget: Column(
                                            key: _bundleOffKey,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              _buildPopoverItem('整单折扣', 1.50,
                                                  extra: '20% off'),
                                              _buildPopoverItem('菜品折扣', 1.50),
                                              _buildPopoverItem('菜品活动', 1.50,
                                                  extra: '买一送一'),
                                            ],
                                          ),
                                        );
                                      });
                                    })),
                                Visibility(
                                    visible: data['title'] == 'Tax（5%）',
                                    child: LayoutBuilder(builder: (ctx, _) {
                                      return Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        child: Tag.activity(
                                          text: context.locale.bundleTax,
                                          showArrow: true,
                                        ),
                                      ).onTap(() {
                                        KPCustomPopover.show(
                                          context: context,
                                          targetContext: ctx,
                                          contentKey: _bundleTaxKey,
                                          contentWidget: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            key: _bundleTaxKey,
                                            children: [
                                              _buildPopoverItem('GST (8%)', 1.50,
                                                  isDiscount: false),
                                              _buildPopoverItem('Payroll Tax (17%)', 3.20,
                                                  isDiscount: false),
                                            ],
                                          ),
                                        );
                                      });
                                    })),
                                Text(
                                  '${data['price'] < 0 ? '-' : ''}S\$${(data['price'].abs())}',
                                  style: data['price'] < 0
                                      ? KPFontStyle.bodyMedium
                                          .copyWith(color: KPColors.textRedDefault)
                                      : _buildBottomStyle(index == priceMaps.length - 1),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
              ),
            ),
          ],
        );
      },
      error: (error, stackTrace) => const Center(child: Text('Error')),
      loading: () => const Center(child: KPLoading()),
    );
  }

  Widget _buildButtonRow(bool isTableOrdering) {
    // 使用 cartControllerProvider 来获取购物车数据状态
    final cartAsyncValue = ref.watch(cartControllerProvider);
    final dataList = cartAsyncValue.when(
      data: (data) => data,
      loading: () => <CartProductItem>[],
      error: (_, __) => <CartProductItem>[],
    );
    final orderDraftItems = ref.watch(orderDraftsProvider);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          //挂单和取单(直接点单才会显示)
          Visibility(
            visible: !isTableOrdering,
            child: GestureDetector(
              onTap: () {
                if (dataList.isEmpty) {
                  //取单
                  KPDialog.showCustomDialog(
                    context: context,
                    child: const CartPickUpOrder(),
                  );
                } else {
                  //挂单
                  ref.read(cartServiceProvider).holdOrder(context);
                }
              },
              child: Stack(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    // margin: const EdgeInsets.only(right: 12),
                    decoration: const BoxDecoration(
                      color: KPColors.fillGrayLightLighter,
                      borderRadius: BorderRadius.all(Radius.circular(KPRadius.l)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          dataList.isNotEmpty
                              ? Assets.images.iconSaveOrder.path
                              : Assets.images.iconPickUpOrder.path,
                          width: 24,
                          height: 24,
                        ),
                      ],
                    ),
                  ),
                  //已挂单数量提示
                  Positioned(
                    top: 2,
                    right: 2,
                    child: Visibility(
                      visible: dataList.isEmpty && orderDraftItems.isNotEmpty,
                      child: Container(
                        width: 16,
                        height: 16,
                        alignment: Alignment.center,
                        decoration: const BoxDecoration(
                          color: KPColors.fillRedNormal,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          orderDraftItems.length.toString(),
                          style: const TextStyle(
                            fontSize: 11,
                            color: KPColors.textGrayInverse,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Visibility(visible: !isTableOrdering, child: const SizedBox(width: 12)),
          //pay,桌台点餐的支付按钮（显示条件：桌台点餐未下单）
          Visibility(
            visible: isTableOrdering,
            child: Expanded(
              child: Container(
                height: 48,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: const Color(0xFFF3F3F4)),
                child: Text(
                  context.locale.payCaps,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayPrimary),
                ),
              ),
            ),
          ),
          //加菜按钮（显示条件：桌台点餐已下单）
          Visibility(
            visible: false,
            child: Expanded(
              child: Container(
                height: 48,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: const Color(0xFFF3F3F4)),
                child: Text(
                  context.locale.addDishes,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayPrimary),
                ),
              ),
            ),
          ),
          //cash 直接点餐才会显示的现金结账
          Visibility(
            visible: !isTableOrdering,
            child: Expanded(
              child: Container(
                height: 48,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: const Color(0xFFF3F3F4)),
                child: Text(
                  context.locale.cash,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayPrimary),
                ),
              ).onTap(() {
                _submitOrder();
              }),
            ),
          ),
          gapW12,
          //send 下单，桌台点餐的下单按钮（未下单时显示）
          Visibility(
            visible: isTableOrdering,
            child: Expanded(
              child: Container(
                height: 48,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: const Color(0xFF20232B)),
                child: Text(
                  context.locale.sendCaps,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayInverse),
                ),
              ).onTap(() {
                _submitOrder();
              }),
            ),
          ),
          //pay 右边的支付按钮（显示条件：直接点餐或者桌台点餐已下单）
          Visibility(
            visible: !isTableOrdering,
            child: Expanded(
              child: Container(
                height: 48,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.0),
                    color: const Color(0xFF20232B)),
                child: Text(
                  context.locale.payCaps,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textGrayInverse),
                ),
              ).onTap(() {
                _submitOrder();
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopoverItem(String title, double price,
      {String? extra, bool isDiscount = true}) {
    return Row(
      children: [
        Container(
          width: 6,
          height: 6,
          margin: const EdgeInsets.symmetric(horizontal: 12),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
          ),
        ),
        Text(
          "$title : ",
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: KPColors.textGrayInverse,
          ),
        ),
        Text(
          "${isDiscount ? '-' : ''}S\$${price.toStringAsFixed(2)}",
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: KPColors.textGrayInverse,
          ),
        ),
        if (extra != null)
          Text(
            " ($extra)",
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: KPColors.textGrayInverse,
            ),
          ),
      ],
    );
  }

  TextStyle _buildBottomStyle(bool isBold) {
    if (isBold) {
      return const TextStyle(
          fontSize: 20, fontWeight: FontWeight.w700, color: Color(0xFF323643));
    }
    return const TextStyle(fontSize: 14, color: Color(0xFF6F7686));
  }

  Widget _buildActivity() {
    return Container(
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: const BoxDecoration(
          gradient: LinearGradient(
        colors: [
          KPColors.fillRedLightest,
          KPColors.fillOrangeLightest,
        ],
      )),
      child: Row(
        children: [
          Assets.images.iconSaleDiscount.svg(
            width: 20,
            height: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Buy 1 more, can get 1 free',
              style: KPFontStyle.bodySmall.copyWith(
                color: KPColors.textGrayPrimary,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Add',
            style: KPFontStyle.headingXSmall.copyWith(
              color: KPColors.textBrandDefault,
            ),
          ),
        ],
      ),
    );
  }

  // ==💡💡💡 编辑购物车配置开始 ==

  /// 处理购物车商品点击事件
  /// 作用：点击购物车商品时，转换数据并打开商品编辑页面
  void _onProductItemTap(BuildContext context, CartProductItem item) async {
    // 创建回调函数
    clearPopupCallback() {
      if (mounted) {
        ref.read(activePopupItemIdProvider.notifier).state = null;
      }
    }

    KPSlidePopup.dismissFromTarget();

    try {
      debugPrint('🛒 === 购物车商品编辑流程开始 ===');
      debugPrint('🛒 商品ID: ${item.commodityId}');
      debugPrint('🛒 商品名称: ${item.commodityName}');
      debugPrint('🛒 商品类型: ${item.commodityType}');
      debugPrint('🛒 备注: ${item.remark}');

      // 设置当前打开的商品ID
      ref.read(activePopupItemIdProvider.notifier).state = item.orderItemId.toString();

      // 添加弹窗关闭回调
      KPSlidePopup.addDismissCallback(clearPopupCallback);

      // 显示加载状态
      // KPToast.show(
      //   content: '正在加载商品信息...',
      //   duration: const Duration(milliseconds: 1000),
      // );

      //先获取页面数据，再设置编辑状态
      // 1. 先构建ProductItem（不设置编辑状态）
      final productItem = ProductItem(
        commodityId: item.commodityId,
        commodityName: item.commodityName,
        commodityType: item.commodityType,
        price: item.price,
        commodityImagePath: '',
        categoryId: 0,
        priceChangeEnable: 0,
        standaloneSaleEnable: 0,
        discountEnable: 0,
        specType: item.specType ?? 0,
      );

      // 2. 设置编辑状态（在获取页面数据之前）
      ref.read(editingCartItemProvider.notifier).state = item;
      debugPrint('🛒 编辑状态设置完成');

      // 3. 获取页面数据（现在会自动清空默认值并用购物车数据替换）
      final productId = item.commodityId.toInt();
      final pageData = await ref.watch(
          productSpecPageDataProvider(productId, productItem, cartItem: item).future);
      debugPrint('🛒 页面数据获取完成（已自动处理默认值替换）');

      // 4. 立即进行状态回显
      if (mounted) {
        final initialTabIndex = item.commodityType == 3 ? 3 : 0;

        KPSlidePopup.showFromTarget(
          context: context,
          contentWidget: ProductSpecScreen(
            pageData: pageData,
            initialTabIndex: initialTabIndex,
            isEditMode: true, // 设置为编辑模式
          ),
          targetWidth: 360,
          onMaskTap: () {
            // 点击遮罩时清除activePopupItemId
            ref.read(activePopupItemIdProvider.notifier).state = null;
            KPSlidePopup.dismissFromTarget();
          },
        );

        debugPrint('🛒 ✅ 编辑页面已打开');
      }
    } catch (e) {
      debugPrint('打开商品编辑页面失败: $e');
      KPToast.show(content: '打开商品页面失败');
      // 清除编辑状态
      ref.read(editingCartItemProvider.notifier).state = null;
      // 清除activePopupItemId
      ref.read(activePopupItemIdProvider.notifier).state = null;
      // 移除回调避免内存泄漏
      KPSlidePopup.removeDismissCallback(clearPopupCallback);
    }
  }

  // ==💡💡💡 编辑购物车配置结束 ==

  /// 取消退菜操作
  void _onCancelReturn(CartProductItem item) {
    // 弹窗前先重置 provider 状态 (本期默认勾选，置灰不可点击取消勾选)
    // ref.read(cancelReturnConfirmProvider.notifier).state = false;
    KPDialog.showAlertDialog(
      context: context,
      title: context.locale.confirmCancelReturn,
      contentWidget: Consumer(
        builder: (context, ref, _) {
          // final checked = ref.watch(cancelReturnConfirmProvider);
          return Column(
            children: [
              Text(
                context.locale.confirmCancelReturnTip,
                textAlign: TextAlign.center,
                style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(left: 16, right: 16),
                    decoration: BoxDecoration(
                      color: KPColors.fillGrayDarkLightest,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.check,
                      size: 18,
                      color: Colors.white,
                    ),
                  ),
                  // Checkbox(
                  //   value: checked,
                  //   onChanged: (value) {
                  //     ref.read(cancelReturnConfirmProvider.notifier).state =
                  //         value ?? false;
                  //   },
                  // ),
                  Text(
                    context.locale.printCancellationSlip,
                    style:
                        KPFontStyle.bodyLarge.copyWith(color: KPColors.textGrayPrimary),
                  ),
                ],
              ),
            ],
          );
        },
      ),
      rightBtnBgColor: KPColors.fillGrayDarDarkest,
      rightAction: () async {
        // 这里执行取消退菜的逻辑
        await ref.read(cartServiceProvider).cancelReturnDish(item.orderItemId);
        if (mounted) {
          context.pop();
          KPToast.show(
            content: context.locale.returnedDishSuccess,
            isGreen: true,
            position: ToastPosition.bottom,
          );
        }
      },
    );
  }

  //下单
  void _submitOrder() {
    //todo下单逻辑
    if (ref.read(cartServiceProvider).cartItemList.isEmpty) {
      KPToast.show(content: context.locale.addProductFirst);
      return;
    }
    if (widget.table != null && widget.tableGroup != null) {
      //如果是桌台点单,并且是联台,弹出选择桌台下单弹窗
      KPDialog.showCustomDialog(
        context: context,
        child: GroupTableOrderSelector(
          tableList: widget.tableGroup!.tableList,
          curTableTitle: ref.read(selectedTableTitleStateProvider),
        ),
      );
    }
  }
}
