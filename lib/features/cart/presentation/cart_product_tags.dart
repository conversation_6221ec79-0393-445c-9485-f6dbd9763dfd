import 'package:flutter/material.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';

enum CartItemStatus {
  common(value: 0),
  //划菜
  dishesServed(value: 1),
  //催菜
  urgeDishes(value: 2),
  //打包
  packaging(value: 3),
  //等叫
  onHold(value: 4),
  //叫起
  requestService(value: 5),
  //退菜
  returnDish(value: 6);

  const CartItemStatus({
    required this.value,
  });

  static CartItemStatus getTypeByValue(int value) {
    return CartItemStatus.values.firstWhere(
          (e) => e.value == value,
      orElse: () => CartItemStatus.common,
    );
  }

  final int value;
}

enum CartProductTagType {
  ///催菜
  urgeDishes(
    bgColor: KPColors.fillRedLightest,
    borderColor: KPColors.fillRedLightest,
    textColor: KPColors.fillRedNormal,
  ),

  ///打包
  packaging(
    bgColor: KPColors.fillBlueLightest,
    borderColor: KPColors.fillBlueLightest,
    textColor: KPColors.fillBlueNormal,
  ),

  ///退菜
   returnDish(
    bgColor: KPColors.fillGrayLightDark,
    borderColor: KPColors.fillGrayLightDark,
    textColor: KPColors.textGrayPrimary,
  ),

  ///等叫
  onHold(
    bgColor: KPColors.fillBrandLightest,
    borderColor: KPColors.fillBrandLightest,
    textColor: KPColors.fillBrandNormal,
  ),

  ///叫起
  requestService(
    bgColor: KPColors.fillBrandLightest,
    borderColor: KPColors.fillBrandLightest,
    textColor: KPColors.fillBrandNormal,
  ),

  ///活动
  activity(
    bgColor: Colors.white,
    borderColor: KPColors.borderRedDefault,
    textColor: KPColors.textRedDefault,
  );

  const CartProductTagType({
    required this.bgColor,
    required this.borderColor,
    required this.textColor,
  });

  final Color bgColor;
  final Color borderColor;
  final Color textColor;
}

class Tag extends StatefulWidget {
  final String? text;
  final String? time;
  final bool? showArrow;
  final CartProductTagType type;

  const Tag({
    super.key,
    this.text,
    this.time,
    this.showArrow,
    required this.type,
  });

  factory Tag.urgeDishes({
    Key? key,
    String? time,
  }) {
    return Tag(
      key: key,
      time: time,
      type: CartProductTagType.urgeDishes,
    );
  }

  factory Tag.packaging({Key? key}) {
    return Tag(
      key: key,
      type: CartProductTagType.packaging,
    );
  }

  factory Tag.onHold({Key? key}) {
    return Tag(
      key: key,
      type: CartProductTagType.onHold,
    );
  }

  factory Tag.requestService({Key? key,String? time}) {
    return Tag(
      key: key,
      time: time,
      type: CartProductTagType.requestService,
    );
  }

  factory Tag.returnDish({Key? key}) {
    return Tag(
      key: key,
      type: CartProductTagType.returnDish,
    );
  }

  factory Tag.activity({
    Key? key,
    required String text,
    bool? showArrow,
  }) {
    return Tag(
      key: key,
      text: text,
      showArrow: showArrow,
      type: CartProductTagType.activity,
    );
  }

  @override
  State<Tag> createState() => _TagState();
}

class _TagState extends State<Tag> {
  String getTextByTagType(CartProductTagType type) {
    switch (type) {
      case CartProductTagType.urgeDishes:
        return KPLocale.of(context).urgeDishes;
      case CartProductTagType.packaging:
        return KPLocale.of(context).packaging;
      case CartProductTagType.returnDish:
        return KPLocale.of(context).returnDish;
      case CartProductTagType.onHold:
        return KPLocale.of(context).onHold;
      case CartProductTagType.requestService:
        return KPLocale.of(context).requestService;
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.type.bgColor,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: widget.type.borderColor,
          width: 0.5,
        ),
      ),
      padding: EdgeInsets.only(left: 6,right: (widget.showArrow??false)?4:6, top: 2,bottom: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            (widget.text ?? '') + getTextByTagType(widget.type) + (" ${(widget.time ?? '')}"),
            style: KPFontStyle.bodyXSmall.copyWith(
              color: widget.type.textColor,
            ),
          ),
          if (widget.showArrow ?? false)
            const Icon(
              Icons.keyboard_arrow_right,
              size: 14,
              color: KPColors.textRedDefault,
            ),
        ],
      ),
    );
  }
}

class TagGroup extends StatelessWidget {
  final List<Tag> tags;
  final double spacing;
  final double runSpacing;

  const TagGroup({
    super.key,
    required this.tags,
    this.spacing = 4.0,
    this.runSpacing = 4.0,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Wrap(
        spacing: spacing,
        runSpacing: runSpacing,
        children: tags.asMap().entries.map((entry) {
          return entry.value;
        }).toList(),
      ),
    );
  }
}
