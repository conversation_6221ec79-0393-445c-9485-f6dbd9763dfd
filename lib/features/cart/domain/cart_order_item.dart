import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';

part 'cart_order_item.freezed.dart';
part 'cart_order_item.g.dart';

@freezed
class CartOrderItem with _$CartOrderItem{
  const factory CartOrderItem({
    required int orderId,
    required int diningMethod,
    required int diningNumber,
    required String tableNumber,
    required String mealVoucher,
    required String remark,
    required int orderStatus,
    required double discount,
    required int discountType,
    String? discountReason,
    required List<CartProductItem> cartItemList,
  }) = _CartOrderItem;

  factory CartOrderItem.fromJson(Map<String, dynamic> json) =>
      _$CartOrderItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<CartOrderItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
