import 'package:freezed_annotation/freezed_annotation.dart';

part 'discount_item.freezed.dart';
part 'discount_item.g.dart';

@freezed
class DiscountItem with _$DiscountItem{
  const factory DiscountItem({
    required int type,
    required double discount,
  }) = _DiscountItem;

  factory DiscountItem.fromJson(Map<String, dynamic> json) =>
      _$DiscountItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<DiscountItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
