import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/product/domain/product_item.dart';

part 'addons_item.freezed.dart';
part 'addons_item.g.dart';

@freezed
class AddonsItem with _$AddonsItem {
  const factory AddonsItem({
    required int orderItemId,
    required int commodityId,
    required String commodityName,
    required int commodityType,
    String? commodityUnit,
    required double price,
    required num quantity,
  }) = _AddonsItem;

  factory AddonsItem.fromJson(Map<String, dynamic> json) =>
      _$AddonsItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<AddonsItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
