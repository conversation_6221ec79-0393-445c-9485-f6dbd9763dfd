import 'package:freezed_annotation/freezed_annotation.dart';

part 'order_draft_product_item.freezed.dart';
part 'order_draft_product_item.g.dart';

@freezed
class OrderDraftProductItem with _$OrderDraftProductItem {
  const factory OrderDraftProductItem({
    required String commodityName,
    required int quantity,
  }) = _OrderDraftProductItem;

  factory OrderDraftProductItem.fromJson(Map<String, dynamic> json) =>
      _$OrderDraftProductItemFromJson(json);
}