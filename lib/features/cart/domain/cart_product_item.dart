import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/cart/domain/addons_item.dart';
import 'package:kpos/features/cart/domain/practice_item.dart';

part 'cart_product_item.freezed.dart';
part 'cart_product_item.g.dart';

@freezed
class CartProductItem with _$CartProductItem {
  const factory CartProductItem({
    required int orderItemId,
    required int commodityId,
    required String commodityName,
    required int commodityType,
    String? commodityUnit,
    required double price,
    required int commoditySkuId,
    num? quantity,
    String? remark,
    List<int>? status, // 0:正常, 1:划菜, 2:催菜, 3:打包, 4:等叫, 5:叫起，6:退菜
    int? specType,
    List<PracticeItem>? practiceList,
    List<AddonsItem>? addonsList,
    List<CartProductItem>? productList, // 套餐的子商品
    int? isUrge, // 是否催菜,0:否, 1:是
    int? urgeTime, // 催菜时间
    int? isReturn, // 是否退菜,0:否, 1:是
    String? returnReason, // 退菜原因
    int? isServed, // 是否划菜,0:否, 1:是
  }) = _CartProductItem;

  factory CartProductItem.fromJson(Map<String, dynamic> json) =>
      _$CartProductItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<CartProductItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
