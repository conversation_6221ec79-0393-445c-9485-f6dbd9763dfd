import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/cart/domain/cart_product_item.dart';
import 'package:kpos/features/product/domain/product_item.dart';

part 'practice_item.freezed.dart';
part 'practice_item.g.dart';

@freezed
class PracticeItem with _$PracticeItem {
  const factory PracticeItem({
    required int orderItemPracticeId,
    required int commodityPracticeId,
    required String commodityPracticeName,
    String? commodityUnit,
    required double price,
  }) = _PracticeItem;

  factory PracticeItem.fromJson(Map<String, dynamic> json) =>
      _$PracticeItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<PracticeItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
