import 'package:freezed_annotation/freezed_annotation.dart';

part 'create_order_item.freezed.dart';
part 'create_order_item.g.dart';

@freezed
class CreateOrderItem with _$CreateOrderItem{
  const factory CreateOrderItem({
    required int orderId,
  }) = _CreateOrderItem;

  factory CreateOrderItem.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<CreateOrderItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
