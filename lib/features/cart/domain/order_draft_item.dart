import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/cart/domain/order_draft_product_item.dart';

part 'order_draft_item.freezed.dart';
part 'order_draft_item.g.dart';

@freezed
class OrderDraftItem with _$OrderDraftItem{
  const factory OrderDraftItem({
    required int orderId,
    required String cartCode,
    required double totalPrice,
    required int createTime,
    required List<OrderDraftProductItem> items,
  }) = _OrderDraftItem;

  factory OrderDraftItem.fromJson(Map<String, dynamic> json) =>
      _$OrderDraftItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<OrderDraftItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
