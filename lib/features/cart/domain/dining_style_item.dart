import 'package:freezed_annotation/freezed_annotation.dart';

part 'dining_style_item.freezed.dart';
part 'dining_style_item.g.dart';

@freezed
class DiningStyleItem with _$DiningStyleItem{
  const factory DiningStyleItem({
    required int saleType,
    required String saleTypeName,
  }) = _DiningStyleItem;

  factory DiningStyleItem.fromJson(Map<String, dynamic> json) =>
      _$DiningStyleItemFromJson(json);

  static List<Map<String, dynamic>> toJsonList(List<DiningStyleItem> items) {
    return items.map((item) => item.toJson()).toList();
  }
}
