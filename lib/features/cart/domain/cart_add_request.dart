import 'package:freezed_annotation/freezed_annotation.dart';

part 'cart_add_request.freezed.dart';
part 'cart_add_request.g.dart';

@freezed
class PracticeConfig with _$PracticeConfig {
  const factory PracticeConfig({
    required int commodityPracticeGroupId,
    required int practiceItemId,
  }) = _PracticeConfig;

  factory PracticeConfig.fromJson(Map<String, dynamic> json) =>
      _$PracticeConfigFromJson(json);
}

@freezed
class AddonsConfig with _$AddonsConfig {
  const factory AddonsConfig({
    required int commodityAddonsGroupId,
    required int addonsCommodityId,
    required num unitQuantity,
    int? addonsCommoditySkuId,
  }) = _AddonsConfig;

  factory AddonsConfig.fromJson(Map<String, dynamic> json) =>
      _$AddonsConfigFromJson(json);
}

@Freezed(toJson: true)
class ProductConfig with _$ProductConfig {
  const factory ProductConfig({
    required int commoditySkuId,
    required List<PracticeConfig> practiceConfigList,
    required List<AddonsConfig> addonsConfigList,
  }) = _ProductConfig;

  factory ProductConfig.fromJson(Map<String, dynamic> json) =>
      _$ProductConfigFromJson(json);
}

@Freezed(toJson: true)
class ComboConfig with _$ComboConfig {
  const factory ComboConfig({
    required int comboGroupingId,
    required int commodityId,
    required int commoditySkuId,
    required num unitQuantity,
    List<PracticeConfig>? practiceConfigList,
    List<AddonsConfig>? addonsConfigList,
    String? remark,
  }) = _ComboConfig;

  factory ComboConfig.fromJson(Map<String, dynamic> json) =>
      _$ComboConfigFromJson(json);
}

@Freezed(toJson: true)
class CartAddRequest with _$CartAddRequest {
  const factory CartAddRequest.singleProduct({
    required int orderId,
    required int productId,
    required num quantity,
    String? remark,
    int? discountType,
    double? discount,
    String? discountReason,
    double? updatePrice,
    required ProductConfig productConfig,
  }) = _CartAddSingleProductRequest;

  const factory CartAddRequest.combo({
    required int orderId,
    required int productId,
    required num quantity,
    String? remark,
    int? discountType,
    double? discount,
    String? discountReason,
    double? updatePrice,
    required List<ComboConfig> comboConfig,
  }) = _CartAddComboRequest;

  factory CartAddRequest.fromJson(Map<String, dynamic> json) => _$CartAddRequestFromJson(json);
} 