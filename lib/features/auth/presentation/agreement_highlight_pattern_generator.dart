import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class AgreementHighlightPatternGenerator {
  const AgreementHighlightPatternGenerator();

  List<EasyRichTextPattern> call(BuildContext context) {
    final locale = KPLocale.of(context);
    return [
      EasyRichTextPattern(
        targetString: locale.userAgreementHighlight,
        style: TextStyle(color: TDTheme.of(context).brandNormalColor),
        recognizer: TapGestureRecognizer()
          ..onTap = () => _userAgreementButtonAction(context),
      ),
      EasyRichTextPattern(
        targetString: locale.privacyPolicyHighlight,
        style: TextStyle(color: TDTheme.of(context).brandNormalColor),
        recognizer: TapGestureRecognizer()
          ..onTap = () => _privacyPolicyButtonAction(context),
      ),
    ];
  }

  void _userAgreementButtonAction(BuildContext context) {
    if (context.mounted) {
      UserAgreementRoute().push(context);
    }
  }

  void _privacyPolicyButtonAction(BuildContext context) {
    if (context.mounted) {
      UserAgreementRoute().push(context);
    }
  }
}
