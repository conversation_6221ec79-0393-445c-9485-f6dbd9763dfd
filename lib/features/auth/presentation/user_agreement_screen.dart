import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:webview_flutter/webview_flutter.dart';

class UserAgreementScreen extends StatefulWidget {
  const UserAgreementScreen({super.key});

  @override
  State<UserAgreementScreen> createState() => _UserAgreementScreenState();
}

class _UserAgreementScreenState extends State<UserAgreementScreen> {
  final _progressController = ValueNotifier(0);

  final _controller = WebViewController();

  @override
  void initState() {
    super.initState();
    _controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onProgress: (int progress) {
          print(progress);
          _progressController.value = progress;
        },
        onPageStarted: (url) => debugPrint('Loading $url'),
        onPageFinished: (url) => debugPrint('Loaded $url'),
        onWebResourceError: (error) =>
            debugPrint('Error: ${error.description}'),
      ))
      ..loadRequest(Uri.parse("https://pub.dev/"));
  }

  @override
  void dispose() {
    _controller.setNavigationDelegate(NavigationDelegate(
      onProgress: (_) {}, // 空回调
      onPageStarted: (_) {},
      onPageFinished: (_) {},
      onWebResourceError: (_) {},
    ));
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const TDText("用户协议"),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ValueListenableBuilder(valueListenable: _progressController, builder: (context,value,_) {
            return TDProgress(
                type: TDProgressType.linear,
                showLabel: false,
                strokeWidth: 10.0,
                value: value/100.0);
          }),
          Expanded(
              child: WebViewWidget(controller: _controller)),
        ],
      ),
    );
  }
}
