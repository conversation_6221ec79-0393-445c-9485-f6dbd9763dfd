import 'dart:async';

import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_service.dart';
import 'package:kpos/features/auth/domain/user_item.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/auth_repository.dart';

part 're_login_screen_controller.g.dart';


@riverpod
class ReLoginScreenController extends _$ReLoginScreenController {
  @override
  Future<List<UserItem>> build() async {
    final keyword = ref.watch(usersSearchKeywordProvider);
    return _fetch(keyword);
  }

  Future<List<UserItem>> _fetch(String keyword) async {
    final stomp = ref.watch(stompServiceProvider);
    if (!stomp.isConnected) {
      await stomp.connectionState.firstWhere((connected) => connected == true);
    }
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    final list = await ref.read(authRepositoryProvider).fetchUsers(storeId: boundStore.storeId,organizationId: boundStore.organizationId,keyword: keyword);
    list.add(UserItem(employeeId: 1, employeeName: "wzw", email: "<EMAIL>", roleName: "店长", roleId: 1, commissionStatus: 1, commissionId: 1, inviteStatus: 1, organizationId: 1));
    return list;
  }
}

@riverpod
class UsersSearchKeyword extends _$UsersSearchKeyword {
  @override
  String build() {
    return "";
  }

  void changeKeyword(String value) {
    state = value;
  }
}