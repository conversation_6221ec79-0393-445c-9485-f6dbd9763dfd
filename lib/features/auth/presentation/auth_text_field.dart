import 'package:flutter/material.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';

class AuthTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final bool obscure;
  final bool showSuffix;
  final FormFieldValidator<String>? validator;

  final TextInputType? textInputType;

  final String? tips;

  const AuthTextField(
      {super.key,
        required this.controller,
        required this.label,
        this.obscure = false,
        this.showSuffix = false,
        this.validator,
        this.textInputType,
        this.tips
      });

  @override
  State<AuthTextField> createState() => AuthTextFieldState();
}

class AuthTextFieldState extends State<AuthTextField> {
  late final FocusNode _focusNode;
  late final ValueNotifier<bool> _obscureNotifier;
  late final ValueNotifier<String?> _errorNotifier;
  late final ValueNotifier<bool> _hasFocusNotifier;

  bool validate() {
    _validate();
    return _errorNotifier.value == null;
  }

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _obscureNotifier = ValueNotifier(widget.obscure);
    _errorNotifier = ValueNotifier(null);
    _hasFocusNotifier = ValueNotifier(false);
    _addListeners();
  }

  void _addListeners() {
    _focusNode.addListener(() {
      _hasFocusNotifier.value = _focusNode.hasFocus;
      // if (!_focusNode.hasFocus) _validate();
    });
  }

  void _validate() {
    final error = widget.validator?.call(widget.controller.text);
    if (_errorNotifier.value != error) {
      _errorNotifier.value = error;
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _obscureNotifier.dispose();
    _errorNotifier.dispose();
    _hasFocusNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInputContainer(),
        _buildErrorText(),
      ],
    );
  }

  Widget _buildInputContainer() {
    return ListenableBuilder(
        listenable: Listenable.merge([_hasFocusNotifier, _errorNotifier]),
        builder: (context, _) {
          final hasError = _errorNotifier.value != null;
          final hasFocus = _hasFocusNotifier.value;
          return Container(
            height: 56,
            decoration: BoxDecoration(
              border: Border.all(
                color: hasError
                    ? context.theme.warningColor1
                    : (hasFocus
                    ? context.theme.brandNormalColor
                    : context.theme.grayColor6),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: _buildTextField(),
          );
        });
  }

  Widget _buildTextField() {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12),
      child: ValueListenableBuilder(
        valueListenable: _obscureNotifier,
        builder: (context,obscure,_) {
          return TextField(
            focusNode: _focusNode,
            cursorColor: context.theme.brandNormalColor,
            cursorHeight: 18,
            cursorWidth: 1.5,
            keyboardType: widget.textInputType ?? TextInputType.text,
            decoration: InputDecoration(
              label: RichText(
                text: TextSpan(
                  text: widget.label,
                  style: TextStyle(
                      color: context.theme.grayColor2, fontSize: 16),
                  children: [
                    TextSpan(
                      text: '*',
                      style: TextStyle(
                          color: context.theme.warningColor1, fontSize: 16),
                    ),
                  ],
                ),
              ),
              border: InputBorder.none,
              suffixIcon: widget.showSuffix
                  ? IconButton(
                iconSize: 20,
                icon: obscure ? Assets.images.iconEyeOff.svg() : Assets.images.iconEyeOn.svg(),
                onPressed: () {
                  _obscureNotifier.value =
                  !_obscureNotifier.value;
                },
              )
                  : null,
            ),
            controller: widget.controller,
            obscureText: obscure,
            onChanged: (value) => _validate(),
          );
        }
      ),
    );
  }

  Widget _buildErrorText() {
    return ValueListenableBuilder(
        valueListenable: _errorNotifier,
        builder: (context, error, _) {
          return error != null
              ? Padding(
                  padding: const EdgeInsets.only(top: 4, left: 12),
                  child: Text(
                    error,
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                )
              : const SizedBox.shrink();
        });
  }
}
