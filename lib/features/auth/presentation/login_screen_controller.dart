import 'package:kpos/features/auth/application/auth_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'login_screen_controller.g.dart';


@riverpod
class LoginScreenController extends _$LoginScreenController {
  @override
  FutureOr<void> build() async {}

  Future<void> login({required String account,required String password}) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => ref.read(authServiceProvider).login(account: account, password: password));
    if (state.hasError) {
      Error.throwWithStackTrace(state.error!, state.stackTrace ?? StackTrace.current);
    }
  }

}
