import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../routing/pages_route.dart';
import '../../language_settings/presentation/select_language_popup.dart';
import 'auth_app_bar.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';

class AuthCustomNavBar extends ConsumerWidget implements PreferredSizeWidget {
  const AuthCustomNavBar({super.key});

  static const menus = [MenuType.locale, MenuType.unbindStore];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    return AppBar(
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.white,
      leadingWidth: double.infinity,
      leading: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              padding: DeviceUtil.isMobile()
                  ? const EdgeInsets.only(left: 20)
                  : null,
              child: Row(
                children: [
                  Assets.images.iconTest1.image(width: 40, height: 40),
                  const SizedBox(
                    width: 12,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          boundStore.brandName,
                          style: const TextStyle(
                              color: Colors.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w700),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          boundStore.storeName,
                          style:
                          TextStyle(color: context.theme.grayColor2),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
          LayoutBuilder(builder: (context, constraints) {
            return GestureDetector(
                onTap: () {
                  KPPopover.showPopover(
                    context: context,
                    width: 240,
                    height: 150,
                    contentWidget: _buildPopoverList(context, ref),
                    placement: TDPopoverPlacement.bottomRight,
                  );
                },
                child: Container(
                  margin: DeviceUtil.isMobile()
                      ? const EdgeInsets.only(right: 20)
                      : null,
                  width: 32,
                  height: 32,
                  alignment: Alignment.center,
                  child: Assets.images.iconMenu.svg(
                    width: 18,
                    height: 18,
                  ),
                ));
          }),
        ],
      ),
    );
  }

  Widget _buildPopoverList(BuildContext context, WidgetRef ref) {
    final locale = ref.read(currentLocaleProvider);
    final deviceInfo = ref.read(deviceServiceProvider).currentDeviceInfo;
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final totalHeight = constraints.maxHeight - 46;
          const separatorHeight = 1.0;
          final itemCount = menus.length;
          final totalSeparatorsHeight = separatorHeight * (itemCount - 1);
          final itemHeight = (totalHeight - totalSeparatorsHeight) / itemCount;
          return SingleChildScrollView(
            child: Column(
              children: [
                _buildPosContainer(context, deviceInfo),
                ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemCount: itemCount,
                    separatorBuilder: (context, index) => const Divider(
                          height: 0.5,
                          indent: 20,
                          color: KPColors.borderGrayLightBase,
                        ),
                    itemBuilder: (context, index) {
                      final item = menus[index];
                      return LayoutBuilder(builder: (context, constrains) {
                        return GestureDetector(
                          onTap: () {
                            _onMenuItemTap(context, item, ref);
                          },
                          child: Container(
                            color: Colors.white,
                            height: itemHeight,
                            alignment: Alignment.centerLeft,
                            padding: const EdgeInsets.only(left: 20),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                item == MenuType.locale
                                    ? Assets.images.buttonLanguage.svg()
                                    : Assets.images.iconUnbindStore.svg(),
                                const SizedBox(width: 8),
                                Text(
                                  item == MenuType.locale
                                      ? (locale?.localeName(context.locale) ??
                                          "")
                                      : context.locale.unbindStore,
                                  style: const TextStyle(fontSize: 16),
                                )
                              ],
                            ),
                          ),
                        );
                      });
                    })
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPosContainer(BuildContext context, CurrentDeviceInfo deviceInfo) {
    return Container(
      height: 46,
      padding: const EdgeInsets.only(left: 20, right: 20, top: 16, bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Assets.images.iconDeviceDefault.svg(width: 15, height: 15),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              "${deviceInfo.deviceName}-${deviceInfo.physicalDeviceIdToString}",
              style: TextStyle(
                  color: context.theme.grayColor2,
                  fontSize: 14,
                  fontWeight: FontWeight.w400),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 4),
          if (!DeviceUtil.isMobile())
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: const Color(0xfffff7e6)),
              child: Text(
                deviceInfo.type == PosType.main
                    ? context.locale.mainPos
                    : context.locale.subPos,
                style: TextStyle(
                    fontSize: 10, color: context.theme.brandNormalColor),
              ),
            )
        ],
      ),
    );
  }

  void _onMenuItemTap(BuildContext context, MenuType type, WidgetRef ref) {
    final ctx = context;
    if (type == MenuType.unbindStore) {
      Navigator.pop(context);
      final posType = ref.read(deviceServiceProvider).currentDeviceInfo.type;
      if (posType == PosType.main || DeviceUtil.isMobile()) {
        UnbindStoreConfirmRoute().push(context);
      } else {
        UnbindStoreRoute().push(context);
      }
    } else if (type == MenuType.locale) {
      if (DeviceUtil.isMobile()) {
        KPSlidePopup.showSlidePopup(
            context: context,
            contentWidget: SelectLanguagePopup(onLocaleSelected: (local) {
              if (!ctx.mounted) return;
              Navigator.pop(ctx);
            }));
      } else {
        KPPopover.showPopover(
            showArrow: true,
            arrowSize: 0,
            offset: -4,
            context: ctx,
            width: 240,
            height: 156,
            contentWidget: const SelectLanguagePopup(isSecondPop: true),
            placement: TDPopoverPlacement.leftTop,
            onComplete: (value) {
              if (!ctx.mounted) return;
              Navigator.pop(ctx);
            });
      }
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);
}
