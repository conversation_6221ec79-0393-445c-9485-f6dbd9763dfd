import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';

class FullLockScreen extends ConsumerStatefulWidget {
  const FullLockScreen({super.key});

  @override
  ConsumerState createState() => _FullLockScreenState();
}

class _FullLockScreenState extends ConsumerState<FullLockScreen> {

  String _enteredPassword = '';
  final int _passwordLength = 6;
  bool _showError = false;

  final List<String> _numbers = ['1', '2', '3','4', '5', '6','7', '8', '9','', '0', 'delete'];

  void _onKeyPressed(String value) {
    if (value == 'delete') {
      if (_enteredPassword.isNotEmpty) {
        setState(() => _enteredPassword = _enteredPassword.substring(0, _enteredPassword.length - 1));
      }
    } else if (_enteredPassword.length < _passwordLength) {
      setState(() => _enteredPassword += value);
      _checkPassword();
    }
  }

  Future<void> _checkPassword() async {
    if (_enteredPassword.length == _passwordLength) {
      final isValid = await _validatePassword(_enteredPassword);
      if (!isValid) {
        setState(() => _showError = true);
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() => _showError = false);
        _enteredPassword = '';
      } else {
        if (mounted) {
          Navigator.pop(context);
        }
      }
    }
  }

  Future<bool> _validatePassword(String password) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return password == '666666';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.brandColor2,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildHeader(context),
          _buildPasswordDots(),
          _buildNumberPad(),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: context.theme.brandNormalColor,
              borderRadius: BorderRadius.circular(28)),
          child: Assets.images.iconLock.svg(width: 24,height: 24),
        ),
        const SizedBox(height: 40),
        Text(
          _showError ? context.locale.passwordError : context.locale.inputPinLogin,
          style: TextStyle(
              color: _showError ? Colors.red : Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w700
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordDots() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(top: 20,bottom: 40),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_passwordLength, (index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index < _enteredPassword.length ? Colors.white : Colors.transparent,
              border: index < _enteredPassword.length
                  ? null
                  : Border.all(
                color: Colors.white,
                width: 1.0,
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildNumberPad() {
    const crossAxisCount = 3;
    const itemWidth = 80.0;
    const itemHeight = 80.0;
    const columnGap = 16.0;
    const rowGap = 16.0;
    return LayoutGrid(
      columnSizes: List.generate(
          crossAxisCount, (_) => const FixedTrackSize(itemWidth)),
      rowSizes: List.generate(
        (_numbers.length / crossAxisCount).ceil(),
            (_) => const FixedTrackSize(itemHeight),
      ),
      columnGap: columnGap,
      rowGap: rowGap,
      children: List.generate(_numbers.length, (index) {
        return Container(
          alignment: Alignment.center,
          child: _buildNumberItemView(_numbers[index]),
        );
      }),
    );;
  }

  Widget _buildNumberItemView(String value) {
    return value.isEmpty ? const SizedBox() : GestureDetector(
      onTap: () => _onKeyPressed(value),
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: value != 'delete' ? const Color(0xFF323843) : null,
        ),
        child: Center(
          child: value == 'delete'
              ? Assets.images.iconDelete.svg(width: 24,height: 24)
              : Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}