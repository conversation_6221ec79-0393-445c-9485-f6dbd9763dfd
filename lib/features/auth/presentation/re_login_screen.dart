import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/domain/user_item.dart';
import 'package:kpos/features/auth/presentation/auth_custom_nav_bar.dart';
import 'package:kpos/features/auth/presentation/re_login_screen_controller.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/responsive_two_part_layout.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/utils/device_util.dart';

class ReLoginScreen extends ConsumerStatefulWidget {
  const ReLoginScreen({super.key});

  @override
  ConsumerState createState() => _ReLoginScreenState();
}

class _ReLoginScreenState extends ConsumerState<ReLoginScreen> {

  late PosType posType;

  @override
  void initState() {
    posType = ref.read(deviceServiceProvider).currentDeviceInfo.type;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: DeviceUtil.isMobile() ? const AuthCustomNavBar() : null,
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        body: ResponsiveTwoPartLayout(
            startFlex: DeviceUtil.isMobile() ? 0 : 1,
            startContent: !DeviceUtil.isMobile()
                ? SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: Assets.images.iconBanner.image(
                        fit: BoxFit.cover, alignment: Alignment.centerLeft),
                  )
                : Container(),
            endContent: KPAsyncValueWidget(
                asyncValueProvider: reLoginScreenControllerProvider,
                dataBuilder: (ct, ref, data) {
                  return _buildEndContent(data);
                }),
            spacing: 0));
  }

  Widget _buildEndContent(List<UserItem> items) {
    const columnGap = 0.0;
    const rowGap = 20.0;
    final showSearchButton = items.length > 6;
    final displayItems = items.take(6).toList();
    var rows = 0;
    var  columns = 0;
    if (DeviceUtil.isMobile()) {
      columns = 2;
      if (displayItems.length < 3) {
        rows = 1;
      } else if (displayItems.length < 5) {
        rows = 2;
      } else {
        rows = 3;
      }
      if (displayItems.length == 1) {
        columns = 1;
      }
    } else {
      if (displayItems.length <= 2) {
        columns = displayItems.length;
      } else {
        columns = 3;
      }
      if (displayItems.length < 4) {
        rows = 1;
      } else {
        rows = 2;
      }
    }
    final gridViewH = rows * 130.0;
    final rowSizes = List.generate(rows, (_) => 1.fr);
    final columnSizes = List.generate(columns, (_) => 1.fr);
    return Stack(
      children: [
        if (!DeviceUtil.isMobile())
        const Positioned(
            left: 24,
            right: 30,
            top: 0,
            child: AuthCustomNavBar()),
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
                height: gridViewH,
                child: LayoutBuilder(builder: (context, constraints) {
                  return LayoutGrid(
                    columnSizes: columnSizes,
                    rowSizes: rowSizes,
                    columnGap: columnGap,
                    rowGap: rowGap,
                    children: List.generate(displayItems.length, (index) {
                      return _buildItemView(displayItems[index]);
                    }),
                  );
                }),
              ),
              if (showSearchButton)
              GestureDetector(
                onTap: () {
                  ReSearchUsersRoute().push(context);
                },
                child: Container(
                  margin: const EdgeInsets.only(top: 48),
                  padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 8),
                  decoration: BoxDecoration(
                        border:
                            Border.all(color: context.theme.grayColor6, width: 1),
                        borderRadius: BorderRadius.circular(100)),
                    height: 36,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Assets.images.iconSearch.svg(width: 16,height: 16),
                      const SizedBox(width: 4),
                      Text(context.locale.search,style: const TextStyle(fontSize: 14)),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        Positioned(
            left: 0,
            right: 0,
            bottom: 20,
            child: SafeArea(
                child:Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.iconHelpCircle
                        .svg(width: 18, height: 18),
                    const SizedBox(width: 4),
                    Text(context.locale.helpCenter,style: TextStyle(
                        fontSize: 16, color: context.theme.grayColor2))
                  ],
                )),
        )
      ],
    );
  }

  Widget _buildItemView(UserItem item) {
    return GestureDetector(
      onTap: () {
        GoRouter.of(context).push(
          '/reLoginPassword',
          extra: item,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        alignment: Alignment.center,
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: context.theme.brandNormalColor,
                borderRadius: BorderRadius.circular(24.0),
              ),
              child: Text(
                item.employeeName.substring(0,2),
                style: const TextStyle(
                    fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 8),
            Text(item.employeeName,maxLines: 1,overflow: TextOverflow.ellipsis),
            const SizedBox(height: 2),
            Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: context.theme.grayColor1),
              child:Text(item.roleName,style: const TextStyle(fontSize: 10),),
            )
          ],
        ),
      ),
    );
  }
}
