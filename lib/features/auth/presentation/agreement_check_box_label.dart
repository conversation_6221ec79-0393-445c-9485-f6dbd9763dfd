import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/features/auth/presentation/agreement_highlight_pattern_generator.dart';

class AgreementCheckBoxLabel extends StatelessWidget {
  const AgreementCheckBoxLabel({super.key, required this.controller});

  final ValueNotifier<bool> controller;

  final highlightPatternGenerator = const AgreementHighlightPatternGenerator();

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () {
            controller.value = !controller.value;
          },
          child: ValueListenableBuilder(
              valueListenable: controller,
              builder: (ct, isAgree, _) {
                return SizedBox.square(
                  dimension: 24,
                  child: Checkbox(
                      value: isAgree,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      onChanged: (value) {
                        controller.value = value!;
                      }),
                );
              }),
        ),
        const SizedBox(width: 4),
        Flexible(child: EasyRichText(
            KPLocale.of(context).loginAgreement(KPLocale.of(context).userAgreementHighlight,KPLocale.of(context).privacyPolicyHighlight),
          defaultStyle: TextStyle(fontSize: 14),
          patternList: [
            EasyRichTextPattern(
                targetString: KPLocale.of(context).loginAgreementAgreeTarget,
              style: TextStyle(fontSize: 14),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  controller.value = !controller.value;
                }
            ),
            ...highlightPatternGenerator.call(context)
          ],
        ))
      ],
    );
  }
}
