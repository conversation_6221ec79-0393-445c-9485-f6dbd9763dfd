import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/features/language_settings/presentation/select_language_popup.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../language_settings/domain/locale_config.dart';

class AuthMobileAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String? titleText;
  final Function? onLocaleSelected;
  const AuthMobileAppBar({super.key, this.titleText,this.onLocaleSelected});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const locales = LocaleConfig.supportedLocales;
    final currentLanguageController =
        ValueNotifier(ref.read(currentLocaleProvider));
    return AppBar(
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
      ),
      leading: Icon<PERSON>utton(
        icon: Assets.images.iconBackBlack.svg(width: 24,height: 24),
        onPressed: () => context.pop(),
      ),
      backgroundColor: Colors.white,
      centerTitle: true,
      title: Text(
        titleText ?? "",
        style: const TextStyle(fontSize: 16),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20),
          child: GestureDetector(
            onTap: () {
              KPSlidePopup.showSlidePopup(
                  context: context,
                  contentWidget: SelectLanguagePopup(
                    onLocaleSelected: (local) {
                      if (onLocaleSelected != null) {
                        onLocaleSelected!();
                      }
                    },
                  ));
            },
            child: Container(
              width: 32,
              height: 32,
              alignment: Alignment.center,
              child: Assets.images.buttonLanguage.svg(
                width: 18,
                height: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}
