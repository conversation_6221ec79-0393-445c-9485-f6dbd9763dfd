import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/presentation/auth_custom_nav_bar.dart';
import 'package:kpos/features/auth/presentation/re_login_screen_controller.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/responsive_two_part_layout.dart';
import '../../../common/utils/device_util.dart';

class ReSearchUsersScreen extends ConsumerStatefulWidget {

  const ReSearchUsersScreen({super.key});

  @override
  ConsumerState createState() => _ReSearchUsersScreenState();
}

class _ReSearchUsersScreenState extends ConsumerState<ReSearchUsersScreen> {

  final TextEditingController _searchTextController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    _searchFocusNode.requestFocus();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: DeviceUtil.isMobile() ? const AuthCustomNavBar() : null,
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        body: ResponsiveTwoPartLayout(
            startFlex: DeviceUtil.isMobile() ? 0 : 1,
            startContent: !DeviceUtil.isMobile()
                ? SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: Assets.images.iconBanner.image(
                        fit: BoxFit.cover, alignment: Alignment.centerLeft),
                  )
                : Container(),
            endContent: _buildEndContent(),
            spacing: 0));
  }

  Widget _buildEndContent() {
    return Column(
      children: [
        if (!DeviceUtil.isMobile())
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: AuthCustomNavBar(),
        ),
        DeviceUtil.isMobile() ? const SizedBox(height: 40) : const SizedBox(height: 80),
        Center(
          child: Container(
            width: !DeviceUtil.isMobile() ? 368 : DeviceUtil.deviceWidth-40,
            alignment: Alignment.center,
            child: Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: 40,
                    child: TextField(
                      controller: _searchTextController,
                      focusNode: _searchFocusNode,
                      cursorColor: context.theme.brandNormalColor,
                      textInputAction: TextInputAction.search,
                      onSubmitted: (value) {
                        if (value.isNotEmpty) {
                          ref.read(usersSearchKeywordProvider.notifier).changeKeyword(value);
                        }
                      },
                      decoration: InputDecoration(
                        prefixIcon: IconButton(
                          iconSize: 20,
                          icon: Assets.images.iconSearchLine.svg(),
                          onPressed: () {},
                        ),
                        hintText: context.locale.searchEmployeeName,
                        hintStyle: TextStyle(color: context.theme.grayColor5),
                        fillColor: context.theme.grayColor1,
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        enabledBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: context.theme.grayColor1, width: 1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: context.theme.brandNormalColor, width: 2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: () {
                    _searchTextController.clear();
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                        color: context.theme.grayColor7,
                        borderRadius: BorderRadius.circular(6.0)),
                    child: Center(
                        child: Assets.images.iconClose.svg(width: 20,height: 20)),
                  ),
                )
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        Expanded(
          child: SizedBox(
            width: !DeviceUtil.isMobile() ? 368 : DeviceUtil.deviceWidth-40,
            child: KPAsyncValueWidget(
              asyncValueProvider: reLoginScreenControllerProvider,
                dataBuilder: (ct,ref,data) {
              return ListView.separated(
                  padding: EdgeInsets.zero,
                  itemCount: data.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 0.5,
                    indent: 44,
                    color: context.theme.grayColor3,
                  ),
                  itemBuilder: (context, index) {
                    final item = data[index];
                    return GestureDetector(
                      onTap: () {
                        GoRouter.of(context).push(
                          '/reLoginPassword',
                          extra: item,
                        );
                      },
                      child: Container(
                        color: Colors.white,
                        height: 64,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 36,
                                  height: 36,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: context.theme.brandNormalColor,
                                    borderRadius: BorderRadius.circular(24.0),
                                  ),
                                  child: Text(
                                  item.employeeName.substring(0,2),
                                    style: const TextStyle(
                                        fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(item.employeeName)
                              ],
                            ),
                            Container(
                              padding:
                              const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: context.theme.grayColor1),
                              child: Text(
                                item.roleName,
                                style: const TextStyle(fontSize: 10),
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }
              );
            }),
          ),
        )
      ],
    );
  }
}
