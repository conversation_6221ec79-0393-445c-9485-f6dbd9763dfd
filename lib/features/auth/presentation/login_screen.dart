import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/form_fields/kp_form_builder_text_field.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_toast.dart';
import 'package:kpos/common/components/responsive_two_part_layout.dart';
import 'package:kpos/common/constant/app_sizes.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/string_extension.dart';
import 'package:kpos/common/services/language_settings_service/language_settings_service.dart';
import 'package:kpos/features/auth/data/auth_repository.dart';
import 'package:kpos/features/auth/presentation/auth_mobile_app_bar.dart';
import 'package:kpos/features/auth/presentation/login_screen_controller.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';
import 'package:kpos/features/language_settings/presentation/select_language_popup.dart';
import 'package:kpos/features/product/data/product_repository.dart';
import 'package:kpos/features/product/presentation/product_controller.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:kpos/common/utils/device_util.dart';

import '../../../common/components/button/button_circular_indicator.dart';
import '../../../common/services/database/app_database.dart';
import '../../../routing/pages_route.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _accountFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  late final ValueNotifier<Locale?> _currentLanguageController;
  final ValueNotifier<bool> _obscureNotifier = ValueNotifier(true);
  static const double contentW = 380;
  static const double contentH = 440;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    final locale = ref.read(currentLocaleProvider);
    _currentLanguageController = ValueNotifier(locale);
    _accountFocusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    _accountController.dispose();
    _passwordController.dispose();
    _currentLanguageController.dispose();
    _obscureNotifier.dispose();
    _accountFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final statusBarHeight = MediaQuery.of(context).padding.top;
    return Scaffold(
        appBar: DeviceUtil.isMobile()
            ? AuthMobileAppBar(
                onLocaleSelected: () {
                  if (_formKey.currentState != null) {
                    _formKey.currentState!.validate();
                  }
                },
              )
            : null,
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        body: ResponsiveTwoPartLayout(
            startFlex: DeviceUtil.isMobile() ? 0 : 1,
            startContent: !DeviceUtil.isMobile()
                ? SizedBox(
                    height: MediaQuery.of(context).size.height,
                    child: Assets.images.iconBanner.image(
                        fit: BoxFit.cover, alignment: Alignment.centerLeft),
                  )
                : Container(),
            endContent: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: SingleChildScrollView(
                keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
                child: Column(
                  children: [
                    DeviceUtil.isMobile()
                        ? const SizedBox(height: 40)
                        : SizedBox(
                            height:
                                (MediaQuery.of(context).size.height - contentH) *
                                    1 /
                                    3.0,
                            child: Stack(
                              children: [
                                Positioned(
                                    right: 24,
                                    top: DeviceUtil.isDesktop()
                                        ? 16
                                        : statusBarHeight,
                                    child: LayoutBuilder(
                                        builder: (context, constraints) {
                                      return ValueListenableBuilder(
                                          valueListenable:
                                              _currentLanguageController,
                                          builder: (ct, locale, _) {
                                            return InkWell(
                                              borderRadius: BorderRadius.circular(
                                                  context.theme.radiusDefault),
                                              onTap: () {
                                                KPPopover.showPopover(
                                                  context: context,
                                                  width: 240,
                                                  height: 156,
                                                  contentWidget:
                                                      SelectLanguagePopup(
                                                    onLocaleSelected:
                                                        (Locale locale) {
                                                      _currentLanguageController
                                                          .value = locale;
                                                    },
                                                  ),
                                                  placement: TDPopoverPlacement
                                                      .bottomRight,
                                                );
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16,
                                                        vertical: 10),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.end,
                                                  children: [
                                                    Assets.images.buttonLanguage
                                                        .svg(
                                                            width: 20,
                                                            height: 20),
                                                    gapW8,
                                                    Text(
                                                        locale?.localeName(
                                                                context.locale) ??
                                                            "_",
                                                        style: const TextStyle(
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.w700))
                                                  ],
                                                ),
                                              ),
                                            );
                                          });
                                    })),
                              ],
                            ),
                          ),
                    SizedBox(
                      width: DeviceUtil.isMobile()
                          ? DeviceUtil.deviceWidth - 40
                          : contentW,
                      height: contentH,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              context.locale.login,
                              style: const TextStyle(
                                  fontSize: 32, fontWeight: FontWeight.w700),
                            ),
                          ),
                          const SizedBox(height: 40),
                          KPFormBuilderTextField(
                            key: _formKey,
                            name: "account",
                            focusNode: _accountFocusNode,
                            controller: _accountController,
                            label: RichText(
                              text: TextSpan(
                                text: context.locale.email,
                                style: TextStyle(
                                    color: context.theme.grayColor2,
                                    fontSize: 16),
                                children: [
                                  TextSpan(
                                    text: '*',
                                    style: TextStyle(
                                        color: context.theme.warningColor1,
                                        fontSize: 16),
                                  ),
                                ],
                              ),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            autovalidateMode: AutovalidateMode.onUnfocus,
                            validator: (value) {
                              return (value?.isCorrectEmail ?? true)
                                  ? null
                                  : context.locale.accountInvalidate;
                            },
                          ),
                          const SizedBox(height: 24),
                          ValueListenableBuilder(
                              valueListenable: _obscureNotifier,
                              builder: (context, obscure, _) {
                                return KPFormBuilderTextField(
                                  name: "password",
                                  focusNode: _passwordFocusNode,
                                  controller: _passwordController,
                                  autoValidate: true,
                                  label: RichText(
                                    text: TextSpan(
                                      text: context.locale.password,
                                      style: TextStyle(
                                          color: context.theme.grayColor2,
                                          fontSize: 16),
                                      children: [
                                        TextSpan(
                                          text: '*',
                                          style: TextStyle(
                                              color: context.theme.warningColor1,
                                              fontSize: 16),
                                        ),
                                      ],
                                    ),
                                  ),
                                  keyboardType: TextInputType.text,
                                  suffixIcon: IconButton(
                                    iconSize: 18,
                                    icon: obscure
                                        ? Assets.images.iconEyeOff.svg()
                                        : Assets.images.iconEyeOn.svg(),
                                    onPressed: () {
                                      _obscureNotifier.value =
                                          !_obscureNotifier.value;
                                    },
                                  ),
                                  obscureText: obscure,
                                );
                              }),
                          const SizedBox(height: 24),
                          KPLoadingButton(
                              width: double.maxFinite,
                              onPressed: _onLoginAction,
                              text: context.locale.login,
                              isLoading: ref.watch(loginScreenControllerProvider).isLoading),
                          const SizedBox(height: 24),
                          Container(
                            alignment: Alignment.center,
                            child: Text(context.locale.forgetPassword,style: TextStyle(
                                color: context.theme.grayColor2, fontSize: 16),),
                          ),
                          // AgreementCheckBoxLabel(
                          //     controller: agreementCheckBoxController)
                        ],
                      ),
                    ),
                    Container(
                      height: (MediaQuery.of(context).size.height - contentH) *
                          2 /
                          3.0,
                      padding: const EdgeInsets.only(bottom: 20),
                      alignment: Alignment.bottomCenter,
                      child: SafeArea(
                          child:Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Assets.images.iconHelpCircle
                                  .svg(width: 18, height: 18),
                              const SizedBox(width: 4),
                              Text(context.locale.helpCenter,style: TextStyle(
                                  fontSize: 16, color: context.theme.grayColor2))
                            ],
                          )),
                    ),
                  ],
                ),
              ),
            ),
            spacing: 0));
  }

  _onLoginAction() async {
    final ctx = context;
    FocusScope.of(ctx).unfocus();
    final account = _accountController.text;
    if (!account.isCorrectEmail) {
      return;
    }
    if (ref.read(loginScreenControllerProvider).isLoading) {
      return;
    }
    ref
        .read(loginScreenControllerProvider.notifier)
        .login(
            account: _accountController.text,
            password: _passwordController.text)
        .then((value) {
      if (!ctx.mounted) return;
      SelectStoreRoute().go(ctx);
    }).onError((error, stack) {
      String errorMessage = error.toString();
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.replaceAll('Exception: ', '');
      }
      if (!ctx.mounted) return;
      KPToast.show(content: errorMessage);
    });
  }
}
