import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/domain/user_item.dart';
import 'package:kpos/features/auth/presentation/full_lock_screen.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:kpos/routing/route_names.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/button/button_circular_indicator.dart';
import '../../../common/components/form_fields/kp_form_builder_text_field.dart';
import '../../../common/components/kp_toast.dart';
import '../../../common/components/responsive_two_part_layout.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/utils/device_util.dart';
import 'auth_custom_nav_bar.dart';
import 'auth_text_field.dart';
import 'login_screen_controller.dart';

class ReLoginPasswordScreen extends ConsumerStatefulWidget {

  final UserItem user;

  const ReLoginPasswordScreen(this.user, {super.key});

  @override
  ConsumerState createState() => _ReLoginPasswordScreenState();
}

class _ReLoginPasswordScreenState extends ConsumerState<ReLoginPasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _passwordFocusNode = FocusNode();
  final ValueNotifier<bool> _obscureNotifier = ValueNotifier(true);
  final GlobalKey<AuthTextFieldState> _passwordKey = GlobalKey<AuthTextFieldState>();
  @override
  void initState() {
    _passwordFocusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    _obscureNotifier.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: DeviceUtil.isMobile() ? const AuthCustomNavBar() : null,
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false,
        body: ResponsiveTwoPartLayout(
            startFlex: DeviceUtil.isMobile() ? 0 : 1,
            startContent: !DeviceUtil.isMobile()
                ? SizedBox(
              height: MediaQuery.of(context).size.height,
              child: Assets.images.iconBanner.image(
                  fit: BoxFit.cover, alignment: Alignment.centerLeft),
            )
                : Container(),
            endContent: _buildEndContent(),
            spacing: 0));
  }

  Widget _buildEndContent() {
    return SingleChildScrollView(
      child: Column(children: [
        if (!DeviceUtil.isMobile())
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: AuthCustomNavBar(),
        ),
        DeviceUtil.isMobile() ? const SizedBox(height: 96) : const SizedBox(height: 60),
        // Assets.images.iconTest3.image(width: 48,height: 48),
        Container(
          width: 48,
          height: 48,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: context.theme.brandNormalColor,
            borderRadius: BorderRadius.circular(24.0),
          ),
          child: Text(
            widget.user.employeeName.substring(0,2),
            style: const TextStyle(
                fontSize: 16, fontWeight: FontWeight.w700, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        Text(widget.user.employeeName,
            style: const TextStyle(fontWeight: FontWeight.w700),
            maxLines: 1,
            overflow: TextOverflow.ellipsis),
        const SizedBox(height: 8),
        Container(
          padding:
          const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: context.theme.grayColor1),
          child: Text(
            widget.user.roleName,
            style: const TextStyle(fontSize: 10),
          ),
        ),
        const SizedBox(height: 24),
        SizedBox(
          width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
          child: ValueListenableBuilder(
              valueListenable: _obscureNotifier,
              builder: (context, obscure, _) {
                return KPFormBuilderTextField(
                  name: "password",
                  focusNode: _passwordFocusNode,
                  controller: _passwordController,
                  autoValidate: true,
                  label: RichText(
                    text: TextSpan(
                      text: context.locale.password,
                      style: TextStyle(
                          color: context.theme.grayColor2,
                          fontSize: 16),
                      children: [
                        TextSpan(
                          text: '*',
                          style: TextStyle(
                              color: context.theme.warningColor1,
                              fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                  keyboardType: TextInputType.text,
                  suffixIcon: IconButton(
                    iconSize: 18,
                    icon: obscure
                        ? Assets.images.iconEyeOff.svg()
                        : Assets.images.iconEyeOn.svg(),
                    onPressed: () {
                      _obscureNotifier.value =
                      !_obscureNotifier.value;
                    },
                  ),
                  obscureText: obscure,
                );
              }),
        ),
        const SizedBox(height: 24),
        _buildLoginButton(context, ref),
        const SizedBox(height: 16),
        GestureDetector(
          onTap:_onChangeUserAction,
          child: Container(
            height: 24,
            alignment: Alignment.center,
            child: Text(context.locale.changeUser,
                style: TextStyle(
                    color: context.theme.grayColor2, fontSize: 16)),
          ),
        ),
      ]),
    );
  }

  Widget _buildLoginButton(BuildContext context, WidgetRef ref) {
    return TDButton(
        height: 48,
        width: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth - 40 : 440,
        onTap: _onLoginAction,
        style: TDButtonStyle(backgroundColor: Colors.black),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Consumer(builder: (ct, ref, child) {
              return ref.watch(loginScreenControllerProvider).isLoading
                  ? const Padding(
                padding: EdgeInsets.only(right: 9),
                child: ButtonCircularIndicator(),
              )
                  : const SizedBox();
            }),
            Center(
              child: Text(
                context.locale.login,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w700),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ));
  }

  void _onChangeUserAction() async {
    final ctx = context;
    FocusScope.of(ctx).unfocus();
    await Future.delayed(const Duration(milliseconds: 100));
    if (!ctx.mounted) return;
    ReLoginRoute().push(ctx);
  }

  _onLoginAction() {
    FocusScope.of(context).unfocus();
    _executeLogin(context);
  }

  _executeLogin(BuildContext context) async {
    if (ref.read(loginScreenControllerProvider).isLoading) {
      return;
    }

    ref
        .read(loginScreenControllerProvider.notifier)
        .login(
        account: widget.user.email,
        password: _passwordController.text)
        .then((value) {
      if (!context.mounted) return;
      final posType = ref.read(deviceServiceProvider).currentDeviceInfo.type;
      if (posType != PosType.unknown) {
        ConnectDeviceRoute(type: posType).go(context);
      } else {
        SelectStoreRoute().go(context);
      }
    }).onError((error, stack) {
      String errorMessage = error.toString();
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.replaceAll('Exception: ', '');
      }
      if (!context.mounted) return;
      KPToast.show(content: errorMessage);
    });
  }
}
