import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/dialog/kp_dialog.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../common/utils/device_util.dart';
import '../../../routing/pages_route.dart';
import '../../language_settings/presentation/select_language_popup.dart';

enum MenuType { locale, unbindStore, loginOut }

class AuthAppBar extends ConsumerWidget implements PreferredSizeWidget {
  static const menus = [MenuType.locale, MenuType.loginOut];

  final String? titleText;

  final bool? showLeading;

  const AuthAppBar({super.key, this.titleText, this.showLeading = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      automaticallyImplyLeading: false,
      backgroundColor: Colors.white,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
      ),
      leading: showLeading == false
          ? null
          : IconButton(
              icon: Assets.images.iconBackBlack.svg(width: 24, height: 24),
              onPressed: () => context.pop(),
            ),
      centerTitle: true,
      title: Text(
        titleText ?? "",
        style: const TextStyle(fontSize: 16),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 30),
          child: LayoutBuilder(builder: (context, constraints) {
            return GestureDetector(
                onTap: () {
                  KPPopover.showPopover(
                    context: context,
                    width: 240,
                    height: 170,
                    contentWidget: _buildPopoverList(context, ref),
                    placement: TDPopoverPlacement.bottomRight,
                  );
                },
                child: Container(
                  width: 32,
                  height: 32,
                  alignment: Alignment.center,
                  child: Assets.images.iconMenu.svg(
                    width: 18,
                    height: 18,
                  ),
                ));
          }),
        ),
      ],
    );
  }

  Widget _buildPopoverList(BuildContext context, WidgetRef ref) {
    final locale = ref.read(currentLocaleProvider);
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final totalHeight = constraints.maxHeight - 66;
          const separatorHeight = 1.0;
          final itemCount = menus.length;
          final totalSeparatorsHeight = separatorHeight * (itemCount - 1);
          final itemHeight = (totalHeight - totalSeparatorsHeight) / itemCount;
          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(left: 20, right: 4),
                  height: 66,
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: context.theme.brandNormalColor,
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        child: const Text(
                          "WZ",
                          style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text("<EMAIL>",
                                overflow: TextOverflow.ellipsis, // 设置超出部分显示省略号
                                maxLines: 1),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                      context.theme.radiusSmall),
                                  color: const Color(0XFFFFF7E6)),
                              child: Text("Manager",
                                  style: TextStyle(
                                    color: context.theme.brandNormalColor,
                                    fontSize: 10
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    itemCount: itemCount,
                    separatorBuilder: (context, index) => const Divider(
                          height: 0.5,
                          indent: 20,
                          color: KPColors.borderGrayLightBase,
                        ),
                    itemBuilder: (context, index) {
                      final item = menus[index];
                      return LayoutBuilder(builder: (context, constraints) {
                        return GestureDetector(
                          onTap: () => _onMenuItemTap(context, item, ref),
                          child: Container(
                            color: Colors.white,
                            height: itemHeight,
                            alignment: Alignment.centerLeft,
                            padding: const EdgeInsets.only(left: 20),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                item == MenuType.locale
                                    ? Assets.images.buttonLanguage.svg()
                                    : Assets.images.iconLoginOut.svg(),
                                const SizedBox(width: 8),
                                Text(
                                  item == MenuType.locale
                                      ? (locale?.localeName(context.locale) ??
                                          "")
                                      : context.locale.loginOut,
                                  style: const TextStyle(fontSize: 16),
                                )
                              ],
                            ),
                          ),
                        );
                      });
                    })
              ],
            ),
          );
        },
      ),
    );
  }

  void _onMenuItemTap(BuildContext context, MenuType type, WidgetRef ref) {
    final ctx = context;
    if (type == MenuType.loginOut) {
      KPDialog.showExitConfirmDialog(
          context: context,
          title: context.locale.loginOut,
          confirmBtnText: context.locale.confirm,
          cancelBtnText: context.locale.cancel,
          confirmAction: () {
            // 用户点击确认按钮后的操作
            LaunchRoute().go(context);
          },
          cancelAction: () {
            // 用户点击取消按钮后的操作
          });
    } else if (type == MenuType.locale) {
      if (DeviceUtil.isMobile()) {
        KPSlidePopup.showSlidePopup(
            context: context,
            contentWidget: SelectLanguagePopup(onLocaleSelected: (local) {
              if (!ctx.mounted) return;
              Navigator.pop(ctx);
            }));
      } else {
        KPPopover.showPopover(
            showArrow: true,
            arrowSize: 0,
            offset: -4,
            context: ctx,
            width: 240,
            height: 156,
            contentWidget: const SelectLanguagePopup(isSecondPop: true),
            placement: TDPopoverPlacement.leftTop,
            onComplete: (value) {
              if (!ctx.mounted) return;
              Navigator.pop(ctx);
            });
      }
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);
}
