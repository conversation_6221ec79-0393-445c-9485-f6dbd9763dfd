
import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_destinations.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_request_service.dart';
import 'package:kpos/features/auth/domain/user_item.dart';
import 'package:kpos/features/product/domain/product_category_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/remote_service/base_response.dart';
import '../../../common/services/web_socket/stomp/stomp_request_exception.dart';

part 'auth_repository.g.dart';

class AuthRepository {
  final StompRequestService _stompRequestService;

  AuthRepository({required StompRequestService stompRequestService})
      : _stompRequestService = stompRequestService;

  Future<BaseResponse<Map<String, dynamic>>> login(
      {required String account, required String password}) async {
    try {
      return await _stompRequestService.request<BaseResponse<Map<String, dynamic>>>(
        sendDestination: StompDestinations.login,
        subscribeDestination: StompDestinations.loginSub,
        body:{"email":account,"password":password},
        response: (json) => BaseResponse<Map<String, dynamic>>.fromJson(
          json,
              (data) => data as Map<String, dynamic>,
        ),
      );
    } on StompRequestException catch (e) {
      return BaseResponse(
        code: e.code,
        message: e.message,
        data: {"data": null},
      );
    }
  }

  Future<List<UserItem>> fetchUsers({
    required int storeId,
    required int organizationId,
    String keyword = "",
  }) async {
    try {
      Map<String,dynamic> params = {"storeId": storeId,"organizationId":organizationId};
      if (keyword.isNotEmpty) {
        params.addAll({"email":keyword});
      }
      return await _stompRequestService.requestListData(
          sendDestination: StompDestinations.getUsers,
          subscribeDestination: StompDestinations.getUsersSub,
          body: params,
          itemParser: (json) =>
              UserItem.fromJson(json as Map<String, dynamic>),isPaged: true);
    } on StompRequestException catch (e) {
      return [];
    }
  }

  Future<BaseResponse<Map<String, dynamic>>> logout() async {
    try {
      return await _stompRequestService.request<BaseResponse<Map<String, dynamic>>>(
        sendDestination: StompDestinations.logout,
        subscribeDestination: StompDestinations.logoutSub,
        body:{"userId":1},
        response: (json) => BaseResponse<Map<String, dynamic>>.fromJson(
          json,
              (data) => data as Map<String, dynamic>,
        ),
      );
    } on StompRequestException catch (e) {
      return BaseResponse(
        code: e.code,
        message: e.message,
        data: {"data": null},
      );
    }
  }

}

@riverpod
AuthRepository authRepository(Ref ref) {
  return AuthRepository(stompRequestService: ref.watch(stompRequestServiceProvider));
}