import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_item.freezed.dart';
part 'user_item.g.dart';


@freezed
class UserItem with _$UserItem {
  const factory UserItem({
    required double employeeId,
    required String employeeName,
    required String email,
    required String roleName,
    required int roleId,
    required int commissionStatus,
    required int commissionId,
    required int inviteStatus,
    required double organizationId,
    String? areaCode, // 可空
    String? phone, // 可空
    int? gender, // 可空
    String? address, // 可空
    String? pinCode, // 可空
    String? headPortraitPath, // 可空
  }) = _UserItem;

  factory UserItem.fromJson(Map<String, dynamic> json) =>
      _$UserItemFromJson(json);
}
