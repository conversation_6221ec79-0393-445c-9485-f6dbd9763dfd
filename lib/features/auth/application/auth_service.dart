import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/features/auth/data/auth_repository.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/domain/bound_store_info.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_service.g.dart';

class AuthService {
  final Ref _ref;

  AuthService({required Ref ref}) : _ref = ref;

  Future<void> _syncAuthToStorageService(String? account,int? tenantId) async {
    final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
    await Future.wait([
      keyValueStorage.setAccount(account),
      keyValueStorage.setTenantId(tenantId),
    ]);
  }

  Future<void> clearAuthToStorageService() async {
    final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
    _ref.read(deviceServiceProvider).currentDeviceInfo = CurrentDeviceInfo();
    _ref.read(storeServiceProvider).boundStoreInfo = BoundStoreInfo();
    await Future.wait([
      keyValueStorage.setAccount(""),
      keyValueStorage.setTenantId(0),
      keyValueStorage.setBoundStore(""),
      keyValueStorage.setDeviceInfo(""),
    ]);
  }

  Future<void> login({required String account, required String password}) async {
    try {
      final response = await _ref.read(authRepositoryProvider).login(account: account, password: password);
      if (response.code == StompStatusCode.success) {
        print(response.data);
        await _syncAuthToStorageService( account,response.data["tenantId"]);
      } else {
        throw Exception(response.message);
      }
    } catch (e, stackTrace) {
      throw Exception(e);
    }
  }

  Future<void> logout() async {
    try {
      final response = await _ref.read(authRepositoryProvider).logout();
      if (response.code == StompStatusCode.success) {
      } else {
        throw Exception(response.message);
      }
    } catch (e, stackTrace) {
      throw Exception(e);
    }
  }
}

@riverpod
AuthService authService(Ref ref) {
  return AuthService(ref: ref);
}