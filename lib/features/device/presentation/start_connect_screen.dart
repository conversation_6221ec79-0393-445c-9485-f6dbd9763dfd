import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/responsive_center.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/presentation/auth_app_bar.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/kp_toast.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/services/broadcast/broadcast_service.dart';
import '../../../common/services/networking/intranet_service/api_intranet_service.dart';
import '../../../common/services/web_socket/web_socket_service.dart';
import '../../../common/utils/device_util.dart';
import '../../../routing/main_route.dart';
import '../../../routing/pages_route.dart';
import '../../store/presentation/store_bind_device_controller.dart';

class StartConnectScreen extends ConsumerStatefulWidget {
  final PosType type;
  final int mainDeviceId;
  const StartConnectScreen(this.type, this.mainDeviceId, {super.key});
  @override
  ConsumerState createState() => _StartConnectScreenState();
}

class _StartConnectScreenState extends ConsumerState<StartConnectScreen> {

  late List<String> datas;

  @override
  Widget build(BuildContext context) {
    final storeItem = ref.read(storeServiceProvider).boundStoreInfo;
    final mainTips = [
      context.locale.connectAsMainDeviceTip1,
      context.locale.connectAsMainDeviceTip2,
      context.locale.connectAsMainDeviceTip3,
    ];

    final subTips = [
      context.locale.connectAsSubDeviceTip1,
      context.locale.connectAsSubDeviceTip2,
    ];
    if (widget.type == PosType.main) {
      datas = mainTips;
    } else {
      datas = subTips;
    }
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AuthAppBar(titleText: "Store: ${storeItem.storeName}",),
      body: ResponsiveCenter(
        child: LayoutBuilder(builder: (context, constraints) {
          final double maxWidth = constraints.maxWidth;
          double widthFactor = maxWidth < DeviceUtil.tablet ? 1 : 0.33;
          return Container(
            padding: const EdgeInsets.only(top: 60),
            alignment: Alignment.topCenter,
            width: maxWidth * widthFactor,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Column(
                      children: [
                        widget.type == PosType.main ? Assets.images.iconMainPos.image(width: 56,height: 56):Assets.images.iconSubPos.image(width: 56,height: 56),
                        const SizedBox(height: 12),
                        Text(
                            widget.type == PosType.main
                                ? context.locale.connectAsMainDevice
                                : context.locale.connectAsSubDevice,
                            style: const TextStyle(
                                fontSize: 24, fontWeight: FontWeight.w700)),
                      ],
                    ),
                  ),
                  const SizedBox(height: 48),
                  Text(context.locale.pleaseMakeSure,
                      style: TextStyle(
                          fontSize: 16, color: context.theme.grayColor2)),
                  const SizedBox(height: 20),
                  ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return _buildItemView(index + 1, datas[index]);
                      },
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 20),
                      itemCount: datas.length),
                  const SizedBox(height: 48),
                  KPLoadingButton(
                      height: 48,
                      onPressed: _jump,
                      text: context.locale.startConnecting,
                      isLoading: ref
                          .watch(storeBindDeviceControllerProvider)
                          .isLoading),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildItemView(int index, String item) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: context.theme.brandNormalColor, // 圆形背景颜色
            shape: BoxShape.circle, // 设置为圆形
          ),
          child: Text(
            '$index',
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
            child: Text(
          item,
          style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
          softWrap: true,
        ))
      ],
    );
  }

  Future<void> _jump() async {
    final ctx = context;
    ref
        .read(storeBindDeviceControllerProvider.notifier)
        .bindDevice(
        deviceId: widget.type == PosType.sub ? widget.mainDeviceId : 0,
        type: widget.type)
        .then((value) {
      if (!ctx.mounted) return;
      ConnectDeviceRoute(type: widget.type).push(ctx);
    }).onError((error, stack) {
      String errorMessage = error.toString();
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.replaceAll('Exception: ', '');
      }
      if (!ctx.mounted) return;
      KPToast.show(content: errorMessage);
    });
  }
}
