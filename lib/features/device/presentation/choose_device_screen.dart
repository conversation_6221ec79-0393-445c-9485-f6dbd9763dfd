import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/presentation/auth_app_bar.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/device/domain/device_item.dart';
import 'package:kpos/features/device/presentation/device_controller.dart';
import 'package:kpos/features/store/domain/bound_store_info.dart';
import 'package:kpos/features/store/domain/store_item.dart';
import 'package:kpos/features/store/presentation/store_bind_device_controller.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/kp_refresh_button.dart';
import '../../../common/components/kp_toast.dart';
import '../../../common/constant/pos_type.dart';
import '../../store/application/store_service.dart';
class ChooseDeviceScreen extends ConsumerStatefulWidget {
  const ChooseDeviceScreen({super.key});

  @override
  ConsumerState createState() => _ChooseDeviceScreenState();
}

class _ChooseDeviceScreenState extends ConsumerState<ChooseDeviceScreen> {
  final ValueNotifier<PosType> chooseTypeNotifier = ValueNotifier(PosType.sub);

  final GlobalKey<KPRefreshButtonState> refreshIconKey = GlobalKey<KPRefreshButtonState>();

  late final KPRefreshButton refreshButton;

  @override
  void initState() {
    refreshButton = KPRefreshButton(controllerKey: refreshIconKey);
    super.initState();
  }
  
  @override
  void dispose() {
    super.dispose();
    chooseTypeNotifier.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final List<ChoosePosItem> data = [
      ChoosePosItem(
          type: PosType.sub,
          title: context.locale.chooseSubDeviceTitle,
          desc: context.locale.chooseSubDeviceDesc,
        tip: context.locale.chooseSubDeviceTip
      ),
      ChoosePosItem(
        type: PosType.main,
        title: context.locale.chooseMainDeviceTitle,
        desc: context.locale.chooseMainDeviceDesc,
        tip:
        context.locale.chooseMainDeviceTip,
      ),
    ];
    final storeItem = ref.read(storeServiceProvider).boundStoreInfo;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AuthAppBar(titleText: "Store: ${storeItem.storeName}",),
      body: ResponsiveCenterContainer(
          child: SingleChildScrollView(
        child: KPAsyncValueWidget(
            asyncValueProvider: mainDeviceInfoControllerProvider,
            dataBuilder: (context,ref,deviceItem) {
            return Column(
              children: [
                _buildTopView(),
                const SizedBox(height: 24),
                (deviceItem != null && deviceItem.masterSlaveFlag == 1) ? _buildDeviceInfoView(deviceItem) : const SizedBox(),
                ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: data.length,
                    itemBuilder: (context, index) {
                      return _buildDeviceItem(data[index],deviceItem);
                    }),
                ValueListenableBuilder(
                  valueListenable: chooseTypeNotifier,
                  builder: (context,type,_) {
                    var isDisabled = false;
                    if (type == PosType.sub && deviceItem?.deviceStatus != 1) {
                      isDisabled = true;
                    }
                    return KPLoadingButton(
                          height: 48,
                          isDisabled: isDisabled,
                          disabledColor: isDisabled ? KPColors.fillGrayLightBase:Colors.black,
                          textColor: isDisabled ? KPColors.textGrayQuaternary:Colors.white,
                          onPressed: () => _onBindDevice(deviceItem != null ? deviceItem.deviceId : 0),
                          text: context.locale.confirm,
                          isLoading: ref.watch(storeBindDeviceControllerProvider).isLoading);
                  }
                ),
                ],
            );
          }
        ),
      )),
    );
  }

  Widget _buildDeviceItem(ChoosePosItem item,DeviceItem? deviceItem) {
    return ValueListenableBuilder(
        valueListenable: chooseTypeNotifier,
        builder: (context, chooseType, _) {
          return GestureDetector(
            onTap: () {
              chooseTypeNotifier.value = item.type;
            },
            child: Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: context.theme.grayColor1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        chooseType == item.type
                            ? Assets.images.iconRadioSelected.svg()
                            : Assets.images.iconRadioDefault.svg(),
                        const SizedBox(
                          width: 8,
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(item.title,
                                  style: const TextStyle(
                                      fontSize: 16, fontWeight: FontWeight.w700)),
                              const SizedBox(
                                height: 4,
                              ),
                              Text(
                                item.desc,
                                style: TextStyle(
                                    fontSize: 14,
                                    color: context.theme.grayColor5),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                             chooseType != item.type ? const SizedBox() : _buildTipView(chooseType,item.tip!,deviceItem)
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  item.type == PosType.main
                      ? Assets.images.iconMainPos.image(width: 60, height: 60)
                      : Assets.images.iconSubPos.image(width: 60, height: 60)
                ],
              ),
            ),
          );
        });
  }

  Widget _buildTipView(PosType chooseType,String tip,DeviceItem? deviceItem) {
    if (chooseType == PosType.sub && deviceItem != null && deviceItem.deviceStatus != 1) {
      return Text(
        tip,
        style: TextStyle(
            fontSize: 14,
            color: context.theme.warningColor1),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      );
    }
    if (chooseType == PosType.main && deviceItem != null) {
      return Text(
        tip,
        style: TextStyle(
            fontSize: 14,
            color: context.theme.brandNormalColor),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      );
    }
    return const SizedBox();
  }

  Widget _buildTopView() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(context.locale.chooseBindDeviceTitle,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
        ],
      ),
    );
  }

  Widget _buildDeviceInfoView(DeviceItem deviceInfo) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 13),
      margin: const EdgeInsets.only(bottom: 24),
      constraints: const BoxConstraints(minHeight: 48),
      decoration: BoxDecoration(
        color: deviceInfo.deviceStatus == 1 ? context.theme.grayColor1:KPColors.fillRedLightest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text("${context.locale.existingMainDevice} ${deviceInfo.deviceName}-${deviceInfo.physicalDeviceIdToString}",
                style: TextStyle(color: context.theme.grayColor4),
              softWrap: true,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: deviceInfo.deviceStatus == 1
                      ? const Color(0x1A22A06B)
                      : const Color(0x1AE2483D),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                    deviceInfo.deviceStatus == 1
                        ? context.locale.online
                        : context.locale.offline,
                    style: TextStyle(
                        color: deviceInfo.deviceStatus == 1
                            ? const Color(0xFF22A06B)
                            : KPColors.textRedDefault,
                        fontSize: 12)),
              ),
              const SizedBox(width: 12),
              deviceInfo.deviceStatus != 1
                  ? GestureDetector(
                      onTap: () {
                        print(**********);
                        refreshIconKey.currentState?.start();
                        Future.delayed(const Duration(milliseconds: 1500), () {
                          ref.invalidate(mainDeviceInfoControllerProvider);
                          refreshIconKey.currentState?.stop();
                        });
                      },
                      child: refreshButton)
                  : const SizedBox()
            ],
          ),
        ],
      ),
    );
  }

  void _onBindDevice(int deviceId) {
    StartConnectRoute(type: chooseTypeNotifier.value,deviceId)
        .push(context);
  }
}

class ChoosePosItem {
  PosType type;
  String title;
  String desc;
  String? tip;

  ChoosePosItem({
    required this.type,
    required this.title,
    required this.desc,
    this.tip,
  });
}