import 'package:kpos/features/product/data/product_repository.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/data/store_repository.dart';
import 'package:kpos/features/table/data/table_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'connect_cloud_controller.g.dart';

class InitializeState {
  final int step;
  InitializeState(this.step);
}

@riverpod
class InitializeData extends _$InitializeData {
  @override
  InitializeState build() {
    // 初始状态
    return InitializeState(1);
  }

  Future<void> initializeAll() async {

    await ref.read(productRepositoryProvider).initializeProducts();
    await ref.read(productRepositoryProvider).initializeProductCategory();
    await ref.read(productRepositoryProvider).initializeComboInfoList();
    state = InitializeState(1);

    await ref.read(productRepositoryProvider).initializeProductPractice();
    await ref.read(productRepositoryProvider).initializeProductSpec();
    await ref.read(storeRepositoryProvider).initializeDiningStyleList();
    await ref.read(storeRepositoryProvider).initializeMerchantStoreInfo();
    state = InitializeState(2);

    await ref.read(productRepositoryProvider).initializeProductAddons();
    await ref.read(storeRepositoryProvider).initializeStorePreparedReasonList();
    await ref.read(tableRepositoryProvider).initializeStoreTablesList();
    await ref.read(storeRepositoryProvider).initializeMerchantEmployee();
    await ref.read(storeServiceProvider).init();
    await ref.read(storeRepositoryProvider).initializeTaxAndFeeList();
    await ref.read(storeRepositoryProvider).initializeTaxRelation();
    await ref.read(storeRepositoryProvider).initializeProductTaxRelation();
    state = InitializeState(3);
  }
}

@riverpod
class ConnectCloudController extends _$ConnectCloudController {
  @override
  AsyncValue<Map<String, String>> build() {
    final initState = ref.watch(initializeDataProvider);
    if (initState.step >= 3) {
      return const AsyncData({"deviceName": "cloud"});
    } else {
      return const AsyncLoading();
    }
  }
}



