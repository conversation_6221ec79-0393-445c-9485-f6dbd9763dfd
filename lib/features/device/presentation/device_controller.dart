import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/device_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'device_controller.g.dart';

@riverpod
class MainDeviceInfoController extends _$MainDeviceInfoController {
  @override
  FutureOr<DeviceItem?> build() async {
    return _fetch();
  }

  Future<DeviceItem?> _fetch() async {
    return await ref.read(deviceServiceProvider).fetchMainDeviceInfo();
  }
}
