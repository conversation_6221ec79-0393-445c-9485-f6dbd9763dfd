import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/responsive_center_container.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/utils/device_util.dart';
import '../../auth/presentation/auth_app_bar.dart';
import '../../store/application/store_service.dart';

class BindMainDeviceConfirmScreen extends ConsumerStatefulWidget {
  const BindMainDeviceConfirmScreen({super.key});

  @override
  ConsumerState createState() => _BindMainDeviceConfirmScreenState();
}

class _BindMainDeviceConfirmScreenState extends ConsumerState<BindMainDeviceConfirmScreen> {

  final mainTips = [
    "The new device will take over the functions of the original cash register device",
    "Please make sure that the original device is online and confirm that the original data has been uploaded to the cloud, otherwise, data loss may occur.",
  ];

  @override
  Widget build(BuildContext context) {
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AuthAppBar(titleText: 'Store:${boundStore.storeName}'),
        body: ResponsiveCenterContainer(
          padding: const EdgeInsets.only(left: 20,right: 20,top: 60),
          contentWidth: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth : 516,
          child:  SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    children: [
                      Assets.images.iconMainPos.image(width: 56,height: 56),
                      const Text("Connect the new device",style: TextStyle(fontSize: 24,fontWeight: FontWeight.w700)),
                    ],
                  ),
                ),
                const SizedBox(height: 48),
                Text("Please make sure：",
                    style: TextStyle(
                        fontSize: 16, color: context.theme.grayColor2)),
                const SizedBox(height: 20),
                ListView.builder(
                    itemCount: mainTips.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context,index) {
                      return _buildItemView(index+1, mainTips[index]);
                    }),
                const SizedBox(height: 48),
                Container(
                  margin: const EdgeInsets.only(left: 20,right: 20),
                  child: TDButton(
                    width: double.infinity,
                    height: 48,
                    style: TDButtonStyle(backgroundColor: Colors.black),
                    textStyle: TextStyle(color: Colors.white),
                    text: context.locale.continueBind,
                    onTap: () {
                      const ConnectDeviceRoute(type: PosType.main).push(context);
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildItemView(int index, String item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: context.theme.brandNormalColor,
              shape: BoxShape.circle,
            ),
            child: Text(
              '$index',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
              child: Text(
                item,
                style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                softWrap: true,
              ))
        ],
      ),
    );
  }
}
