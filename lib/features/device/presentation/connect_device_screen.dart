import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/common/constant/intranet.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/string_extension.dart';
import 'package:kpos/common/services/broadcast/discovery_service.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_service.dart';
import 'package:kpos/features/device/presentation/connect_cloud_controller.dart';
import 'package:kpos/features/device/presentation/device_controller.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/dialog/kp_input_dialog.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/services/broadcast/broadcast_service.dart';
import '../../../common/services/networking/intranet_service/api_intranet_service.dart';
import '../../../common/services/web_socket/web_socket_service.dart';
import '../../../common/utils/device_util.dart';
import '../../../routing/main_route.dart';
import '../../auth/presentation/auth_app_bar.dart';
import '../application/device_service.dart';
import '../domain/device_item.dart';
import 'choose_device_screen.dart';

class ConnectDeviceScreen extends ConsumerStatefulWidget {
  const ConnectDeviceScreen(this.type, {super.key});

  final PosType type;

  @override
  ConsumerState createState() => _ConnectDeviceScreenState();
}

class _ConnectDeviceScreenState extends ConsumerState<ConnectDeviceScreen> with WidgetsBindingObserver {
  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isKeyboardVisible = false; // 跟踪键盘状态
  final mainDeviceNotifier = ValueNotifier<DeviceItem?>(null);

  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
    WidgetsBinding.instance.addObserver(this); // 添加观察者
    Future.microtask(() {
      ref.read(initializeDataProvider.notifier).initializeAll();
    });
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = WidgetsBinding.instance.window.viewInsets.bottom;
    final newIsKeyboardVisible = bottomInset > 0.0;
    
    // 只有当键盘状态发生变化时才更新
    if (newIsKeyboardVisible != _isKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = newIsKeyboardVisible;
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // 移除观察者
    _focusNode.dispose();
    _textEditingController.dispose();
    mainDeviceNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<DeviceItem?>>(
      mainDeviceInfoControllerProvider,
          (prev, next) {
        next.whenOrNull(data: (device) {
          mainDeviceNotifier.value = device;
        });
      },
    );
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AuthAppBar(titleText: 'Store:${boundStore.storeName}',showLeading: false,),
      body: ResponsiveCenterContainer(
          padding: const EdgeInsets.only(top: 60),
          contentWidth: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth : 516,
          child: KPAsyncValueWidget(
            skipLoadingOnRefresh: false,
            asyncValueProvider: widget.type == PosType.main ? connectCloudControllerProvider : discoveryServiceProvider,
            loadingBuilder: (ct, ref) {
              return _buildLoadingView();
            },
            errorBuilder: (context, ref, error, st) {
              return _buildErrorView(ref);
            },
            dataBuilder: (ct, ref, data) {
              _jump();
              return _buildDataView(data["deviceName"] ?? "",widget.type);
            },
          )),
    );
  }

  /// 构建加载视图UI
  /// 使用Stack布局将"手动输入IP地址"按钮固定在距离屏幕底部80像素的位置
  /// 同时保持内容可滚动，避免键盘弹出时的UI溢出问题
  Widget _buildLoadingView() {
    final stepData = ref.watch(initializeDataProvider);
    final mainTips = [context.locale.fetchingStoreInfo,context.locale.fetchingStoreData,context.locale.storeDataFetched];
    return Stack(
      // StackFit.expand使Stack填充可用空间
      fit: StackFit.expand,
      children: [
        // 使用SingleChildScrollView使内容可滚动
        SingleChildScrollView(
          child: Column(
            // 内容顶部对齐
            mainAxisAlignment: MainAxisAlignment.start,
            // 设置为min以避免强制扩展整个屏幕高度
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // 标题文本 - "连接中..."
                  Text(context.locale.connectingDevice,
                      style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
                  // 根据设备类型显示不同的描述文本
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Text(
                        widget.type == PosType.main? context.locale.connectingCloudDesc:
                        context.locale.connectingMainDesc,
                        style: TextStyle(fontSize: 16, color: context.theme.grayColor2),
                        textAlign: TextAlign.center,
                        softWrap: true),
                  ),
                  const SizedBox(height: 50),
                  // 显示圆形图片
                  ClipOval(
                      child: Assets.images.iconTest2
                          .image(width: 240, height: 240, fit: BoxFit.cover)),
                  const SizedBox(height: 80),
                  Text(widget.type == PosType.main ? "${mainTips[stepData.step-1]}(${stepData.step}/3)":context.locale.connectingMainTip,
                      style:
                      TextStyle(fontSize: 14, color: context.theme.grayColor2)),
                ],
              ),
              // 增加底部空间，确保内容不被底部固定的按钮遮挡
              const SizedBox(height: 150),
            ],
          ),
        ),
        // Positioned用于精确定位子组件
        // 这里将按钮固定在距离底部80像素的位置
        // 只有当键盘不可见时才显示按钮
        if (!_isKeyboardVisible)
          Positioned(
            bottom: 80, // 距离底部80像素
            left: 0,
            right: 0, // left:0和right:0使按钮水平居中并占据整个宽度
            child: Center( // Center进一步确保按钮水平居中
              child: widget.type == PosType.sub ? GestureDetector(
                // 点击事件处理
                onTap: _addIPAddress,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
                  // 设置按钮边框样式
                  decoration: BoxDecoration(
                    border: Border.all(color: context.theme.grayColor6, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  // 按钮文本
                  child: Text(
                    context.locale.manuallyEnterIPAddress,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ) : const SizedBox(), // 如果不是副设备类型，则不显示按钮
            ),
          ),
      ],
    );
  }

  Widget _buildErrorView(WidgetRef ref) {
    return Stack(
      fit: StackFit.expand,
      children: [
        SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Assets.images.iconXCircle.svg(width: 32, height: 32),
                      const SizedBox(width: 2),
                      Text(context.locale.connectionFailed,
                          style:
                          const TextStyle(fontSize: 24,fontWeight: FontWeight.w700)),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(context.locale.connectionFailedDesc,
                      style: TextStyle(fontSize: 16, color: context.theme.grayColor2),
                      softWrap: true),
                  const SizedBox(height: 80),
                  ClipOval(
                      child: Assets.images.iconTest2
                          .image(width: 240, height: 240, fit: BoxFit.cover)),
                  const SizedBox(height: 80),
                  GestureDetector(
                    onTap: ()=> _retryAction(ref),
                    child: Container(
                      width: 113,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.black),
                      child: Text(context.locale.tryAgain,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w700)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (!_isKeyboardVisible)
          Positioned(
            bottom: 80, // 距离底部80像素
            left: 0,
            right: 0, // left:0和right:0使按钮水平居中并占据整个宽度
            child: Center( // Center进一步确保按钮水平居中
              child: widget.type == PosType.sub ? GestureDetector(
                // 点击事件处理
                onTap: _addIPAddress,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
                  // 设置按钮边框样式
                  decoration: BoxDecoration(
                    border: Border.all(color: context.theme.grayColor6, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  // 按钮文本
                  child: Text(
                    context.locale.manuallyEnterIPAddress,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ) : const SizedBox(), // 如果不是副设备类型，则不显示按钮
            ),
          ),
      ],
    );
  }

  Widget _buildDataView(String data,PosType type) {
    final stepData = ref.watch(initializeDataProvider);
    final mainTips = [context.locale.fetchingStoreData,context.locale.fetchingStoreInfo,context.locale.storeDataFetched];
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.images.iconCheckGreen.svg(width: 32, height: 32),
              const SizedBox(width: 2),
              Flexible(
                child: Text(
                  widget.type == PosType.main
                      ? context.locale.connectingSuccessful: context.locale.connectingMainTip,
                  style: const TextStyle(
                      fontSize: 24, fontWeight: FontWeight.w700),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          Text(
            context.locale.boundMainDeviceTip(data),
            style: TextStyle(fontSize: 16, color: context.theme.grayColor2),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 80),
          ClipOval(
              child: Assets.images.iconTest2
                  .image(width: 240, height: 240, fit: BoxFit.cover)),
        ],
      ),
    );
  }
  
  void _retryAction(WidgetRef ref) {
    if (widget.type == PosType.main) {
      ref.invalidate(connectCloudControllerProvider);
    } else {
      ref.invalidate(discoveryServiceProvider);
    }
  }

  void _addIPAddress() {
    ref.invalidate(discoveryServiceProvider);
    KpInputDialog.show(
        context: context,
        title: context.locale.enterIPAddress,
        label: context.locale.ipAddress,
        hintText: context.locale.getIPAddressTip,
        confirmButtonText: context.locale.confirm,
        cancelButtonText: context.locale.cancel,
        keyboardType: TextInputType.number,
        type: InputDialogType.ipAddress, //连续输入3位
        //确认
        onConfirm: (inputText) {
          if (inputText == null || inputText.isEmpty || !inputText.isIpv4) {
            KPToast.show(content: context.locale.ipError);
          } else {
            ref.read(intranetBaseUrlProvider.notifier).updateUrl('$inputText:${Intranet.serverPort}');
            _jump();
          }
        },
        // 取消
        onCancel: () {

        }
    );
  }

  Future<void> _jump() async {
    if (widget.type == PosType.main) {
      final db = ref.read(databaseProvider);
      await db.customSelect('SELECT 1').get();
      var mainDeviceName = "";
      if (mainDeviceNotifier.value != null) {
        final mainDevice = mainDeviceNotifier.value!;
        mainDeviceName = "${mainDevice.deviceName}-${mainDevice.physicalDeviceIdToString}";
      }
      ref.read(broadcastServiceProvider).startUdpBroadcast(mainDeviceName);
      await ref.read(apiIntranetServiceProvider).startServer();
      ref.read(webSocketServiceProvider).connect();
      ref.watch(webSocketListenerProvider);
    } else {
      await ref.read(webSocketServiceProvider).connect();
      ref.watch(webSocketListenerProvider);
    }
    if (!mounted) return;
    const ProductRoute().go(context);
  }
}
