import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/responsive_center_container.dart';
import '../../../common/utils/device_util.dart';
import '../../auth/presentation/auth_app_bar.dart';
import '../../store/application/store_service.dart';

class BindMainDeviceScreen extends ConsumerStatefulWidget {
  const BindMainDeviceScreen({super.key});

  @override
  ConsumerState createState() => _BindMainDeviceScreenState();
}

class _BindMainDeviceScreenState extends ConsumerState<BindMainDeviceScreen> {
  @override
  Widget build(BuildContext context) {
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AuthAppBar(titleText: 'Store:${boundStore.storeName}'),
        body: ResponsiveCenterContainer(
          padding: const EdgeInsets.only(top: 60),
          contentWidth: DeviceUtil.isMobile() ? DeviceUtil.deviceWidth : 516,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Assets.images.iconErrorCircle.svg(width: 80,height: 80),
              const SizedBox(height: 16),
              const Text("The current store has bound POS, proceed?",
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700),textAlign: TextAlign.center,),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.only(left: 20,right: 20),
                padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 13),
                decoration: BoxDecoration(color: context.theme.grayColor1,borderRadius: BorderRadius.circular(8)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Expanded(child: Text("Existing device: POS-03485",maxLines: 2,overflow: TextOverflow.ellipsis,)),
                    Container(
                      decoration: BoxDecoration(color: Color(0x1A22A06B),borderRadius: BorderRadius.circular(4)),
                      padding: const EdgeInsets.symmetric(horizontal: 6,vertical: 2),
                      child: const Text("Online",style: TextStyle(color: Color(0xFF22A06B),fontSize: 12),textAlign: TextAlign.center,),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 80),
              Container(
                margin: const EdgeInsets.only(left: 20,right: 20),
                child: TDButton(
                  width: double.infinity,
                  height: 48,
                  style: TDButtonStyle(backgroundColor: Colors.black),
                  textStyle: TextStyle(color: Colors.white),
                  text: context.locale.continueBind,
                  onTap: () {
                    BindMainDeviceConfirmRoute().push(context);
                  },
                ),
              )
            ],
          ),
        ));
  }
}
