import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/features/device/data/device_repository.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/device/domain/device_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import 'package:collection/collection.dart';

part 'device_service.g.dart';

class DeviceService {
  late final Ref _ref;
  late CurrentDeviceInfo currentDeviceInfo;

  DeviceService({required Ref ref}) {
    _ref = ref;
    try {
      final keyValueStorageService = _ref.read(keyValueStorageServiceProvider);
      final jsonString = keyValueStorageService.getDeviceInfo();
      print(232323232);
      print(jsonString);
      if (jsonString == null) {
        currentDeviceInfo = CurrentDeviceInfo();
      } else {
        Map<String, dynamic> jsonMap = jsonDecode(jsonString);
        currentDeviceInfo = CurrentDeviceInfo.fromJson(jsonMap);
      }
    } catch (e) {
      currentDeviceInfo = CurrentDeviceInfo();
    }
  }

  Future<void> syncDeviceToStorageService(CurrentDeviceInfo deviceInfo) async {
    final keyValueStorageService = _ref.read(keyValueStorageServiceProvider);
    try {
      await keyValueStorageService.setDeviceInfo(jsonEncode(deviceInfo.toJson()));
      currentDeviceInfo = deviceInfo;
    } catch (e, st) {
      print('保存 deviceInfo 失败: $e');
      print('堆栈信息: $st');
    }
  }

  Future<DeviceItem?> fetchMainDeviceInfo() async {
    final response = await _ref.read(deviceRepositoryProvider).fetchDeviceInfos();
    if (response.code == StompStatusCode.success) {
      final List<Map<String, dynamic>> data = response.data.cast<Map<String, dynamic>>();
      final Map<String, dynamic>? matchedJson = data.firstWhereOrNull(
            (item) => item['masterSlaveFlag'] == 1,
      );
      return matchedJson != null ? DeviceItem.fromJson(matchedJson) : null;
    }
    return null;
  }

}

@Riverpod(keepAlive: true)
DeviceService deviceService(Ref ref) {
  return DeviceService(ref: ref);
}