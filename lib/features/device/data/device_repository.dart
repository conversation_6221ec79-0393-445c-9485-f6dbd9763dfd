import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_destinations.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/features/device/domain/device_item.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/data/store_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../common/services/networking/remote_service/base_response.dart';
import '../../../common/services/web_socket/stomp/stomp_request_exception.dart';
import '../../../common/services/web_socket/stomp/stomp_request_service.dart';

part 'device_repository.g.dart';

class DeviceRepository {
  final StompRequestService _stompRequestService;
  final Ref _ref;
  DeviceRepository(this._ref, {required StompRequestService stompRequestService})
      : _stompRequestService = stompRequestService;

  Future<BaseResponse<List<Map<String, dynamic>>>> fetchDeviceInfos() async {
    try {
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (boundStore.storeId == 0) {
        return const BaseResponse(
          code: StompStatusCode.responseError,
          message: 'Not bind Store',
          data: [],
        );
      }
      return await _stompRequestService.request<BaseResponse<List<Map<String, dynamic>>>>(
        sendDestination: StompDestinations.deviceInfo,
        subscribeDestination: StompDestinations.deviceInfoSub,
        body: {"storeId": boundStore.storeId},
        response: (json) => BaseResponse<List<Map<String, dynamic>>>.fromJson(
          json,
              (data) => (data as List).cast<Map<String, dynamic>>(),
        ),
      );
    } on StompRequestException catch (e) {
      return BaseResponse(
        code: e.code,
        message: e.message,
        data: [],
      );
    }
  }

}

@riverpod
DeviceRepository deviceRepository(Ref ref) {
  return DeviceRepository(ref,stompRequestService: ref.watch(stompRequestServiceProvider));
}
