import 'package:freezed_annotation/freezed_annotation.dart';

part 'device_item.freezed.dart';
part 'device_item.g.dart';

@freezed
class DeviceItem with _$DeviceItem {
  const factory DeviceItem({
    required int deviceId,
    required int physicalDeviceId,
    required String physicalDeviceIdToString,
    required String deviceName,
    required int deviceType,
    required int masterSlaveFlag,
    required int deviceStatus,
  }) = _DeviceItem;

  factory DeviceItem.fromJson(Map<String, dynamic> json) =>
      _$DeviceItemFromJson(json);
}
