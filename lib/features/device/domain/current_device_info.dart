import '../../../common/constant/pos_type.dart';

class CurrentDeviceInfo {
  PosType type;
  int physicalDeviceId;
  String physicalDeviceIdToString;
  String deviceName;

  CurrentDeviceInfo({
    this.type = PosType.main,
    this.physicalDeviceId = 0,
    this.physicalDeviceIdToString = "",
    this.deviceName = "",
  });

  Map<String, dynamic> toJson() => {
    'type': type.value,
    'physicalDeviceId': physicalDeviceId,
    'physicalDeviceIdToString': physicalDeviceIdToString,
    'deviceName': deviceName,
  };

  factory CurrentDeviceInfo.fromJson(Map<String, dynamic> json) {
    return CurrentDeviceInfo(
      type: PosType.fromValue(json['type'] ?? 0),
      physicalDeviceId: json['physicalDeviceId'] ?? 0,
      physicalDeviceIdToString: json['physicalDeviceIdToString'] ?? "",
      deviceName: json['deviceName'] ?? "",
    );
  }
}
