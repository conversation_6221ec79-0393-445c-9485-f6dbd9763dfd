import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/routing/main_route.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/constant/pos_type.dart';
import '../../../common/services/broadcast/broadcast_service.dart';
import '../../../common/services/database/app_database.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import '../../../common/services/networking/intranet_service/api_intranet_service.dart';
import '../../../common/services/web_socket/web_socket_service.dart';
import '../../language_settings/presentation/select_language_popup.dart';

class LaunchScreen extends ConsumerStatefulWidget {
  const LaunchScreen({super.key});

  @override
  ConsumerState createState() => _LaunchScreenState();
}

class _LaunchScreenState extends ConsumerState<LaunchScreen> {
  @override
  void initState() {
    if (!DeviceUtil.isMobile()) {
      _jump(context);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: !DeviceUtil.isMobile() ? SizedBox() : Stack(
        children: [
          Positioned.fill(
              child: Assets.images.iconTest4.image(fit: BoxFit.cover)),
          SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height:48,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                  width: double.infinity,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Assets.images.iconLoginLogo.svg(),
                      GestureDetector(
                        onTap: () {
                          KPSlidePopup.showSlidePopup(context: context, contentWidget: const SelectLanguagePopup());
                        },
                        child: Assets.images.iconButtonLanguageWhite
                            .svg(width: 20, height: 20),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(left: 20,right: 20,bottom: 80),
                  child: TDButton(
                    width: double.infinity,
                    height: 48,
                    text: context.locale.login,
                    onTap: () => _jump(context),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _jump(BuildContext context) async {
    final storeService = ref.read(storeServiceProvider);
    await storeService.init();
    final db = ref.read(databaseProvider);
    await db.customSelect('SELECT 1').get();
    ref.read(broadcastServiceProvider).startUdpBroadcast("1212121");
    await ref.read(apiIntranetServiceProvider).startServer();
    ref.read(webSocketServiceProvider).connect();
    ref.watch(webSocketListenerProvider);
    final ct = context;
    if (!ct.mounted) return;
    ProductRoute().go(context);

    // final storeService = ref.read(storeServiceProvider);
    // await storeService.init();
    // final boundStoreInfo = storeService.boundStoreInfo;
    // final deviceInfo = await ref.read(deviceServiceProvider).currentDeviceInfo;
    // final ct = context;
    // if (!ct.mounted) return;
    // if (boundStoreInfo.storeId != 0 && deviceInfo.physicalDeviceId != 0) {
    //   ReLoginRoute().go(ct);
    // } else {
    //   LoginRoute().go(ct);
    // }
  }
}
