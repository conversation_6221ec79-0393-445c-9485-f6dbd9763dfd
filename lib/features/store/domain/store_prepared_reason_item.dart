import 'package:freezed_annotation/freezed_annotation.dart';

part 'store_prepared_reason_item.g.dart';
part 'store_prepared_reason_item.freezed.dart';

@freezed
class StorePreparedReasonItem with _$StorePreparedReasonItem {
  const factory StorePreparedReasonItem({
    required String preparedReasonName,
    required int preparedReasonId,
    required int preparedReasonType,
    required int orderNum,
  }) = _StorePreparedReasonItem;

  factory StorePreparedReasonItem.fromJson(Map<String, dynamic> json) =>
      _$StorePreparedReasonItemFromJson(json);
}
