import 'package:freezed_annotation/freezed_annotation.dart';

part 'service_fee_item.g.dart';
part 'service_fee_item.freezed.dart';

@freezed
class ServiceFeeItem with _$ServiceFeeItem {
  const factory ServiceFeeItem({
    required int id,
    required String name,
    required int type,// 收取方式（1：按比例收取，2：固定费用收取）
    required int number,
    required double chargeRate,//收取比例(收取方式为按比例收取)
    required double fixedCharge,//收取费用（收取方式为固定费用收取）
    required int isCheck,// 是否选中 1=选中 0=未选中
  }) = _ServiceFeeItem;

  factory ServiceFeeItem.fromJson(Map<String, dynamic> json) =>
      _$ServiceFeeItemFromJson(json);
}
