import 'package:freezed_annotation/freezed_annotation.dart';

part 'store_item.g.dart';
part 'store_item.freezed.dart';

@freezed
class StoreItem with _$StoreItem {
  const factory StoreItem({
    required String storeName,
    required int storeId,
    required String brandName,
    required int storeIsBindDevice
  }) = _StoreItem;

  factory StoreItem.fromJson(Map<String, dynamic> json) =>
      _$StoreItemFromJson(json);
}
