class BoundStoreInfo {
  int storeId;
  String storeName;
  int brandId;
  String brandName;
  String logoImageUrl;
  String currentUnit;
  int organizationId;

  BoundStoreInfo({
    this.storeId = 0,
    this.storeName = "",
    this.brandId = 0,
    this.brandName = "",
    this.logoImageUrl = "",
    this.currentUnit = "",
    this.organizationId = 0
  });

  Map<String, dynamic> toJson() => {
    'storeId': storeId,
    'storeName': storeName,
    'brandId': brandId,
    'brandName': brandName,
    'logoImageUrl': logoImageUrl,
    'currentUnit': currentUnit,
    'organizationId':organizationId
  };

  factory BoundStoreInfo.fromJson(Map<String, dynamic> json) => BoundStoreInfo(
    storeId: json['storeId'] ?? 0,
    storeName: json['storeName'] ?? '',
    brandId: json['brandId'] ?? 0,
    brandName: json['brandName'] ?? '',
    logoImageUrl: json['logoImageUrl'] ?? '',
    currentUnit: json['currentUnit'] ?? '',
    organizationId: json['organizationId'] ?? 0,
  );
}
