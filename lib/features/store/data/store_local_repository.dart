import 'package:drift/drift.dart';
import 'package:kpos/features/store/data/store_local_repository_interface.dart';

import '../../../common/services/database/app_database.dart';

class StoreLocalRepository implements StoreLocalRepositoryInterface {
  final AppDatabase db;

  StoreLocalRepository(this.db);

  @override
  Future<List<Map<String, dynamic>>> getStorePreparedReasonList({int type = 3}) async {
    final query = db.select(db.storePreparedReason)
      ..where((t) => t.preparedReasonType.equals(type))
      ..orderBy([
            (t) => OrderingTerm(expression: t.orderNum, mode: OrderingMode.asc),
      ]);

    final rows = await query.get();

    final result = rows
        .map((row) => {
      'orderNum': row.orderNum,
      'preparedReasonId': row.preparedReasonId,
      'preparedReasonName': row.preparedReasonName,
      'preparedReasonType': row.preparedReasonType,
    })
        .toList();

    return result;
  }

  @override
  Future<List<Map<String, Object>>> getServerService() async {
    try {
      const serviceFeeSql = ''' SELECT 
    sfm.service_fee_management_id, 
    sfm.service_fee_name, 
    CASE 
        WHEN sfm.service_charge_method = 1 THEN CONCAT(sfm.service_charge_rate, '%')
        WHEN sfm.service_charge_method = 2 THEN CAST(sfm.service_fixed_charge AS VARCHAR)
        ELSE '未知收费方式'
    END AS "service_fee_price",
    sfm.service_charge_method,
    sfm.service_duty_free
FROM 
    service_fee_management sfm
JOIN 
    store_service_fee_relation ssfr ON sfm.service_fee_management_id = ssfr.service_fee_management_id
ORDER BY 
    sfm.service_fee_management_id''';
      final serviceFeeResult = await db.customSelect(serviceFeeSql).get();

      final serviceFeeList = serviceFeeResult
          .map((row) => {
        'serviceFeeManagementId':
        row.read<int>('service_fee_management_id'),
        'serviceFeeName': row.read<String>('service_fee_name')
      })
          .toList();
      return serviceFeeList;
    } catch (e, stack) {
      print('Query failed: $e');
      print(stack);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, Object>>> getTax(int productId) async {
    try {
      const taxSql = '''SELECT 
    tm.tax_management_id,  
    tm.tax_name, 
    tm.tax_rate 
FROM 
    tax_management tm
LEFT JOIN 
    store_tax_relation str ON tm.tax_management_id = str.tax_management_id
INNER JOIN 
    product_tax_relation ptr ON tm.tax_management_id = ptr.tax_management_id
WHERE 
     ptr.product_id = ? 
ORDER BY 
    tm.tax_management_id''';
      final taxResult = await db
          .customSelect(taxSql, variables: [Variable.withInt(productId)]).get();
      final taxList = taxResult
          .map((row) => {
        'taxManagementId': row.read<int>('tax_management_id'),
        'taxName': row.read<String>('tax_name'),
        'taxRate': row.read<double>('tax_rate')
      })
          .toList();

      return taxList;
    } catch (e, stack) {
      print('Query failed: $e');
      print(stack);
      rethrow;
    }
  }

  @override
  Future<Map<String, Object>?> getStoreDetail(int storeId) async {
    try {
      final query = db.customSelect(
        '''
      SELECT
        ms.store_id, 
        ms.store_name,
        ms.organization_id,
        ms.region_id,
        ms.store_timezone,
        ms.currency,
        ms.organization_id
      FROM
        merchant_store ms
      WHERE
        ms.store_id = ?
      ''',
        variables: [Variable.withInt(storeId)],
      );

      final result = await query.getSingleOrNull();

      if (result == null) {
        return null;
      }

      return {
        'store_id': result.read<int>('store_id'),
        'store_name': result.read<String>('store_name'),
        'organization_id': result.read<int>('organization_id'),
        'region_id': result.read<int>('region_id'),
        'store_timezone': result.read<String>('store_timezone'),
        'currency': result.read<String>('currency'),
      };
    } catch (e, stackTrace) {
      print('getStoreDetail 查询出错: $e\n$stackTrace');
      return null;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getServiceFeeList() async {
    final list = await db.customSelect(
        '''SELECT
            *
            FROM
        service_fee_management sfm'''
    ).get();

    final data = list
        .map((row) => {
      'id': row.read<int>('service_fee_management_id'),
      'name': row.readNullable<String>('service_fee_name') ?? '',
      'type': row.readNullable<int>('service_charge_method') ?? 0,
      /// TODO 这里先固定写死1，后面有对应需求再改
      'number': 1,
      'chargeRate': row.readNullable<double>('service_charge_rate') ?? 0.0,
      'fixedCharge': row.readNullable<double>('service_fixed_charge') ?? 0.0,
    })
        .toList();

    return data;
  }

}
