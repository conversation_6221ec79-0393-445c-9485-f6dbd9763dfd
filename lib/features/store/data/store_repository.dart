import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/pos_type.dart';
import 'package:kpos/common/services/networking/remote_service/base_response.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_destinations.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/store/domain/brand_item.dart';
import 'package:kpos/features/store/domain/store_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../common/services/database/app_database.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import '../../../common/services/web_socket/stomp/stomp_request_exception.dart';
import '../../../common/services/web_socket/stomp/stomp_request_service.dart';
import '../../../common/utils/kp_list_util.dart';
import '../application/store_service.dart';

part 'store_repository.g.dart';

class StoreRepository {
  final StompRequestService _stompRequestService;
  final Ref _ref;
  StoreRepository(this._ref, {required StompRequestService stompRequestService})
      : _stompRequestService = stompRequestService;
  Future<List<BrandItem>> getBrandList() async {
    final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
    final tenantId = await  keyValueStorage.getTenantId();
    try {
      final response = await _stompRequestService.request<BaseResponse<List<BrandItem>>>(
        sendDestination: StompDestinations.brandList,
        subscribeDestination: StompDestinations.brandListSub,
        body:{"tenantId":tenantId ?? 1},
        response: (json) => BaseResponse<List<BrandItem>>.fromJson(
          json,
              (data) => (data as List<dynamic>)
              .map((item) => BrandItem.fromJson(item as Map<String, dynamic>))
              .toList(),
        ),
      );

      if (response.code == StompStatusCode.success) {
        return response.data;
      } else {
        return [];
      }
    } on StompRequestException {
      return [];
    }
  }


  Future<List<StoreItem>> getStoreList(int brandId) async {
    final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
    final tenantId = await  keyValueStorage.getTenantId();
    try {
      final baseResponse = await _stompRequestService.request<BaseResponse<List<StoreItem>>>(
        sendDestination: StompDestinations.storeList,
        subscribeDestination: StompDestinations.storeListSub,
        body:{"tenantId":tenantId ?? 1,"brandIdList":[brandId]},
        response: (json) => BaseResponse<List<StoreItem>>.fromJson(
          json,
              (data) => (data as List<dynamic>)
              .map((item) => StoreItem.fromJson(item as Map<String, dynamic>))
              .toList(),
        ),
      );

      // 根据响应状态判断是否成功
      if (baseResponse.code == StompStatusCode.success) {
        return baseResponse.data;
      } else {
        return [];
      }
    } on StompRequestException catch (e) {
      return [];
    }
  }

  /// 绑定设备信息
  Future<BaseResponse<Map<String, dynamic>>> insertDevice({
    int deviceId = 0,
    required PosType type,
    required int storeId,
  }) async {
    final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
    final tenantId = await  keyValueStorage.getTenantId();
    try {
      final body = {
        "tenantId": tenantId ?? 1,
        "deviceName": "POS",
        "deviceType": DeviceUtil.deviceWidth > DeviceUtil.windowMinWidth ? 1 : 2,
        "masterSlaveFlag": type.value,
        "storeId": storeId,
        "deviceUuid": DeviceUtil().terminalSerialNumber.toString(),
      };
      if (type.value != 1) {
        body["deviceId"] = deviceId;
      }
      return await _stompRequestService.request<BaseResponse<Map<String, dynamic>>>(
        sendDestination: StompDestinations.deviceInsert,
        subscribeDestination: StompDestinations.deviceInsertSub,
        body: body,
        response: (json) => BaseResponse<Map<String, dynamic>>.fromJson(
          json,
              (data) => data as Map<String, dynamic>,
        ),
      );
    } on StompRequestException catch (e) {
      return BaseResponse(
        code: e.code,
        message: e.message,
        data: {"data": null},
      );
    }

  }

  /// 解绑设备
  Future<BaseResponse<Map<String, dynamic>>> unbindDevice({
    required int deviceId,
    required PosType type,
  }) async {
    try {
      final keyValueStorage = _ref.read(keyValueStorageServiceProvider);
      final tenantId = await  keyValueStorage.getTenantId();
      return await _stompRequestService.request<BaseResponse<Map<String, dynamic>>>(
        sendDestination: StompDestinations.unbindDevice,
        subscribeDestination: StompDestinations.unbindDeviceSub,
        body: {
          "tenantId": tenantId ?? 1,
          "operationType": type.value,
          "deviceId":deviceId
        },
        response: (json) => BaseResponse<Map<String, dynamic>>.fromJson(
          json,
              (data) => data as Map<String, dynamic>,
        ),
      );
    } on StompRequestException catch (e) {
      return BaseResponse(
        code: e.code,
        message: e.message,
        data: {"data": null},
      );
    }
  }

  // 门店信息数据初始化
  Future<void> initializeMerchantStoreInfo() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
    final jsonMap = await _stompRequestService.requestMapData(
        sendDestination: StompDestinations.stores,
        subscribeDestination: StompDestinations.storeSub,
        body: {
          "organizationId":boundStore.organizationId,
          "storeId": boundStore.storeId,
          "tenantId": tenantId ?? 1
        });

    if (jsonMap.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    final storeMap = <int, MerchantStoreCompanion>{};

    try {
      final companion = MerchantStoreCompanion.insert(
        storeId: Value(jsonMap['storeId'] as int),
        storeName: jsonMap['storeName'] as String,
        organizationId: jsonMap['organizationId'] as int,
        regionId: jsonMap['regionId'] as int,
        storeTimezone: jsonMap['storeTimezone'] as String,
        currency: jsonMap['currency'] as String,
        address: Value(jsonMap['address'] as String),
        postcode: Value(jsonMap['postcode'] as String),
        areaCode: Value(jsonMap['areaCode'] as String),
        phone: Value(jsonMap['phone'] as String),
        brandId: jsonMap['brandId'] as int,
        businessState: Value(jsonMap['businessState'] as int),
        manualClose: Value(jsonMap['manualClose'] as int),
        currencySymbol: Value(jsonMap['currencySymbol'] as String),
      );
      storeMap[jsonMap['storeId'] as int] = companion;

      // 批量插入
      try {
        if (storeMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.merchantStore, storeMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${storeMap.length}  store info');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing store info data: $e\n$st');
    }
  }

  // 就餐方式数据初始化
  Future<void> initializeDiningStyleList() async {
    final list = await _stompRequestService.requestListData(
      sendDestination: StompDestinations.diningTypes,
      subscribeDestination: StompDestinations.diningTypeSub,
      body: {},
      itemParser: (json) => json as Map<String, dynamic>,
    );

    final db = _ref.watch(databaseProvider);
    final diningStyleMap = <int, DiningStyleCompanion>{};

    try {
      for (var item in list) {
        final companion = DiningStyleCompanion.insert(
            saleType: Value(item['saleType'] as int),
            saleTypeName: item['saleTypeName'] as String);
        diningStyleMap[item['saleType'] as int] = companion;
      }

      // 批量插入
      try {
        if (diningStyleMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.diningStyle, diningStyleMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${diningStyleMap.length}  eat model, ');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing eat model data: $e\n$st');
    }
  }

  // 初始化服务费和税费
  Future<void> initializeTaxAndFeeList() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonMap = await _stompRequestService.requestMapListData(
      sendDestination: StompDestinations.taxAndFees,
      subscribeDestination: StompDestinations.taxAndFeeSub,
      body: {
        "storeId": boundStore.storeId,
      },
      itemParser: (json) => json as Map<String, dynamic>,
    );

    if (jsonMap.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    final taxManagementMap = <int, TaxManagementCompanion>{};
    final serviceFeeManagementMap = <int, ServiceFeeManagementCompanion>{};

    try {
      final taxManagementList = KPListUtil.safeList(jsonMap['taxManagement']);
      final serviceFeeManagementList =
      KPListUtil.safeList(jsonMap['serviceFeeManagement']);

      for (var item in taxManagementList) {
        final companion = TaxManagementCompanion.insert(
          taxManagementId: Value(item['taxManagementId'] as int),
          taxName: item['taxName'] as String,
          taxRate: item['taxRate'],
          taxType: item['taxType'] as int,
          taxTemporaryDishes: item['taxTemporaryDishes'] as int,
        );
        taxManagementMap[item['taxManagementId'] as int] = companion;
      }

      for (var item in serviceFeeManagementList) {
        final companion = ServiceFeeManagementCompanion.insert(
          serviceFeeManagementId: Value(item['serviceFeeManagementId'] as int),
          serviceFeeName: item['serviceFeeName'] as String,
          serviceChargeMethod: item['serviceChargeMethod'] as int,
          serviceDutyFree: item['serviceDutyFree'] as int,
          serviceChargeRate:
          (item['serviceChargeRate'] as num?)?.toDouble() ?? 0.0,
          serviceFixedCharge:
          Value((item['serviceFixedCharge'] as num?)?.toDouble()),
          serviceFeeTax:
          Value((item['serviceFixedCharge'] as num?)?.toDouble() ?? 0.0),
        );
        serviceFeeManagementMap[item['serviceFeeManagementId'] as int] =
            companion;
      }

      // 批量插入
      try {
        if (taxManagementMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.taxManagement, taxManagementMap.values.toList());
        }
        if (serviceFeeManagementMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.serviceFeeManagement, serviceFeeManagementMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${taxManagementMap.length}  tax management, '
            '${serviceFeeManagementMap.length} service fee management');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing tax management, service fee management data: $e\n$st');
    }
  }

  // 租户预设原因初始化
  Future<void> initializeStorePreparedReasonList() async {
    final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
    if (tenantId == null) {
      print("租户id为空");
      return;
    }
    final jsonMap = await _stompRequestService.requestMapData(
      sendDestination: StompDestinations.preparedReasons,
      subscribeDestination: StompDestinations.preparedReasonSub,
      body:{
        "tenantId":tenantId,
        "page":1,
        "rows":10
      },
    );

    final db = _ref.watch(databaseProvider);
    final storePreparedReasonMap = <int, StorePreparedReasonCompanion>{};

    try {
      for (var item in  KPListUtil.safeList(jsonMap['data'])) {
        final companion = StorePreparedReasonCompanion.insert(
            preparedReasonId: Value(item['preparedReasonId'] as int),
            preparedReasonName:item['preparedReasonName'] as String,
            preparedReasonType: item['preparedReasonType'] as int,
            orderNum: item['orderNum'] as int);
        storePreparedReasonMap[item['preparedReasonId'] as int] = companion;
      }

      // 批量插入
      try {
        if (storePreparedReasonMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.storePreparedReason, storePreparedReasonMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${storePreparedReasonMap.length}  store prepared reason');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing addons data: $e\n$st');
    }
  }

  Future<void> initializeMenuSkuRelation() async {
    final jsonMap = await _stompRequestService.requestMapData(
      sendDestination: StompDestinations.menuSkuRelations,
      subscribeDestination: StompDestinations.menuSkuRelationSub,
      body: {"storeId": 615029888918962176, "page": 1, "rows": 50},
    );

    if (jsonMap.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    final productStoreMenuSkuRelationsCompanionMap= <int, ProductStoreMenuSkuRelationsCompanion>{};

    try {
      final totalCount = jsonMap['totalCount'];
      final dataList = KPListUtil.safeList(jsonMap['data']);

      for (var item in dataList) {
        final productStoreMenuSkuRelationsCompanion = ProductStoreMenuSkuRelationsCompanion
            .insert(
          storeMenuSkuRelationId: Value(item['storeMenuSkuRelationId'] as int),
          storeMenuId: item['storeMenuId'] as int,
          skuId: item['skuId'] as int,
          storeMenuPrice: item['storeMenuPrice'] as double,
        );
        productStoreMenuSkuRelationsCompanionMap[item['storeMenuSkuRelationId'] as int] =
            productStoreMenuSkuRelationsCompanion;
      }

      // 批量插入
      try {
        if (productStoreMenuSkuRelationsCompanionMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.productStoreMenuSkuRelations, productStoreMenuSkuRelationsCompanionMap.values.toList());
        }

      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }

        print('✅  Successfully inserted: '
            '${productStoreMenuSkuRelationsCompanionMap.length}  product store menu sku relations ');
    } catch (e, st) {
      print('Error processing product store menu sku relation data: $e\n$st');
    }
  }

  /// 门店员工数据初始化
  Future<void> initializeMerchantEmployee() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final dataList = await _stompRequestService.requestListData(
      sendDestination: StompDestinations.getEmployee,
      subscribeDestination: StompDestinations.getEmployeeSub,
      body: {"storeId": boundStore.storeId},
      itemParser: (json) => json as Map<String, dynamic>,
      isPaged: true,
    );

    final employeeMap = <int, MerchantEmployeeCompanion>{};

    if (dataList.isNotEmpty) {
      for (final item in dataList) {
        final companion = MerchantEmployeeCompanion(
          employeeId: Value(item['employeeId'] as int),
          roleId: Value(item['roleId'] as int),
          email: Value(item['email'] as String),
          firstName: Value(item['firstName'] as String?),
          lastName: Value(item['lastName'] as String?),
          employeeName: Value(item['employeeName'] as String?),
          areaCode: Value(item['areaCode'] as String?),
          phone: Value(item['phone'] as String?),
          gender: Value(item['gender'] as int?),
          address: Value(item['address'] as String?),
          pinCode: Value(item['pinCode'] as String?),
          commissionStatus: Value(item['commissionStatus'] as int),
          commissionId: Value(item['commissionId'] as int),
          headPortraitPath: Value(item['headPortraitPath'] as String?),
        );
        employeeMap[item['employeeId'] as int] = companion;
      }

      final db = _ref.watch(databaseProvider);

      try {
        if (employeeMap.isNotEmpty) {
          await db.batch((batch) {
            batch.insertAllOnConflictUpdate(
              db.merchantEmployee,
              employeeMap.values.toList(),
            );
          });

          print('✅ Successfully inserted: ${employeeMap.length} employees');
        }
      } catch (e, st) {
        print('Employee batch insert failed: $e\n$st');
      }
    }
  }

  Future<void> initializeTaxRelation() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    try {
      final jsonList = await _stompRequestService.requestListData(
        sendDestination: StompDestinations.getTaxRelation,
        subscribeDestination: StompDestinations.getTaxRelationSub,
        body: {
          "storeId": boundStore.storeId,
        },
        itemParser: (json) => json as Map<String, dynamic>,
      );

      final jsonMap = <int, ServiceFeeTaxRelationCompanion>{};
      if (jsonList.isNotEmpty) {
        for (final item in jsonList) {
          final storeServiceFeeRelationId = item['storeServiceFeeRelationId'] as int;
          final serviceFeeManagementId = item['serviceFeeManagementId'] as int;
          final taxManagementId = item['taxManagementId'] as int;

          final companion = ServiceFeeTaxRelationCompanion.insert(
            serviceFeeTaxRelationId: Value(storeServiceFeeRelationId),
            serviceFeeManagementId: serviceFeeManagementId,
            taxManagementId: taxManagementId,
          );
          jsonMap[storeServiceFeeRelationId] = companion;
        }

        final db = _ref.watch(databaseProvider);
        try {
          if (jsonMap.isNotEmpty) {
            await db.insertOrReplaceBatch(
              db.serviceFeeTaxRelation,
              jsonMap.values.toList(),
            );
            print('✅ Successfully inserted ${jsonMap.length} serviceFeeTaxRelation');
          }
        } catch (e, st) {
          print('serviceFeeTaxRelation batch insert failed: $e\n$st');
        }
      } else {
        print('initializeServiceFeeTaxRelation: no data to insert.');
      }
    } catch (e, st) {
      print('initializeServiceFeeTaxRelation error: $e');
      print(st);
    }
  }

  Future<void> initializeProductTaxRelation() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    try {
      final jsonList = await _stompRequestService.requestListData(
        sendDestination: StompDestinations.getProductRelationTax,
        subscribeDestination: StompDestinations.getProductRelationTaxSub,
        body: {
          "storeId": boundStore.storeId,
        },
        itemParser: (json) => json as Map<String, dynamic>,
      );

      List<ProductTaxRelationCompanion> dataList = [];
      if (jsonList.isNotEmpty) {
        for (final item in jsonList) {
          final productId = item['productId'] as int;
          final taxManagementId = item['taxManagementId'] as int;
          final type = item['type'] as int;

          final companion = ProductTaxRelationCompanion.insert(
            productId: productId,
            type: type,
            taxManagementId: taxManagementId,
          );
          dataList.add(companion);
        }

        final db = _ref.watch(databaseProvider);
        try {
          if (dataList.isNotEmpty) {
            await db.insertOrReplaceBatch(
              db.productTaxRelation,
              dataList,
            );
            print('✅ Successfully inserted ${dataList.length} productTaxRelation');
          }
        } catch (e, st) {
          print('productTaxRelation batch insert failed: $e\n$st');
        }
      } else {
        print('initializeProductTaxRelation: no data to insert.');
      }
    } catch (e, st) {
      print('initializeProductTaxRelation error: $e');
      print(st);
    }
  }


}

@riverpod
StoreRepository storeRepository(Ref ref) {
  return StoreRepository(ref,stompRequestService: ref.watch(stompRequestServiceProvider));
}