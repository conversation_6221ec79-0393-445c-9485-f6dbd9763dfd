import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import 'package:kpos/features/store/data/store_endpoint.dart';
import 'package:kpos/features/store/domain/service_fee_item.dart';
import 'package:kpos/features/store/domain/tax_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/remote_service/api_interface.dart';
import '../domain/store_prepared_reason_item.dart';
import '../../cart/data/cart_endpoint.dart';

part 'store_intranet_repository.g.dart';

class StoreIntranetRepository {
  final ApiInterface _apiService;

  StoreIntranetRepository({required ApiInterface apiService})
      : _apiService = apiService;

  ///预设原因类型1-折扣原因，2-退货原因，3-备注，4-取消原因，5-服务费取消原因
  Future<List<StorePreparedReasonItem>> getStorePreparedReasonItemList(int type) async {
    return await _apiService.getListData(endpoint: StoreEndpoint.storePreparedReasonList, converter: StorePreparedReasonItem.fromJson,queryParams: {"type":type});
  }

  Future<List<TaxItem>> getStoreTaxList(int productId) async {
    return await _apiService.getListData(endpoint: StoreEndpoint.storeTax, converter: TaxItem.fromJson, queryParams: {"productId":productId});
  }
}

@riverpod
StoreIntranetRepository storeIntranetRepository(Ref ref) {
  return StoreIntranetRepository(apiService: ref.watch(kposApiIntranetServiceProvider));
}
