import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/main_app_bar.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/responsive_center.dart';
import '../../../common/utils/device_util.dart';

class UnbindStoreConfirmScreen extends ConsumerStatefulWidget {
  const UnbindStoreConfirmScreen({super.key});

  @override
  ConsumerState createState() => _UnbindStoreConfirmScreenState();
}

class _UnbindStoreConfirmScreenState extends ConsumerState<UnbindStoreConfirmScreen> {
  @override
  Widget build(BuildContext context) {
    final datas = [
      context.locale.unbindMainPosTip1,
      context.locale.unbindMainPosTip2,
    ];
    return Scaffold(
      appBar: const MainAppBar(),
      backgroundColor: Colors.white,
      body: ResponsiveCenterContainer(child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Column(
                children: [
                  Assets.images.iconUnbindStoreLogo.image(width: 56,height: 56),
                  const SizedBox(height: 16),
                  Text(context.locale.unbindStore,style: const TextStyle(fontSize: 24,fontWeight: FontWeight.w700)),
                ],
              ),
            ),
            const SizedBox(height: 48),
            Text(context.locale.pleaseMakeSure,
                style: TextStyle(
                    fontSize: 16, color: context.theme.grayColor2)),
            const SizedBox(height: 20),
            ListView.builder(
                padding: EdgeInsets.zero,
                itemCount: datas.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context,index) {
                  return _buildItemView(index+1, datas[index]);
                }),
            const SizedBox(height: 28),
            GestureDetector(
              onTap: _onUnbindAction,
              child: Container(
                height: 56,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.black),
                child: Text(context.locale.unbind,
                    style: const TextStyle(
                        color: Colors.white, fontSize: 16)),
              ),
            ),
          ],
        ),
      )),
    );
  }

  Widget _buildItemView(int index, String item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: context.theme.brandNormalColor,
              shape: BoxShape.circle,
            ),
            child: Text(
              '$index',
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
              child: Text(
                item,
                style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 16),
                softWrap: true,
              ))
        ],
      ),
    );
  }

  void _onUnbindAction() {
    UnbindStoreRoute().push(context);
  }
}
