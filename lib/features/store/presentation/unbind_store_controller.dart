import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/constant/pos_type.dart';

part 'unbind_store_controller.g.dart';

@riverpod
class UnbindStoreController extends _$UnbindStoreController {
  @override
  FutureOr<void> build() async {}

  Future<void> unbindDevice({required int deviceId,required PosType type}) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => ref.read(storeServiceProvider).unbindDevice(deviceId: deviceId, type: type));
    if (state.hasError) {
      Error.throwWithStackTrace(state.error!, state.stackTrace ?? StackTrace.current);
    }
  }
}
