import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/pos_type.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'store_bind_device_controller.g.dart';

@riverpod
class StoreBindDeviceController extends _$StoreBindDeviceController {
  @override
  FutureOr<void> build() async {}

  Future<void> bindDevice({int deviceId = 0,required PosType type}) async {
    state = const AsyncValue.loading();
    final boundStore =  ref.read(storeServiceProvider).boundStoreInfo;
    state = await AsyncValue.guard(() => ref.read(storeServiceProvider).insertDevice(deviceId: deviceId,type: type, storeId: boundStore.storeId));
    if (state.hasError) {
      Error.throwWithStackTrace(state.error!, state.stackTrace ?? StackTrace.current);
    }
  }

  Future<void> bindExistMainDevice(int deviceId) async {
    state = const AsyncValue.loading();
    try {
      await ref.read(storeServiceProvider).unbindDevice(
        deviceId: deviceId,
        type: PosType.main,
      );
      final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
      await ref.read(storeServiceProvider).insertDevice(
        type: PosType.main,
        storeId: boundStore.storeId,
      );
      state = const AsyncValue.data(null);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
      Error.throwWithStackTrace(e, st);
    }
  }

}
