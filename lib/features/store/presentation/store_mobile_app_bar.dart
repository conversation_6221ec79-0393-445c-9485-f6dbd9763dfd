import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/presentation/store_menu_screen.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';

class StoreMobileAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const StoreMobileAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final boundStore = ref.read(storeServiceProvider).boundStoreInfo;
    return AppBar(
      backgroundColor: Colors.white,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      title: Row(
        children: [
          Assets.images.iconTest1.image(width: 24, height: 24),
          const SizedBox(width: 10),
          Expanded(
              child: Text(
            '${context.locale.welcome},${boundStore.brandName}',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w700),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          )),
        ],
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 20),
          child: GestureDetector(
            onTap: () {
              KPPopover.showPopover(
                showArrow: true,
                context: context,
                width: 240,
                height: 170,
                contentWidget: const StoreMenuScreen(),
                placement: TDPopoverPlacement.bottomRight,
              );
            },
            child: Container(
              width: 32,
              height: 32,
              alignment: Alignment.center,
              child: Assets.images.iconMenu.svg(
                width: 18,
                height: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}
