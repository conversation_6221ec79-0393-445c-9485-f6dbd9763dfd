import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/components/button/kp_loading_button.dart';
import 'package:kpos/common/components/kp_toast.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/common/constant/pos_type.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/auth/application/auth_service.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/presentation/unbind_store_controller.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/form_fields/kp_form_builder_text_field.dart';
import '../../../common/components/main_app_bar.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/extension/string_extension.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';

class UnbindStoreScreen extends ConsumerStatefulWidget {
  const UnbindStoreScreen({super.key});

  @override
  ConsumerState createState() => _UnbindStoreScreenState();
}

class _UnbindStoreScreenState extends ConsumerState<UnbindStoreScreen> {

  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final FocusNode _accountFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final ValueNotifier<bool> _obscureNotifier = ValueNotifier(true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const MainAppBar(),
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: ResponsiveCenterContainer(
          padding: const EdgeInsets.only(top: 0),
          child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(top: 60),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(context.locale.unbindPosTitle,
                  style:
                      const TextStyle(fontSize: 24, fontWeight: FontWeight.w700)),
              const SizedBox(height: 24),
              KPFormBuilderTextField(
                name: "account",
                focusNode: _accountFocusNode,
                controller: _accountController,
                label: RichText(
                  text: TextSpan(
                    text: context.locale.email,
                    style:
                        TextStyle(color: context.theme.grayColor2, fontSize: 16),
                    children: [
                      TextSpan(
                        text: '*',
                        style: TextStyle(
                            color: context.theme.warningColor1, fontSize: 16),
                      ),
                    ],
                  ),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  return (value?.isCorrectEmail ?? true)
                      ? null
                      : context.locale.accountInvalidate;
                },
              ),
              const SizedBox(height: 24),
              ValueListenableBuilder(
                  valueListenable: _obscureNotifier,
                  builder: (context, obscure, _) {
                    return KPFormBuilderTextField(
                      name: "password",
                      focusNode: _passwordFocusNode,
                      controller: _passwordController,
                      autoValidate: true,
                      label: RichText(
                        text: TextSpan(
                          text: context.locale.password,
                          style: TextStyle(
                              color: context.theme.grayColor2, fontSize: 16),
                          children: [
                            TextSpan(
                              text: '*',
                              style: TextStyle(
                                  color: context.theme.warningColor1,
                                  fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                      keyboardType: TextInputType.text,
                      suffixIcon: IconButton(
                        iconSize: 18,
                        icon: obscure
                            ? Assets.images.iconEyeOff.svg()
                            : Assets.images.iconEyeOn.svg(),
                        onPressed: () {
                          _obscureNotifier.value = !_obscureNotifier.value;
                        },
                      ),
                      obscureText: obscure,
                    );
                  }),
              const SizedBox(height: 24),
              KPLoadingButton(
                      onPressed: () => _onConfirmAction(ref),
                      text: context.locale.confirm,
                      isLoading:
                          ref.watch(unbindStoreControllerProvider).isLoading)
                ],
          ),
        ),
      )),
    );
  }

  void _onConfirmAction(WidgetRef ref) async {
    final ctx = context;
    FocusScope.of(ctx).unfocus();
    final account = _accountController.text;
    if (!account.isCorrectEmail) {
      return;
    }
    final currentDevice = ref.read(deviceServiceProvider).currentDeviceInfo;
    ref
        .read(unbindStoreControllerProvider.notifier)
        .unbindDevice(deviceId: currentDevice.physicalDeviceId, type: currentDevice.type)
        .then((value) async {
       await ref.read(authServiceProvider).clearAuthToStorageService();
       if (!ctx.mounted) return;
       if (currentDevice.type == PosType.sub) {
         KPToast.show(content: ctx.locale.unbindPosSuccess,duration: const Duration(seconds: 2),onDismiss: () {
           LaunchRoute().go(ctx);
         });
       } else {
         KPToast.show(content: ctx.locale.unbindSuccess,duration: const Duration(seconds: 2),onDismiss: () {
           LaunchRoute().go(ctx);
         });
       }
    }).onError((error, stack) {
      String errorMessage = error.toString();
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.replaceAll('Exception: ', '');
      }
      if (!ctx.mounted) return;
      KPToast.show(content: errorMessage);
    });
  }
}
