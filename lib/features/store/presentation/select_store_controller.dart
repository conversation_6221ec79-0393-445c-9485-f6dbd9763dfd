import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/data/store_repository.dart';
import 'package:kpos/features/store/domain/brand_item.dart';
import 'package:kpos/features/store/domain/store_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'select_store_controller.g.dart';

@riverpod
class SelectStoreController extends _$SelectStoreController {
  @override
  FutureOr<List<StoreItem>> build() async {
    final brandId = ref.watch(selectBrandItemProvider)?.brandId;
    return _fetch(brandId);
  }

  Future<List<StoreItem>> _fetch(int? brandId) async {
    if (brandId == null) return [];
    return await ref.read(storeRepositoryProvider).getStoreList(brandId);
  }
}

@riverpod
class SelectBrandController extends _$SelectBrandController {
  @override
  FutureOr<List<BrandItem>> build() async {
    return _fetch();
  }

  Future<List<BrandItem>> _fetch() async{
    return await ref.read(storeRepositoryProvider).getBrandList();
  }
}

@riverpod
class SelectBrandItem extends _$SelectBrandItem {
  @override
  BrandItem? build() {
    ref.listen(selectBrandControllerProvider, _onAllBrandChanged);
    return null;
  }

  void _onAllBrandChanged(AsyncValue<List<BrandItem>>? previous, AsyncValue<List<BrandItem>>? next) {
    if (state != null) return;
    if (next != null && next.hasValue && next.value!.isNotEmpty) {
      final defaultBrand = next.value!.first;
      state = defaultBrand;
    }
  }

  void changeBrandItem(BrandItem item) {
    state = item;
  }
}
