import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/common/utils/device_util.dart';
import 'package:kpos/features/auth/presentation/auth_app_bar.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/device/presentation/choose_device_screen.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/domain/bound_store_info.dart';
import 'package:kpos/features/store/domain/brand_item.dart';
import 'package:kpos/features/store/domain/store_item.dart';
import 'package:kpos/features/store/presentation/select_store_controller.dart';
import 'package:kpos/features/store/presentation/store_mobile_app_bar.dart';
import 'package:kpos/routing/pages_route.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../assets/assets.gen.dart';
import '../../../common/components/index.dart';
import '../../../common/constant/pos_type.dart';

class SelectStoreScreen extends ConsumerStatefulWidget {
  const SelectStoreScreen({super.key});

  @override
  ConsumerState createState() => _SelectStoreScreenState();
}

class _SelectStoreScreenState extends ConsumerState<SelectStoreScreen> {
  final showPopoverNotifier = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final selectBrand = ref.watch(selectBrandItemProvider);
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: DeviceUtil.isMobile()
          ? const StoreMobileAppBar()
          : const AuthAppBar(showLeading: false),
      body: ResponsiveCenterContainer(
          child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildTopView(selectBrand),
              const SizedBox(height: 32),
              _buildBrandView(context,selectBrand),
              const SizedBox(height: 4),
              _buildStoreView()
            ],
          ),
        ),
      )),
    );
  }

  Widget _buildTopView(BrandItem? selectBrand) {
    return DeviceUtil.isMobile()
        ? Container(
            padding: const EdgeInsets.only(top: 40),
            alignment: Alignment.centerLeft,
            child: Text(
              context.locale.associateStoreTip,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          )
        : Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        selectBrand == null
            ? Assets.images.iconTest1
            .image(width: 48, height: 48, fit: BoxFit.cover)
            : CachedNetworkImage(
          imageUrl: selectBrand.logoImageUrl,
          width: 48,
          height: 48,
          fit: BoxFit.cover,
          errorWidget: (context, url, error) => Assets
              .images.iconTest1
              .image(width: 24, height: 24, fit: BoxFit.cover),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${context.locale.welcome}, 租户1",
                style: TextStyle(
                    color: context.theme.grayColor2, fontSize: 16),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                context.locale.associateStoreTip,
                style: const TextStyle(
                    fontSize: 20, fontWeight: FontWeight.w700),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildBrandView(BuildContext context,BrandItem? selectBrand) {
    return LayoutBuilder(builder: (context, constrains) {
      return GestureDetector(
        onTap: () => _selectBrand(context),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              selectBrand == null
                  ? Assets.images.iconTest1
                  .image(width: 24, height: 24, fit: BoxFit.cover)
                  : CachedNetworkImage(
                imageUrl: selectBrand.logoImageUrl,
                width: 24,
                height: 24,
                fit: BoxFit.cover,
                errorWidget: (context, url, error) => Assets
                    .images.iconTest1
                    .image(width: 24, height: 24, fit: BoxFit.cover),
              ),
              const SizedBox(width: 10),
              Text(selectBrand != null ? selectBrand.brandName : "请选择品牌",
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w700)),
              const SizedBox(width: 10),
              ValueListenableBuilder(
                  valueListenable: showPopoverNotifier,
                  builder: (context, show, _) {
                    return show
                        ? Assets.images.iconArrowUp.svg()
                        : Assets.images.iconArrowDown.svg();
                  })
            ],
          ),
        ),
      );
    });
  }

  Widget _buildStoreView() {
    return KPAsyncValueWidget(
      asyncValueProvider: selectStoreControllerProvider,
      dataBuilder: (context,ref,data) {
        return ListView.separated(
              itemBuilder: (context, index) {
                return _buildStoreItemView(data[index], ref);
              },
              separatorBuilder: (context, index) {
                return const SizedBox(height: 12);
              },
              itemCount: data.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics());
        }
    );
  }

  Widget _buildStoreItemView(StoreItem item, WidgetRef ref) {
    return InkWell(
      onTap: () => _selectStore(item),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: context.theme.grayColor3, width: 1)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Text(item.storeName,
                    softWrap: true, style: const TextStyle(fontSize: 16)),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                item.storeIsBindDevice == 1 ? const SizedBox():Text(context.locale.notBound,
                    style: const TextStyle(fontSize: 14, color: Colors.blue)),
                const SizedBox(width: 8),
                Assets.images.iconChevronRight.svg(),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBrandListPopover(List<BrandItem> data) {
    final bottomH = MediaQuery.of(context).viewPadding.bottom;
    var containH = data.length * 56 > 400 ? 400 : data.length * 56;
    final brandItem = ref.read(selectBrandItemProvider);
    return Material(
      borderRadius: BorderRadius.circular(8),
      clipBehavior: Clip.antiAlias,
      child: Container(
        height: DeviceUtil.isMobile() ? containH + bottomH : null,
        margin: DeviceUtil.isMobile()
            ? EdgeInsets.only(bottom: bottomH)
            : null,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
        child: LayoutBuilder(
          builder: (context, constraints) {
            return ListView.separated(
                padding: EdgeInsets.zero,
                itemCount: data.length,
                separatorBuilder: (context, index) => const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Divider(
                    height: 1,
                    color: KPColors.borderGrayLightBase,
                  ),
                ),
                itemBuilder: (context, index) {
                  final item = data[index];
                  return Container(
                    padding: const EdgeInsets.only(left: 10),
                    child: SizedBox(
                      height: 56,
                      child: GestureDetector(
                        onTap: () {
                          ref.read(selectBrandItemProvider.notifier).changeBrandItem(item);
                          Navigator.pop(context);
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: CachedNetworkImage(
                                imageUrl: item.logoImageUrl,
                                width: 24,
                                height: 24,
                                fit: BoxFit.cover,
                                errorWidget: (context, url, error) => Assets.images.iconTest1
                                    .image(
                                    width: 24,
                                    height: 24,
                                    fit: BoxFit.cover),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item.brandName,
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                            if (brandItem?.brandId == item.brandId)
                              Padding(
                                padding: const EdgeInsets.only(right: 20),
                                child: Icon(
                                  Icons.check,
                                  color: context.theme.brandNormalColor,
                                  size: 24,
                                ),
                              )
                          ],
                        ),
                      ),
                    ),
                  );
                });
          },
        ),
      ),
    );
  }

  void _selectStore(StoreItem item) async {
    final brandItem = ref.read(selectBrandItemProvider);
    final boundStoreInfo = BoundStoreInfo(storeId: item.storeId,storeName: item.storeName);
    if (brandItem != null) {
      boundStoreInfo.brandId = brandItem.brandId;
      boundStoreInfo.brandName = brandItem.brandName;
      boundStoreInfo.logoImageUrl = brandItem.logoImageUrl;
    }
    await ref.read(storeServiceProvider).syncStoreToStorageService(boundStoreInfo);
    final ct = context;
    if (item.storeIsBindDevice == 0) {
      await ref
          .read(storeServiceProvider)
          .insertDevice(type: PosType.main, storeId: item.storeId)
          .then((value) {
        if (!ct.mounted) return;
        const ConnectDeviceRoute(type: PosType.main).push(ct);
      }).onError((error, stack) {
        String errorMessage = error.toString();
        if (errorMessage.startsWith('Exception: ')) {
          errorMessage = errorMessage.replaceAll('Exception: ', '');
        }
        if (!ct.mounted) return;
        KPToast.show(content: errorMessage);
      });
    } else {
      if (!ct.mounted) return;
      if (DeviceUtil.isMobile()) {
        const ConnectDeviceRoute(type: PosType.main).push(ct);
      } else {
        ChooseDeviceRoute().push(ct);
      }
    }
  }

  void _selectBrand(BuildContext context) {
    final value = ref.watch(selectBrandControllerProvider);
    value.maybeWhen(
      data: (data) {
        if (DeviceUtil.isMobile()) {
          KPSlidePopup.showSlidePopup(
              context: context,
              contentWidget: _buildBrandListPopover(data)
          );
        } else {
          showPopoverNotifier.value = true;
          KPPopover.showPopover(
            context: context,
            width: 375,
            height: data.length*56 > 400 ? 400 : data.length*56,
            contentWidget: _buildBrandListPopover(data),
            onComplete: (value) {
              showPopoverNotifier.value = false;
            },
          );
        }
      },
      orElse: () => const CircularProgressIndicator(),
    );
  }
}
