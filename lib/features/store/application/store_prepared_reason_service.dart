import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/store_intranet_repository.dart';
import '../domain/store_prepared_reason_item.dart';

part 'store_prepared_reason_service.g.dart';

/// 预设原因类型枚举
enum PreparedReasonType {
  discount(1),//折扣原因
  returnGoods(2),//退货原因
  remark(3),//备注
  cancel(4),//取消原因
  serviceFeeCancel(5);//服务费取消原因

  const PreparedReasonType(this.value);
  final int value;
}

/// 预设原因数据服务
class StorePreparedReasonService {
  final StoreIntranetRepository _repository;

  StorePreparedReasonService({required StoreIntranetRepository repository})
      : _repository = repository;

  /// 获取指定类型的预设原因列表
  Future<List<StorePreparedReasonItem>> getPreparedReasonList(PreparedReasonType type) async {
    return await _repository.getStorePreparedReasonItemList(type.value);
  }

  /// 获取指定类型的预设原因列表（通过类型值）
  Future<List<StorePreparedReasonItem>> getPreparedReasonListByValue(int typeValue) async {
    return await _repository.getStorePreparedReasonItemList(typeValue);
  }

  /// 获取所有类型的预设原因列表
  Future<Map<PreparedReasonType, List<StorePreparedReasonItem>>> getAllPreparedReasons() async {
    final Map<PreparedReasonType, List<StorePreparedReasonItem>> result = {};
    
    for (final type in PreparedReasonType.values) {
      try {
        final reasons = await _repository.getStorePreparedReasonItemList(type.value);
        result[type] = reasons;
      } catch (e) {
        // 如果某个类型获取失败，设置为空列表
        result[type] = [];
      }
    }
    
    return result;
  }

  /// 根据ID查找预设原因
  Future<StorePreparedReasonItem?> findPreparedReasonById(
    PreparedReasonType type,
    int reasonId,
  ) async {
    final reasons = await getPreparedReasonList(type);
    try {
      return reasons.firstWhere((reason) => reason.preparedReasonId == reasonId);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称查找预设原因
  Future<StorePreparedReasonItem?> findPreparedReasonByName(
    PreparedReasonType type,
    String reasonName,
  ) async {
    final reasons = await getPreparedReasonList(type);
    try {
      return reasons.firstWhere((reason) => reason.preparedReasonName == reasonName);
    } catch (e) {
      return null;
    }
  }
}

/// Riverpod Provider for StorePreparedReasonService
@riverpod
StorePreparedReasonService storePreparedReasonService(Ref ref) {
  return StorePreparedReasonService(
    repository: ref.watch(storeIntranetRepositoryProvider),
  );
}



/// 折扣原因列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> discountReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(PreparedReasonType.discount);
}

/// 退货原因列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> returnReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(PreparedReasonType.returnGoods);
}

/// 备注列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> remarkReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(PreparedReasonType.remark);
}

/// 取消原因列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> cancelReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(PreparedReasonType.cancel);
}

/// 服务费取消原因列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> serviceFeeCancelReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(PreparedReasonType.serviceFeeCancel);
}

/// 所有预设原因列表 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<Map<PreparedReasonType, List<StorePreparedReasonItem>>> allPreparedReasons(Ref ref) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getAllPreparedReasons();
}

/// 根据类型获取预设原因列表的通用 Provider - 直接请求，但会被缓存
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> preparedReasonsByType(
  Ref ref,
  PreparedReasonType type,
) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonList(type);
}

/// 根据类型值获取预设原因列表的通用 Provider
@Riverpod(keepAlive: true)
Future<List<StorePreparedReasonItem>> preparedReasonsByTypeValue(
  Ref ref,
  int typeValue,
) {
  final service = ref.watch(storePreparedReasonServiceProvider);
  return service.getPreparedReasonListByValue(typeValue);
}