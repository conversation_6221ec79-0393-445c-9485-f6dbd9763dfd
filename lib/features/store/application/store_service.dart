import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/common/constant/enum/kp_currency.dart';
import 'package:kpos/common/constant/pos_type.dart';
import 'package:kpos/common/services/web_socket/stomp/stomp_status_code.dart';
import 'package:kpos/features/device/application/device_service.dart';
import 'package:kpos/features/device/domain/current_device_info.dart';
import 'package:kpos/features/store/data/store_repository.dart';
import 'package:kpos/features/store/domain/bound_store_info.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import '../data/store_local_repository.dart';

part 'store_service.g.dart';

class StoreService {
  late final Ref _ref;
  late BoundStoreInfo boundStoreInfo;

  StoreService({required Ref ref}) {
    _ref = ref;
    boundStoreInfo = BoundStoreInfo(); // 初始化
  }

  Future<void> init() async {
    try {
      final keyValueStorageService = _ref.read(keyValueStorageServiceProvider);
      final jsonString = await keyValueStorageService.getBoundStore();
      if (jsonString != null) {
        Map<String, dynamic> jsonMap = jsonDecode(jsonString);
        boundStoreInfo = BoundStoreInfo.fromJson(jsonMap);
        final storeLocal = StoreLocalRepository(_ref.watch(databaseProvider));
        final storeData = await storeLocal.getStoreDetail(boundStoreInfo.storeId);
        if (storeData != null) {
          if (storeData["currency"] != null) {
            final currencyCode = storeData["currency"] as String?;
            boundStoreInfo.currentUnit = KPCurrency.symbolByCode(currencyCode!); 
          }
          if (storeData["organization_id"] != null) {
            final organizationId = storeData["organization_id"] as int;
            boundStoreInfo.organizationId = organizationId;
          }  
        }
      }
    } catch (_) {}
  }

  Future<void> syncStoreToStorageService(BoundStoreInfo boundStoreInfo) async {
    final keyValueStorageService = _ref.read(keyValueStorageServiceProvider);
    try {
      await keyValueStorageService.setBoundStore(jsonEncode(boundStoreInfo.toJson()));
      this.boundStoreInfo = boundStoreInfo;
    } catch (e, st) {
      print('保存 boundStore 失败: $e');
      print('堆栈信息: $st');
    }
  }

  Future<void> clearStoreInStorageService() async {
    final keyValueStorageService = _ref.read(keyValueStorageServiceProvider);
    await keyValueStorageService.resetKeys();
  }

  Future<void> insertDevice({
    int deviceId = 0,
    required PosType type,
    required int storeId,
  }) async {
    try {
      final response = await _ref
          .read(storeRepositoryProvider)
          .insertDevice(deviceId: deviceId,type: type, storeId: storeId);
      if (response.code == StompStatusCode.success) {
        final deviceInfo = CurrentDeviceInfo.fromJson(response.data);
        deviceInfo.type = type;
        await _ref
            .read(deviceServiceProvider)
            .syncDeviceToStorageService(deviceInfo);
        await AppDatabase.deleteDatabaseFile();
      } else {
        throw Exception(response.message);
      }
    } catch (e, stackTrace) {
      throw Exception(e);
    }
  }

  Future<void> unbindDevice({
    required int deviceId,
    required PosType type,
  }) async {
    try {
      final response = await _ref
          .read(storeRepositoryProvider)
          .unbindDevice(deviceId: deviceId,type: type);
      if (response.code != StompStatusCode.success) {
        throw Exception(response.message);
      }
    } catch (e, stackTrace) {
      throw Exception(e);
    }
  }
}

@Riverpod(keepAlive: true)
StoreService storeService(Ref ref) {
  return StoreService(ref: ref);
}
