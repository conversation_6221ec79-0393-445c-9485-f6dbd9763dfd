import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'store_prepared_reason_service.dart';
import '../domain/store_prepared_reason_item.dart';

part 'store_prepared_reason_cache.g.dart';

/// 预设原因数据缓存服务
/// 提供同步访问已缓存数据的方法
class StorePreparedReasonCache {
  final Ref _ref;

  StorePreparedReasonCache({required Ref ref}) : _ref = ref;

  /// 获取折扣原因列表（同步，从缓存）
  List<StorePreparedReasonItem>? getDiscountReasons() {
    final state = _ref.read(discountReasonsProvider);
    return state.value;
  }

  /// 获取退货原因列表（同步，从缓存）
  List<StorePreparedReasonItem>? getReturnReasons() {
    final state = _ref.read(returnReasonsProvider);
    return state.value;
  }

  /// 获取备注列表（同步，从缓存）
  List<StorePreparedReasonItem>? getRemarkReasons() {
    final state = _ref.read(remarkReasonsProvider);
    return state.value;
  }

  /// 获取取消原因列表（同步，从缓存）
  List<StorePreparedReasonItem>? getCancelReasons() {
    final state = _ref.read(cancelReasonsProvider);
    return state.value;
  }

  /// 获取服务费取消原因列表（同步，从缓存）
  List<StorePreparedReasonItem>? getServiceFeeCancelReasons() {
    final state = _ref.read(serviceFeeCancelReasonsProvider);
    return state.value;
  }

  /// 根据类型获取原因列表（同步，从缓存）
  List<StorePreparedReasonItem>? getReasonsByType(PreparedReasonType type) {
    final state = _ref.read(preparedReasonsByTypeProvider(type));
    return state.value;
  }

  /// 检查指定类型的数据是否已加载
  bool isDataLoaded(PreparedReasonType type) {
    final state = _ref.read(preparedReasonsByTypeProvider(type));
    return state.hasValue;
  }

  /// 检查所有数据是否已加载
  bool isAllDataLoaded() {
    return isDataLoaded(PreparedReasonType.discount) &&
           isDataLoaded(PreparedReasonType.returnGoods) &&
           isDataLoaded(PreparedReasonType.remark) &&
           isDataLoaded(PreparedReasonType.cancel) &&
           isDataLoaded(PreparedReasonType.serviceFeeCancel);
  }

  /// 强制刷新指定类型的数据
  void refreshData(PreparedReasonType type) {
    _ref.refresh(preparedReasonsByTypeProvider(type));
  }

  /// 强制刷新所有数据
  void refreshAllData() {
    _ref.refresh(discountReasonsProvider);
    _ref.refresh(returnReasonsProvider);
    _ref.refresh(remarkReasonsProvider);
    _ref.refresh(cancelReasonsProvider);
    _ref.refresh(serviceFeeCancelReasonsProvider);
  }
}

/// Riverpod Provider for StorePreparedReasonCache
@riverpod
StorePreparedReasonCache storePreparedReasonCache(Ref ref) {
  return StorePreparedReasonCache(ref: ref);
}

/// 缓存状态 Provider
@riverpod
class CacheState extends _$CacheState {
  @override
  Map<PreparedReasonType, bool> build() {
    return {
      PreparedReasonType.discount: false,
      PreparedReasonType.returnGoods: false,
      PreparedReasonType.remark: false,
      PreparedReasonType.cancel: false,
      PreparedReasonType.serviceFeeCancel: false,
    };
  }

  /// 更新指定类型的缓存状态
  void updateCacheState(PreparedReasonType type, bool isLoaded) {
    state = {...state, type: isLoaded};
  }

  /// 检查指定类型是否已缓存
  bool isTypeCached(PreparedReasonType type) {
    return state[type] ?? false;
  }

  /// 检查所有类型是否都已缓存
  bool isAllCached() {
    return state.values.every((isCached) => isCached);
  }
} 