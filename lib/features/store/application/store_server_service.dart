import 'dart:convert';
import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import '../../../common/services/networking/intranet_service/api_intranet_exception.dart';
import '../../../common/services/networking/intranet_service/api_intranet_localization.dart';
import '../../../common/services/networking/intranet_service/api_intranet_response.dart';
import '../data/store_local_repository.dart';
part 'store_server_service.g.dart';
class StoreServerService {
  final Ref _ref;

  StoreServerService({required Ref ref}) : _ref = ref;

  Locale get currentLocale => _ref.read(languageSettingsServiceProvider).currentLocale;

  Future<Response> getStorePreparedReasonList(Request request) async {
    try {
      final payload = await request.readAsString();
      Map<String, dynamic> data = {};
      if (payload.isNotEmpty) {
        data = jsonDecode(payload);
      }

      final storeLocal = StoreLocalRepository(_ref.watch(databaseProvider));
      List<Map<String, dynamic>> list;
      if (data["type"] != null) {
        list = await storeLocal.getStorePreparedReasonList(type: data["type"] as int);
      } else {
        list = await storeLocal.getStorePreparedReasonList();
      }

      return ApiIntranetResponse.success(list);
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }


  Future<Response> getStoreServerService(Request request) async {
    try {
      final storeLocal = StoreLocalRepository(_ref.watch(databaseProvider));
      final list = await storeLocal.getServerService();
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getTax(Request request) async {
    try {
      final payload = await request.readAsString();
      final data = jsonDecode(payload);
      final int leafProductId = int.tryParse(data['productId'].toString()) ?? 0;
      final storeLocal = StoreLocalRepository(_ref.watch(databaseProvider));
      final list = await storeLocal.getTax(leafProductId);
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getServiceFeeList(Request request) async {
    try {
      final storeLocal = StoreLocalRepository(_ref.watch(databaseProvider));
      final list = await storeLocal.getServiceFeeList();
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }
}

@riverpod
StoreServerService storeServerService(Ref ref) {
  return StoreServerService(ref: ref);
}