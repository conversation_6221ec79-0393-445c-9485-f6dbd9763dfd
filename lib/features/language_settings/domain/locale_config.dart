import 'dart:ui';

import 'package:kpos/assets/kp_locale.g.dart';

class LocaleConfig {
  static const _zhSG = Locale('zh','SG');
  static const _zhHK = Locale('zh','HK');
  static const _en = Locale('en');

  static const List<Locale> supportedLocales = <Locale>[
    _zhSG,
    _zhHK,
    _en,
  ];
}

extension LocaleDisplay on Locale {
  String localeName(KPLocale locale) {
    switch(this) {
      case LocaleConfig._zhSG:
        return locale.languageZhSG;
      case LocaleConfig._zhHK:
        return locale.languageZhHK;
      case LocaleConfig._en:
        return locale.languageEn;
    }
    return toString();
  }
}