import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/list_container.dart';
import 'package:kpos/common/services/language_settings_service/language_settings_service.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';
import 'package:kpos/features/language_settings/presentation/locale_item_view.dart';

class LanguageSettingsScreen extends ConsumerStatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  ConsumerState createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState
    extends ConsumerState<LanguageSettingsScreen> {
  final List<Locale> _locales = LocaleConfig.supportedLocales;

  late final ValueNotifier<Locale?> _currentLanguageController;

  @override
  void initState() {
    final locale = ref.read(currentLocaleProvider);
    print(111111);
    _currentLanguageController = ValueNotifier(locale);
    super.initState();
  }

  @override
  void dispose() {
    _currentLanguageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F2F4),
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(KPLocale.of(context).shortcutLanguage),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          clipBehavior: Clip.antiAliasWithSaveLayer,
          child: ListContainer.separated(
              itemCount: _locales.length,
              itemBuilder: (ct, index) {
                return LocaleItemView(
                    locale: _locales[index],
                    controller: _currentLanguageController,
                    onTap: (value) => _onItemViewTap(context, locale: value));
              }),
        ),
      ),
    );
  }

  Future<void> _onItemViewTap(BuildContext context,
      {required Locale locale}) async {
    _currentLanguageController.value = locale;
    try {
      await ref.read(languageSettingsServiceProvider).setLanguage(locale);
    } catch (error) {
      _currentLanguageController.value =
          ref.read(languageSettingsServiceProvider).currentLocale;
    }
  }
}
