import 'package:flutter/material.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/features/language_settings/domain/locale_config.dart';

class LocaleItemView extends StatelessWidget {
  final Locale locale;

  final ValueNotifier<Locale?> controller;

  final ValueChanged<Locale> onTap;

  const LocaleItemView(
      {super.key,
      required this.locale,
      required this.controller,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onTap(locale),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 16),
        constraints: const BoxConstraints(minHeight: 56),
        child: Row(
          children: [
            Expanded(child: Text(locale.localeName(KPLocale.of(context)))),
            ValueListenableBuilder(
                valueListenable: controller,
                builder: (_, current, __) {
                  return Radio(
                      value: locale,
                      groupValue: current,
                      onChanged: (value) => onTap(value!));
                })
          ],
        ),
      ),
    );
  }
}
