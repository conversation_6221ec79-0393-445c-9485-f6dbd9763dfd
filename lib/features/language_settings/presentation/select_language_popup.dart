import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../domain/locale_config.dart';

class SelectLanguagePopup extends ConsumerWidget {

  final bool? isSecondPop;

  final ValueChanged<Locale>? onLocaleSelected;

  const SelectLanguagePopup({
    super.key,
    this.isSecondPop = false,
    this.onLocaleSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const locales = LocaleConfig.supportedLocales;
    final currentLanguageController =
    ValueNotifier(ref.read(currentLocaleProvider));
    final bottomH = isSecondPop == true ? 0 : MediaQuery.of(context).viewPadding.bottom;
    const  containH = 156.0;
    return Material(
      borderRadius: BorderRadius.circular(8),
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8),color: Colors.white),
        height: bottomH+containH,
        child: LayoutBuilder(
          builder: (context, constraints) {
            const totalHeight = containH;
            const separatorHeight = 1.0;
            final itemCount = locales.length;
            final totalSeparatorsHeight =
                separatorHeight * (itemCount - 1);
            final itemHeight =
                (totalHeight - totalSeparatorsHeight) / itemCount;
            return ListView.separated(
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              itemCount: itemCount,
              separatorBuilder: (context, index) => const Divider(
                height: 0.5,
                indent: 20,
                color: KPColors.borderGrayLightBase,
              ),
              itemBuilder: (context, index) => GestureDetector(
                onTap: () => _onLocaleItemTap(
                    context, locales[index], ref),
                child: Container(
                  color: Colors.white,
                  height: itemHeight,
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.only(left: 20),
                  child: Row(
                    mainAxisAlignment:
                    MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          locales[index].localeName(context.locale),
                          style: const TextStyle(
                              color: Colors.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w400),
                          softWrap: true,
                        ),
                      ),
                      if (locales[index]
                          .localeName(context.locale) ==
                          currentLanguageController.value
                              ?.localeName(context.locale))
                        Padding(
                          padding:
                          const EdgeInsets.only(right: 20),
                          child: Icon(
                            Icons.check,
                            color: context.theme.brandNormalColor,
                            size: 24,
                          ),
                        )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  _onLocaleItemTap(BuildContext context, Locale locale, WidgetRef ref) {
    ref.read(languageSettingsServiceProvider).setLanguage(locale);
    onLocaleSelected?.call(locale);
    Navigator.of(context).pop();
  }
}
