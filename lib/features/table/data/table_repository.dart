import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/web_socket/stomp/stomp_destinations.dart';
import '../../../common/services/web_socket/stomp/stomp_request_service.dart';
import '../../../common/utils/kp_list_util.dart';
part 'table_repository.g.dart';

class TableRepository {

  final StompRequestService _stompRequestService;
  final Ref _ref;
  TableRepository(this._ref, {required StompRequestService stompRequestService})
      : _stompRequestService = stompRequestService;

  // 桌台列表数据初始化
  Future<void> initializeStoreTablesList() async {
    final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
    final jsonList = await _stompRequestService.requestListData(
      sendDestination: StompDestinations.storeTables,
      subscribeDestination: StompDestinations.storeTableSub,
      body: {"storeId": boundStore.storeId, "rows": 50}, itemParser: (json) => json as Map<String, dynamic>,
      isPaged: true
    );

    if (jsonList.isEmpty) return;

    final db = _ref.watch(databaseProvider);
    // ✅ 这里先定义两个 List 来拆数据
    final List<Map<String, dynamic>> areaList = [];
    final List<Map<String, dynamic>> diningTableInfoList = [];

    try {

      for (var item in jsonList) {
        // 分组信息
        areaList.add({
          "areaName": item['areaName'],
          "areaId": item['areaId'],
          "storeId": item['storeId'],
          "sort": item['sort'],
        });
        final List<dynamic> tables = KPListUtil.safeList(item['tableList']);
        for (var p in tables) {
          diningTableInfoList.add(p as Map<String, dynamic>);
        }
      }


      final areaMap = <int, AreaInfosCompanion>{};
      final diningTableInfosMap = <String, DiningTableInfosCompanion>{};

      for (var item in areaList) {
        final areaCompanion = AreaInfosCompanion.insert(
            areaName: item['areaName'] as String,
            areaId: Value(item['areaId'] as int),
            storeId: item['storeId'] as int,
            sort: Value(item['sort'] as int));
        areaMap[item['areaId'] as int] = areaCompanion;
        final tableList = KPListUtil.safeList(item['tableList']);
        for (var tableItem in diningTableInfoList) {
          final tableCompanion = DiningTableInfosCompanion.insert(
            seatedNumber: tableItem['seatedNumber'] as int,
            tableTitle: Value(tableItem['tableTitle'] as String),
            areaId: tableItem['areaId'] as int,
            tableShape: tableItem['tableShape'] as int,
            tableHeight: Value(tableItem['tableHeight'].toDouble()),
            tableWidth: Value(tableItem['tableWidth'].toDouble()),
            nodeX: Value(tableItem['nodeX'].toDouble()),
            nodeY: Value(tableItem['nodeY'].toDouble()),
            sort: Value(tableItem['sort'] as int),
          );
          diningTableInfosMap[tableItem['tableTitle'] as String] = tableCompanion;
        }
      }

      // 批量插入
      try {
        if (areaMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.areaInfos, areaMap.values.toList());
        }

        if (diningTableInfosMap.isNotEmpty) {
          await db.insertOrReplaceBatch(
              db.diningTableInfos, diningTableInfosMap.values.toList());
        }

        print('✅  Successfully inserted: '
            '${areaMap.length}  area, '
            '${diningTableInfosMap.length} dining table infos');
      } catch (e, st) {
        print('Batch insert failed: $e\n$st');
      }
    } catch (e, st) {
      print('Error processing area, dining table infos data: $e\n$st');
    }
  }

}

@riverpod
TableRepository tableRepository(Ref ref) {
  return TableRepository(ref,stompRequestService: ref.watch(stompRequestServiceProvider));
}