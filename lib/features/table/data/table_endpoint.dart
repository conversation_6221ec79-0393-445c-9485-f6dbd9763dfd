import '../../../common/services/networking/remote_service/api_endpoint.dart';

enum TableEndpoint implements ApiEndpoint {

  //获取所有桌台信息
  tableInfos("/table/infos"),
  //转台
  transferTable("/transfer/table"),
  //联台
  mergeTable("/merge/table"),
  //拼桌
  shareTable("/share/table"),
  //获取拼桌订单数据
  getShareTableOrders("/get/share/table/orders"),
  //撤台
  cancelTable("/cancel/table"),
  //清台
  cleanTable("/clean/table");

  const TableEndpoint(this.path);

  @override
  final String path;

}