import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/table/data/table_endpoint.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/services/networking/intranet_service/kpos_api_intranet_service.dart';
import '../../../common/services/networking/remote_service/api_interface.dart';
import '../domain/table_area_list_item.dart';

part 'table_intranet_repository.g.dart';

class TableIntranetRepository {

  final ApiInterface _apiService;

  TableIntranetRepository({required ApiInterface apiService})
      : _apiService = apiService;

  Future<List<TableAreaListItem>> getTableList() async {
    var data = await _apiService.getListData(
        endpoint: TableEndpoint.tableInfos,
        converter: TableAreaListItem.fromJson);
    return data;
  }

  /// 撤台操作
  /// [orderId] 订单ID
  /// [tableNum] 桌台名称
  Future<void> cancelTable({
    required int orderId, 
    required String tableNum
  }) async {
    await _apiService.postData(
      endpoint: TableEndpoint.cancelTable,
      data: {
        "orderId": orderId,
        "tableNum": tableNum
      }
    );
  }
  
  /// 转台操作
  /// [orderId] 订单ID
  /// [originTableNumber] 转出桌台名称
  /// [newTableNumber] 转入桌台名称
  Future<void> transferTable({
    required int orderId,
    required String originTableNumber,
    required String newTableNumber,
  }) async {
    await _apiService.postData(
      endpoint: TableEndpoint.transferTable,
      data: {
        "orderId": orderId,
        "originTableNumber": originTableNumber,
        "newTableNumber": newTableNumber
      }
    );
  }
  
  /// 拼桌操作
  /// [orderId] 订单ID
  /// [tableNum] 桌台名
  /// [subTableNum] 子桌台名
  /// [subNumber] 子桌台人数
  Future<void> shareTable({
    required int orderId,
    required String tableNum,
    required String subTableNum,
    required int subNumber,
  }) async {
    await _apiService.postData(
      endpoint: TableEndpoint.shareTable,
      data: {
        "orderId": orderId,
        "tableNum": tableNum,
        "subTableNum": subTableNum,
        "subNumber": subNumber
      }
    );
  }
  
  /// 清台操作
  /// [tableNumber] 桌台名称
  Future<void> cleanTable({
    required String tableNumber
  }) async {
    await _apiService.postData(
      endpoint: TableEndpoint.cleanTable,
      data: {
        "tableNumber": tableNumber
      }
    );
  }
}

@riverpod
TableIntranetRepository tableIntranetRepository(Ref ref){
return TableIntranetRepository(apiService: ref.watch(kposApiIntranetServiceProvider));
}