import 'package:kpos/features/table/domain/table_area_list_item.dart';

abstract class TableLocalRepositoryInterface {
  Future<List<Map<String, dynamic>>> getList();

  Future<void> transferTable(
      int orderId, String originTableNumber, String newTableNumber);

  Future<void> mergeTable(
      int tenantId,
      int storeId,
      String storeName,
      int brandId,
      String brandName,
      String tableConfig, int number);

  Future<int> shareTable(
      int tenantId,
      int storeId,
      String storeName,
      int brandId,
      String brandName,
      int orderId,
      String tableNum,
      String subTableNum,
      int subNumber);

  Future<List<Map<String, dynamic>>> getShareTableOrders(int shareTableId);

  Future<void> cancelTable(int orderId,String tableNumber);

  Future<void> clearTable(String tableNumber);
}