import 'dart:convert';

import 'package:drift/drift.dart';
import 'package:kpos/common/utils/snowflake_id_generator.dart';
import 'package:kpos/features/cart/data/cart_local_repository.dart';
import 'package:kpos/features/table/data/table_local_repository_interface.dart';
import '../../../common/services/database/app_database.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';

class TableLocalRepository implements TableLocalRepositoryInterface {

  final AppDatabase db;
  TableLocalRepository(this.db);

  // @override
  @override
  Future<List<Map<String, dynamic>>> getList() async {
    final result = await db.customSelect(
      '''SELECT
  ai.area_name,
  ai.sort,
  dti.*,
  o.order_id,
  o.merge_table_flag,
  o.merge_table_id,
  o.kp_merge_table_name,
  o.kp_shared_table_flag,
  o.kp_shared_table_id,
  o.create_time,
  CASE
    WHEN o.order_id IS NULL THEN 1
    WHEN o.order_state = 1 THEN 2
    WHEN o.order_state = 3 THEN 3
    ELSE 1
  END AS state
FROM
  area_infos ai
  LEFT JOIN dining_table_infos dti ON ai.area_id = dti.area_id
  LEFT JOIN orders o ON dti.table_title = o.table_number AND o.deleted != 1
ORDER BY
  ai.sort,
  dti.sort''',
    ).get();

    final Map<int, Map<String, dynamic>> groupMap = {};

    for (final row in result) {
      final int areaId = row.read<int>('area_id');
      if (!groupMap.containsKey(areaId)) {
        groupMap[areaId] = {
          "areaId":row.read<int>('area_id'),
          "areaName": row.read<String>('area_name'),
          "tableList": []
        };
      }

      final int sharedFlag = row.readNullable<int>('kp_shared_table_flag') ?? 0;
      final int sharedId = row.readNullable<int>('kp_shared_table_id') ?? 0;

      // 获取拼桌订单状态
      int sharedTableState = 0;
      int subOrderNumber = 0;
      if (sharedFlag == 1 && sharedId != 0) {
        final sharedOrders = await db.customSelect(
          '''
    SELECT order_state FROM orders
    WHERE kp_shared_table_flag = 1
      AND kp_shared_table_id = ?
      AND deleted != 1
    ''',
          variables: [Variable.withInt(sharedId)],
        ).get();
        subOrderNumber = sharedOrders.length;

        final states = sharedOrders.map((e) => e.read<int>('order_state')).toSet();

        if (states.isEmpty) {
          sharedTableState = 1;
        } else if (states.every((s) => s == 1)) {
          sharedTableState = 1;
        } else if (states.every((s) => s == 3)) {
          sharedTableState = 3;
        } else {
          sharedTableState = 2;
        }
      }

      (groupMap[areaId]!["tableList"] as List).add({
        "tableInfoId": row.read<int>('table_info_id'),
        "tableTitle": row.read<String>('table_title'),
        "seatedNumber": row.read<double>('seated_number'),
        "tableShape": row.read<int>('table_shape'),
        "tableHeight": row.read<int>('table_height'),
        "tableWidth": row.read<int>('table_width'),
        "nodeX": row.read<int>('node_x'),
        "nodeY": row.read<int>('node_y'),
        "state": row.readNullable<int>('kp_uncleaned_table_flag') != 1 ? row.read<int>('state') : 4,
        "orderId": row.readNullable<int>('order_id') ?? 0,
        "mergeTableFlag": row.readNullable<int>('merge_table_flag') ?? 0,
        "mergeTableId": row.readNullable<int>('merge_table_id') ?? 0,
        "mergeTableName": row.readNullable<String>('kp_merge_table_name') ?? '',
        "sharedTableFlag": row.readNullable<int>('kp_shared_table_flag') == 1 ? sharedTableState : 0,
        "sharedTableId": row.readNullable<int>('kp_shared_table_id') ?? 0,
        "createTime": row.readNullable<int>('create_time') ?? 0,
        "subOrderNumber": subOrderNumber,
      });
    }
    return groupMap.values.toList();
  }

  /// 转台操作
  @override
  Future<void> transferTable(int orderId, String originTableNumber, String newTableNumber) async {
    await db.transaction(() async {
      /// 1、查询转入转台，如果是拼桌且超过5个订单，不让转
      final newTableResult = await db.customSelect(
        '''
    SELECT *
    FROM orders
    WHERE
      table_number = ?
      AND (merge_table_flag = 0 OR merge_table_flag IS NULL)
      AND deleted = 0
      AND state NOT IN (5, 6, 7)
    ORDER BY create_time DESC
    LIMIT 1
  ''',
        variables: [Variable.withString(newTableNumber)],
      ).get();

      QueryRow? newTable;
      if (newTableResult.isNotEmpty) {
        newTable = newTableResult.first;
      }
      if (newTable != null && newTable.readNullable<int>('kp_shared_table_flag') == 1) {
        final shareOrders = await db.customSelect(
          '''
  SELECT * FROM orders o
  WHERE o.kp_shared_table_id = ?
    AND o.deleted = 0
    AND o.state NOT IN (5,6,7);
  ''',
          variables: [Variable.withInt(newTable.read<int>('kp_shared_table_id'))],
        ).get();
        if (shareOrders.length >= 5) {
          throw Exception(MessageKey.invalidParam);
        }
      }

      /// 2、查询转出桌台数据
      final originResult = await db.customSelect(
        '''
    SELECT *
    FROM orders
    WHERE
      order_id = ?
      AND table_number = ?
      AND (merge_table_flag = 0 OR merge_table_flag IS NULL)
      AND deleted = 0
      AND state NOT IN (5, 6, 7)
    ORDER BY create_time DESC
    LIMIT 1
  ''',
        variables: [
          Variable.withInt(orderId),
          Variable.withString(originTableNumber),
        ],
      ).get();

      if (originResult.isEmpty) {
        throw Exception(MessageKey.invalidParam);
      }

      /// 3、判断原桌台是否是拼桌
      final originTable = originResult.first;
      final int originOrderId = originTable.read<int>('order_id');
      final originSharedFlag = originTable.readNullable<int>('kp_shared_table_flag');

      if (originSharedFlag == null || originSharedFlag == 0) {
        /// 4、非拼桌桌台转空桌台
        if (newTable == null) {
          await (db.update(db.diningTableInfos)
            ..where((tbl) => tbl.tableTitle.equals(originTableNumber)))
              .write(
            const DiningTableInfosCompanion(
              kpUncleanedTableFlag: Value(1)
            ),
          );
          await (db.update(db.orders)
            ..where((tbl) => tbl.orderId.equals(originOrderId)))
              .write(
            OrdersCompanion(
              kpOriginTableNumber: Value(originTableNumber),
              tableNumber: Value(newTableNumber),
            ),
          );
        } else { /// 5、非拼桌桌台转拼桌
          final newSharedFlag = newTable.readNullable<int>('kp_shared_table_flag');
          final newSharedId = newTable.readNullable<int>('kp_shared_table_id') ?? SnowflakeIdGenerator().nextId();
          await (db.update(db.diningTableInfos)
            ..where((tbl) => tbl.tableTitle.equals(originTableNumber)))
              .write(
            const DiningTableInfosCompanion(
                kpUncleanedTableFlag: Value(1)
            ),
          );
          if (newSharedFlag == 1) {
            await (db.update(db.orders)
              ..where((tbl) => tbl.orderId.equals(originOrderId)))
                .write(
              OrdersCompanion(
                  kpOriginTableNumber: Value(originTableNumber),
                  tableNumber: Value(newTableNumber),
                  kpSharedTableFlag: const Value(1),
                  kpSharedTableId: Value(newSharedId),
              ),
            );
          } else {
            await (db.update(db.orders)
              ..where((tbl) => tbl.orderId.equals(originOrderId)))
                .write(
              OrdersCompanion(
                  kpOriginTableNumber: Value(originTableNumber),
                  tableNumber: Value(newTableNumber),
                  kpSharedTableFlag: const Value(1),
                  kpSharedTableId: Value(newSharedId)
              ),
            );
            await (db.update(db.orders)
              ..where((tbl) => tbl.orderId.equals(newTable!.read<int>('order_id'))))
                .write(
              OrdersCompanion(
                  kpSharedTableFlag: const Value(1),
                  kpSharedTableId: Value(newSharedId)
              ),
            );
          }
        }
      } else {
        if (newTable == null) { /// 6、拼桌桌台转空桌台
          final originShareOrders = await db.customSelect(
            '''
  SELECT * FROM orders o
  WHERE o.kp_shared_table_id = ?
    AND o.deleted = 0
    AND o.state NOT IN (5,6,7);
  ''',
            variables: [Variable.withInt(originTable.read<int>('kp_shared_table_id'))],
          ).get();
          await (db.update(db.orders)
            ..where((tbl) => tbl.orderId.equals(originOrderId)))
              .write(
            OrdersCompanion(
                kpOriginTableNumber: Value(originTableNumber),
                tableNumber: Value(newTableNumber),
                kpSharedTableFlag: const Value(0),
                kpSharedTableId: const Value.absent()
            ),
          );
          if (originShareOrders.length == 2) {
            final originShareOrdersExcludingCurrent = await db.customSelect(
              '''
  SELECT * FROM orders o
  WHERE o.kp_shared_table_id = ?
    AND o.deleted = 0
    AND o.order_id != ?
    AND o.state NOT IN (5,6,7);
  ''',
              variables: [Variable.withInt(originTable.read<int>('kp_shared_table_id')), Variable.withInt(originOrderId)],
            ).get();
            if (originShareOrdersExcludingCurrent.isNotEmpty) {
              final row = originShareOrdersExcludingCurrent.first;
              await (db.update(db.orders)
                ..where((tbl) => tbl.orderId.equals(row.read<int>('order_id'))))
                  .write(
                const OrdersCompanion(
                    kpSharedTableFlag: Value(0),
                    kpSharedTableId: Value.absent()
                ),
              );
            }
          }
        }
        throw Exception(MessageKey.invalidParam);
      }
    });
  }

  /// 联台操作
  @override
  Future<void> mergeTable(
      int tenantId,
      int storeId,
      String storeName,
      int brandId,
      String brandName,
      String tableConfig,
      int number,
      ) async {
    final List<dynamic> decodedList = jsonDecode(tableConfig);
    final List<Map<String, dynamic>> mapList = decodedList.cast<Map<String, dynamic>>();
    if (mapList.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }

    final orderLocal = CartLocalRepository(db);
    final mergeId = SnowflakeIdGenerator().nextId();
    final groupName = await getNextAvailableGroupName();

    await db.transaction(() async {
      for (var item in mapList) {
        final tableNumber = item["tableNumber"];
        final orderId = item["orderId"] ?? await orderLocal.createOrder(
          tenantId, storeId, storeName, brandId, brandName, tableNumber,
        );

        await (db.update(db.orders)..where((tbl) => tbl.orderId.equals(orderId))).write(
          OrdersCompanion(
            mergeTableFlag: const Value(1),
            mergeTableId: Value(mergeId),
            tableNumber: Value(tableNumber),
            diningNumber: Value(number),
            kpMergeTableName: Value(groupName)
          )
        );
      }
    });
  }

  Future<String> getNextAvailableGroupName() async {
    final result = await db.customSelect(
      '''
    SELECT DISTINCT kp_merge_table_name
    FROM orders
    WHERE merge_table_flag = 1
      AND deleted = 0
      AND state NOT IN (5, 6, 7)
      AND kp_merge_table_name LIKE 'Group%'
      AND kp_merge_table_name IS NOT NULL
      AND kp_merge_table_name != '0'
    ''',
    ).get();

    final existingNames = result
        .map((row) => row.read<String?>('kp_merge_table_name'))
        .whereType<String>()
        .toList();

    final usedGroupNumbers = existingNames
        .map((name) {
      final match = RegExp(r'Group(\d+)').firstMatch(name);
      return match != null ? int.tryParse(match.group(1) ?? '') : null;
    })
        .whereType<int>()
        .toSet();

    int nextGroupNumber = 1;
    while (usedGroupNumbers.contains(nextGroupNumber)) {
      nextGroupNumber++;
    }

    return 'Group$nextGroupNumber';
  }

  /// 拼桌操作
  @override
  Future<int> shareTable(
      int tenantId,
      int storeId,
      String storeName,
      int brandId,
      String brandName,
      int orderId,
      String tableNum,
      String subTableNum,
      int subNumber,
      ) async {
    return await db.transaction(() async {
      final result = await db.customSelect(
        '''
      SELECT *
      FROM orders
      WHERE
        order_id = ?
        AND table_number = ?
        AND deleted != 1
      ORDER BY create_time DESC
      LIMIT 1
      ''',
        variables: [
          Variable.withInt(orderId),
          Variable.withString(tableNum),
        ],
      ).get();

      if (result.isEmpty) {
        throw Exception(MessageKey.invalidParam);
      }

      final order = result.first;
      final shareFlag = order.readNullable<int>('kp_shared_table_flag');
      final shareId = order.readNullable<int>('kp_shared_table_id') ?? SnowflakeIdGenerator().nextId();

      if (shareFlag == null || shareFlag == 0) {
        await (db.update(db.orders)..where((tbl) => tbl.orderId.equals(orderId)))
            .write(OrdersCompanion(
          kpSharedTableFlag: const Value(1),
          kpSharedTableId: Value(shareId),
        ));
      }

      final orderLocal = CartLocalRepository(db);
      final subOrderId = await orderLocal.createOrder(
        tenantId,
        storeId,
        storeName,
        brandId,
        brandName,
        subTableNum,
      );

      await (db.update(db.orders)..where((tbl) => tbl.orderId.equals(subOrderId)))
          .write(OrdersCompanion(
        kpSharedTableFlag: const Value(1),
        kpSharedTableId: Value(shareId),
        diningNumber: Value(subNumber)
      ));

      return subOrderId;
    });
  }

  @override
  Future<List<Map<String, dynamic>>> getShareTableOrders(int shareTableId) async {
    const query = '''
    SELECT
      o.order_id,
      o.table_number,
      o.dining_number,
      o.state,
      o.create_time,
      oi.commodity_id,
      oi.commodity_name
    FROM orders o
    LEFT JOIN order_item oi 
      ON oi.order_id = o.order_id 
      AND oi.parent_order_item_id IS NULL
    WHERE o.kp_shared_table_id = ? 
      AND o.state NOT IN (6, 7, 8)
      AND o.deleted != 1
    ORDER BY o.create_time DESC;
  ''';

    final rows = await db.customSelect(
      query,
      variables: [Variable.withInt(shareTableId)],
    ).get();

    final Map<int, Map<String, dynamic>> resultMap = {};

    for (final row in rows) {
      final orderId = row.read<int>('order_id');

      // 初始化订单（不存在时）
      resultMap.putIfAbsent(orderId, () {
        return {
          'orderId': orderId,
          'tableNumber': row.readNullable<String>('table_number') ?? "",
          'diningNumber': row.readNullable<int>('dining_number') ?? 0,
          'state': row.readNullable<int>('state') ?? 0,
          'createTime': row.readNullable<int>('create_time') ?? 0,
          'items': <Map<String, dynamic>>[],
        };
      });

      // 添加商品项（忽略无商品的空行）
      final commodityId = row.readNullable<int>('commodity_id') ?? 0;
      final commodityName = row.readNullable<String>('commodity_name') ?? '';

      if (commodityId != 0 || commodityName.isNotEmpty) {
        resultMap[orderId]!['items'].add({
          'commodityId': commodityId,
          'commodityName': commodityName,
        });
      }
    }

    return resultMap.values.toList();
  }

  /// 撤台操作
  @override
  Future<void> cancelTable(int orderId, String tableNum) async {
    final result = await db.customSelect(
      '''
      SELECT *
      FROM orders
      WHERE
        order_id = ?
        AND table_number = ?
        AND deleted != 1
      ORDER BY create_time DESC
      LIMIT 1
      ''',
      variables: [
        Variable.withInt(orderId),
        Variable.withString(tableNum),
      ],
    ).get();

    if (result.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }
    await (db.update(db.orders)..where((tbl) => tbl.orderId.equals(orderId)))
        .write(const OrdersCompanion(
      deleted: Value(1),
    ));
  }

  @override
  Future<void> clearTable(String tableNumber) async {
    final result = await db.customSelect(
      '''
      SELECT
	      *
      FROM
	      dining_table_infos dti
      WHERE
	    dti.table_title = ?
      LIMIT 1
      ''',
      variables: [
        Variable.withString(tableNumber),
      ],
    ).get();

    if (result.isEmpty) {
      throw Exception(MessageKey.invalidParam);
    }
    await (db.update(db.diningTableInfos)..where((tbl) => tbl.tableTitle.equals(tableNumber)))
        .write(const DiningTableInfosCompanion(
      kpUncleanedTableFlag: Value(0),
    ));
  }

}