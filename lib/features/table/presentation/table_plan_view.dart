import 'dart:math';
import 'package:flutter/rendering.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/utils/common_util.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/features/table/presentation/widgets/table_top_action_area.dart';

class TablePlanView extends ConsumerStatefulWidget {
  final List<TableItem> tableList;

  const TablePlanView({super.key, required this.tableList});

  @override
  ConsumerState createState() => _TablePlanViewState();
}

class _TablePlanViewState extends ConsumerState<TablePlanView> with TickerProviderStateMixin{
  final TransformationController _transformationController =
      TransformationController();

  // 存储每个桌台的边界信息，用于自定义点击检测
  List<_TableItemBound> _tableBounds = [];

  // 根据点击位置获取对应的桌台
  TableItem? _hitTestTable(Offset localPosition) {
    // 将点击位置转换为内容坐标（考虑缩放和平移）
    final Matrix4 inverse = Matrix4.inverted(_transformationController.value);
    final Offset contentPos = MatrixUtils.transformPoint(inverse, localPosition);
    for (final bound in _tableBounds.reversed) {
      if (bound.rect.contains(contentPos)) {
        return bound.table;
      }
    }
    return null;
  }

  Rect _calculateContentBoundary() {
    double minX = double.infinity;
    double maxX = -double.infinity;
    double minY = double.infinity;
    double maxY = -double.infinity;

    for (var table in widget.tableList) {
      final effectiveWidth = max(table.tableWidth * 10, 100); // 后台值乘以10，最小宽度100
      final effectiveHeight = max(table.tableHeight * 10, 100); // 后台值乘以10，最小高度100
      final halfWidth = (effectiveWidth + 12) / 2; // 包含边框
      final halfHeight = (effectiveHeight + 12) / 2;
      //计算边界
      minX = min(minX, table.nodeX - halfWidth);
      maxX = max(maxX, table.nodeX + halfWidth);
      minY = min(minY, table.nodeY - halfHeight);
      maxY = max(maxY, table.nodeY + halfHeight);
    }

    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }

  @override
  void initState() {
    super.initState();
  }

  void _moveSelectedTableToVisibleArea(
      TableItem selectedTable, BoxConstraints constraints, Rect contentRect) {
    final effectiveWidth = max(selectedTable.tableWidth * 10, 100);
    final tableLeftX = selectedTable.nodeX - contentRect.left - effectiveWidth / 2;
    final tableRightX = tableLeftX + effectiveWidth;

    final availableWidth = constraints.maxWidth - 360; // 蓝色区域宽度
    const leftEdge = 0.0; // 蓝色区域左边界
    final rightEdge = availableWidth - 40; // 蓝色区域右边界，右侧留 40px 边距

    double dx = 0;
    if (tableRightX > rightEdge) {
      // 右侧被遮挡，向左平移
      dx = rightEdge - tableRightX;
    } else if (tableLeftX < leftEdge) {
      // 左侧被遮挡，向右平移
      dx = leftEdge - tableLeftX;
    }
    _transformationController.value = Matrix4.identity()..translate(dx);
  }

  @override
  Widget build(BuildContext context) {
    final timeShowType = ref.watch(tableTimeDisplayShowProvider);
    final selectedItem = ref.watch(selectedTableProvider);
    final isPopupVisible = ref.watch(showTableSlidePopUpProvider);
    final areaId = ref.watch(tableServiceProvider).curArea?.areaId;
    final filterStatus = ref.watch(areaFilterStatusProvider.select((map) => map[areaId] ?? []));

    //为了解决超出屏幕的桌台无法点击的问题，这里使用Listener监听点击位置来反推点击了哪个桌台
    return Listener(
      behavior: HitTestBehavior.translucent,
      onPointerDown: (event) {
        final table = _hitTestTable(event.localPosition);
        if (table != null) {
          final filterStatus = ref.read(areaFilterStatusProvider.select((map) => map[areaId] ?? []));
          if (filterStatus.isNotEmpty && !filterStatus.contains(table.state)) {
            return;
          }
          ref.read(tableServiceProvider).itemTapEvent(context, table);
        }else{
          // 如果没有点击到任何桌台，退出弹窗
          if (isPopupVisible) {
            ref.read(showTableSlidePopUpProvider.notifier).set(false);
            ref.read(selectedTableProvider.notifier).state = null;
          }
        }
      },
      child: InteractiveViewer(
        transformationController: _transformationController,
        boundaryMargin: const EdgeInsets.all(double.infinity),
        minScale: 0.1,
        maxScale: 4.0,
        child: LayoutBuilder(
          builder: (context, constraints) {
        // 每次构建清空边界缓存
        _tableBounds = [];
        final contentRect = _calculateContentBoundary();
    final contentWidth = contentRect.width;
    final contentHeight = contentRect.height;

    // 弹窗弹出时平移视图
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isPopupVisible && selectedItem != null) {
        _moveSelectedTableToVisibleArea(selectedItem, constraints, contentRect);
      } else if (!isPopupVisible) {
        // 弹窗关闭时重置视图
        _transformationController.value = Matrix4.identity();
      }
    });

    final availableWidth = constraints.maxWidth;
    final availableHeight = constraints.maxHeight;

    return Container(
      width: availableWidth,
      height: availableHeight,
      padding: const EdgeInsets.only(left: 16),
      child: Stack(
        clipBehavior: Clip.none,
        children: widget.tableList.map((table) {
          final effectiveWidth = max(table.tableWidth * 10, 100);
          final effectiveHeight = max(table.tableHeight * 10, 100);
          final left = table.nodeX - contentRect.left - effectiveWidth / 2;
          final top = table.nodeY - contentRect.top - effectiveHeight / 2;

          // 缓存边界用于自定义 hit test
          _tableBounds.add(_TableItemBound(table, Rect.fromLTWH(left, top, effectiveWidth.toDouble(), effectiveHeight.toDouble())));

          return Positioned(
            left: left,
            top: top,
            child: _buildTableItem(table, timeShowType, selectedItem, filterStatus),
          );
        }).toList(),
      ),
    );

  },
    ),
      ),
    );
  }

  Widget _buildTableItem(TableItem table,int timeShowType,TableItem? selectedItem,List<int> filterStatus) {
    // 确保桌位尺寸不小于最小值
    final effectiveWidth = max(table.tableWidth * 10, 100); // 后台值乘以10，最小宽度100
    final effectiveHeight = max(table.tableHeight * 10, 100); // 后台值乘以10，最小高度100

    Color useBorderColor =
        TableState.stateValueToBorderColor(table.state ?? 0);
    int tableAction = ref.watch(tableActionTypeProvider);
    bool isSelected = selectedItem?.tableTitle == table.tableTitle;
    if(tableAction==TableAction.group.value){
      //联台操作下选中状态为多选
      List<TableItem> tableGroup = ref.watch(multiSelectedTablesProvider);
      isSelected = tableGroup.any((e)=>e.tableTitle==table.tableTitle);
    }
    final curTransferTable = ref.watch(curTransferTableProvider);
    if(curTransferTable.$1?.tableTitle==table.tableTitle||curTransferTable.$2?.tableTitle==table.tableTitle){
      isSelected = true;
    }
    //拼桌需要展示多种状态颜色的情况
    bool isSharingAndMulti = table.sharedTableFlag==TableSharingType.pendingWithUnpaid.value&&isSelected;
    bool isSharing = table.sharedTableFlag>0;
    //联台号（为空时代表不是联台）
    String? groupName = ref.read(groupDataProvider.notifier).findTableGroup(table)?.groupName;
    return Container(
      //底部和右侧边框容器
      decoration: BoxDecoration(
        shape: table.tableShape == 1 ? BoxShape.rectangle : BoxShape.circle,
        borderRadius: table.tableShape == 1 ? BorderRadius.circular(8) : null,
        border: isSharingAndMulti? const Border(
          bottom: BorderSide(color: Color(0xFF1B73DE), width: 4,strokeAlign: BorderSide.strokeAlignOutside,),
          right: BorderSide(color: Color(0xFF1B73DE), width: 4,strokeAlign: BorderSide.strokeAlignOutside,),
        ):Border.all(
          color: isSelected ? useBorderColor : Colors.transparent,
          width: 4,
          strokeAlign: BorderSide.strokeAlignOutside,
        ),
      ),
      child: Container(
        // 顶部和左侧边框容器
        decoration: BoxDecoration(
          shape: table.tableShape == 1 ? BoxShape.rectangle : BoxShape.circle,
          borderRadius: table.tableShape == 1 ? BorderRadius.circular(8) : null,
          border: isSharingAndMulti? const Border(
            top: BorderSide(color: Color(0xFFDE4B11), width: 4),
            left: BorderSide(color: Color(0xFFDE4B11), width: 4),
          ):Border.all(
            color: isSelected ? useBorderColor : Colors.transparent,
            width: 4,
            strokeAlign: BorderSide.strokeAlignOutside,
          ),
        ),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            //最底层的装饰容器
            Container(
              width: effectiveWidth.toDouble(),
              height: effectiveHeight.toDouble(),
              // 反向补偿边框宽度
              margin: const EdgeInsets.all(2),
              decoration: _buildDecoration(table),
            ),
            //拼桌的对半遮罩层
            Visibility(
              visible: isSharing,
              child: CustomPaint(
                painter: DiagonalOverlayPainter(
                  bottomRightOpacity: table.sharedTableFlag==TableSharingType.pendingWithUnpaid.value?0.1:0.25,
                  topLeftOpacity: 0,
                ),
                size: Size(effectiveWidth+2, effectiveHeight+2),
              ),
            ),
            //核心内容层
            SizedBox(
              width: effectiveWidth.toDouble(),
              height: effectiveHeight.toDouble(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //开台时间或时长
                  Visibility(
                    visible: (table.state == TableState.pendingOrder.value || table.state == TableState.unpaid.value)&&!isSharing,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        timeShowType == 1
                            ? Assets.images.iconHourglass.svg(width: 16, height: 16)
                            : Assets.images.iconClock.svg(width: 16, height: 16),
                        const SizedBox(width: 4),
                        Builder(
                          builder: (context) {
                            if (table.createTime == null) return const Text('--:--:--');
                            return StreamBuilder<int>(
                              stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
                              builder: (context, snapshot) {
                                final duration = CommonUtil.durationFromTimestamp(table.createTime!);
                                return Text(
                                  timeShowType == 1
                                      ? CommonUtil.formatDuration(duration)
                                      : CommonUtil.formatTimestamp(table.createTime!,pattern: 'HH:mm:ss'),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w400,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  //预订时间
                  Visibility(
                    visible: table.state == TableState.reserved.value,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Assets.images.iconCalendarCheck
                            .svg(width: 16, height: 16),
                        const SizedBox(width: 4),
                        const Text(
                          '18:00',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF323843),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  //待清台图标
                  Visibility(
                    visible: table.state == TableState.needsCleanup.value,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 3),
                      child: Assets.images.iconClean.svg(width: 16, height: 16),
                    ),
                  ),
                  if(table.state==TableState.available.value||isSharing)
                    const SizedBox(height: 19),
                  Text(
                    table.tableTitle,
                    style: TextStyle(
                      color:
                          TableState.stateValueToTitleColor(table.state ?? 0),
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 3),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      //座位图标
                      Visibility(
                        visible: (table.state == TableState.available.value || table.state == TableState.needsCleanup.value)&&!isSharing,
                        child: Assets.images.iconSeat.svg(
                          width: 16,
                          height: 16,
                          color: table.state == 4 ? Colors.white : null,
                        ),
                      ),
                      //人数图标
                      Visibility(
                        visible: (table.state == TableState.pendingOrder.value || table.state == TableState.unpaid.value||table.state == TableState.reserved.value)&&!isSharing,
                        child: Assets.images.iconPeopleNum.svg(
                          width: 16,
                          height: 16,
                          color: table.state == TableState.reserved.value ? const Color(0xFF323843) : null,
                        ),
                      ),
                      //拼桌图标
                      Visibility(
                        visible: isSharing,
                        child: Assets.images.iconTableSharing.svg(
                          width: 16,
                          height: 16,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        !isSharing?table.seatedNumber.toString():table.subOrderNumber.toString(),
                        style: TextStyle(
                          fontSize: 14,
                          color: TableState.stateValueToTitleColor(table.state ?? 0),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Visibility(
              visible: filterStatus.isNotEmpty && !filterStatus.contains(table.state),
              child: Container(
                width: effectiveWidth.toDouble(),
                height: effectiveHeight.toDouble(),
                // 反向补偿边框宽度
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.8),
                  shape:
                      table.tableShape == 1 ? BoxShape.rectangle : BoxShape.circle,
                ),
              ),
            ),
            //转台图标
            Positioned(
              bottom: 7,
              right: 7,
              child: Visibility(
                visible: curTransferTable.$1?.tableTitle==table.tableTitle||curTransferTable.$2?.tableTitle==table.tableTitle,
                child: Container(
                  width: 22,
                  height: 22,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: curTransferTable.$1?.tableTitle==table.tableTitle?KPColors.fillBrandNormal:KPColors.fillBlueNormal,
                      border: Border.all(width: 1.5,color: KPColors.borderGrayLightLightest)
                  ),
                  child: SvgPicture.asset(curTransferTable.$1?.tableTitle==table.tableTitle?Assets.images.iconCornerUpRight.path:Assets.images.iconCornerLeftDown.path,width: 16,height: 16,),
                ),
              ),
            ),
            //联台图标
            Positioned(
              right: 7,
              bottom: 7,
              child: Visibility(
                visible: tableAction==TableAction.group.value&&isSelected,
                child: Container(
                  width: 22,
                  height: 22,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: KPColors.fillBrandNormal,
                    border: Border.all(width: 1.5,color: KPColors.borderGrayLightLightest),
                  ),
                  child: Assets.images.iconTableLink.svg(
                    width: 14,
                    height: 14,
                  ),
                ),
              ),
            ),
            //催菜图标
            Positioned(
              right: table.tableShape == 1 ? -8 : 0,
              top: table.tableShape == 1 ? -8 : 0,
              child: Visibility(
                visible: table.state == TableState.unpaid.value,
                child: Stack(
                  children: [
                    Assets.images.iconUrge.svg(width: 24, height: 24),
                    // 蒙层SVG通过颜色滤镜实现
                    Visibility(
                      visible:
                      filterStatus.isNotEmpty && !filterStatus.contains(table.state),
                      child: ColorFiltered(
                        colorFilter: ColorFilter.mode(
                          Colors.white.withOpacity(0.8),
                          BlendMode.srcIn,
                        ),
                        child:
                            Assets.images.iconUrge.svg(width: 24, height: 24),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if(groupName!=null)
              Positioned(
                bottom: -8,
                child: Container(
                  height: 16,
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: KPColors.fillGrayLightLightest,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(width: 1,color: KPColors.borderGrayDarkLightest),
                  ),
                  child: Row(
                    children: [
                      Assets.images.iconTableLink.svg(
                        width: 12,
                        height: 12,
                        color: KPColors.iconGrayPrimary,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        groupName,
                        style: KPFontStyle.bodySmall.copyWith(color: KPColors.textGrayPrimary),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildDecoration(TableItem item) {
    Color useColor = TableState.stateValueToColor(item.state ?? 0);
    switch (item.tableShape) {
      case 1:
        return BoxDecoration(
          color: useColor,
          borderRadius: BorderRadius.circular(8),
          gradient: item.sharedTableFlag==TableSharingType.pendingWithUnpaid.value? const LinearGradient(
            colors: [
              Color(0xFFDE4B11),
              Color(0xFF984C68),
              Color(0xFF0762D2),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            stops: [0.0,0.5,1]
          ):null,
        );
      case 2:
        return BoxDecoration(
          color: useColor,
          shape: BoxShape.circle,
          gradient: item.sharedTableFlag==TableSharingType.pendingWithUnpaid.value? const LinearGradient(
              colors: [
                Color(0xFFDE4B11),
                Color(0xFF984C68),
                Color(0xFF0762D2),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              stops: [0.0,0.5,1]
          ):null,
        );
      default: // rectangle
        return BoxDecoration(
          color: useColor,
          borderRadius: BorderRadius.circular(8),
        );
    }
  }
}

class DiagonalOverlayPainter extends CustomPainter {
  final double bottomRightOpacity;
  final double topLeftOpacity;

  DiagonalOverlayPainter({
    this.bottomRightOpacity = 0.2,
    this.topLeftOpacity = 0.4,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // 绘制右下角三角形蒙层
    paint.color = KPColors.iconGrayInverse.withValues(alpha: bottomRightOpacity);
    var path = Path();
    path.moveTo(size.width, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();
    canvas.drawPath(path, paint);

    // 绘制左上角三角形蒙层
    paint.color = KPColors.iconGrayInverse.withValues(alpha: topLeftOpacity);
    path = Path();
    path.moveTo(0, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, 0);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// 用于存储桌台及其边界的简单数据结构
class _TableItemBound {
  final TableItem table;
  final Rect rect;
  _TableItemBound(this.table, this.rect);
}
