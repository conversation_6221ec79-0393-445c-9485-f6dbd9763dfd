import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/table_intranet_repository.dart';
import '../domain/table_area_list_item.dart';

part 'table_screen_controller.g.dart';


@riverpod
class TableScreenController extends _$TableScreenController {

  @override
  FutureOr<List<TableAreaListItem>> build() async {
    final data = await getTableList();
    await Future.delayed(const Duration(milliseconds: 300));
    ref.read(tableServiceProvider).areaList = data;
    if(data.isNotEmpty){
      ref.read(tableServiceProvider).curArea = data.first;
    }
    return data;

    //先用假数据
    await Future.delayed(const Duration(milliseconds: 300));
    ref.read(tableServiceProvider).areaList = temAreaList;
    if(temAreaList.isNotEmpty){
        ref.read(tableServiceProvider).curArea = temAreaList.first;
    }
    return temAreaList;
  }

  Future<List<TableAreaListItem>> getTableList() async {
    return ref.read(tableIntranetRepositoryProvider).getTableList();
  }

  List<TableAreaListItem> temAreaList = const [
    TableAreaListItem(
      areaId: 599105923792998400,
      areaName: 'Hall',
      tableList: [
        TableItem(
          tableInfoId: 599105926171168768,
          tableTitle: "A01",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 276,
          nodeY: 76,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168769,
          tableTitle: "A02",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 535,
          nodeY: 76,
          state: 2,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168770,
          tableTitle: "A03",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 794,
          nodeY: 76,
          state: 3,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168771,
          tableTitle: "A04",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 14.2,
          nodeX: 1168,
          nodeY: 76,
          state: 4,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168772,
          tableTitle: "A05",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 420,
          nodeY: 260,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168773,
          tableTitle: "A06",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 679,
          nodeY: 260,
          state: 2,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168774,
          tableTitle: "A07",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 938,
          nodeY: 260,
          state: 5,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168775,
          tableTitle: "A08",
          seatedNumber: 4,
          tableShape: 1,
          tableHeight: 9.0,
          tableWidth: 20.0,
          nodeX: 321,
          nodeY: 433,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168776,
          tableTitle: "A09",
          seatedNumber: 4,
          tableShape: 1,
          tableHeight: 9.0,
          tableWidth: 20.0,
          nodeX: 679,
          nodeY: 433,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168777,
          tableTitle: "A10",
          seatedNumber: 4,
          tableShape: 1,
          tableHeight: 9.0,
          tableWidth: 20.0,
          nodeX: 1037,
          nodeY: 863,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
      ],
    ),
    TableAreaListItem(
      areaId: 599105923792998401,
      areaName: 'Private',
      tableList: [
        TableItem(
          tableInfoId: 599105926171168778,
          tableTitle: "B01",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 535,
          nodeY: 76,
          state: 2,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168779,
          tableTitle: "B02",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 794,
          nodeY: 76,
          state: 3,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168780,
          tableTitle: "B03",
          seatedNumber: 2,
          tableShape: 1,
          tableHeight: 11.2,
          tableWidth: 14.2,
          nodeX: 1068,
          nodeY: 76,
          state: 4,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168781,
          tableTitle: "B04",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 420,
          nodeY: 260,
          state: 1,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168782,
          tableTitle: "B05",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 679,
          nodeY: 260,
          state: 2,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
        TableItem(
          tableInfoId: 599105926171168783,
          tableTitle: "B06",
          seatedNumber: 4,
          tableShape: 2,
          tableHeight: 11.2,
          tableWidth: 11.2,
          nodeX: 938,
          nodeY: 260,
          state: 5,
          mergeTableFlag: 0,
          sharedTableFlag: 0,
        ),
      ],
    ),
  ];

}
