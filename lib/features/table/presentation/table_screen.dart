import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_async_value_widget.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_grid_view.dart';
import 'package:kpos/features/table/presentation/table_plan_view.dart';
import 'package:kpos/features/table/presentation/table_screen_controller.dart';
import 'package:kpos/features/table/presentation/table_slide_popup.dart';
import 'package:kpos/features/table/presentation/widgets/index.dart';
import 'package:kpos/routing/app_routing.dart';

enum TableState {
  unknown(
      value: 0,
      color: Colors.red,
      borderColor: Colors.transparent,
      titleColor: Colors.white),
  available(
      value: 1,
      color: Color(0xFFEEEEF0),
      borderColor: Color(0xFF8C8C8C),
      titleColor: Color(0xFF323843)),
  pendingOrder(
      value: 2,
      color: Color(0xFF1B73DE),
      borderColor: Color(0xFF1677FF),
      titleColor: Colors.white),
  unpaid(
      value: 3,
      color: Color(0xFFDE4B11),
      borderColor: Color(0xFFFA541C),
      titleColor: Colors.white),
  needsCleanup(
      value: 4,
      color: Color(0xFF787C96),
      borderColor: Color(0xFF787C96),
      titleColor: Colors.white),
  reserved(
      value: 5,
      color: Color(0xFFFFE291),
      borderColor: Color(0xFFFAAD14),
      titleColor: Color(0xFF323843));

  const TableState({
    required this.value,
    required this.color,
    required this.borderColor,
    required this.titleColor,
  });

  final int value;
  final Color color;
  final Color borderColor;
  final Color titleColor;

  static TableState stateValueToType(int value) {
    return TableState.values.firstWhere(
      (e) => e.value == value,
      orElse: () => TableState.unknown,
    );
  }

  static Color stateValueToColor(int value) {
    return TableState.values
        .firstWhere(
          (e) => e.value == value,
          orElse: () => TableState.unknown,
        )
        .color;
  }

  static Color stateValueToBorderColor(int value) {
    return TableState.values
        .firstWhere(
          (e) => e.value == value,
          orElse: () => TableState.unknown,
        )
        .borderColor;
  }

  static Color stateValueToTitleColor(int value) {
    return TableState.values
        .firstWhere(
          (e) => e.value == value,
          orElse: () => TableState.unknown,
        )
        .titleColor;
  }
}

///拼桌类型，2=待下单拼待支付；1=所有都是待下单；3=所有都是待支付
enum TableSharingType {
  pendingWithUnpaid(value: 2),
  bothPending(value: 1),
  bothUnpaid(value: 3);

  const TableSharingType({
    required this.value,
  });

  final int value;
}

final selectedTableProvider = StateProvider<TableItem?>((ref) {
  return null;
});

final tableTabControllerProvider = StateProvider<TabController?>((ref) => null);
final currentTabIndexProvider = StateProvider<int>((ref) => 0);

class TableScreen extends ConsumerStatefulWidget {
  const TableScreen({super.key});

  @override
  ConsumerState createState() => TableScreenState();
}

class TableScreenState extends ConsumerState<TableScreen>
    with SingleTickerProviderStateMixin {
  // 获取go_router实例
  late final GoRouter _goRouter;

  @override
  void initState() {
    super.initState();
    _goRouter = ref.read(goRouterProvider);
    // 添加路由监听
    _goRouter.routerDelegate.addListener(_onRouteChanged);
  }

  @override
  void dispose() {
    // 移除路由监听
    _goRouter.routerDelegate.removeListener(_onRouteChanged);
    ref.read(tableTabControllerProvider.notifier).state?.dispose();
    super.dispose();
  }

  void _onRouteChanged() {
    // 当路由变化时，移除选中桌台状态，检查当前是否显示弹窗，并关闭
    ref.read(selectedTableProvider.notifier).state = null;
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final areaList = ref.watch(tableServiceProvider.select((s) => s.areaList));
    final showType = ref.watch(tableViewShowTypeProvider);
    final groupList = ref.watch(groupDataProvider);
    final isRightPopUpVisible = ref.watch(showTableSlidePopUpProvider);
    bool isSelectedGroup = ref.watch(selectedGroupAreaProvider);
    const animationDuration = Duration(milliseconds: 300);
    final currentTabIndex = ref.watch(currentTabIndexProvider);
    return KPAsyncValueWidget(
      asyncValueProvider: tableScreenControllerProvider,
      dataBuilder: (context, ref, data) {
        return Container(
          color: Colors.white,
          child: Column(
            children: [
              Container(
                width: double.maxFinite,
                height: 1,
                color: const Color(0xFFEEEEF0),
              ),
              if (isSelectedGroup) TableGroupView(groupList: groupList),
              if (!isSelectedGroup)
                Expanded(
                  child: IndexedStack(
                    index: currentTabIndex,
                    children: List.generate(areaList.length, (index) {
                      return Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const TableTopActionArea(),
                                const SizedBox(height: 20),
                                Expanded(
                                  child: showType == 1
                                      ? TablePlanView(
                                          tableList: areaList[index].tableList,
                                        )
                                      : TableGridView(
                                          tableList: areaList[index].tableList,
                                        ),
                                ),
                              ],
                            ),
                          ),
                          AnimatedContainer(
                            duration: animationDuration,
                            curve: Curves.easeInOut,
                            width: isRightPopUpVisible ? 360 : 0,
                            child: isRightPopUpVisible
                                ? const SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: TableSlidePopup(),
                                  )
                                : null,
                          ),
                        ],
                      );
                    }),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
