import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/kp_number_keyboard.dart';
import 'package:kpos/common/components/kp_toast.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/cart/domain/cart_order_item.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/widgets/table_need_clean.dart';
import 'package:kpos/features/table/presentation/widgets/table_open_top.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:oktoast/oktoast.dart';

class TableSlidePopup extends ConsumerStatefulWidget {
  const TableSlidePopup({super.key});

  @override
  ConsumerState createState() => _TableSlidePopupState();
}

class _TableSlidePopupState extends ConsumerState<TableSlidePopup> {
  @override
  Widget build(BuildContext context) {
    TableItem? table = ref.watch(selectedTableProvider);
    return Container(
      height: double.maxFinite,
      color: Colors.white,
      child: mainWidget(table),
    );
  }

  Widget mainWidget(TableItem? table) {
    //先检查是否是在拼桌后开台阶段
    bool isOpening = ref.watch(sharingTableOpeningProvider);
    if (isOpening) {
      return _buildSharingOpening(table!);
    }
    switch (TableState.stateValueToType(table?.state ?? 0)) {
      case TableState.available:
        //空桌台
        return _buildAvailable(table!);
      case TableState.pendingOrder:
      case TableState.unpaid:
        //待下单、待支付、拼桌
        return _sharingTableOrders(table!);
      case TableState.needsCleanup:
        //待清台
        return TableNeedCleanWidget(table: table!);
      case TableState.reserved:
        //预订
        return Container(
          width: 360,
          height: double.maxFinite,
          color: Colors.yellow,
        );
      default:
        return const SizedBox();
    }
  }

  Widget _buildAvailable(TableItem table) {
    return Container(
      width: 360,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
          // top: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          TableOpenTopWidget(
            tableTitle: table.tableTitle,
            seatNum: table.seatedNumber,
          ),
          //数字键盘Widget
          KPNumberKeyboard(
            buttonWidth: 101,
            buttonHeight: 54,
            buttonSpacing: 8,
            // hintText: KPLocale.of(context).enterNumOfPeople,
            showDot: false,
            maxValue: 999,
            minValue: 1,
            isAutoClamp: true,
            value: table.seatedNumber==0 ? 1 : table.seatedNumber.toDouble(),
            title: KPLocale.of(context).numberOfCovers,
            titleKey: (dialogContext) {
              return KPLocale.of(dialogContext).numberOfCovers;
            },
            onConfirmPressed: (double? value) {
              ref.read(showTableSlidePopUpProvider.notifier).set(false);
              ref.read(selectedTableProvider.notifier).state = null;
              ref.read(tableServiceProvider).setTableState(table: table, state: TableState.pendingOrder);
              TableGroupItem? group = ref.read(groupDataProvider.notifier).findTableGroup(table);
              GoRouter.of(context).go('/product',
                  extra: {"table":table.copyWith(seatedNumber: value?.toInt() ?? 1),"group":group});
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSharingOpening(TableItem table) {
    return Container(
      width: 360,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
          top: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          TableOpenTopWidget(
            tableTitle: '${table.tableTitle}-${(table.subOrderNumber ?? 0) + 1}',
            seatNum: table.seatedNumber,
          ),
          //数字键盘Widget
          KPNumberKeyboard(
            buttonWidth: 101,
            buttonHeight: 54,
            buttonSpacing: 8,
            hintText: KPLocale.of(context).enterNumOfPeople,
            showDot: false,
            maxValue: 999,
            minValue: 1,
            isAutoClamp: true,
            title: KPLocale.of(context).numberOfCovers,
            titleKey: (dialogContext) {
              return KPLocale.of(dialogContext).numberOfCovers;
            },
            onConfirmPressed: (double? value) {
              if(value==null) return;
              ref.read(showTableSlidePopUpProvider.notifier).set(false);
              ref.read(sharingTableOpeningProvider.notifier).set(false);
              ref.read(selectedTableProvider.notifier).state = null;
              ref.read(tableServiceProvider).shareTable(table: table, subNumber: value.toInt());
              TableGroupItem? group = ref.read(groupDataProvider.notifier).findTableGroup(table);
              GoRouter.of(context).go('/product',
                  extra: {"table":table.copyWith(seatedNumber: value.toInt()),"group":group});
              KPToast.show(
                content: context.locale.tableSharingSuccess,
                position: ToastPosition.bottom,
                isGreen: true,
              );
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _sharingTableOrders(TableItem table) {
    List<CartOrderItem> dataList = [];
    final showList = dataList.take(3).toList();
    final hasMore = dataList.length > 3;
    return Container(
      width: 360,
      height: double.maxFinite,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
          border:
              Border(left: BorderSide(width: 1, color: KPColors.borderGrayLightDarkest))),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                table.tableTitle,
                style: KPFontStyle.headingXLarge.copyWith(
                  color: KPColors.textGrayPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                context.locale.shareSomeOrders(4),
                style: KPFontStyle.bodyMedium.copyWith(
                  color: KPColors.fillGrayDarkLighter,
                ),
              ),
            ],
          ),
          const SizedBox(height: 26),
          Expanded(
            child: ListView.separated(
              padding: EdgeInsetsDirectional.zero,
              shrinkWrap: true,
              itemCount: 4,
              separatorBuilder: (context,i){
                return const SizedBox(height: 12);
              },
              itemBuilder: (ctx, index) {
                return Container(
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: const Border(
                      left: BorderSide(width: 6,color: Color(0xFFDE4B11)),
                    ),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: const Border(
                        right: BorderSide(width: 1,color: KPColors.borderGrayLightDarkest),
                        top: BorderSide(width: 1,color: KPColors.borderGrayLightDarkest),
                        bottom: BorderSide(width: 1,color: KPColors.borderGrayLightDarkest),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Text(
                              '${table.tableTitle}${index==0?'': '-$index'}',
                              style: KPFontStyle.headingMedium.copyWith(
                                color: KPColors.textGrayPrimary,
                              ),
                            ),
                            const Expanded(child: SizedBox()),
                            Text(
                              'Unpaid',
                              style: KPFontStyle.headingXSmall.copyWith(
                                color: const Color(0xFFDE4B11),
                              ),
                            ),
                            const Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 16,
                              color: Color(0xFFDE4B11),
                            )
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            //人数图标
                            Assets.images.iconPeopleNum.svg(
                              width: 16,
                              height: 16,
                              color: KPColors.textGraySecondary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              table.seatedNumber.toString(),
                              style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                            ),
                            Container(
                              width: 1,
                              height: 12,
                              color: KPColors.iconGrayQuaternary,
                              margin: const EdgeInsets.symmetric(horizontal: 8),
                            ),
                            Assets.images.iconDishNum.svg(
                              width: 16,
                              height: 16,
                              // color: KPColors.textGraySecondary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '5',
                              style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                            ),
                            const Expanded(child: SizedBox()),
                            Text(
                              'S\$43.00',
                              style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        // 商品列表(最多显示3个，多于3个显示省略号)
                        IntrinsicHeight(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Container(
                                width: 1,
                                margin: const EdgeInsets.only(right: 12),
                                color: KPColors.borderGrayLightDarkest,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ...showList.map((item) => _productItem('', 3)),
                                    if (hasMore)
                                      Text(
                                        '...',
                                        style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _productItem(String title, int num) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
          ),
        ),
        Text(
          "×$num",
          style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
        ),
      ],
    );
  }
}
