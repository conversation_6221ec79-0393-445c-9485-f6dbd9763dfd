import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/utils/common_util.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_plan_view.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/features/table/presentation/widgets/index.dart';

class TableGridView extends ConsumerStatefulWidget {
  final List<TableItem> tableList;

  const TableGridView({super.key, required this.tableList});

  @override
  ConsumerState createState() => _TableGridViewState();
}

class _TableGridViewState extends ConsumerState<TableGridView> {
  // 配置参数
  final double itemWidth = 120;
  final double itemHeight = 120;
  final double rowGap = 10;
  final double columnGap = 10;
  final double horizontalPadding = 20;

  // 状态过滤
  List<TableItem> _filterTables(List<TableItem> original, List<int> statuses) {
    if (statuses.isEmpty) {
      return original;
    }
    return original.where((item) => statuses.contains(item.state)).toList();
  }

  int getColumnCount(double width) {
    if (width >= 1818) return 14;
    if (width >= 1696) return 13;
    if (width >= 1574) return 12;
    if (width >= 1452) return 11;
    if (width >= 1330) return 10;
    if (width >= 1208) return 9;
    if (width >= 1086) return 8;
    if (width >= 964) return 7;
    return 6;
  }

  @override
  Widget build(BuildContext context) {
    final areaId = ref.watch(tableServiceProvider).curArea?.areaId;
    final filterStatus = ref.watch(areaFilterStatusProvider.select((map) => map[areaId] ?? []));
    //过滤数据
    final filteredList = _filterTables(widget.tableList, filterStatus);
    final selectedItem = ref.watch(selectedTableProvider);
    //时间展示格式 1=时长 2=时间
    final timeShowType = ref.watch(tableTimeDisplayShowProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: LayoutBuilder(builder: (context, constraints) {
        final width = constraints.maxWidth;
        final columnCount = getColumnCount(width);
        final rowCount = (filteredList.length / columnCount).ceil();
        final totalColumnGap = columnGap * (columnCount - 1);
        final itemWidth = (width - totalColumnGap) / columnCount;
        const itemHeight = 120.0;
        final columnSizes = List.generate(columnCount, (_) => fixed(itemWidth));
        final rowSizes = List.generate(rowCount, (_) => fixed(itemHeight));
        if(rowSizes.isEmpty){
          return _buildNoTable();
        }
        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: LayoutGrid(
            columnSizes: columnSizes,
            rowSizes: rowSizes,
            rowGap: rowGap,
            columnGap: columnGap,
            children: [
              for (var i = 0; i < filteredList.length; i++)
                _buildItem(
                  filteredList[i],
                  selectedItem?.tableInfoId == filteredList[i].tableInfoId,
                  timeShowType,
                )
            ],
          ),
        );
      }),
    );
    // return LayoutBuilder(
    //   builder: (context, constraints) {
    //     //过滤数据
    //     final filteredList = _filterTables(widget.tableList, filterStatus);
    //     //计算列数
    //     final columnCount =
    //     _calculateColumnCount(constraints.maxWidth - horizontalPadding * 2);
    //     //计算行数
    //     final rowCount = (filteredList.length / columnCount).ceil();
    //
    //     //空状态判断
    //     if (filteredList.isEmpty) {
    //       return _buildNoTable();
    //     }
    //
    //     return Container(
    //       alignment: Alignment.topLeft,
    //       padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
    //       child: SingleChildScrollView(
    //         child: Container(
    //           color: Colors.white,
    //           // 设置外层Container宽度
    //           width: columnCount * itemWidth + (columnCount - 1) * columnGap,
    //           child: LayoutGrid(
    //             columnSizes:
    //             List.generate(columnCount, (_) => fixed(itemWidth)),
    //             rowSizes: List.generate(rowCount, (_) => fixed(itemHeight)),
    //             columnGap: columnGap,
    //             rowGap: rowGap,
    //             children: List.generate(
    //               filteredList.length,
    //                   (index) => _buildItem(filteredList[index]),
    //             ),
    //           ),
    //         ),
    //       ),
    //     );
    //   },
    // );
  }

  // 动态计算列数
  int _calculateColumnCount(double availableWidth) {
    final effectiveWidth = itemWidth + columnGap;
    final maxPossibleColumns = (availableWidth / effectiveWidth).floor();
    return maxPossibleColumns < 2 ? 1 : maxPossibleColumns;
  }


  Widget _buildItem(TableItem table, bool isSelected,int timeShowType) {
    Color useColor = TableState.stateValueToColor(table.state ?? 0);
    Color useBorderColor = TableState.stateValueToBorderColor(table.state ?? 0);
    int tableAction = ref.watch(tableActionTypeProvider);
    if(tableAction==TableAction.group.value){
      //联台操作下选中状态为多选
      List<TableItem> tableGroup = ref.watch(multiSelectedTablesProvider);
      isSelected = tableGroup.any((e)=>e.tableTitle==table.tableTitle);
    }
    final curTransferTable = ref.watch(curTransferTableProvider);
    if(curTransferTable.$1?.tableTitle==table.tableTitle||curTransferTable.$2?.tableTitle==table.tableTitle){
      isSelected = true;
    }
    //拼桌需要展示多种状态颜色的情况
    bool isSharingAndMulti = table.sharedTableFlag==TableSharingType.pendingWithUnpaid.value&&isSelected;
    bool isSharing = table.sharedTableFlag>0;
    //联台号（为空时代表不是联台）
    String? groupName = ref.read(groupDataProvider.notifier).findTableGroup(table)?.groupName;
    return Container(
      //底部和右侧边框容器
      decoration: BoxDecoration(
        border: isSharingAndMulti? const Border(
          bottom: BorderSide(color: Color(0xFF1B73DE), width: 4,strokeAlign: BorderSide.strokeAlignOutside,),
          right: BorderSide(color: Color(0xFF1B73DE), width: 4,strokeAlign: BorderSide.strokeAlignOutside,),
        ):Border.all(
          color: isSelected ? useBorderColor : Colors.transparent,
          width: 4,
          strokeAlign: BorderSide.strokeAlignOutside,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        // 顶部和左侧边框容器
        decoration: BoxDecoration(
          border: isSharingAndMulti? const Border(
            top: BorderSide(color: Color(0xFFDE4B11), width: 4),
            left: BorderSide(color: Color(0xFFDE4B11), width: 4),
          ):Border.all(
            color: isSelected ? useBorderColor : Colors.transparent,
            width: 4,
            strokeAlign: BorderSide.strokeAlignOutside,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: [
          //最底层的装饰容器
          Container(
            constraints: const BoxConstraints.expand(),
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: useColor,
              borderRadius: BorderRadius.circular(8),
              gradient: table.sharedTableFlag==TableSharingType.pendingWithUnpaid.value? const LinearGradient(
                  colors: [
                    Color(0xFFDE4B11),
                    Color(0xFF984C68),
                    Color(0xFF0762D2),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  stops: [0.0,0.5,1]
              ):null,
            ),
          ),
          //拼桌的对半遮罩层
          Visibility(
            visible: isSharing,
            child: CustomPaint(
              painter: DiagonalOverlayPainter(
                bottomRightOpacity: table.sharedTableFlag==TableSharingType.pendingWithUnpaid.value?0.1:0.25,
                topLeftOpacity: 0,
              ),
              size: Size(const BoxConstraints.expand().maxWidth+2, const BoxConstraints.expand().maxHeight+2),
            ),
          ),
          //核心内容层
          Container(
            constraints: const BoxConstraints.expand(),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //开台时间或时长
                Visibility(
                  visible: (table.state == TableState.pendingOrder.value || table.state == TableState.unpaid.value)&&!isSharing,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      timeShowType == 1
                          ? Assets.images.iconHourglass.svg(width: 16, height: 16)
                          : Assets.images.iconClock.svg(width: 16, height: 16),
                      const SizedBox(width: 4),
                      Builder(
                        builder: (context) {
                          if (table.createTime == null) return const Text('--:--:--');
                          return StreamBuilder<int>(
                            stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
                            builder: (context, snapshot) {
                              final duration = CommonUtil.durationFromTimestamp(table.createTime!);
                              return Text(
                                timeShowType == 1
                                    ? CommonUtil.formatDuration(duration)
                                    : CommonUtil.formatTimestamp(table.createTime!,pattern: 'HH:mm:ss'),
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
                //预订时间
                Visibility(
                  visible: table.state == TableState.reserved.value,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Assets.images.iconCalendarCheck.svg(width: 16, height: 16),
                      const SizedBox(width: 4),
                      const Text(
                        '18:00',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF323843),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                //待清台图标
                Visibility(
                  visible: table.state == TableState.needsCleanup.value,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 3),
                    child: Assets.images.iconClean.svg(width: 16, height: 16),
                  ),
                ),
                if (table.state == TableState.available.value||isSharing) const SizedBox(height: 19),
                Text(
                  table.tableTitle,
                  style: TextStyle(
                    color: TableState.stateValueToTitleColor(table.state ?? 0),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 3),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    //座位图标
                    Visibility(
                      visible: (table.state == TableState.available.value || table.state == TableState.needsCleanup.value)&&!isSharing,
                      child: Assets.images.iconSeat.svg(
                        width: 16,
                        height: 16,
                        color: table.state == 4 ? Colors.white : null,
                      ),
                    ),
                    //人数图标
                    Visibility(
                      visible: (table.state == TableState.pendingOrder.value || table.state == TableState.unpaid.value || table.state == TableState.reserved.value)&&!isSharing,
                      child: Assets.images.iconPeopleNum.svg(
                        width: 16,
                        height: 16,
                        color: table.state == TableState.reserved.value ? const Color(0xFF323843) : null,
                      ),
                    ),
                    //拼桌图标
                    Visibility(
                      visible: isSharing,
                      child: Assets.images.iconTableSharing.svg(
                        width: 16,
                        height: 16,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      !isSharing?table.seatedNumber.toString():table.subOrderNumber.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        color: TableState.stateValueToTitleColor(table.state ?? 0),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          //转台图标
          Positioned(
            bottom: 7,
            right: 7,
            child: Visibility(
              visible: curTransferTable.$1?.tableTitle==table.tableTitle||curTransferTable.$2?.tableTitle==table.tableTitle,
              child: Container(
                width: 22,
                height: 22,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: curTransferTable.$1?.tableTitle==table.tableTitle?KPColors.fillBrandNormal:KPColors.fillBlueNormal,
                    border: Border.all(width: 1.5,color: KPColors.borderGrayLightLightest)
                ),
                child: SvgPicture.asset(curTransferTable.$1?.tableTitle==table.tableTitle?Assets.images.iconCornerUpRight.path:Assets.images.iconCornerLeftDown.path,width: 16,height: 16,),
              ),
            ),
          ),
          //联台图标
          Positioned(
            right: 7,
            bottom: 7,
            child: Visibility(
              visible: tableAction==TableAction.group.value&&isSelected,
              child: Container(
                width: 22,
                height: 22,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: KPColors.fillBrandNormal,
                  border: Border.all(width: 1.5,color: KPColors.borderGrayLightLightest),
                ),
                child: Assets.images.iconTableLink.svg(
                  width: 14,
                  height: 14,
                ),
              ),
            ),
          ),
          //催菜图标
          Positioned(
            right: -8,
            top: -8,
            child: Visibility(
              visible: table.state == TableState.unpaid.value,
              child: Assets.images.iconUrge.svg(width: 24, height: 24),
            ),
          ),
          if(groupName!=null)
            Positioned(
              bottom: -8,
              child: Container(
                height: 16,
                padding: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: KPColors.fillGrayLightLightest,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(width: 1,color: KPColors.borderGrayDarkLightest),
                ),
                child: Row(
                  children: [
                    Assets.images.iconTableLink.svg(
                      width: 12,
                      height: 12,
                      color: KPColors.iconGrayPrimary,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      groupName,
                      style: KPFontStyle.bodySmall.copyWith(color: KPColors.textGrayPrimary),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      ),
    ).onTap(() {
      ref.read(tableServiceProvider).itemTapEvent(context, table);
    });
  }

  Widget _buildNoTable() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Assets.images.iconNoTable.svg(width: 32, height: 32),
        const SizedBox(height: 8),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            KPLocale.of(context).noTableTip,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF323843),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }
}
