import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/button/kp_button.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_loading.dart';
import 'package:kpos/features/table/application/table_server_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/data/table_intranet_repository.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/features/table/presentation/table_screen_controller.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class TableNeedCleanWidget extends ConsumerWidget {
  final TableItem table;

  const TableNeedCleanWidget({super.key, required this.table});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 360,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
          top: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                table.tableTitle,
                style: const TextStyle(
                  fontSize: 24,
                  color: Color(0xFF323843),
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${table.seatedNumber} ${KPLocale.of(context).seats}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF858A99),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          const SizedBox(height: 59),
          Assets.images.cleanTable
              .image(width: 240, height: 240, fit: BoxFit.cover),
          Text(
            KPLocale.of(context).tableCleanConfirm,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 20,
              color: Color(0xFF323843),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            KPLocale.of(context).tableCleanConfirmTip,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF858A99),
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 54),
          KPButton(
            width: 320,
            height: 48,
            style: TDButtonStyle(
              backgroundColor: const Color(0xFF20232B),
            ),
            child: Center(
              child: Text(
                KPLocale.of(context).confirmCaps,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            onPressed: () async {
              try {
                // 调用清台接口
                await ref.read(tableIntranetRepositoryProvider).cleanTable(
                  tableNumber: table.tableTitle,
                );
                // 刷新桌台页面
                ref.invalidate(tableScreenControllerProvider);
                ref.read(showTableSlidePopUpProvider.notifier).set(false);
                if (context.mounted) {
                  KPToast.show(
                    content: KPLocale.of(context).tableCleanSuccessfully,
                    duration: const Duration(seconds: 1),
                    position: ToastPosition.bottom,
                  );
                }
              } catch (e) {
                debugPrint("清台失败: $e");
              }
            },
          ),
        ],
      ),
    );
  }
}
