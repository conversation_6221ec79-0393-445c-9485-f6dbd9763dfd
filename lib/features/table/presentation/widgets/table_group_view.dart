import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/utils/common_util.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:oktoast/oktoast.dart';

class TableGroupView extends ConsumerStatefulWidget {
  final List<TableGroupItem> groupList;

  const TableGroupView({super.key, required this.groupList});

  @override
  ConsumerState createState() => _TableGroupViewState();
}

class _TableGroupViewState extends ConsumerState<TableGroupView> {
  final double itemWidth = 120;
  final double itemHeight = 120;
  final double rowGap = 10;
  final double columnGap = 10;
  final double horizontalPadding = 20;

  final editNameErrorProvider = StateProvider<bool>((ref) => false);

  ValueNotifier<int> showTextFieldIndex = ValueNotifier(0);
  final nameEditController = TextEditingController();
  final _focusNode = FocusNode();

  int getColumnCount(double width) {
    if (width >= 1818) return 14;
    if (width >= 1696) return 13;
    if (width >= 1574) return 12;
    if (width >= 1452) return 11;
    if (width >= 1330) return 10;
    if (width >= 1208) return 9;
    if (width >= 1086) return 8;
    if (width >= 964) return 7;
    return 6;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        if(_focusNode.hasFocus){
          _focusNode.unfocus();
          showTextFieldIndex.value = 0;
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        shrinkWrap: true,
        itemCount: widget.groupList.length,
        itemBuilder: (context, index) {
          return _buildItem(widget.groupList[index],index);
        },
      ),
    );
  }

  Widget _buildItem(TableGroupItem item,int index) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final columnCount = getColumnCount(width);
        final rowCount = (item.tableList.length / columnCount).ceil();
        final columnSizes = List.generate(columnCount, (_) => fixed(itemWidth));
        final rowSizes = List.generate(rowCount, (_) => fixed(itemHeight));
        //时间展示格式 1=时长 2=时间
        final timeShowType = ref.watch(tableTimeDisplayShowProvider);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 30),
            Row(
              children: [
                ValueListenableBuilder(valueListenable: showTextFieldIndex, builder: (ctx,i,_){
                  if(i==index+1){
                    return _buildNameEditView(item);
                  }
                  return Row(
                    children: [
                      Text(
                        item.groupName,
                        style:
                        KPFontStyle.headingMedium.copyWith(color: KPColors.textGrayPrimary),
                      ),
                      GestureDetector(
                        onTap:(){
                          showTextFieldIndex.value = index+1;
                          _focusNode.requestFocus();
                          nameEditController.text = item.groupName;
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 7),
                          child: Assets.images.iconEdit02.svg(width: 16, height: 16),
                        ),
                      ),
                    ],
                  );
                },),
                const SizedBox(width: 12),
                Text(
                  context.locale.tablesWithCount(item.tableList.length),
                  style:
                      KPFontStyle.bodyMedium.copyWith(color: KPColors.textGraySecondary),
                ),
                const Expanded(child: SizedBox()),
                //点击取消联台
                GestureDetector(
                  onTap: () {
                    KPDialog.showAlertDialog(
                      context: context,
                      title: context.locale
                          .confirmUngroupTip(item.tableList.length, item.groupName),
                      content: context.locale.ungroupRemind,
                      rightBtnTitle: context.locale.confirmCaps,
                      rightBtnBgColor: KPColors.fillRedNormal,
                      rightAction: () {
                        Navigator.of(context).pop();
                        ref.read(tableServiceProvider).removeGroup(item);
                        KPToast.show(
                          content: context.locale.tableUngroupSuccess,
                          position: ToastPosition.bottom,
                          backgroundColor: KPColors.fillGreenNormal,
                        );
                      },
                    );
                  },
                  child: Row(
                    children: [
                      Assets.images.iconLinkBroken.svg(width: 18, height: 18),
                      const SizedBox(width: 4),
                      Text(
                        context.locale.ungroupTables,
                        style: KPFontStyle.headingXSmall
                            .copyWith(color: KPColors.textRedDefault),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            LayoutGrid(
              columnSizes: columnSizes,
              rowSizes: rowSizes,
              rowGap: rowGap,
              columnGap: columnGap,
              children: [
                for (var i = 0; i < item.tableList.length; i++)
                  _buildTableItem(item.tableList[i], timeShowType).onTap(() {
                    GoRouter.of(context).go('/product',
                        extra: {"table":item.tableList[i],"group":item});
                  }),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              width: double.maxFinite,
              height: 1,
              color: KPColors.borderGrayLightBase,
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableItem(TableItem table, int timeShowType) {
    Color useColor = TableState.stateValueToColor(table.state ?? 0);
    return Stack(
      clipBehavior: Clip.none,
      alignment: Alignment.center,
      children: [
        //最底层的装饰容器
        Container(
          width: itemWidth,
          height: itemHeight,
          margin: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: useColor,
            borderRadius: BorderRadius.circular(8),
            gradient: table.sharedTableFlag == TableSharingType.pendingWithUnpaid.value
                ? const LinearGradient(
                    colors: [
                      Color(0xFFDE4B11),
                      Color(0xFF984C68),
                      Color(0xFF0762D2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    stops: [0.0, 0.5, 1])
                : null,
          ),
        ),
        //核心内容层
        Container(
          constraints: const BoxConstraints.expand(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              //开台时间或时长
              Visibility(
                visible: (table.state == TableState.pendingOrder.value ||
                        table.state == TableState.unpaid.value) &&
                    table.sharedTableFlag == 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    timeShowType == 1
                        ? Assets.images.iconHourglass.svg(width: 16, height: 16)
                        : Assets.images.iconClock.svg(width: 16, height: 16),
                    const SizedBox(width: 4),
                    Builder(
                      builder: (context) {
                        if (table.createTime == null) return const Text('--:--:--');
                        return StreamBuilder<int>(
                          stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
                          builder: (context, snapshot) {
                            final now = DateTime.now();
                            final duration = CommonUtil.durationFromTimestamp(table.createTime!);
                            return Text(
                              timeShowType == 1
                                  ? CommonUtil.formatDuration(duration)
                                  : CommonUtil.formatTimestamp(table.createTime!,pattern: 'HH:mm:ss'),
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w400,
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
              //预订时间
              Visibility(
                visible: table.state == TableState.reserved.value,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.iconCalendarCheck.svg(width: 16, height: 16),
                    const SizedBox(width: 4),
                    const Text(
                      '18:00',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF323843),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              //待清台图标
              Visibility(
                visible: table.state == TableState.needsCleanup.value,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 3),
                  child: Assets.images.iconClean.svg(width: 16, height: 16),
                ),
              ),
              if (table.state == TableState.available.value || table.sharedTableFlag >0)
                const SizedBox(height: 19),
              Text(
                table.tableTitle,
                style: TextStyle(
                  color: TableState.stateValueToTitleColor(table.state ?? 0),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 3),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  //座位图标
                  Visibility(
                    visible: (table.state == TableState.available.value ||
                            table.state == TableState.needsCleanup.value) &&
                        table.sharedTableFlag == 0,
                    child: Assets.images.iconSeat.svg(
                      width: 16,
                      height: 16,
                      color: table.state == 4 ? Colors.white : null,
                    ),
                  ),
                  //人数图标
                  Visibility(
                    visible: (table.state == TableState.pendingOrder.value ||
                            table.state == TableState.unpaid.value ||
                            table.state == TableState.reserved.value) &&
                        table.sharedTableFlag == 0,
                    child: Assets.images.iconPeopleNum.svg(
                      width: 16,
                      height: 16,
                      color: table.state == TableState.reserved.value
                          ? const Color(0xFF323843)
                          : null,
                    ),
                  ),
                  //拼桌图标
                  Visibility(
                    visible: table.sharedTableFlag >0,
                    child: Assets.images.iconTableSharing.svg(
                      width: 16,
                      height: 16,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    table.seatedNumber.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      color: TableState.stateValueToTitleColor(table.state ?? 0),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        //催菜图标
        Positioned(
          right: -8,
          top: -8,
          child: Visibility(
            visible: table.state == TableState.unpaid.value,
            child: Assets.images.iconUrge.svg(width: 24, height: 24),
          ),
        ),
      ],
    );
  }

  Widget _buildNameEditView(TableGroupItem item) {
    bool isError = ref.watch(editNameErrorProvider);
    return Container(
      height: 48,
      width: 240,
      padding: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isError
                ? KPColors.textRedDefault
                : KPColors.textBrandDefault,
            width: 3.0,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.locale.groupName,
            style: TextStyle(
              fontSize: 12,
              color: isError
                  ? KPColors.textRedDefault
                  : KPColors.textBrandDefault,
            ),
          ),
          Expanded(
            child: SizedBox(
              height: 24,
              child: TextField(
                focusNode: _focusNode,
                controller: nameEditController,
                cursorColor: isError
                    ? KPColors.textRedDefault
                    : KPColors.textBrandDefault,
                cursorHeight: 16,
                cursorWidth: 1,
                maxLength: 10, // 限制最多10个字符
                decoration: InputDecoration(
                  border: InputBorder.none,
                  isDense: true,
                  suffixIcon: isError
                      ? const Icon(
                    Icons.error,
                    color: KPColors.textRedDefault,
                    size: 24,
                  ) : null,
                  suffixIconConstraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                    maxWidth: 32,
                    maxHeight: 32,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    vertical: 2,
                    horizontal: 0,
                  ),
                ),
                buildCounter: (
                    BuildContext context, {
                      required int currentLength,
                      required bool isFocused,
                      required int? maxLength,
                    }) => null,// 不显示计数器
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400),
                onSubmitted: (value) {
                  showTextFieldIndex.value = 0;
                  if(value.isNotEmpty){
                    ref.read(tableServiceProvider).renameGroup(item, value);
                  }
                },
                onChanged: (v) {
                  if(v.isEmpty){
                    ref.read(editNameErrorProvider.notifier).state = true;
                  }else{
                    if(isError){
                      ref.read(editNameErrorProvider.notifier).state = false;
                    }
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
