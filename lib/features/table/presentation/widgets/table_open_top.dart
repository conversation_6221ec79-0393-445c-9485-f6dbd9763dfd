import 'package:flutter/material.dart';
import 'package:kpos/assets/kp_locale.g.dart';

class TableOpenTopWidget extends StatelessWidget {
  final String tableTitle;
  final int seatNum;
  const TableOpenTopWidget({super.key, required this.tableTitle, required this.seatNum});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 319,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            tableTitle,
            style: const TextStyle(
              fontSize: 24,
              color: Color(0xFF323843),
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '$seatNum ${KPLocale.of(context).seats}',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF858A99),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
