import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/features/table/presentation/widgets/table_status_fitter_bar.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../../assets/kp_locale.g.dart';

enum TableAction {
  ///转台
  transfer(value: 1),

  ///撤台
  clearing(value: 2),

  ///拼桌
  sharing(value: 3),

  ///联台
  group(value: 4);

  const TableAction({
    required this.value,
  });

  final int value;

  static TableAction typeToAction(int value) {
    return TableAction.values.firstWhere((e) => e.value == value);
  }
}

class TableTopActionArea extends ConsumerStatefulWidget {
  const TableTopActionArea({super.key});

  @override
  ConsumerState createState() => _TableTopActionAreaState();
}

class _TableTopActionAreaState extends ConsumerState<TableTopActionArea> {
  // int _selectedIndex = 0;
  //
  // final List<TabItem> _tabItems = [
  //   TabItem(
  //     icon: Assets.images.iconGrid.path,
  //     label: 'Grid',
  //     selectedColor: const Color(0xFFFF8500),
  //     unselectedColor: const Color(0xFF323843),
  //   ),
  //   TabItem(
  //     icon: Assets.images.iconCalendarCheck.path,
  //     label: 'Calendar',
  //     selectedColor: const Color(0xFFFF8500),
  //     unselectedColor: const Color(0xFF323843),
  //   ),
  // ];

  //根据TableAction获取国际化文字
  String getActionTitle(BuildContext context, TableAction action) {
    switch (action) {
      case TableAction.sharing:
        return context.locale.sharingTable;
      case TableAction.group:
        return context.locale.groupTables;
      case TableAction.transfer:
        return context.locale.transferTable;
      case TableAction.clearing:
        return context.locale.clearingTable;
    }
  }

  //TableAction点击事件
  void actionOnTap(BuildContext context, TableAction action) {
    if (context.mounted) {
      context.pop();
    }
    ref.read(tableActionTypeProvider.notifier).set(action.value);
    switch (action) {
      case TableAction.sharing:
        ref
            .read(areaFilterStatusProvider.notifier)
            .setStatus([TableState.pendingOrder.value, TableState.unpaid.value]);
        ref.read(selectedTableProvider.notifier).state = null;
        break;
      case TableAction.group:
        ref.read(areaFilterStatusProvider.notifier).setStatus([
          TableState.available.value,
          TableState.pendingOrder.value,
          TableState.unpaid.value
        ]);
        break;
      case TableAction.transfer:
        ref
            .read(areaFilterStatusProvider.notifier)
            .setStatus([TableState.pendingOrder.value, TableState.unpaid.value]);
        ref.read(curTransferTableProvider.notifier).reset();
        ref.read(selectedTableProvider.notifier).state = null;
        break;
      case TableAction.clearing:
        ref
            .read(areaFilterStatusProvider.notifier)
            .setStatus([TableState.pendingOrder.value]);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final tableActionType = ref.watch(tableActionTypeProvider);
    final areaList = ref.watch(tableServiceProvider).curArea;
    // 获取当前区域 areaId
    final areaId = areaList?.areaId ?? '';
    // 获取当前区域的筛选状态
    List<int> filterStatus =
        ref.watch(areaFilterStatusProvider.select((map) => map[areaId] ?? []));
    final curTransferTable = ref.watch(curTransferTableProvider);
    int actionType = ref.watch(tableActionTypeProvider);
    List<TableItem> tableGroup = ref.watch(multiSelectedTablesProvider);
    bool isConfirmActive =
        actionType == TableAction.transfer.value ? curTransferTable.$2 != null : actionType == TableAction.group.value?tableGroup.length>1:true;
    return SizedBox(
      width: double.maxFinite,
      height: 60,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // const SizedBox(width: 22),
          // Container(
          //   height: 40,
          //   decoration: BoxDecoration(
          //     color: const Color(0xFFF4F5F7),
          //     borderRadius: BorderRadius.circular(8),
          //   ),
          //   child: Row(
          //     children: [
          //       // 左侧网格按钮
          //       _buildTabItem(
          //         index: 0,
          //         item: _tabItems[0],
          //         isSelected: _selectedIndex == 0,
          //       ),
          //       // 中间分隔线
          //       Container(
          //         width: 1,
          //         height: 16,
          //         color: const Color(0xFFA2A6B1),
          //         margin: const EdgeInsets.symmetric(horizontal: 4),
          //       ),
          //       // 右侧日历按钮
          //       _buildTabItem(
          //         index: 1,
          //         item: _tabItems[1],
          //         isSelected: _selectedIndex == 1,
          //       ),
          //     ],
          //   ),
          // ),
          //筛选框
          Visibility(
            visible: tableActionType == 0,
            child: Expanded(
              child: Center(
                child: TableStatusFitterBar(
                  tableList: areaList?.tableList ?? [],
                  selectedStatus: filterStatus.isEmpty ? 0 : filterStatus.first,
                  onFilter: (status) {
                    ref
                        .read(areaFilterStatusProvider.notifier)
                        .setStatus(status == 0 ? [] : [status]);
                    ref.read(selectedTableProvider.notifier).state = null;
                  },
                ),
              ),
            ),
          ),
          Visibility(visible: tableActionType > 0&&tableActionType!=TableAction.clearing.value, child: const SizedBox(width: 200)),
          //Action操作提示
          Visibility(
            visible: tableActionType > 0,
            child: Expanded(
              child: _buildTipText(context, tableActionType, curTransferTable),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(width: 16),
              //action 按钮
              Visibility(
                visible: tableActionType < 1,
                child: _buildActionButton(),
              ),
              //取消按钮
              Visibility(
                visible: tableActionType > 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF3F3F4),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    KPLocale.of(context).cancelCaps,
                    style: KPFontStyle.headingSmall
                        .copyWith(color: KPColors.textGrayPrimary),
                  ),
                ).onTap(() {
                  ref.read(tableActionTypeProvider.notifier).set(0);
                  ref.read(areaFilterStatusProvider.notifier).reset();
                  ref.read(curTransferTableProvider.notifier).reset();
                  ref.read(multiSelectedTablesProvider.notifier).clear();
                }),
              ),
              //确认按钮
              Visibility(
                visible:
                    tableActionType > 0 && tableActionType != TableAction.clearing.value,
                child: Container(
                  margin: const EdgeInsets.only(left: 12),
                  padding: EdgeInsets.symmetric(
                      horizontal: isConfirmActive ? 16 : 15,
                      vertical: isConfirmActive ? 8 : 7),
                  decoration: BoxDecoration(
                    color: isConfirmActive
                        ? KPColors.fillGrayDarDarkest
                        : KPColors.fillGrayLightLight,
                    borderRadius: BorderRadius.circular(8),
                    border: isConfirmActive
                        ? null
                        : Border.all(width: 1, color: KPColors.borderGrayLightDarkest),
                  ),
                  child: Text(
                    KPLocale.of(context).confirmCaps,
                    style: KPFontStyle.headingSmall.copyWith(
                        color: isConfirmActive
                            ? KPColors.textGrayInverse
                            : KPColors.textGrayTertiary),
                  ),
                ).onTap(() {
                  if (!isConfirmActive) return;
                  switch (TableAction.typeToAction(actionType)) {
                    case TableAction.transfer:
                      if (curTransferTable.$1 == null || curTransferTable.$2 == null) {
                        return;
                      }
                      if(curTransferTable.$2?.state!=TableState.available.value){
                        //todo 拼桌的情况
                        sharingTable(curTransferTable.$2!,false,inTable: curTransferTable.$1!);
                      }else{
                        // 调用转台API
                        ref.read(tableServiceProvider).transferTable(
                          from: curTransferTable.$1!,
                          to: curTransferTable.$2!,
                        ).then((_) {
                          if(context.mounted){
                            KPToast.show(
                              content: context.locale.tableTransferSuccess,
                              backgroundColor: KPColors.fillGreenNormal,
                              position: ToastPosition.bottom,
                            );
                          }
                          back();
                        }).catchError((e) {
                          debugPrint("转台失败: $e");
                        });
                      }
                      break;
                    case TableAction.clearing:
                      break;
                    case TableAction.sharing:
                      TableItem? table = ref.watch(selectedTableProvider);
                      if (table == null) return;
                      //将桌台改为拼桌
                      sharingTable(table,true);
                      back();
                      break;
                    case TableAction.group:
                      groupConfirm(tableGroup);
                      break;
                  }
                }),
              ),
            ],
          ),
          const SizedBox(width: 20),
        ],
      ),
    );
  }

  //拼桌
  void sharingTable(TableItem outTable,bool isOpenTable,{TableItem? inTable}){
    if(isOpenTable){
      ref.read(tableServiceProvider).setTableState(
        table: outTable,
        state: TableState.stateValueToType(outTable.state ?? 2),
        sharedTableFlag: 1,
      );
      ref.read(sharingTableOpeningProvider.notifier).set(true);
      ref.read(showTableSlidePopUpProvider.notifier).set(true);
      back();
    }else{
      KPDialog.showAlertDialog(
        context: context,
        title: context.locale
            .pleaseConfirm,
        content: context.locale.sharingContinueTip(inTable?.tableTitle??'', outTable.tableTitle),
        rightBtnBgColor: KPColors.fillGrayDarDarkest,
        rightAction: (){
          ref.read(tableServiceProvider).setTableState(
            table: outTable,
            state: TableState.stateValueToType(outTable.state ?? 2),
            sharedTableFlag: 1,
          );
          context.pop();
          ref.read(tableServiceProvider).shareTable(table: outTable, subNumber: inTable?.seatedNumber??1);
          KPToast.show(
            content: context.locale.tableSharingSuccess,
            backgroundColor: KPColors.fillGreenNormal,
            position: ToastPosition.bottom,
          );
          back();
        }
      );
    }
  }

  void groupConfirm(List<TableItem> tableGroup) {
    KPDialog.showCustomDialog(
      context: context,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.locale.pleaseConfirm,
              style: KPFontStyle.headingXLarge.copyWith(color: KPColors.textGrayPrimary),
            ),
            Container(
              width: 438,
              margin: const EdgeInsets.symmetric(vertical: 16),
              child: Text(
                context.locale.tableGroupConfirmTip(tableGroup.map((e) => e.tableTitle).join(','),),
                style: KPFontStyle.bodyMedium.copyWith(color: KPColors.textGrayPrimary),
              ),
            ),
            KPNumberKeyboard(
              buttonWidth: 138,
              buttonHeight: 54,
              buttonSpacing: 8,
              hintText: KPLocale.of(context).enterNumOfPeople,
              showDot: false,
              maxValue: 999,
              minValue: 1,
              isAutoClamp: true,
              title: KPLocale.of(context).numberOfCovers,
              titleKey: (dialogContext) {
                return KPLocale.of(dialogContext).numberOfCovers;
              },
              onCancelPressed: () {
                ref.read(multiSelectedTablesProvider.notifier).clear();
                back();
              },
              onConfirmPressed: (double? v) {
                context.pop();
                ref.read(multiSelectedTablesProvider.notifier).clear();
                back();
                ref.read(tableServiceProvider).setGroupList(tableGroup);
                KPToast.show(
                  content: context.locale.tableLinkingSuccess,
                  backgroundColor: KPColors.fillGreenNormal,
                  position: ToastPosition.bottom,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  //点击确认返回前的操作，重置状态
  void back() {
    ref.read(tableActionTypeProvider.notifier).set(0);
    ref.read(areaFilterStatusProvider.notifier).reset();
    ref.read(curTransferTableProvider.notifier).reset();
  }

  Widget _buildActionButton() {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        height: 40,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color(0xFFF3F3F4),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          KPLocale.of(context).action,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF323843),
            fontWeight: FontWeight.w700,
          ),
        ),
      ).onTap(() {
        KPPopover.showPopover(
          context: context,
          width: 240,
          height: 208,
          overlayColor: Colors.transparent,
          contentWidget: SingleChildScrollView(
            child: Column(
              children: List.generate(TableAction.values.length, (index) {
                return Column(
                  children: [
                    Visibility(
                      visible: index != 0,
                      child: Container(
                        width: double.maxFinite,
                        height: 1,
                        color: const Color(0xFFEEEEEE),
                        margin: const EdgeInsets.only(left: 20),
                      ),
                    ),
                    Container(
                      width: 240,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                      child: Text(
                        getActionTitle(context, TableAction.values[index]),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Color(0xFF323843),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ).onTap(() => actionOnTap(context, TableAction.values[index])),
                  ],
                );
              }),
            ),
          ),
          placement: TDPopoverPlacement.bottomRight,
        );
      });
    });
  }

  Widget _buildTipText(
    BuildContext context,
    int type,
    (TableItem?, TableItem?) curTransferTable,
  ) {
    if (type < 1) {
      return const SizedBox();
    }
    String useTitle = '';
    TableItem? table = ref.watch(selectedTableProvider);
    List<TableItem> tableGroup = ref.watch(multiSelectedTablesProvider);
    switch (TableAction.typeToAction(type)) {
      case TableAction.transfer:
        if (curTransferTable.$1 == null) {
          useTitle = context.locale.tableTransferOutTip;
        } else if (curTransferTable.$2 == null) {
          useTitle = context.locale.tableTransferInTip;
        } else if(curTransferTable.$2?.state!=TableState.available.value){
          useTitle = context.locale.hasOrderToSharingTip(curTransferTable.$2!.tableTitle,
            curTransferTable.$1!.tableTitle,);
        }else{
          useTitle = context.locale.tableTransferFinalTip(
            curTransferTable.$2!.tableTitle,
            curTransferTable.$1!.tableTitle,
          );
        }
        break;
      case TableAction.clearing:
        useTitle = context.locale.tableClearingTip(context.locale.pendingOrder);
        break;
      case TableAction.group:
        useTitle = context.locale.selectMultipleTables;
        break;
      case TableAction.sharing:
        useTitle = context.locale.tableSharingTip;
        break;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          useTitle,
          maxLines: 1,
          style: KPFontStyle.headingSmall.copyWith(
              color: type == TableAction.transfer.value
                  ? KPColors.textGrayPrimary
                  : KPColors.textGraySecondary),
        ),
        Visibility(
          visible: type == TableAction.sharing.value,
          child: Text(
            table == null
                ? context.locale.noTableSelected
                : context.locale.selectedTableTip(table.tableTitle),
            maxLines: 1,
            style: KPFontStyle.headingSmall.copyWith(
                color:
                    table == null ? KPColors.textRedDefault : KPColors.textGrayPrimary),
          ),
        ),
        Visibility(
          visible: type == TableAction.group.value,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  tableGroup.isEmpty
                      ? context.locale.noTableSelected
                      : context.locale.selectedTableTip(
                          '${tableGroup.map((e) => e.tableTitle).join(',')},',
                        ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: KPFontStyle.headingSmall.copyWith(
                      color: tableGroup.isEmpty
                          ? KPColors.textRedDefault
                          : KPColors.textGrayPrimary),
                ),
              ),
              Visibility(
                visible: tableGroup.length == 1,
                child: Text(
                  context.locale.selectOneMoreTable,
                  maxLines: 1,
                  style:
                      KPFontStyle.headingSmall.copyWith(color: KPColors.textRedDefault),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

// Widget _buildTabItem({
//   required int index,
//   required TabItem item,
//   required bool isSelected,
// }) {
//   return GestureDetector(
//     onTap: () => setState(() => _selectedIndex = 0),
//     child: AnimatedContainer(
//       duration: const Duration(milliseconds: 200),
//       padding: const EdgeInsets.all(2),
//       child: Container(
//         width: 36,
//         height: 36,
//         decoration: BoxDecoration(
//           color: isSelected ? Colors.white : Colors.transparent,
//           borderRadius: BorderRadius.circular(7),
//           border: isSelected
//               ? Border.all(color: Colors.black.withValues(alpha: 0.04), width: 0.5)
//               : null,
//           boxShadow: isSelected
//               ? [
//                   BoxShadow(
//                     color: Colors.black.withValues(alpha: 0.04),
//                     blurRadius: 1,
//                     offset: const Offset(0, 3),
//                   ),
//                   BoxShadow(
//                     color: Colors.black.withValues(alpha: 0.12),
//                     blurRadius: 8,
//                     offset: const Offset(0, -2),
//                   ),
//                 ]
//               : [],
//         ),
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             SvgPicture.asset(
//               item.icon,
//               width: 20,
//               height: 20,
//               fit: BoxFit.cover,
//               color: isSelected ? item.selectedColor : item.unselectedColor,
//             ),
//           ],
//         ),
//       ),
//     ),
//   );
// }
}

class TabItem {
  final String icon;
  final String label;
  final Color selectedColor;
  final Color unselectedColor;

  const TabItem({
    required this.icon,
    required this.label,
    required this.selectedColor,
    required this.unselectedColor,
  });
}
