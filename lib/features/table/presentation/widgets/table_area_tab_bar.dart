import 'package:extended_tabs/extended_tabs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/presentation/table_plan_view.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class TableAreaTabBarWidget extends ConsumerStatefulWidget {
  const TableAreaTabBarWidget({super.key});

  @override
  ConsumerState<TableAreaTabBarWidget> createState() => _TableAreaTabBarWidgetState();
}

class _TableAreaTabBarWidgetState extends ConsumerState<TableAreaTabBarWidget>
    with TickerProviderStateMixin {
  bool isCreateTabController = false;

  ProviderSubscription<TableService>? _tableServiceListener;

  @override
  void initState() {
    super.initState();
    _tableServiceListener = ref.listenManual<TableService>(
      tableServiceProvider,
          (pre, next) {
        if (next.areaList.isNotEmpty&&!isCreateTabController) {
          ref.read(tableTabControllerProvider.notifier).state?.dispose();
          ref.read(tableTabControllerProvider.notifier).state =
              TabController(length: next.areaList.length, vsync: this);
          isCreateTabController = true;
        }
      },
    );
  }

  @override
  void dispose() {
    _tableServiceListener?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final areaList = ref.watch(tableServiceProvider.select((s) => s.areaList));
    final controller = ref.watch(tableTabControllerProvider);

    if (controller == null || areaList.isEmpty) {
      return const SizedBox();
    }

    final groupList = ref.watch(groupDataProvider);
    final tabs = areaList
        .map((area) => ExtendedTab(
              height: 48,
              text: area.areaName,
            ))
        .toList();

    bool isSelectedGroup = ref.watch(selectedGroupAreaProvider);

    return Container(
      margin: const EdgeInsets.only(left: 16),
      child: Row(
        children: [
          ExtendedTabBar(
            controller: controller,
            isScrollable: true,
            labelPadding: const EdgeInsets.only(left: 0, right: 32),
            indicatorColor:
                isSelectedGroup ? Colors.transparent : context.theme.brandNormalColor,
            indicatorSize: TabBarIndicatorSize.label,
            splashBorderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            indicatorWeight: 3,
            dividerColor: Colors.transparent,
            labelColor:
                isSelectedGroup ? KPColors.textGrayPrimary : KPColors.textBrandDefault,
            labelStyle: KPFontStyle.headingXSmall.copyWith(
              color:
                  isSelectedGroup ? KPColors.textGrayPrimary : KPColors.textBrandDefault,
            ),
            unselectedLabelColor: KPColors.textGrayPrimary,
            unselectedLabelStyle:
                KPFontStyle.headingXSmall.copyWith(color: KPColors.textGrayPrimary),
            scrollDirection: Axis.horizontal,
            onTap: (index) async {
              if (mounted) {
                ref.read(selectedGroupAreaProvider.notifier).set(false);
              }
              final isPopupOpen = ref.read(showTableSlidePopUpProvider);
              if (isPopupOpen) {
                ref.read(showTableSlidePopUpProvider.notifier).set(false);
                ref.read(selectedTableProvider.notifier).state = null;
                await Future.delayed(const Duration(milliseconds: 300));
              }
              ref.read(currentTabIndexProvider.notifier).state = index;
              final service = ref.read(tableServiceProvider);
              service.curArea = areaList[index];
            },
            tabs: tabs,
          ),
          Visibility(
            visible: groupList.isNotEmpty,
            child: GestureDetector(
              onTap: () {
                if (isSelectedGroup) return;
                if (mounted) {
                  ref.read(selectedGroupAreaProvider.notifier).set(true);
                }
              },
              child: IntrinsicWidth(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(height: 3),
                    Text(
                      context.locale.groupedTables,
                      style: KPFontStyle.headingXSmall.copyWith(
                        color: isSelectedGroup
                            ? KPColors.textBrandDefault
                            : KPColors.textGrayPrimary,
                      ),
                    ),
                    FractionallySizedBox(
                      widthFactor: 0.9,
                      child: Container(
                        height: 3,
                        decoration: BoxDecoration(
                          color: isSelectedGroup
                              ? context.theme.brandNormalColor
                              : Colors.transparent,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
