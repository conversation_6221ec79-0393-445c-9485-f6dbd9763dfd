import 'package:flutter/material.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/features/table/domain/table_item.dart';

class TableStatusFitterBar extends StatefulWidget {
  final List<TableItem> tableList;
  final int selectedStatus;
  final Function(int status) onFilter;

  const TableStatusFitterBar({
    super.key,
    required this.tableList,
    required this.selectedStatus,
    required this.onFilter,
  });

  @override
  State<TableStatusFitterBar> createState() => _TableStatusFitterBarState();
}

class _TableStatusFitterBarState extends State<TableStatusFitterBar> {
  final List<FilterTag> _tags = const [
    FilterTag(status: 0),
    FilterTag(status: 1, color: Color(0xFFEEEEF0)),
    FilterTag(status: 2, color: Color(0xFF1677FF)),
    FilterTag(status: 3, color: Color(0xFFFA541C)),
    FilterTag(status: 4, color: Color(0xFF787C96)),
    FilterTag(status: 5, color: Color(0xFFFAAD14)),
  ];

  String getTextByTableState(int state) {
    switch (state) {
      case 1:
        return KPLocale.of(context).available;
      case 2:
        return KPLocale.of(context).pendingOrder;
      case 3:
        return KPLocale.of(context).unpaid;
      case 4:
        return KPLocale.of(context).needsCleanup;
      case 5:
        return KPLocale.of(context).reserved;
      default:
        return KPLocale.of(context).all;
    }
  }

  int getCountByTableState(int state) {
    if (state == 0) {
      return widget.tableList.length;
    }
    return widget.tableList.where((e) => e.state == state).length;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        height: 42,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFF0F0F0), width: 1),
        ),
        child: Row(
          children: List.generate(_tags.length, (index) {
            final isSelected = widget.selectedStatus == index;
            return Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFFEEEEF0)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      Visibility(
                        visible: _tags[index].status!=0,
                        child: Container(
                          width: 8,
                          height: 8,
                          margin: const EdgeInsets.only(right: 4),
                          decoration: BoxDecoration(
                            color: _tags[index].color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      Text(
                        '${getTextByTableState(_tags[index].status)}(${getCountByTableState(_tags[index].status)})',
                        style: TextStyle(
                          color: const Color(0xFF323843),
                          fontWeight: isSelected
                              ? FontWeight.w700
                              : FontWeight.w400,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ).onTap(() {
                  widget.onFilter(index);
                }),
              ],
            );
          }),
        ),
      ),
    );
  }
}

class FilterTag {
  final int status;
  final Color? color;

  const FilterTag({
    required this.status,
    this.color,
  });
}
