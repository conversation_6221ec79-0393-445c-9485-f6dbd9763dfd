import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'table_group_service.g.dart';

final groupDataProvider = StateNotifierProvider<GroupDataNotifier, List<TableGroupItem>>(
      (ref) => GroupDataNotifier(),
);

class GroupDataNotifier extends StateNotifier<List<TableGroupItem>> {
  GroupDataNotifier() : super([]);

  //找到某个桌台在联台列表中的联台信息
  TableGroupItem? findTableGroup(TableItem table) {
    for (int i = 0; i < state.length; i++) {
      if (state[i].tableList.any((t) => t.tableTitle == table.tableTitle)) {
        return state[i];
      }
    }
    return null; // 不在 groupData 中
  }

  // 设置联台数据
  void setGroupData(List<TableGroupItem> newGroupData) {
    state = newGroupData;
  }

  // 添加联台
  void addGroup(TableGroupItem group) {
    state = [...state, group];
  }

  // 删除联台
  void removeGroup(String groupName) {
    state = state.where((group) => group.groupName != groupName).toList();
  }

  // 修改联台名称
  void renameGroup(String oldName, String newName) {
    state = state.map((group) {
      if (group.groupName == oldName) {
        return group.copyWith(groupName: newName);
      }
      return group;
    }).toList();
  }
}

///联台操作下的多选模式选中的桌台列表
@Riverpod(keepAlive: true)
class MultiSelectedTables extends _$MultiSelectedTables {
  @override
  List<TableItem> build() => [];

  void onTableTap(TableItem value) {
    final groupData = ref.read(groupDataProvider);
    // 查找 value 所在的联台组
    final group = groupData.firstWhere(
          (g) => g.tableList.any((t) => t.tableTitle == value.tableTitle),
      orElse: () => const TableGroupItem(groupName: '', tableList: []),
    ).tableList;

    final isSelected = state.any((e) => e.tableTitle == value.tableTitle);

    if (isSelected) {
      // 取消选中：如果在联台组，移除该组所有桌台，否则只移除自己
      if (group.isNotEmpty) {
        final groupTitles = group.map((t) => t.tableTitle).toSet();
        state = [
          for (final t in state)
            if (!groupTitles.contains(t.tableTitle)) t
        ];
      } else {
        state = [
          for (final t in state)
            if (t.tableTitle != value.tableTitle) t
        ];
      }
    } else {
      // 选中：如果在联台组，添加该组所有桌台，否则只添加自己
      if (group.isNotEmpty) {
        final groupTitles = state.map((t) => t.tableTitle).toSet();
        state = [
          ...state,
          for (final t in group)
            if (!groupTitles.contains(t.tableTitle)) t
        ];
      } else {
        state = [...state, value];
      }
    }
  }

  void clear() {
    state = [];
  }
}

/// 检查是否选中联台区域的 provider
@Riverpod(keepAlive: true)
class SelectedGroupArea extends _$SelectedGroupArea {
  @override
  bool build() => false;

  void set(bool value) {
    state = value;
  }
}