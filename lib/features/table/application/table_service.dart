import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/components/kp_loading.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/services/database/app_database.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/cart/application/cart_service.dart';
import 'package:kpos/features/cart/application/order_creation_service.dart';
import 'package:kpos/features/table/application/table_group_service.dart';
import 'package:kpos/features/table/data/table_intranet_repository.dart';
import 'package:kpos/features/table/data/table_local_repository.dart';
import 'package:kpos/features/table/domain/table_area_list_item.dart';
import 'package:kpos/features/table/domain/table_group_item.dart';
import 'package:kpos/features/table/domain/table_infos.dart';
import 'package:kpos/features/table/domain/table_item.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:kpos/features/table/presentation/table_screen_controller.dart';
import 'package:kpos/features/table/presentation/widgets/table_top_action_area.dart';
import 'package:kpos/routing/app_routing.dart';
import 'package:oktoast/oktoast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'table_service.g.dart';

class TableService {
  final Ref _ref;

  TableService({required Ref ref}) : _ref = ref;

  TableAreaListItem? _curArea;

  TableAreaListItem? get curArea => _curArea;
  // TableAreaListItem? get curArea => areaList.first;

  List<TableAreaListItem> _areaList = [];
  List<TableAreaListItem> get areaList => _areaList;

  set areaList(List<TableAreaListItem> list) {
    _areaList = list;
    _ref.notifyListeners();
  }

  set curArea(TableAreaListItem? area) {
    _curArea = area;
    _ref.notifyListeners();
  }

  // 查找桌台所在区域
  TableAreaListItem? _findAreaByTable(TableItem table) {
    return areaList.firstWhere(
      (area) => area.tableList.any((t) => t.tableTitle == table.tableTitle),
    );
  }

  int? _findAreaIndexByTable(TableItem table) {
    final idx = areaList.indexWhere(
      (area) => area.tableList.any((t) => t.tableTitle == table.tableTitle),
    );
    return idx == -1 ? null : idx;
  }

  /// 桌台点击事件
  void itemTapEvent(BuildContext context, TableItem table) {
    int actionType = _ref.read(tableActionTypeProvider);
    //不处于action操作状态
    if (actionType < 1) {
      switch (TableState.stateValueToType(table.state ?? 0)) {
        case TableState.available:
          _ref.read(selectedTableProvider.notifier).state = table;
          _ref.read(showTableSlidePopUpProvider.notifier).set(true);
          break;
        case TableState.pendingOrder:
          _ref.read(selectedTableProvider.notifier).state = table;
          if (table.sharedTableFlag >0) {
            _ref.read(showTableSlidePopUpProvider.notifier).set(true);
            return;
          }
          TableGroupItem? group = _ref.read(groupDataProvider.notifier).findTableGroup(table);
          GoRouter.of(context).go('/product',
              extra: {"table":table,"group":group});
        case TableState.unpaid:
          if (table.sharedTableFlag >0) {
            _ref.read(showTableSlidePopUpProvider.notifier).set(true);
            return;
          }
          TableGroupItem? group = _ref.read(groupDataProvider.notifier).findTableGroup(table);
          GoRouter.of(context).go('/product',
              extra: {"table":table,"group":group});
        case TableState.needsCleanup:
          _ref.read(selectedTableProvider.notifier).state = table;
          _ref.read(showTableSlidePopUpProvider.notifier).set(true);
          break;
        case TableState.reserved:
          _ref.read(selectedTableProvider.notifier).state = table;
          break;
        default:
          return;
      }
    } else if (actionType == TableAction.group.value) {
      //联台操作的桌台点击事件
      _ref.read(multiSelectedTablesProvider.notifier).onTableTap(table);
    } else {
      //其他action操作下的点击事件
      switch (TableState.stateValueToType(table.state ?? 0)) {
        case TableState.available:
          actionOnAvailableTap(context, table, actionType);
          break;
        case TableState.pendingOrder:
          actionOnPendingOrderTap(context, table, actionType);
          break;
        case TableState.unpaid:
          actionOnUnpaidTap(context, table, actionType);
          break;
        case TableState.needsCleanup:
          actionOnNeedCleanTap(context, table, actionType);
          break;
        case TableState.reserved:
          actionOnReserveTap(context, table, actionType);
          break;
        default:
          return;
      }
    }
  }

  //action操作下点击空桌台
  void actionOnAvailableTap(BuildContext context, TableItem table, int actionType) {
    switch (TableAction.typeToAction(actionType)) {
      case TableAction.transfer:
        // 使用统一的转台选择逻辑
        transferSelect(table);
        break;
      case TableAction.clearing:
        break;
      case TableAction.sharing:
        break;
      case TableAction.group:
        break;
    }
  }

  //action操作下点击待下单桌台
  void actionOnPendingOrderTap(BuildContext context, TableItem table, int actionType) {
    switch (TableAction.typeToAction(actionType)) {
      case TableAction.transfer:
        transferSelect(table);
        break;
      case TableAction.clearing:
        KPDialog.showAlertDialog(
            context: context,
            title: context.locale.reminder,
            content: context.locale.withdrawalConfirmTip(table.tableTitle),
            rightAction: () async {
              //确认撤台操作
              if (context.mounted) {
                try {
                  
                  // 调用撤台接口
                  await _ref.read(tableIntranetRepositoryProvider).cancelTable(
                    orderId: table.orderId ?? 0,
                    tableNum: table.tableTitle,
                  );
                  
                  // 更新本地桌台状态
                  await setTableState(table: table, state: TableState.available);
                  
                  // 重置UI状态
                  _ref.read(selectedTableProvider.notifier).state = null;
                  _ref.read(areaFilterStatusProvider.notifier).setStatus([]);
                  _ref.read(tableActionTypeProvider.notifier).set(0);
                  
                  // 提示成功
                  if(context.mounted){
                    // 清除订单ID
                    _ref.read(orderCreationServiceProvider).clearCurrentOrderId();
                    // 清除桌台标题状态
                    _ref.read(selectedTableTitleStateProvider.notifier).set('');
                    // 设置为非桌台点餐状态
                    _ref.read(tableOrderingStateProvider.notifier).set(false);
                    KPToast.show(
                      content: context.locale.tableClearingCompleted,
                      position: ToastPosition.bottom,
                    );
                    // 关闭对话框
                    context.pop();
                  }
                } catch (e) {
                  // 处理错误
                  debugPrint("撤台失败: $e");
                  if (context.mounted) {
                    KPToast.show(
                      content: "撤台失败: ${e.toString()}",
                      position: ToastPosition.bottom,
                    );
                    // 关闭对话框
                    context.pop();
                  }
                } finally {
                  // 关闭对话框
                  if (context.mounted) {
                    context.pop();
                  }
                }
              }
            },
            leftAction: () {
              if (context.mounted) {
                _ref.read(selectedTableProvider.notifier).state = null;
              }
            });
        break;
      case TableAction.sharing:
        _ref.read(selectedTableProvider.notifier).state = table;
        break;
      case TableAction.group:
        break;
    }
  }

  //action操作下点击待支付桌台
  void actionOnUnpaidTap(BuildContext context, TableItem table, int actionType) {
    switch (TableAction.typeToAction(actionType)) {
      case TableAction.transfer:
        transferSelect(table);
        break;
      case TableAction.clearing:
        KPDialog.showAlertDialog(
            context: context,
            title: context.locale.reminder,
            content: context.locale.withdrawalConfirmTip(table.tableTitle),
            rightAction: () async {
              //确认撤台操作
              if (context.mounted) {
                try {
                  // 显示加载中
                  KPLoading.show();
                  
                  // 调用撤台接口
                  await _ref.read(tableIntranetRepositoryProvider).cancelTable(
                    orderId: table.orderId ?? 0,
                    tableNum: table.tableTitle,
                  );
                  
                  // 更新本地桌台状态
                  await setTableState(table: table, state: TableState.available);
                  
                  // 重置UI状态
                  _ref.read(selectedTableProvider.notifier).state = null;
                  _ref.read(areaFilterStatusProvider.notifier).setStatus([]);
                  _ref.read(tableActionTypeProvider.notifier).set(0);
                  
                  // 提示成功
                  if(context.mounted){
                    // 清除订单ID
                    _ref.read(orderCreationServiceProvider).clearCurrentOrderId();
                    // 清除桌台标题状态
                    _ref.read(selectedTableTitleStateProvider.notifier).set('');
                    // 设置为非桌台点餐状态
                    _ref.read(tableOrderingStateProvider.notifier).set(false);
                    KPToast.show(
                      content: context.locale.tableClearingCompleted,
                      position: ToastPosition.bottom,
                    );
                    // 关闭对话框
                    context.pop();
                  }
                } catch (e) {
                  // 处理错误
                  debugPrint("撤台失败: $e");
                  if (context.mounted) {
                    KPToast.show(
                      content: "撤台失败: ${e.toString()}",
                      position: ToastPosition.bottom,
                    );
                  }
                } finally {
                  // 关闭加载
                  KPLoading.dismiss();
                  
                  // 关闭对话框
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }
                }
              }
            },
            leftAction: () {
              if (context.mounted) {
                _ref.read(selectedTableProvider.notifier).state = null;
              }
            });
        break;
      case TableAction.sharing:
        _ref.read(selectedTableProvider.notifier).state = table;
        break;
      case TableAction.group:
        break;
    }
  }

  //action操作下点击待清台桌台
  void actionOnNeedCleanTap(BuildContext context, TableItem table, int actionType) {
    switch (TableAction.typeToAction(actionType)) {
      case TableAction.transfer:
        // 使用统一的转台选择逻辑
        transferSelect(table);
        break;
      case TableAction.clearing:
        break;
      case TableAction.sharing:
        break;
      case TableAction.group:
        break;
    }
  }

  //action操作下点击预定桌台
  void actionOnReserveTap(BuildContext context, TableItem table, int actionType) {
    switch (TableAction.typeToAction(actionType)) {
      case TableAction.transfer:
        break;
      case TableAction.clearing:
        break;
      case TableAction.sharing:
        break;
      case TableAction.group:
        break;
    }
  }

  //转台选中逻辑
  void transferSelect(TableItem table){
    final curTransferTable = _ref.read(curTransferTableProvider);
    debugPrint("转台选择: 选中桌台=${table.tableTitle}, 当前转出桌台=${curTransferTable.$1?.tableTitle}, 当前转入桌台=${curTransferTable.$2?.tableTitle}");
    
    if (curTransferTable.$1 == null && curTransferTable.$2 == null) {
      // 都未选，先设置转出
      debugPrint("转台选择: 设置转出桌台=${table.tableTitle}");
      _ref.read(curTransferTableProvider.notifier).setTableOut(table);
      _ref.read(areaFilterStatusProvider.notifier)
          .setStatus([TableState.available.value,TableState.pendingOrder.value, TableState.unpaid.value]);
    } else if (curTransferTable.$1?.tableTitle == table.tableTitle) {
      if(curTransferTable.$2 != null) return;
      // 再次点击已选的转出桌台，取消选中
      debugPrint("转台选择: 取消转出桌台=${table.tableTitle}");
      _ref.read(curTransferTableProvider.notifier).setTableOut(null);
      _ref.read(areaFilterStatusProvider.notifier)
          .setStatus([TableState.pendingOrder.value, TableState.unpaid.value]);
    } else if (curTransferTable.$2?.tableTitle == table.tableTitle) {
      // 再次点击已选的转入桌台，取消选中
      debugPrint("转台选择: 取消转入桌台=${table.tableTitle}");
      _ref.read(curTransferTableProvider.notifier).setTableIn(null);
    } else if (curTransferTable.$1 == null) {
      // 只设置转出
      debugPrint("转台选择: 设置转出桌台=${table.tableTitle}");
      _ref.read(curTransferTableProvider.notifier).setTableOut(table);
      _ref.read(areaFilterStatusProvider.notifier)
          .setStatus([TableState.available.value,TableState.pendingOrder.value, TableState.unpaid.value]);
    } else if (curTransferTable.$2 == null) {
      // 设置转入，这里直接是拼桌
      debugPrint("转台选择: 设置转入桌台=${table.tableTitle}");
      _ref.read(curTransferTableProvider.notifier).setTableIn(table);
    }
    
    // 打印最终状态
    final finalState = _ref.read(curTransferTableProvider);
    debugPrint("转台选择结束: 转出桌台=${finalState.$1?.tableTitle}, 转入桌台=${finalState.$2?.tableTitle}");
  }

  // 状态变更方法
  Future<void> setTableState({
    required TableItem table,
    required TableState state,
    int? sharedTableFlag,
  }) async {
    final areaIdx = _findAreaIndexByTable(table);
    if (areaIdx == null) return;
    final area = areaList[areaIdx];
    final tableIdx = area.tableList.indexWhere((t) => t.tableTitle == table.tableTitle);
    if (tableIdx == -1) return;

    final updatedTable = table.copyWith(
      state: state.value,
      sharedTableFlag: sharedTableFlag??0,
      subOrderNumber: sharedTableFlag != null
          ? (table.subOrderNumber ?? 1) + 1
          : (table.subOrderNumber ?? 1),
    );

    final updatedTableList = List<TableItem>.from(area.tableList);
    updatedTableList[tableIdx] = updatedTable;

    areaList = [
      for (int i = 0; i < areaList.length; i++)
        if (i == areaIdx)
          area.copyWith(tableList: updatedTableList)
        else
          areaList[i]
    ];
  }

  // 转台业务方法
  Future<void> transferTable({
    required TableItem from,
    required TableItem to,
  }) async {
    try {
      
      // 调用转台API
      await _ref.read(tableIntranetRepositoryProvider).transferTable(
        orderId: from.orderId ?? 0,
        originTableNumber: from.tableTitle,
        newTableNumber: to.tableTitle,
      );
      
      // 清空转台选择状态
      _ref.read(curTransferTableProvider.notifier).reset();
      
      // 清空过滤器状态
      _ref.read(areaFilterStatusProvider.notifier).reset();
      
      // 取消操作模式
      _ref.read(tableActionTypeProvider.notifier).set(0);

      _ref.invalidate(tableScreenControllerProvider);
      
    } catch (e) {
      debugPrint("转台失败: $e");
    }
  }
  
  // 拼桌业务方法
  Future<void> shareTable({
    required TableItem table,
    required int subNumber,
  }) async {
    try {
      // 生成子桌台名
      final subTableNum = '${table.tableTitle}-${(table.subOrderNumber??0)+1}';
      
      // 调用拼桌API
      await _ref.read(tableIntranetRepositoryProvider).shareTable(
        orderId: table.orderId ?? 0,
        tableNum: table.tableTitle,
        subTableNum: subTableNum,
        subNumber: subNumber,
      );
      
      // 清空转台选择状态
      _ref.read(curTransferTableProvider.notifier).reset();
      
      // 清空过滤器状态
      _ref.read(areaFilterStatusProvider.notifier).reset();
      
      // 取消操作模式
      _ref.read(tableActionTypeProvider.notifier).set(0);

      _ref.invalidate(tableScreenControllerProvider);
      
    } catch (e) {
      debugPrint("拼桌失败: $e");
    }
  }

  //设置联台
  void setGroupList(List<TableItem> items) {
    final groupDataNotifier = _ref.read(groupDataProvider.notifier);

    // 找出与 items 有交集的 group
    List<TableGroupItem> intersectGroups = [];
    for (var group in _ref.read(groupDataProvider)) {
      if (group.tableList.any((t) => items.any((i) => i.tableTitle == t.tableTitle))) {
        intersectGroups.add(group);
      }
    }

    // 合并所有相关桌台
    final mergedSet = <TableItem>{...items};
    for (var group in intersectGroups) {
      mergedSet.addAll(group.tableList);
    }

    // 移除原有相关组
    groupDataNotifier.setGroupData(
      _ref.read(groupDataProvider).where((group) => !intersectGroups.contains(group)).toList(),
    );

    // 添加合并后的新组
    groupDataNotifier.addGroup(
      TableGroupItem(groupName: 'Group${_ref.read(groupDataProvider).length + 1}', tableList: mergedSet.toList()),
    );
  }

  // 取消某个联台
  void removeGroup(TableGroupItem item) {
    _ref.read(groupDataProvider.notifier).removeGroup(item.groupName);
    final list = _ref.read(groupDataProvider);
    if(list.isEmpty){
      _ref.read(selectedGroupAreaProvider.notifier).set(false);
    }
  }

  // 修改某个联台名称
  void renameGroup(TableGroupItem item, String newGroupName) {
    _ref.read(groupDataProvider.notifier).renameGroup(item.groupName,newGroupName);
  }
}

@riverpod
TableService tableService(Ref ref) {
  return TableService(ref: ref);
}

@riverpod
class ShowTableSlidePopUp extends _$ShowTableSlidePopUp {
  @override
  bool build() => false;

  // 切换弹出框显示状态
  void change() {
    state = !state;
  }

  // 设置弹出框显示状态
  void set(bool value) {
    state = value;
  }
}

@riverpod
class TableActionType extends _$TableActionType {
  @override
  int build() {
    return 0;
  }

  // 设置桌台Action状态
  void set(int value) {
    state = value;
  }
}

@riverpod
class CurTransferTable extends _$CurTransferTable {
  //(TableItem?,TableItem?) =（待转出桌台,待转入桌台）
  @override
  (TableItem?, TableItem?) build() {
    return (null, null);
  }

  // 设置当前待转出的桌台
  void setTableOut(TableItem? value) {
    state = (value, state.$2);
  }

  // 设置当前待转入的桌台
  void setTableIn(TableItem? value) {
    state = (state.$1, value);
  }

  void reset() {
    state = (null, null);
  }
}

@Riverpod(keepAlive: true)
class TableViewShowType extends _$TableViewShowType {
  @override
  int build() => 1;

  // 切换类型
  void change() {
    state = state == 1 ? 2 : 1;
  }

  // 设置类型
  void set(int value) {
    state = value;
  }

  ///初始化桌台布局展示类型
  void init(KeyValueStorageService storage) {
    final tableShowType = storage.getTableShowType();
    set(tableShowType);
  }
}

@Riverpod(keepAlive: true)
class TableTimeDisplayShow extends _$TableTimeDisplayShow {
  @override
  int build() => 1;

  // 切换类型
  void change() {
    state = state == 1 ? 2 : 1;
  }

  // 设置类型
  void set(int value) {
    state = value;
  }

  ///初始化桌台时间展示类型
  void init(KeyValueStorageService storage) {
    final tableTimeDisplay = storage.getTableTimeDisplay();
    set(tableTimeDisplay);
  }
}

/// 检查和设置是否处于拼桌后开台阶段的 provider
@Riverpod(keepAlive: true)
class SharingTableOpening extends _$SharingTableOpening {
  @override
  bool build() => false;

  void set(bool value) {
    state = value;
  }
}

///多区域状态筛选 provider
final areaFilterStatusProvider =
    StateNotifierProvider<AreaFilterStatusNotifier, Map<int, List<int>>>(
  (ref) => AreaFilterStatusNotifier(ref),
);

class AreaFilterStatusNotifier extends StateNotifier<Map<int, List<int>>> {
  final Ref ref;

  AreaFilterStatusNotifier(this.ref) : super({});

  List<int> getStatus([int? areaId]) {
    final id = areaId ?? ref.read(tableServiceProvider).curArea?.areaId ?? 0;
    return state[id] ?? [];
  }

  void setStatus(List<int> statuses, {int? areaId}) {
    final id = areaId ?? ref.read(tableServiceProvider).curArea?.areaId ?? 0;
    state = {...state, id: statuses};
  }

  void reset({int? areaId}) {
    final id = areaId ?? ref.read(tableServiceProvider).curArea?.areaId ?? 0;
    state = {...state, id: []};
  }
}


