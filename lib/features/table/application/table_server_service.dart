import 'dart:convert';
import 'dart:ui';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/table/data/table_local_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shelf/shelf.dart';

import '../../../common/services/database/app_database.dart';
import '../../../common/services/language_settings_service/language_settings_service.dart';
import '../../../common/services/local_storage/key_value_storage_service.dart';
import '../../../common/services/networking/constants/api_intranet/api_intranet_message_key.dart';
import '../../../common/services/networking/intranet_service/api_intranet_exception.dart';
import '../../../common/services/networking/intranet_service/api_intranet_localization.dart';
import '../../../common/services/networking/intranet_service/api_intranet_response.dart';
import '../../store/application/store_service.dart';

part 'table_server_service.g.dart';

class TableServerService {
  final Ref _ref;

  TableServerService({required Ref ref}) : _ref = ref;

  Locale get currentLocale => _ref.read(languageSettingsServiceProvider).currentLocale;

  Future<Response> getTableInfos(Request request) async {
    try {
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      final list = await tableLocal.getList();
      return ApiIntranetResponse.success(list);
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> transferTable(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["originTableNumber"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["newTableNumber"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      tableLocal.transferTable(data["orderId"], data["originTableNumber"], data["newTableNumber"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> mergeTable(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["tableConfig"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["number"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      tableLocal.mergeTable(
          tenantId,
          boundStore.storeId,
          boundStore.storeName,
          boundStore.brandId,
          boundStore.brandName,
          data["tableConfig"],
          data["number"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> shareTable(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      final tenantId = await _ref.read(keyValueStorageServiceProvider).getTenantId();
      final boundStore = _ref.read(storeServiceProvider).boundStoreInfo;
      if (tenantId == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["tableNum"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["subTableNum"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["subNumber"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      tableLocal.shareTable(
          tenantId,
          boundStore.storeId,
          boundStore.storeName,
          boundStore.brandId,
          boundStore.brandName,
          data["orderId"],
          data["tableNum"],
          data["subTableNum"],
          data["subNumber"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> getShareTableOrders(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["shareTableId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      final result = await tableLocal.getShareTableOrders(data["shareTableId"]);
      return ApiIntranetResponse.success(result);
    } catch (e) {
      print(e);
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> cancelTable(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["orderId"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      if (data["tableNum"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      tableLocal.cancelTable(data["orderId"], data["tableNum"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

  Future<Response> clearTable(Request request) async {
    try {
      final payload = await request.readAsString();
      final Map<String,dynamic> data = jsonDecode(payload);
      if (data["tableNumber"] == null) {
        return ApiIntranetResponse.failure(
            message:Localization.locale(MessageKey.invalidParam,
                locale: currentLocale));
      }
      final tableLocal = TableLocalRepository(_ref.watch(databaseProvider));
      tableLocal.clearTable( data["tableNumber"]);
      return ApiIntranetResponse.success(Localization.locale(MessageKey.operationSuccess,locale: currentLocale));
    } catch (e) {
      if (e is ApiIntranetException) {
        return ApiIntranetResponse.failure(message:Localization.locale(e.message,locale: currentLocale));
      }
      return ApiIntranetResponse.failure(message:Localization.locale(MessageKey.operationFailure,locale: currentLocale));
    }
  }

}

@riverpod
TableServerService tableServerService(Ref ref) {
  return TableServerService(ref: ref);
}
