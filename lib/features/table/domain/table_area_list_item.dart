import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/table/domain/table_item.dart';

part 'table_area_list_item.freezed.dart';
part 'table_area_list_item.g.dart';

@freezed
class TableAreaListItem with _$TableAreaListItem {
  const factory TableAreaListItem({
    required int areaId,
    required String areaName,
    required List<TableItem> tableList,
  }) = _TableAreaListItem;

  factory TableAreaListItem.fromJson(Map<String, dynamic> json) =>
      _$TableAreaListItemFromJson(json);
}
