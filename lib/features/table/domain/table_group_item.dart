import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/table/domain/table_item.dart';

part 'table_group_item.freezed.dart';
part 'table_group_item.g.dart';

@freezed
class TableGroupItem with _$TableGroupItem {
  const factory TableGroupItem({
    required String groupName,
    required List<TableItem> tableList,
  }) = _TableGroupItem;

  factory TableGroupItem.fromJson(Map<String, dynamic> json) =>
      _$TableGroupItemFromJson(json);
}
