import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:kpos/features/table/domain/table_area_list_item.dart';
import 'package:kpos/features/table/domain/table_item.dart';

part 'table_infos.freezed.dart';
part 'table_infos.g.dart';

@freezed
class TableInfos with _$TableInfos {
  const factory TableInfos({
    required String storeTableInfoId,
    required List<TableAreaListItem> areaList,
  }) = _TableInfos;

  factory TableInfos.fromJson(Map<String, dynamic> json) =>
      _$TableInfosFromJson(json);
}
