import 'package:freezed_annotation/freezed_annotation.dart';

part 'table_item.freezed.dart';
part 'table_item.g.dart';

@freezed
class TableItem with _$TableItem {
  const factory TableItem({
    required int tableInfoId,
    required String tableTitle,
    required int seatedNumber,
    required int tableShape,
    required double tableHeight,
    required double tableWidth,
    required double nodeX,
    required double nodeY,
    int? state,
    int? orderId,
    int? createTime,
    required int mergeTableFlag,
    int? mergeTableId,
    String? mergeTableName,
    required int sharedTableFlag,
    int? sharedTableId,
    int? subOrderNumber,
  }) = _TableItem;

  factory TableItem.fromJson(Map<String, dynamic> json) =>
      _$TableItemFromJson(json);
}
