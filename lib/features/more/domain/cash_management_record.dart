
import 'dart:math';

class CashRecord {
  final String type; // 'pay_in' (收款) 或 'pay_out' (付款)
  final String paymentMethod; // 付款方式，如 '现金', '信用卡'
  final double amount; // 金额 (收款为正, 付款为负)
  final String note; // 备注
  final DateTime dateTime; // 交易日期和时间
  final String operator; // 操作员

  CashRecord({
    required this.type,
    required this.paymentMethod,
    required this.amount,
    required this.note,
    required this.dateTime,
    required this.operator,
  });

  /// 创建一个随机的现金记录实例
  /// 用于生成模拟数据。
  /// @param id - 一个唯一的字符串，用作随机数种子，以确保可重复的随机结果。
  static CashRecord random({required String id}) {
    final random = Random(id.hashCode);
    
    // 随机决定是收款还是付款
    final type = random.nextBool() ? 'pay_in' : 'pay_out';
    
    // 随机选择付款方式
    final paymentMethods = ['现金', '信用卡', '借记卡', '微信', '支付宝'];
    final paymentMethod = paymentMethods[random.nextInt(paymentMethods.length)];
    
    // 随机生成一个正数金额
    final positiveAmount = (random.nextInt(99000) + 1000) / 100.0;
    
    // 随机选择备注
    final notes = [
      '日常经营', '补充库存', '设备维修', '员工工资', '店面租金',
      '水电费用', '营销推广', '货物采购', '杂项支出', '客户退款'
    ];
    final note = notes[random.nextInt(notes.length)];
    
    // 随机生成过去30天内的日期时间
    final now = DateTime.now();
    final daysAgo = random.nextInt(30);
    final dateTime = now.subtract(Duration(
      days: daysAgo,
      hours: random.nextInt(24),
      minutes: random.nextInt(60),
    ));
    
    // 随机选择操作员
    final operators = ['吴老板', '余经理', '罗帅哥', '小妹'];
    final operator = operators[random.nextInt(operators.length)];
    
    return CashRecord(
      type: type,
      paymentMethod: paymentMethod,
      // 关键逻辑：如果是付款，金额自动转为负数
      amount: type == 'pay_in' ? positiveAmount : -positiveAmount,
      note: note,
      dateTime: dateTime,
      operator: operator,
    );
  }
}