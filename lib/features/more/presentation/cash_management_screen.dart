// 导入必要的Flutter和Dart包
import 'dart:math';  // 随机数生成
import 'package:flutter/material.dart';  // Flutter的核心UI包
import 'package:flutter/services.dart';  // 用于输入格式化
import 'package:flutter_riverpod/flutter_riverpod.dart';  // 状态管理库
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';  // 国际化和格式化库，用于日期格式化
import 'package:kpos/assets/assets.gen.dart';  // 资源管理
import 'package:kpos/common/components/form_fields/kp_form_builder_text_field.dart';  // 自定义表单字段
import 'package:kpos/common/components/kp_drop_down_menu.dart';  // 自定义下拉菜单组件
import 'package:kpos/common/extension/build_context_extension.dart';  // 上下文扩展
import 'package:kpos/features/auth/presentation/auth_text_field.dart';  // 认证文本字段
import 'package:kpos/features/more/presentation/widgets/KPDatePicker.dart';
import 'package:kpos/features/more/presentation/widgets/create_pay_dialog.dart';
import 'package:kpos/routing/navigator_title_base.dart';  // 自定义导航标题基类
import 'package:tdesign_flutter/tdesign_flutter.dart';  // UI组件库 // 日期选择器
import 'package:kpos/assets/kp_locale.g.dart';  // 本地化资源// 创建收支对话框组件
import 'package:easy_refresh/easy_refresh.dart';  // 下拉刷新和上拉加载更多
import 'package:kpos/common/components/kp_pagination.dart';  // 自定义分页控件
// == 新增开始 ==
import 'package:kpos/features/more/application/cash_management_notifier.dart';  // Riverpod状态管理
import 'package:kpos/features/more/domain/cash_management_record.dart';  // 数据模型
// == 新增结束 ==

/// 现金管理页面
/// 用于管理收款和付款记录，支持日期范围筛选、交易类型筛选和付款方式筛选
class CashManagementScreen extends TitledConsumerStatefulWidget {
  const CashManagementScreen({Key? key}) : super(key: key);

  @override
  TitledConsumerState<CashManagementScreen> createState() => _CashManagementScreenState();
}

/// 现金管理页面状态类
/// 管理页面的所有状态和交互逻辑
class _CashManagementScreenState extends TitledConsumerState<CashManagementScreen> {
  // == 移除开始：删除所有本地状态变量 ==
  // /// 是否正在加载数据
  // bool _isLoading = false;
  // /// 是否有错误
  // bool _hasError = false;
  // /// 错误信息
  // String _errorMessage = '';
  // /// 总记录数
  // int _totalRecords = 0;
  // /// 当前选中的日期范围显示文本
  // String _selectedDateRange = 'mm/dd/yyyy — mm/dd/yyyy';
  // /// 当前选中的交易类型筛选条件
  // String _selectedType = '全部类型';
  // /// 当前选中的付款方式筛选条件
  // String _selectedPaymentMethod = '全部付款方式';
  // /// 分页控制 - 每页显示的记录数量
  // int _rowsPerPage = 10;
  // /// 分页控制 - 当前页码
  // int _currentPage = 1;
  // /// 分页控制 - 总页数
  // int _totalPages = 13;
  // /// 假数据列表
  // List<CashRecord> _cashRecords = [
  //   // ... 原有的静态数据 ...
  // ];
  // == 移除结束 ==
  
  /// 下拉刷新控制器
  final EasyRefreshController _refreshController = EasyRefreshController();
  
  /// 当前选择的付款类型（用于对话框）
  /// 'pay_in'表示收款，'pay_out'表示付款
  String _paymentType = 'pay_in';
  
  /// 支持的付款方式列表
  final List<String> _paymentMethods = ['现金', '信用卡', '借记卡', '微信', '支付宝'];
  
  /// 对话框中当前选中的付款方式
  /// 默认为'现金'
  String _selectedPaymentMethodForDialog = '现金';
  
  /// 金额输入框的控制器
  /// 用于管理金额输入，默认值为'0.00'
  final TextEditingController _amountController = TextEditingController(text: '0.00');
  
  /// 备注输入框的控制器
  final TextEditingController _noteController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // == 移除：不再需要手动初始化数据 ==
    // _initializeData();
  }

  // == 移除开始：删除手动初始化数据的方法 ==
  // /// 初始化数据
  // void _initializeData() {
  //   // 生成初始数据
  //   _cashRecords = List.generate(
  //     8,
  //     (index) => CashRecord.random(id: 'init_record_$index'),
  //   );
  //   _totalRecords = 100; // 假设总共有100条记录
  //   _totalPages = (_totalRecords / _rowsPerPage).ceil();
  // }
  // == 移除结束 ==
  
  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    _refreshController.dispose();

    super.dispose();
  }

  @override
  String? getPageTitle(BuildContext context) {
    return KPLocale.of(context).cashManagement;
  }
  
  /// 获取页面标题的国际化键
  /// 这个方法返回国际化资源中的键名，而不是实际的文本
  /// @return 国际化资源中的键名
  @override
  String? getPageTitleKey() {
    // 返国际化资源中的键名，而不是实际的文本
    return 'cashManagement';
  }

  /// 构建页面UI
  /// 该方法负责创建现金管理页面的整体布局和结构
  /// @param context - 构建上下文
  /// @return 返回构建的Widget树
  @override
  Widget build(BuildContext context) {
    // == 新增：通过Riverpod监听状态变化 ==
    final state = ref.watch(cashManagementNotifierProvider);
    
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(20.0), // 添加内边距使内容不贴近屏幕边缘
          child: Container(
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start, // 左对齐
              children: [
                // 顶部筛选区域 - 包含日期、类型和付款方式筛选
                _buildFilterArea(state),
        
                Expanded(
                  child: _buildDataTable(state),
                ),
                // 底部分页控制区域
                _buildPagination(state),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建页面顶部的筛选区域
  /// 包含日期范围选择器、交易类型下拉菜单、付款方式下拉菜单和添加收/付款按钮
  Widget _buildFilterArea(CashManagementState state) {
    return Container(
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: Row(
            children: [
               // 日期范围选择器部分
          SizedBox(
            width: 312,
            height: 56,
            child: _buildDateRangePicker(state),
          ),
          const SizedBox(width: 16), // 控件间的水平间距
          
          // 交易类型筛选下拉菜单
          SizedBox(
            width: 156,
            height: 56,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 使用新的KPTextFieldDropDown组件
                KPTextFieldDropDown(
                  items: [
                    DropdownItem(id: "all", name: "全部类型"),
                    DropdownItem(id: "income", name: "收款"),
                    DropdownItem(id: "expense", name: "付款"),
                  ],
                  value: DropdownItem(
                    // == 修改：使用状态中的值 ==
                    id: state.selectedType == "全部类型" ? "all" : 
                       state.selectedType == "收款" ? "income" : "expense",
                    name: state.selectedType,
                  ),
                  labelText: '类型',
                  hintText: '请选择类型',
                  onChanged: (item) {
                    if (item != null) {
                      // == 修改：调用Notifier方法而不是setState ==
                      ref.read(cashManagementNotifierProvider.notifier).setFilterType(item.name);
                    }
                  },
                  menuMaxHeight: 300,
                  menuWidth: 200,
                  itemTextStyle: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF323843),
                    fontWeight: FontWeight.w500,
                  ),
                  dropdownColor: Colors.grey[50],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16), 
          
          // 付款方式筛选下拉菜单
          SizedBox(
            width: 156,
            height: 56,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 使用新的KPTextFieldDropDown组件
                KPTextFieldDropDown(
                  items: [
                    // DropdownItem(id: "all", name: "全部付款方式"),
                    DropdownItem(id: "cash", name: "现金"),
                    // DropdownItem(id: "credit", name: "信用卡"),
                    // DropdownItem(id: "debit", name: "借记卡"),
                    // DropdownItem(id: "wechat", name: "微信"),
                    // DropdownItem(id: "alipay", name: "支付宝"),
                  ],
                  value: DropdownItem(
                    // == 修改：使用状态中的值 ==
                    // id: state.selectedPaymentMethod == "全部付款方式" ? "all" :
                    //    state.selectedPaymentMethod == "现金" ? "cash" :
                    //    state.selectedPaymentMethod == "信用卡" ? "credit" :
                    //    state.selectedPaymentMethod == "借记卡" ? "debit" :
                    //    state.selectedPaymentMethod == "微信" ? "wechat" : "alipay",
                    id: state.selectedPaymentMethod == "现金" ? "cash" : "alipay",
                    name: state.selectedPaymentMethod,
                  ),
                  labelText: '付款方式',
                  hintText: '请选择付款方式',
                  onChanged: (item) {
                    if (item != null) {
                      // == 修改：调用Notifier方法而不是setState ==
                      ref.read(cashManagementNotifierProvider.notifier).setFilterPaymentMethod(item.name);
                    }
                  },
                  menuMaxHeight: 300,
                  menuWidth: 200,
                  itemTextStyle: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF323843),
                    fontWeight: FontWeight.w500,
                  ),
                  dropdownColor: Colors.grey[50],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          ])),


          SizedBox(
            width: 124,
            height: 48, // 固定高度以保持一致的外观
            child: ElevatedButton(
              onPressed: _showPaymentDialog, // 点击时显示添加收/付款对话框
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87, // 按钮背景色
                foregroundColor: Colors.white, // 按钮文字颜色
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4), // 圆角按钮
                ),
              ),
              child: Text(
                KPLocale.of(context).payInOut,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
              ), // 按钮文字
            ),
          ),
        ],
      ),
    );
  }

  
  Widget _buildDateRangePicker(CashManagementState state) {
    return KPDateRangePicker(
      labelText: '日期范围',
      hintText: '请选择日期范围',
      // 选中边框的颜色
      selectedBorderColor: context.theme.brandColor7,
      // 未选中边框的颜色
      unselectedBorderColor: const Color(0xFFDCDDE1),
      suffixIcon: Assets.images.iconDatePicker.svg(width: 24, height: 24),
      onConfirm: (startDate, endDate) {
        print("1日期:${startDate} - ${endDate}");
        
        try {
          // == 修改：调用Notifier方法而不是setState ==
          ref.read(cashManagementNotifierProvider.notifier).setDateRange(startDate, endDate);
          print("2日期:${startDate} - ${endDate}");
          
          if (startDate != null && endDate != null) {
            print("日期3:${state.formattedDateRange}");
          } else {
            print("警告：收到空的日期值");
          }
        } catch (e) {
          print("处理日期时出错: $e");
        }
      }
    );
  }

  /// 显示付款对话框
  void _showPaymentDialog() {
    // 重置对话框状态为默认值
    // == 修改：不再使用setState，直接设置局部变量 ==
    _paymentType = 'pay_in';                    // 默认为收款类型
    _selectedPaymentMethodForDialog = '现金';  // 默认付款方式为现金
    _amountController.text = '0.00';             // 金额默认为0.00
    _noteController.text = '';                   // 备注默认为空

    CreatePayDialog.show(
      context: context,
      initialType: _paymentType,                   // 初始支付类型
      initialPaymentMethod: _selectedPaymentMethodForDialog, // 初始付款方式
      initialAmount: double.tryParse(_amountController.text) ?? 0.00, // 初始金额
      // initialNote: _noteController.text,           // 初始备注
      initialNote: "添加一条备注",  
      onConfirm: (type, paymentMethod, amount, note) {
        // == 修改：不再使用setState，直接更新局部变量 ==
        _paymentType = type ?? 'pay_in'; // 如果类型为空，则默认为收款
        _selectedPaymentMethodForDialog = paymentMethod;
        _amountController.text = amount.toString();
        _noteController.text = note;
        
        // 处理创建付款逻辑
        _handleCreatePayment();
      },
    );
  }

  /// 处理创建收款或付款记录
  void _handleCreatePayment() {
    // 解析并获取输入的金额，如果解析失败则默认为0.0
    double amount = double.tryParse(_amountController.text) ?? 0.00;
    
    // == 修改：调用Notifier方法而不是直接操作本地状态 ==
    ref.read(cashManagementNotifierProvider.notifier).addCashRecord(
      type: _paymentType,                                // 交易类型（收款或付款）
      paymentMethod: _selectedPaymentMethodForDialog,    // 付款方式
      amount: amount,                                    // 金额
      note: _noteController.text,                        // 备注
    );
    
    // 注意：实际应用中应该将数据保存到数据库或调用API保存
  }

  /// 构建现金管理记录数据表格
  Widget _buildDataTable(CashManagementState state) {
    // == 修改：使用状态中的值 ==
    // 如果正在加载中显示加载指示器
    if (state.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(context.theme.brandColor7),
            ),
            const SizedBox(height: 16),
            const Text(
              '正在加载数据...',
              style: TextStyle(color: Colors.black54, fontSize: 14),
            ),
          ],
        ),
      );
    }
    
    // 如果有错误显示错误信息
    if (state.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              state.errorMessage,
              style: const TextStyle(color: Colors.black87, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              // == 修改：调用Notifier的刷新方法 ==
              onPressed: () => ref.read(cashManagementNotifierProvider.notifier).refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.theme.brandColor7,
                foregroundColor: Colors.white,
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
    
    // 如果数据为空显示空状态
    if (state.cashRecords.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.iconNoData.svg(width: 60, height: 72),
            const SizedBox(height: 16),
            const Text(
              'no data',
              style: TextStyle(color: Colors.black87, fontSize: 16, fontWeight: FontWeight.w400),
            ),
          ],
        ),
      );
    }
    
    // 使用EasyRefresh包装数据表格
    return EasyRefresh(
      controller: _refreshController,
      onRefresh: () async {
        // == 修改：调用Notifier的刷新方法 ==
        ref.read(cashManagementNotifierProvider.notifier).refresh();
        _refreshController.finishRefresh();
        _refreshController.resetFooter(); // 重置底部加载状态
        return IndicatorResult.success;
      },
      onLoad: () async {
        // == 修改：使用状态中的分页信息 ==
        // 如果已经是最后一页，则不加载更多
        if (state.currentPage >= state.totalPages) {
          _refreshController.finishLoad(IndicatorResult.noMore);
          return IndicatorResult.noMore;
        }
        
        // == 修改：调用Notifier的翻页方法 ==
        ref.read(cashManagementNotifierProvider.notifier).changePage(state.currentPage + 1);
        final result = state.currentPage >= state.totalPages ? IndicatorResult.noMore : IndicatorResult.success;
        _refreshController.finishLoad(result);
        return result;
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 表格标题行
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(flex: 1, child: _buildTableHeader('类型', TextAlign.left)),
                  Expanded(flex: 1, child: _buildTableHeader('付款方式', TextAlign.left)),
                  Expanded(flex: 1, child: _buildTableHeader('金额', TextAlign.right)),
                  Expanded(flex: 1, child: _buildTableHeader('备注', TextAlign.left)),
                  Expanded(flex: 1, child: _buildTableHeader('日期时间', TextAlign.left)),
                  Expanded(flex: 1, child: _buildTableHeader('操作人', TextAlign.left)),
                ],
              ),
            ),
            
            // 表格数据行
            ListView.builder(
              // shrinkWrap: true - 让ListView的高度自适应内容高度，而不是占据全部可用空间
              shrinkWrap: true,
              // 禁用列表自身滚动
              physics: const NeverScrollableScrollPhysics(),
              
              // == 修改：使用状态中的数据 ==
              itemCount: state.cashRecords.length,
              itemBuilder: (context, index) {
                final record = state.cashRecords[index];
                    
                // 确定金额颜色和前缀
                final amountColor = record.type == 'pay_in' 
                    ? Colors.green.shade700 
                    : Colors.red.shade700;
                final amountPrefix = record.type == 'pay_in' ? '+' : '-';
                
                // 格式化日期时间
                final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(record.dateTime);
                
                return Container(
                  padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                        children: [
                          // 1. 类型
                          Expanded(
                      flex: 1,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16), // 只保留左侧内边距
                        child: Align(
                          alignment: Alignment.centerLeft, // 这已经是靠左的
                          child: Container(
                            // 直接使用Container，去掉多余的Wrap
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: amountColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              record.type == 'pay_in' ? context.locale.payIn : context.locale.payOut,
                              style: TextStyle(
                                color: amountColor,
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                      // 付款方式
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            record.paymentMethod, 
                            style: const TextStyle(
                              fontSize: 14, 
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.left),
                        ),
                      ),

                      // 金额
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            '$amountPrefix S\$ ${record.amount.abs().toStringAsFixed(2)}',
                            style: TextStyle(
                              color: amountColor,
                              fontSize: 14, 
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),

                      // 备注
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            record.note,
                            style: const TextStyle(
                              fontSize: 14, 
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.left,
                            // maxLines: 1,
                            // overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      
                      // 日期时间
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            formattedDate, 
                            style: const TextStyle(
                              fontSize: 14, 
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.left
                            ),
                        ),
                      ),           
                      
                      
                      // 操作人
                      Expanded(
                        flex: 1,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            record.operator,
                            style: const TextStyle(
                              fontSize: 14, 
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.left,
                            ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建数据表格的表头文本
  Widget _buildTableHeader(String title, TextAlign textAlign) {
    return Padding(      
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500, // 使用粗体样式突出表头
          color: Color(0xFF000000),
        ),
        textAlign: textAlign ,
      ),
    );
  }

  /// 创建一个包含页码选择和页面导航按钮的分页控制栏
  Widget _buildPagination(CashManagementState state) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end, // 将内容居中对齐
        children: [
          // 分页导航组件
          Row(
            children: [
              // == 修改：使用状态中的分页信息 ==
              // 当前页码/总页数显示
              Text('第 ${state.currentPage}/${state.totalPages} 页'),
              const SizedBox(width: 16),
              
              // 使用KPPagination组件
              KPPagination(
                totalPage: state.totalPages,
                selectedPage: state.currentPage,
                onPageSelected: (int newPage) {
                  // == 修改：调用Notifier方法而不是setState ==
                  ref.read(cashManagementNotifierProvider.notifier).changePage(newPage);
                },
              ),
              const SizedBox(width: 16),  // 右侧边距
            ],
          ),
        ],
      ),
    );
  }
}