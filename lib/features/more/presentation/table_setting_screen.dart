import 'package:flutter/material.dart';
import 'package:flutter_layout_grid/flutter_layout_grid.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/common/components/index.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/common/extension/widget_extension.dart';
import 'package:kpos/common/services/local_storage/key_value_storage_service.dart';
import 'package:kpos/features/table/application/table_service.dart';
import 'package:kpos/features/table/presentation/table_screen.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

// 桌台设置示例数据模型类
class TableExampleModel {
  final Offset position;
  final String title;
  final int shape;
  final bool? special;

  TableExampleModel({
    required this.position,
    required this.title,
    required this.shape,
    this.special,
  });
}

class TableSettingScreen extends ConsumerStatefulWidget {
  const TableSettingScreen({super.key});

  @override
  ConsumerState createState() => _TableSettingScreenState();
}

class _TableSettingScreenState extends ConsumerState<TableSettingScreen> {
  //页面是否已真正返回
  bool _hasPopped = false;

  ValueNotifier<int> tableShowType = ValueNotifier(1);
  ValueNotifier<int> tableTimeDisplay = ValueNotifier(1);

  ValueNotifier<bool> isTableCleaning = ValueNotifier(true);

  KeyValueStorageService? keyValueStorageService;

  // 新增：用于定位气泡弹出位置
  final GlobalKey _popoverAnchorKey = GlobalKey();

  List<TableExampleModel> planItemList = [
    TableExampleModel(position: const Offset(18, 8), title: 'A1', shape: 1),
    TableExampleModel(
      position: const Offset(155, 8),
      title: 'A2',
      shape: 1,
      special: true,
    ),
    TableExampleModel(position: const Offset(18, 114), title: 'A3', shape: 1),
    TableExampleModel(
      position: const Offset(76, 114),
      title: 'A4',
      shape: 1,
      special: true,
    ),
    TableExampleModel(position: const Offset(222, 114), title: 'A5', shape: 1),
    TableExampleModel(position: const Offset(85, 38), title: 'A6', shape: 2),
    TableExampleModel(position: const Offset(153, 78), title: 'A7', shape: 2),
    TableExampleModel(position: const Offset(219, 38), title: 'A8', shape: 2),
  ];

  @override
  void initState() {
    super.initState();
    //获取已保存的设置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      keyValueStorageService = ref.read(keyValueStorageServiceProvider);
      tableShowType.value = keyValueStorageService!.getTableShowType();
      tableTimeDisplay.value = keyValueStorageService!.getTableTimeDisplay();
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop && !_hasPopped) {
          _hasPopped = true;
          keyValueStorageService!.setTableShowType(tableShowType.value);
          keyValueStorageService!.setTableTimeDisplay(tableTimeDisplay.value);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
          ),
          centerTitle: false,
          title: Text(
            context.locale.tableSettings,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: Color(0xFF323843),
            ),
          ),
        ),
        body: SizedBox(
          width: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.locale.tableLayout,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF323843),
                      ),
                    ),
                    const SizedBox(height: 24),
                    //布局类型选择
                    ValueListenableBuilder(
                      valueListenable: tableShowType,
                      builder: (context, value, _) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _buildViewOption(
                                isSelected: tableShowType.value == 1,
                                title: context.locale.floorPlansView,
                                contentWidget: _buildLayoutView(1),
                                onChange: () {
                                  tableShowType.value =
                                      tableShowType.value == 1 ? 2 : 1;
                                  ref.read(tableViewShowTypeProvider.notifier).set(tableShowType.value);
                                }),
                            const SizedBox(width: 32),
                            _buildViewOption(
                                isSelected: tableShowType.value == 2,
                                title: context.locale.gridView,
                                contentWidget: _buildLayoutView(2),
                                onChange: () {
                                  tableShowType.value =
                                      tableShowType.value == 1 ? 2 : 1;
                                  ref.read(tableViewShowTypeProvider.notifier).set(tableShowType.value);
                                }),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 40),
                    //Table time display
                    Text(
                      context.locale.tableTimeDisplay,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF323843),
                      ),
                    ),
                    const SizedBox(height: 24),
                    //时间展示选择
                    ValueListenableBuilder(
                      valueListenable: tableTimeDisplay,
                      builder: (context, value, _) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _buildViewOption(
                                isSelected: tableTimeDisplay.value == 1,
                                title: context.locale.usageTimePeriod,
                                contentWidget: _buildTimeDisplayView(1),
                                onChange: () {
                                  tableTimeDisplay.value =
                                  tableTimeDisplay.value == 1 ? 2 : 1;
                                  ref.read(tableTimeDisplayShowProvider.notifier).set(tableTimeDisplay.value);
                                }),
                            const SizedBox(width: 32),
                            _buildViewOption(
                                isSelected: tableTimeDisplay.value == 2,
                                title: context.locale.usageTime,
                                contentWidget: _buildTimeDisplayView(2),
                                onChange: () {
                                  tableTimeDisplay.value =
                                  tableTimeDisplay.value == 1 ? 2 : 1;
                                  ref.read(tableTimeDisplayShowProvider.notifier).set(tableTimeDisplay.value);
                                }),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 40),
                    //清台方式
                    Text(
                      context.locale.tableCleaning,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF323843),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ValueListenableBuilder(
                          valueListenable: isTableCleaning,
                          builder: (context, value, _) {
                            return Switch(
                              value: isTableCleaning.value,
                              activeColor: Colors.white,
                              activeTrackColor: context.theme.brandNormalColor,
                              onChanged: (v) {
                                isTableCleaning.value = v;
                              },
                            );
                          },
                        ),
                        const SizedBox(width: 16),
                        Text(
                          context.locale.autoCleanTableTip,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF1D1B20),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: () {
                        // 使用 _popoverAnchorKey.currentContext 作为 anchor
                        final anchorContext = _popoverAnchorKey.currentContext;
                        if (anchorContext != null) {
                          KPPopover.showPopover(
                            context: anchorContext,
                            width: 200,
                            height: 100,
                            content: '气泡内容',
                            placement: TDPopoverPlacement.left, // 左侧弹出
                            showArrow: true,
                          );
                        }
                      },
                      child: Text('弹出气泡'),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      key: _popoverAnchorKey, // 添加 key
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: const Color(0xFF858A99),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Assets.images.iconHourglass.svg(width: 16, height: 16),
                              const SizedBox(width: 4),
                              const Text(
                                '01:00:08:00',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          const Text(
                            "A01",
                            style: TextStyle(
                              color: Color(0xFFA2A6B1),
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: 3),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              //人数图标
                              Assets.images.iconPeopleNum.svg(
                                width: 16,
                                height: 16,
                                color: const Color(0xFFA2A6B1),
                              ),
                              const SizedBox(width: 4),
                              const Text(
                                '2',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFFA2A6B1),
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildViewOption({
    required bool isSelected,
    required String title,
    required Widget contentWidget,
    required Function onChange,
  }) {
    return SizedBox(
      width: 268,
      child: Column(
        children: [
          Row(
            children: [
              isSelected
                  ? Assets.images.iconSettingSelected
                      .svg(
                      width: 24,
                      height: 24,
                    )
                      .onTap(() {
                      onChange();
                    })
                  : Assets.images.iconSettingNotSelected
                      .svg(
                      width: 24,
                      height: 24,
                    )
                      .onTap(() {
                      onChange();
                    }),
              const SizedBox(width: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF1D1B20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          contentWidget
        ],
      ),
    );
  }

  // 根据布局类型构建不同的布局示例视图
  Widget _buildLayoutView(int type) {
    if (type == 1) {
      return Stack(
        children: [
          Container(
            width: 268,
            height: 160,
            decoration: BoxDecoration(
              color: const Color(0xFFFAFBFC),
              border: Border.all(width: 1, color: const Color(0xFFEEEEF0)),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          ...planItemList.map((table) {
            return Positioned(
              left: table.position.dx,
              top: table.position.dy,
              child: _buildTableItem(1, 0, table: table),
            );
          }),
        ],
      );
    }
    return Container(
      width: 268,
      height: 160,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFBFC),
        border: Border.all(width: 1, color: const Color(0xFFEEEEF0)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: LayoutGrid(
        columnSizes: [1.fr, 1.fr, 1.fr, 1.fr, 1.fr],
        rowSizes: [1.fr, 1.fr, 1.fr],
        rowGap: 10,
        columnGap: 10,
        children: [for (var i = 0; i < 14; i++) _buildTableItem(2, i)],
      ),
    );
  }

  Widget _buildTableItem(int type, int index, {TableExampleModel? table}) {
    if (type == 1 && table != null) {
      double useWidth = table.shape == 2 ? 42 : 38;
      return Container(
        width: useWidth,
        height: useWidth,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: table.special != null
              ? const Color(0xFF858A99)
              : const Color(0xFFDCDDE1),
          shape: table.shape == 2 ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: table.shape == 1 ? BorderRadius.circular(4) : null,
        ),
        child: Text(
          table.title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color:
                table.special != null ? Colors.white : const Color(0xFF6F7686),
          ),
        ),
      );
    } else {
      return Container(
        width: 40,
        height: 40,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: index == 1 || index == 8
              ? const Color(0xFF858A99)
              : const Color(0xFFDCDDE1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          "A${index + 1}",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: index == 1 || index == 8
                ? Colors.white
                : const Color(0xFF6F7686),
          ),
        ),
      );
    }
  }

  //根据时间展示类型构建不同的示例视图
  Widget _buildTimeDisplayView(int type) {
    return Container(
      width: 268,
      height: 160,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: const Color(0xFFFAFBFC),
        border: Border.all(width: 1, color: const Color(0xFFEEEEF0)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          color: const Color(0xFF858A99),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                type == 1
                    ? Assets.images.iconHourglass.svg(width: 16, height: 16)
                    : Assets.images.iconClock.svg(width: 16, height: 16),
                const SizedBox(width: 4),
                Text(
                  type == 1 ? '01:00:08:00' : '00:08:06',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            const Text(
              "A01",
              style: TextStyle(
                color: Color(0xFFA2A6B1),
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 3),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //人数图标
                Assets.images.iconPeopleNum.svg(
                  width: 16,
                  height: 16,
                  color: const Color(0xFFA2A6B1),
                ),
                const SizedBox(width: 4),
                const Text(
                  '2',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFFA2A6B1),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
