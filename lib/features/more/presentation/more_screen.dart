
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kpos/assets/assets.gen.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/components/responsive_center_container.dart';
import 'package:kpos/features/store/application/store_service.dart';
import 'package:kpos/features/store/domain/bound_store_info.dart';

class MoreScreen extends ConsumerWidget {
  const MoreScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用ref.watch监听商店信息变化
  final storeItem = ref.read(storeServiceProvider).boundStoreInfo;
    return Scaffold(
      body: ResponsiveCenterContainer(
        contentWidth: 560,
        padding: const EdgeInsets.only(left: 20,right: 20,top: 96, bottom: 20),
        child: Container(
          child: SingleChildScrollView(
            child: Column(

              children: [
                // 商店信息
                _buildTopView(context,storeItem),

                const SizedBox(height: 24),

                // 设置项列表
                ListTile(
                  leading: Assets.images.iconTableSetting.svg(width: 24, height: 24),
                  title: Text(KPLocale.of(context).tableSetting),
                  trailing: const Icon(Icons.arrow_right),
                  onTap: () => GoRouter.of(context).go('/more/table_setting'),
                ),
                // 分隔线组件，用于分隔列表项
                // height: 分隔线在布局中的总高度
                // thickness: 分隔线的宽度
                // indent/endIndent: 左右的缩进距离
                // color: 浅灰色分隔线，不太突兀
                Divider(height: 1, thickness: 0.5, indent: 16, endIndent: 16, color: Colors.grey[350],),

                ListTile(
                  leading: Assets.images.iconCashAnagement.svg(width: 24, height: 24),
                  title: Text(KPLocale.of(context).cashManagement),
                  trailing: const Icon(Icons.arrow_right),
                  onTap: () => GoRouter.of(context).go('/more/cash_management'),
                ),
                Divider(height: 1, thickness: 0.5, indent: 16, endIndent: 16, color: Colors.grey[350],),

                ListTile(
                  leading: Assets.images.iconOtherSetting.svg(width: 24, height: 24),
                  title: const Text('Other setting(我不翻译)'),
                  trailing: const Icon(Icons.arrow_right),
                  // onTap: () => const OtherSettingScreenRoute().go(context),
                ),
                Divider(height: 1, thickness: 0.5, indent: 16, endIndent: 16, color: Colors.grey[350],),

                ListTile(
                  leading: Assets.images.iconOtherSetting.svg(width: 24, height: 24),
                  title: const Text('Other setting(我不翻译)'),
                  trailing: const Icon(Icons.arrow_right),
                  // onTap: () => const OtherSettingScreenRoute().go(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopView(BuildContext context, BoundStoreInfo storeItem) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Align(
        alignment: Alignment.centerLeft, // 左对齐
        child: Column(
          mainAxisSize: MainAxisSize.min, // 列最小化
          crossAxisAlignment: CrossAxisAlignment.start, // 左对齐
          children: [
          Text(
            storeItem.brandName,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
          ),
          Text(
            storeItem.storeName,
            style: const TextStyle(color: Colors.grey),
          ),
        ],
        ),
      ),
    );
  }


}