/// 弹出式日历选择器组件
/// 本文件实现了一个类似 Material Design 风格的弹出式日历选择器
/// 特点：
/// 1. 并排显示两个月份的日历 - 提高日期选择效率
/// 2. 支持日期范围选择 - 适合筛选时间段数据
/// 3. 底部有"CANCEL"和"OK"按钮 - 符合标准对话框设计
/// 4. 使用橙色主题 - 与应用整体风格保持一致

import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart'; // 引入 Syncfusion 日历选择器库
import 'package:kpos/common/extension/build_context_extension.dart'; // 导入上下文扩展，用于获取主题和语言

/// 弹出式日历选择器组件
/// 提供了一个静态方法来显示弹出式日历选择器对话框
class PopupDateRangePicker {
  /// 显示弹出式日历选择器
  /// 
  /// 参数：
  /// - [context] - 构建对话框的上下文
  /// - [initialDateRange] - 可选，初始选中的日期范围
  /// - [firstDate] - 可选，可选择的最早日期，默认为当前日期前一年
  /// - [lastDate] - 可选，可选择的最晚日期，默认为当前日期后两年
  /// - [anchorPoint] - 可选，日历选择器的锚点位置，用于定位日历选择器
  /// - [margin] - 可选，日历选择器与锚点之间的距离，默认为4像素
  /// 
  /// 返回：
  /// - [Future<DateTimeRange?>] - 异步返回选择的日期范围，如果用户取消选择则返回 null
  static Future<DateTimeRange?> show(
    BuildContext context, {
    DateTimeRange? initialDateRange,
    DateTime? firstDate,
    DateTime? lastDate,
    Offset? anchorPoint,
    double margin = 4.0,
  }) async {
    // 获取当前日期
    final DateTime now = DateTime.now();
    
    // 设置初始日期范围，如果没有提供，则默认为当前日期到一周后
    final DateTimeRange initialRange = initialDateRange ??
        DateTimeRange(
          start: now,
          end: now,
          // end: now.add(const Duration(days: 7)),//假如需要设置默认值，可以在这里修改
        );
        
    // 显示对话框并等待结果
    final result = await showDialog<DateTimeRange>(
      context: context,
      // 使用透明背景，这样我们可以用自定义定位的日历
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      builder: (BuildContext context) {
        // 如果提供了锚点位置，则使用自定义定位
        if (anchorPoint != null) {
          // 获取屏幕尺寸
          final screenSize = MediaQuery.of(context).size;
          
          // 日历对话框尺寸
          const double dialogWidth = 640.0;
          const double dialogHeight = 410.0;
          
          // 计算日历对话框的左侧位置，确保不超出屏幕
          double left = anchorPoint.dx;
          if (left + dialogWidth > screenSize.width) {
            left = screenSize.width - dialogWidth - 16;
          }
          if (left < 16) left = 16;
          
          // 计算日历对话框的顶部位置，紧贴输入框底部
          // 这里的偏移量计算是为了视觉上的精确定位：
          // margin是基础间距，-16*2调整位置避免遮挡，+2是微调以获得最佳视觉效果
          double top = anchorPoint.dy + margin - 16*2 + 2;
          
          // 如果对话框超出屏幕底部，则显示在输入框上方
          if (top + dialogHeight > screenSize.height) {
            top = anchorPoint.dy - dialogHeight - margin;
          }
          
          return Stack(
            children: [
              // 全屏幕透明层，用于捕获点击事件关闭对话框
              Positioned.fill(
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(color: Colors.transparent),
                ),
              ),
              // 定位日历对话框
              Positioned(
                left: left,
                top: top,
                child: Material(
                  elevation: 4,
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: dialogWidth,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: _PopupDateRangePickerDialog(
                      initialDateRange: initialRange,
                      firstDate: firstDate ?? now.subtract(const Duration(days: 365)),
                      lastDate: lastDate ?? now.add(const Duration(days: 365 * 2)),
                    ),
                  ),
                ),
              ),
            ],
          );
        }
        
        // 如果没有提供锚点位置，则使用普通对话框
        return _PopupDateRangePickerDialog(
          initialDateRange: initialRange, // 初始日期范围
          firstDate: firstDate ?? now.subtract(const Duration(days: 365)), // 最早可选日期，默认为一年前
          lastDate: lastDate ?? now.add(const Duration(days: 365 * 2)), // 最晚可选日期，默认为两年后
        );
      },
    );
    
    // 返回选择的日期范围
    return result;
  }
}

/// 弹出式日历选择器对话框组件
/// 内部类，用于实现弹出式日历选择器的对话框界面
class _PopupDateRangePickerDialog extends StatefulWidget {
  /// 初始日期范围
  final DateTimeRange initialDateRange;
  
  /// 可选择的最早日期
  final DateTime firstDate;
  
  /// 可选择的最晚日期
  final DateTime lastDate;

  /// 构造函数
  /// 
  /// 参数：
  /// - [initialDateRange] - 初始选中的日期范围
  /// - [firstDate] - 可选择的最早日期
  /// - [lastDate] - 可选择的最晚日期
  const _PopupDateRangePickerDialog({
    required this.initialDateRange,
    required this.firstDate,
    required this.lastDate,
  });

  @override
  State<_PopupDateRangePickerDialog> createState() => _PopupDateRangePickerDialogState();
}

/// 弹出式日历选择器对话框的状态类
class _PopupDateRangePickerDialogState extends State<_PopupDateRangePickerDialog> {
  /// 日历选择器的控制器
  late DateRangePickerController _controller;
  
  /// 选中的开始日期
  late DateTime _startDate;
  
  /// 选中的结束日期
  late DateTime _endDate;
  
  /// 初始化状态
  @override
  void initState() {
    super.initState();
    // 初始化开始和结束日期
    _startDate = widget.initialDateRange.start;
    _endDate = widget.initialDateRange.end;
    
    // 初始化日历控制器
    _controller = DateRangePickerController();
    // 设置初始选中范围
    _controller.selectedRange = PickerDateRange(_startDate, _endDate);
  }
  
  /// 释放资源
  @override
  void dispose() {
    // 释放日历控制器
    _controller.dispose();
    super.dispose();
  }

  /// 构建弹出式日历选择器对话框的用户界面
  @override
  Widget build(BuildContext context) {
    return Dialog(
      // 设置对话框的内边距
      insetPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
      // 设置对话框的圆角
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      // 设置剪裁行为，防止内容超出圆角
      clipBehavior: Clip.antiAlias,
      child: Column(
        // 设置列的大小为最小，以适应内容
        mainAxisSize: MainAxisSize.min,
        children: [
          // 日历选择器区域
          SizedBox(
            // 高度固定410
            height: 410,
            child: SfDateRangePicker(
              // 使用日历控制器
              controller: _controller,
              // 设置视图为月视图
              view: DateRangePickerView.month,
              // 设置选择模式为范围选择
              selectionMode: DateRangePickerSelectionMode.range,
              // 设置最早可选日期
              minDate: widget.firstDate,
              // 设置最晚可选日期
              maxDate: widget.lastDate,
              // 设置月视图的属性
              monthViewSettings: const DateRangePickerMonthViewSettings(
                // 设置周一为一周的第一天
                firstDayOfWeek: 1,
              ),
              // 设置月份格式，只显示月份名称
              monthFormat: 'MMMM',
              // 设置本地化对象，根据当前语言环境设置
              // 注意：SfDateRangePicker 的国际化需要使用 syncfusion_localizations 包
              // 设置头部样式
              headerStyle: const DateRangePickerHeaderStyle(
                // 文本居中
                textAlign: TextAlign.center,
                backgroundColor: Colors.white, //头部背景色
                // 设置文本样式
                textStyle: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              // 设置选中颜色为橙色
              selectionColor: Colors.orange,
              // 设置范围开始颜色为橙色
              startRangeSelectionColor: Colors.orange,
              // 设置范围结束颜色为橙色
              endRangeSelectionColor: Colors.orange,
              // 设置范围背景颜色为半透明橙色 - 轻微突出显示选中的日期范围
              rangeSelectionColor: Colors.orange.withValues(alpha: 0.1),
              // 设置今天高亮颜色为橙色
              todayHighlightColor: Colors.transparent,
              // 显示导航箭头
              showNavigationArrow: true,
              // 允许选择过去的日期
              enablePastDates: true,
              backgroundColor: Colors.white,//日历背景色
              // todayHighlightColor: Colors.transparent, 
              monthCellStyle: DateRangePickerMonthCellStyle(
                todayTextStyle: TextStyle( color: Colors.black87 ),
                todayCellDecoration: BoxDecoration( //移除当天圆圈标记
                  color: Colors.transparent, 
                  shape: BoxShape.rectangle, 
                  border: Border.all(color: Colors.transparent, width: 0
                  ))

              ),
              // 当选择变化时的回调
              onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                if (args.value is PickerDateRange) {
                  // 获取选择的日期范围
                  final PickerDateRange range = args.value as PickerDateRange;
                  setState(() {
                    // 更新开始日期
                    if (range.startDate != null) {
                      _startDate = range.startDate!;
                    }
                    // 更新结束日期，如果没有选择结束日期，则使用开始日期
                    if (range.endDate != null) {
                      _endDate = range.endDate!;
                    } else if (range.startDate != null) {
                      _endDate = range.startDate!;
                    }
                  });
                }
              },
              // 设置导航方向为水平
              navigationDirection: DateRangePickerNavigationDirection.horizontal,
              // 允许视图导航
              allowViewNavigation: true,
              // 启用多视图模式 - 并排显示两个月份的日历，方便用户选择跨月日期范围
              enableMultiView: true,
              // 设置两个月份视图之间的间距 - 视觉上区分两个月份，提高可读性
              viewSpacing: 20,
              // 设置选中日期的形状为圆形 - 符合Material Design设计规范，视觉效果更现代
              selectionShape: DateRangePickerSelectionShape.circle,
              // 设置选中圆角半径
              selectionRadius: 20,
            ),
          ),
          // 底部按钮区域 - 包含取消和确认按钮，遵循标准对话框设计模式
          Container(
            // 设置内边距
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: Colors.white,//底部背景色
            child: Row(
              // 将按钮分别放在两端
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // 取消按钮
                TextButton(
                  onPressed: () {
                    // 关闭对话框，不返回值
                    Navigator.of(context).pop();
                  },     
                  child: Text(
                    context.locale.cancel, // 使用国际化文本
                    style: const TextStyle(color: Colors.orange,
                    fontWeight: FontWeight.w500,
                    fontSize: 18),
                  ),
                ),
                const SizedBox(width: 8), // 添加间距
                // 确认按钮
                TextButton(
                  onPressed: () {
                    // 添加调试输出
                  print("确认按钮被点击");
                  print("选择的日期范围: 开始=${_startDate}, 结束=${_endDate}");
                  
                  // 确保日期值不为空
                  if (_startDate == null || _endDate == null) {
                    print("警告：日期值为空，使用当前日期");
                    final now = DateTime.now();
                    _startDate ??= now;
                    _endDate ??= now;
                  }
                    // 关闭对话框，返回选择的日期范围
                    Navigator.of(context).pop(
                      DateTimeRange(
                        start: _startDate, // 开始日期
                        end: _endDate, // 结束日期
                      ),
                    );
                  },
                  child: Text(
                    context.locale.confirm, // 使用国际化文本
                    style: const TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                      fontSize: 18,
                      ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
