import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:kpos/common/extension/build_context_extension.dart';
import 'package:kpos/features/more/application/create_pay_notifier.dart';

import '../../../../common/components/kp_drop_down_menu.dart';

class CreatePayDialog {
  /// 显示创建收入支出的对话框
  /// 参数：
  /// - context: 上下文
  /// - onConfirm: 确认回调，返回类型、支付方式、金额和备注
  /// - initialType: 初始支付类型（可选）
  /// - initialPaymentMethod: 初始支付方式（可选）
  /// - initialAmount: 初始金额（可选）
  /// - initialNote: 初始备注（可选）
  static Future<void> show({
    required BuildContext context,
    Function(String? type, String paymentMethod, double amount, String note)?
        onConfirm,
    String? initialType,
    String? initialPaymentMethod,
    String? initialPaymentMethodId,
    double? initialAmount,
    String? initialNote,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => _CreatePayDialogContent(
        onConfirm: onConfirm,
        initialType: initialType,
        initialPaymentMethod: initialPaymentMethod,
        initialPaymentMethodId: initialPaymentMethodId,
        initialAmount: initialAmount,
        initialNote: initialNote,
      ),
    );
  }
}

/// 收入支出对话框内容
class _CreatePayDialogContent extends ConsumerStatefulWidget {
  final Function(
          String? type, String paymentMethod, double amount, String note)?
      onConfirm;
  final String? initialType;
  final String? initialPaymentMethod;
  final String? initialPaymentMethodId;
  final double? initialAmount;
  final String? initialNote;

  const _CreatePayDialogContent({
    Key? key,
    this.onConfirm,
    this.initialType,
    this.initialPaymentMethod,
    this.initialPaymentMethodId,
    this.initialAmount,
    this.initialNote,
  }) : super(key: key);

  @override
  ConsumerState<_CreatePayDialogContent> createState() =>
      _CreatePayDialogContentState();
}

class _CreatePayDialogContentState
    extends ConsumerState<_CreatePayDialogContent> {
  // 表单验证key
  final _formKey = GlobalKey<FormState>();

  // 控制器
  late TextEditingController _amountController;
  late TextEditingController _noteController;

  @override
  void initState() {
    super.initState();

    // 初始化控制器
    _amountController = TextEditingController(
      text: widget.initialAmount != null ? widget.initialAmount!.toStringAsFixed(2) : '',
    );

    _noteController = TextEditingController(
      text: widget.initialNote != null ? widget.initialNote! : '',
    );

    // 初始化状态管理器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(createPayNotifierProvider.notifier).initializeWithData(
            paymentType: widget.initialType,
            paymentMethod: widget.initialPaymentMethod,
            paymentMethodId: widget.initialPaymentMethodId,
            amount: widget.initialAmount,
            note: widget.initialNote,
          );
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createPayNotifierProvider);
    final notifier = ref.read(createPayNotifierProvider.notifier);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      child: Container(
        width: 480,
        height: 460,
        color: Colors.white,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                KPLocale.of(context).payInOut,
                style:
                    const TextStyle(fontSize: 20, fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 24),

              // Pay in / Pay out 按钮
              Container(
                height: 56,
                padding: EdgeInsets.symmetric(vertical: 9),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildPayTypeButton(
                        label: KPLocale.of(context).payIn,
                        type: 'pay_in',
                        isSelected: state.paymentType == 'pay_in',
                        onTap: () => notifier.setPaymentType('pay_in'),
                        hasError: state.paymentTypeError != null,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildPayTypeButton(
                        label: KPLocale.of(context).payOut,
                        type: 'pay_out',
                        isSelected: state.paymentType == 'pay_out',
                        onTap: () => notifier.setPaymentType('pay_out'),
                        hasError: state.paymentTypeError != null,
                      ),
                    ),
                  ],
                ),
              ),
              // 支付类型错误提示
              if (state.paymentTypeError != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    state.paymentTypeError!,
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
              const SizedBox(height: 16),

              // 支付方式
              SizedBox(
                height: 56,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    KPTextFieldDropDown(
                      items: [
                        DropdownItem(id: "cash", name: "现金"),
                        // DropdownItem(id: "credit", name: "信用卡"),
                        // DropdownItem(id: "debit", name: "借记卡"),
                        // DropdownItem(id: "wechat", name: "微信"),
                        // DropdownItem(id: "alipay", name: "支付宝"),
                      ],
                      value: DropdownItem(
                        id: state.selectedPaymentMethodId,
                        name: state.selectedPaymentMethod,
                      ),
                      labelText: '付款方式*',
                      hintText: '请选择付款方式',
                      useFixedBorder: true,
                      fixedBorderColor: KPColors.textGrayPrimary,
                      onChanged: (item) {
                        if (item != null) {
                          notifier.setPaymentMethod(item.name, item.id);
                        }
                      },
                      menuMaxHeight: 300,
                      menuWidth: 200,
                      itemTextStyle: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF323843),
                        fontWeight: FontWeight.w500,
                      ),
                      dropdownColor: Colors.grey[50],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 金额
              Container(
                height: 56,
                child: TextFormField(
                  key: const Key('amount'),
                  controller: _amountController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    // 第一个格式化器：只允许输入数字和小数点
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),

                    // 第二个格式化器：限制小数位数不超过2位
                    TextInputFormatter.withFunction((oldValue, newValue) {
                      // 如果输入为空，允许
                      if (newValue.text.isEmpty) {
                        return newValue;
                      }

                      // 检查是否为有效数字
                      if (double.tryParse(newValue.text) != null) {
                        final parts = newValue.text.split('.');
                        // 如果有小数点且小数位超过2位，保持原值
                        if (parts.length > 1 && parts[1].length > 2) {
                          return oldValue;
                        }
                      } else {
                        // 如果不是有效数字，保持原值
                        return oldValue;
                      }
                      return newValue;
                    }),
                  ],
                  onChanged: (value) {
                    notifier.setAmount(value);
                  },
                  decoration: InputDecoration(
                    labelText: '金额*',
                    border:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    enabledBorder:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    focusedBorder:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    prefixText: 'S\$ ',
                    hintText: '0.00',
                    errorText: state.amountError,
                    errorStyle: const TextStyle(color: Colors.red),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 备注
              Container(
                height: 56,
                child: TextFormField(
                  key: const Key('note'),
                  controller: _noteController,
                  maxLines: 2,
                  onChanged: (value) {
                    notifier.setNote(value);
                  },
                  decoration: InputDecoration(
                    labelText: '备注*',
                    border:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    enabledBorder:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    focusedBorder:
                        state.amountError != null ? _errorBorder : _inputBorder,
                    hintText: '添加一条备注',
                    errorText: state.noteError,
                    errorStyle: const TextStyle(color: Colors.red),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // 底部按钮 - 🔑 修复按钮高度为48px
              SizedBox(
                height: 48, // == 修改：固定行高度为48px ==
                child: Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: SizedBox(
                        height: 48, // == 修改：固定按钮高度为48px ==
                        child: ElevatedButton(
                          onPressed: state.isSubmitting
                              ? null
                              : () => Navigator.of(context).pop(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.shade200,
                            foregroundColor: Colors.black87,
                            padding: const EdgeInsets.symmetric(
                                vertical: 8,
                                horizontal: 16), // == 修改：调整内边距适应48px高度 ==
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8)),
                            disabledBackgroundColor: Colors.grey.shade100,
                            disabledForegroundColor: Colors.grey.shade400,
                            minimumSize: const Size(
                                double.infinity, 48), // == 修改：最小尺寸为48px ==
                          ),
                          child: Text(
                            context.locale.cancel,
                            style: const TextStyle(fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // 创建按钮 - 🔑 修复高度为48px
                    Expanded(
                      child: SizedBox(
                        height: 48, // == 修改：固定按钮高度为48px ==
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              width: double.infinity,
                              height: 48, // == 修改：固定按钮高度为48px ==
                              child: ElevatedButton(
                                onPressed: state.isButtonEnabled
                                    ? () => _handleCreateButton(notifier)
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: state.buttonColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8,
                                      horizontal: 16), // == 修改：调整内边距适应48px高度 ==
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8)),
                                  disabledBackgroundColor: Colors.grey.shade300,
                                  minimumSize: const Size(double.infinity,
                                      48), // == 修改：最小尺寸为48px ==
                                ),
                                child: Container(
                                  width: double.infinity,
                                  height: double.infinity,
                                  alignment: Alignment.center, // == 确保内容居中 ==
                                  child: state.isSubmitting
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : Text(
                                          state.buttonText(
                                              context), // == 使用动态文本 ==
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          textAlign: TextAlign.center,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 错误提示
              if (state.hasError)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    state.errorMessage ?? '创建失败',
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 输入边框的UI
  final _inputBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: const BorderSide(color: Color(0xFF323843)),
  );

  // 错误状态的边框
  final _errorBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(8),
    borderSide: const BorderSide(color: Colors.red, width: 2),
  );

  /// 构建支付类型按钮
  Widget _buildPayTypeButton({
    required String label,
    required String type,
    required bool isSelected,
    required VoidCallback onTap,
    bool hasError = false,
  }) {
    Color borderColor;
    if (hasError) {
      borderColor = Colors.red; // 错误时显示红色边框
    } else if (isSelected) {
      borderColor = Colors.orange; // 选中时显示橙色边框
    } else {
      borderColor = Colors.grey.shade300; // 默认灰色边框
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFFFFF7E6) : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: borderColor, width: 2),
        ),
        child: Center(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: hasError
                  ? Colors.red // 错误时文字显示红色
                  : (isSelected ? Colors.orange : Colors.black87),
            ),
          ),
        ),
      ),
    );
  }

  /// 处理创建按钮点击
  void _handleCreateButton(CreatePayNotifier notifier) {
    // 先验证表单
    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }

    // 调用notifier处理按钮逻辑
    notifier.onCreateButtonPressed();

    // 如果是第二次点击（确认创建），执行回调
    if (notifier.state.isSubmitting) {
      final formData = notifier.getFormData();

      // 模拟网络请求延迟，显示菊花转圈
      Future.delayed(const Duration(milliseconds: 800), () {
        if (widget.onConfirm != null) {
          widget.onConfirm!(
            formData['type'] as String?,
            formData['paymentMethod'] as String,
            formData['amount'] as double,
            formData['note'] as String,
          );
        }

        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }
}
