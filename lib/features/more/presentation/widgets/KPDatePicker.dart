// 导入必要的Flutter和国际化包
import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // 用于日期格式化
import 'popup_date_picker.dart'; // 导入弹出式日期选择器

/// KPDateRangePicker
/// 自定义日期范围选择器组件，提供美观的UI和灵活的日期范围选择功能
/// 包含输入框和弹出式日历选择器，支持选择日期范围
class KPDateRangePicker extends StatefulWidget {
  /// 输入框上方显示的标签文本
  final String labelText;

  /// 未选择日期时显示的提示文本
  final String hintText;

  /// 确认选择回调函数
  /// 参数为选中的开始日期和结束日期
  final Function(DateTime? startDate, DateTime? endDate)? onConfirm;

  /// 取消选择回调函数
  final Function()? onCancel;

  /// 选中状态下的边框颜色
  /// 默认为橙色(0xFFFF8500)
  final Color selectedBorderColor;

  /// 未选中状态下的边框颜色
  /// 默认为灰色(0xFFE0E0E0)
  final Color unselectedBorderColor;

  /// 输入框前缀图标
  final Widget? prefixIcon;

  /// 输入框后缀图标
  /// 默认为日历图标
  final Widget? suffixIcon;

  /// 可选择的最早日期限制
  final DateTime? firstDate;

  /// 可选择的最晚日期限制
  final DateTime? lastDate;

  /// 日期显示格式
  /// 默认为'mm/dd/yyyy — mm/dd/yyyy'
  final String dateFormat;

  /// 组件是否启用
  /// 禁用状态下无法打开日期选择器
  final bool enabled;

  /// 构造函数
  ///
  /// [labelText] - 必需参数，输入框标签文本
  /// [hintText] - 提示文本，默认为空字符串
  /// [onConfirm] - 确认选择回调函数
  /// [onCancel] - 取消选择回调函数
  /// [selectedBorderColor] - 选中状态边框颜色，默认为橙色
  /// [unselectedBorderColor] - 未选中状态边框颜色，默认为灰色
  /// [prefixIcon] - 输入框前缀图标
  /// [suffixIcon] - 输入框后缀图标
  /// [firstDate] - 可选择的最早日期限制
  /// [lastDate] - 可选择的最晚日期限制
  /// [dateFormat] - 日期显示格式，默认为'MM/dd/yyyy'
  /// [enabled] - 组件是否启用，默认为true
  const KPDateRangePicker({
    Key? key,
    required this.labelText,
    this.hintText = '',
    this.onConfirm,
    this.onCancel,
    this.selectedBorderColor = const Color(0xFFFF8500), // 橙色作为选中状态颜色
    this.unselectedBorderColor = const Color(0xFFE0E0E0), // 灰色作为非选中状态颜色
    this.prefixIcon,
    this.suffixIcon,
    this.firstDate, // 默认限制最早日期为2025年1月1日
    this.lastDate, // 默认不限制最晚日期
    this.dateFormat = 'MM/dd/yyyy',
    this.enabled = true,
  }) : super(key: key);

  @override
  State<KPDateRangePicker> createState() => _KPDatepickerState();
}

/// 日期范围选择器的状态类
/// 管理组件的内部状态和UI交互逻辑
class _KPDatepickerState extends State<KPDateRangePicker> {
  /// 文本控制器
  /// 用于控制输入框中显示的文本内容
  late TextEditingController _controller;

  /// 焦点节点
  /// 用于管理输入框的焦点状态，监听焦点变化以触发日期选择器的显示
  final FocusNode _focusNode = FocusNode();

  /// 当前选中的日期范围
  /// 存储用户选择的开始日期和结束日期
  DateTimeRange? _selectedDateRange;

  /// 是否处于选中状态（日历弹出期间）
  /// 用于控制输入框的样式变化
  bool _isSelected = false;

  /// 是否已经选择了日期
  /// 用于区分初始状态和已选择状态，影响输入框的样式
  bool _hasSelectedDate = false;

  @override
  void initState() {
    super.initState();
    // 初始化文本控制器，设置初始值为提示文本
    _controller = TextEditingController(text: widget.hintText);
    // 添加焦点监听器，当输入框获得或失去焦点时触发
    _focusNode.addListener(_onFocusChange);
  }

  /// 焦点变化监听器
  /// 当输入框获得焦点时，自动显示日期选择器
  /// 同时更新选中状态以改变输入框的样式
  void _onFocusChange() {
    // 更新选中状态，用于控制输入框的样式
    setState(() {
      _isSelected = _focusNode.hasFocus;
    });

    // 当获得焦点时，显示日期选择器
    if (_focusNode.hasFocus) {
      _showDateRangePicker();
    }
  }

  /// 显示日期范围选择器
  /// 这是一个异步方法，用于处理日期选择器的显示和选择结果
  Future<void> _showDateRangePicker() async {
    // 如果组件处于禁用状态，直接返回不显示选择器
    if (!widget.enabled) return;
    
    // 获取输入框的位置信息，用于定位弹出框
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero); // 输入框在屏幕上的绝对位置
    final size = renderBox.size; // 输入框的尺寸
    
    // 计算日历应该出现的位置（紧贴输入框底部）
    // 设置锚点位置为输入框左下角
    final anchorPoint = Offset(
      position.dx, // x坐标与输入框左侧对齐
      position.dy + size.height // y坐标为输入框底部
    );

    // 使用 PopupDateRangePicker 显示日期选择器
    // 现在我们传入锚点位置参数，让日历选择器紧贴输入框底部
    final result = await PopupDateRangePicker.show(
      context,
      initialDateRange: _selectedDateRange,
      firstDate: widget.firstDate ?? DateTime(2025, 1, 1),
      lastDate: widget.lastDate ?? DateTime(2100, 12, 31),
      // 传入锚点位置，定位日历选择器
      anchorPoint: anchorPoint,
      // 设置距离为0，使其紧贴输入框
      margin: 0,
    );

    // 添加调试输出
    print("PopupDateRangePicker 返回结果: $result");
    
    // 如果用户选择了日期范围
    if (result != null) {
      setState(() {
        // 保存选中的日期范围
        _selectedDateRange = result;
        // 标记已选择日期状态
        _hasSelectedDate = true;

        // 获取当前语言环境
        final locale = Localizations.localeOf(context);
        final isZhLanguage = locale.languageCode == 'zh';
        
        // 创建日期格式化器，根据语言环境选择不同的格式
        final formatter = isZhLanguage 
            ? DateFormat('yyyy年MM月dd日') 
            : DateFormat(widget.dateFormat);
        
        // 格式化并设置选中的日期范围，使用不同的分隔符
        final separator = isZhLanguage ? ' 至 ' : ' — ';
        print("日期5:${result.start}");
        print("日期6:${formatter.format(result.start)}");
        print("日期7:${result.end}");
        print("日期8:${formatter.format(result.end)}");
        _controller.text = '${formatter.format(result.start)}$separator${formatter.format(result.end)}';        

        // 调用回调函数
        if (widget.onConfirm != null) {
          widget.onConfirm!(result.start, result.end);
        }
      });
    } else {
      // 用户取消选择
      if (widget.onCancel != null) {
        widget.onCancel!();
      }
    }
    
    // 取消焦点
    _focusNode.unfocus();
    
    // 更新选中状态
    setState(() {
      _isSelected = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 根据不同状态确定边框颜色
    Color borderColor;
    const double borderWidth = 1.0; // 所有状态下边框宽度都保持为1.0

    if (_isSelected || _hasSelectedDate) {
      // 日历弹出期间或已选择日期 - 选中状态
      borderColor = widget.selectedBorderColor;
    } else if (!widget.enabled) {
      // 禁用状态
      borderColor = widget.unselectedBorderColor.withOpacity(0.5);
    } else {
      // 默认状态
      borderColor = widget.unselectedBorderColor;
    }

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.enabled ? _showDateRangePicker : null,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          // 移除这里的边框设置，只保留TextField的边框，避免重影
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            readOnly: true, // 设为只读，防止键盘弹出
            enabled: widget.enabled,
            decoration: InputDecoration(
              labelText: widget.labelText,
              hintText: widget.hintText,
              // 根据状态调整标签颜色
              labelStyle: TextStyle(
                color: _isSelected || _hasSelectedDate
                    ? widget.selectedBorderColor
                    : Colors.grey[600],
                fontSize: 14,
              ),
              prefixIcon: widget.prefixIcon,
              suffixIcon: widget.suffixIcon != null
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: Center(child: widget.suffixIcon),
                  )
                : IconButton(
                    icon: Icon(
                      Icons.calendar_today,                
                      color: _isSelected || _hasSelectedDate
                          ? widget.selectedBorderColor
                          : Colors.grey[600],
                    ),
                    onPressed: widget.enabled ? _showDateRangePicker : null,
                  ),
              // 使用OutlineInputBorder并根据状态设置边框颜色
              border: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: borderWidth),
                borderRadius: BorderRadius.circular(6),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF323843),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }
}
