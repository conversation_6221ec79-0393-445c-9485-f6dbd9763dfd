import 'package:kpay_component_v2/kpay_component_v2.dart';
import 'package:kpos/assets/kp_locale.g.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter/material.dart';

part 'create_pay_notifier.g.dart';

/// 按钮状态枚举
enum ButtonState { 
  disabled,   // 灰色-不可点击
  enabled,    // 黑色-可点击  
  confirming  // 橙色-二次确认
}

/// 创建支付对话框的状态类
class CreatePayState {
  const CreatePayState({
    this.isSubmitting = false,
    this.paymentType,
    this.selectedPaymentMethod = '现金',
    this.selectedPaymentMethodId = 'cash',
    this.amountText = '',  // == 明确表示是输入文本 ==
    this.note = '',
    this.buttonState = ButtonState.disabled,
    this.hasError = false,
    this.errorMessage,
    this.showFieldErrors = false,  // == 新增：是否显示字段错误 ==
  });

  final bool isSubmitting;
  final String? paymentType;
  final String selectedPaymentMethod;
  final String selectedPaymentMethodId;
  final String amountText;  // == 输入框的文本内容 ==
  final String note;
  final ButtonState buttonState;
  final bool hasError;
  final String? errorMessage;
  final bool showFieldErrors;

  /// == 新增：获取解析后的金额数值 ==
  double? get amountValue => double.tryParse(amountText);

  /// == 新增：检查金额是否有效 ==
  bool get isAmountValid => amountValue != null && amountValue! > 0;

  /// 检查备注是否有效
  bool get isNoteValid => note.trim().isNotEmpty;

  /// 检查支付类型是否有效
  bool get isPaymentTypeValid => paymentType != null && paymentType!.isNotEmpty;

  /// 检查表单是否有效（所有必填项已填写）
  bool get isFormValid => 
    paymentType != null && 
    paymentType!.isNotEmpty &&
    amountText.isNotEmpty && 
    note.isNotEmpty &&
    isAmountValid;  // == 使用专门的金额验证 ==

    /// == 新增：获取金额字段错误信息 ==
  String? get amountError {
    if (!showFieldErrors) return null;
    
    if (amountText.isEmpty) {
      return '请输入金额';
    }
    if (amountValue == null) {
      return '请输入有效的金额';
    }
    if (amountValue! <= 0) {
      return '金额必须大于零';
    }
    return null;
  }

  /// == 新增：获取备注字段错误信息 ==
  String? get noteError {
    if (!showFieldErrors) return null;
    
    if (note.trim().isEmpty) {
      return '请输入备注';
    }
    return null;
  }

  /// == 新增：获取支付类型错误信息 ==
  String? get paymentTypeError {
    if (!showFieldErrors) return null;
    
    if (!isPaymentTypeValid) {
      return '请选择支付类型';
    }
    return null;
  }

  /// == 新增：检查是否有任何字段错误 ==
  bool get hasFieldErrors => 
    showFieldErrors && (amountError != null || noteError != null || paymentTypeError != null);

  /// 获取按钮颜色
  Color get buttonColor {
    if (isSubmitting) return Colors.grey.shade300;
    
    switch (buttonState) {
      case ButtonState.disabled:
        return Colors.grey.shade400;
      case ButtonState.enabled:
        return KPColors.fillGrayDarDarkest;
      case ButtonState.confirming:
        return KPColors.fillBrandNormal;
    }
  }

  /// 获取按钮文本
  String buttonText(BuildContext context) {
    if (isSubmitting) return '创建中...';
    
    switch (buttonState) {
      case ButtonState.disabled:
      case ButtonState.enabled:
        return KPLocale.of(context).create;
      case ButtonState.confirming:
        return KPLocale.of(context).confirmCreate;
    }
  }

  /// 按钮是否可点击
  bool get isButtonEnabled => buttonState != ButtonState.disabled && !isSubmitting;

  CreatePayState copyWith({
    bool? isSubmitting,
    String? paymentType,
    String? selectedPaymentMethod,
    String? selectedPaymentMethodId,
    String? amountText,  // == 使用明确的参数名 ==
    String? note,
    ButtonState? buttonState,
    bool? hasError,
    String? errorMessage,
    bool? showFieldErrors
  }) {
    return CreatePayState(
      isSubmitting: isSubmitting ?? this.isSubmitting,
      paymentType: paymentType ?? this.paymentType,
      selectedPaymentMethod: selectedPaymentMethod ?? this.selectedPaymentMethod,
      selectedPaymentMethodId: selectedPaymentMethodId ?? this.selectedPaymentMethodId,
      amountText: amountText ?? this.amountText,  // == 对应字段名 ==
      note: note ?? this.note,
      buttonState: buttonState ?? this.buttonState,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
      showFieldErrors: showFieldErrors ?? this.showFieldErrors,
    );
  }
}

/// 创建支付对话框的状态管理器
@riverpod
class CreatePayNotifier extends _$CreatePayNotifier {
  @override
  CreatePayState build() {
    return const CreatePayState();
  }

  /// 设置支付类型（Pay in / Pay out）
  void setPaymentType(String? type) {
    final newState = state.copyWith(paymentType: type);
    
    // 🔑 关键逻辑：切换支付类型时，如果按钮是橙色确认状态，重置为黑色状态
    ButtonState newButtonState;
    if (newState.isFormValid) {
      newButtonState = state.buttonState == ButtonState.confirming 
          ? ButtonState.enabled  // 从橙色重置为黑色
          : state.buttonState;   // 保持当前状态
    } else {
      newButtonState = ButtonState.disabled;
    }
    
    state = newState.copyWith(buttonState: newButtonState);
  }

  /// 设置支付方式
  void setPaymentMethod(String method, String methodId) {
    state = state.copyWith(
      selectedPaymentMethod: method,
      selectedPaymentMethodId: methodId,
    );
    _updateButtonState();
  }

  /// 设置金额文本
  void setAmount(String amountText) {
    state = state.copyWith(amountText: amountText);  // == 使用明确的字段名 ==
    _updateButtonState();
  }

  /// 设置备注
  void setNote(String note) {
    state = state.copyWith(note: note);
    _updateButtonState();
  }

  /// 启用字段错误显示 
  void enableFieldErrorDisplay() {
    state = state.copyWith(showFieldErrors: true);
  }

  /// 禁用字段错误显示 
  void disableFieldErrorDisplay() {
    state = state.copyWith(showFieldErrors: false);
  }

  /// 处理创建按钮点击
  void onCreateButtonPressed() {
    // 点击时启用错误显示
    if (!state.isFormValid) {
      enableFieldErrorDisplay();
      return;
    }

    if (!state.isButtonEnabled) return;

    switch (state.buttonState) {
      case ButtonState.enabled:
        // 第一次点击：变成橙色确认状态
        state = state.copyWith(buttonState: ButtonState.confirming);
        break;
      case ButtonState.confirming:
        // 第二次点击：执行创建
        _executeCreate();
        break;
      case ButtonState.disabled:
        // 不应该到达这里
        break;
    }
  }

  /// 重置状态（用于初始化编辑模式）
  void initializeWithData({
    String? paymentType,
    String? paymentMethod,
    String? paymentMethodId,
    double? amount,
    String? note,
  }) {
    state = CreatePayState(
      paymentType: paymentType,
      selectedPaymentMethod: paymentMethod ?? '现金',
      selectedPaymentMethodId: paymentMethodId ?? 'cash',
      amountText: amount?.toString() ?? '',  // == 数值转文本 ==
      note: note ?? '',
      showFieldErrors: false,  // == 初始化时不显示错误 ==
    );
    _updateButtonState();
  }

  /// 更新按钮状态
  void _updateButtonState() {
    ButtonState newButtonState;
    
    if (state.isFormValid) {
      // 如果表单有效，但当前是确认状态，保持确认状态
      // 如果表单有效，且不是确认状态，设为可点击状态
      newButtonState = state.buttonState == ButtonState.confirming 
          ? ButtonState.confirming 
          : ButtonState.enabled;
    } else {
      // 表单无效，设为禁用状态
      newButtonState = ButtonState.disabled;
    }
    
    if (newButtonState != state.buttonState) {
      state = state.copyWith(buttonState: newButtonState);
    }
  }

  /// 执行创建操作
  void _executeCreate() {
    // 这里将调用回调函数，实际的创建逻辑在外部处理
    // 先设置提交状态
    state = state.copyWith(isSubmitting: true);
  }

  /// 获取表单数据（供外部回调使用）
  Map<String, dynamic> getFormData() {
    return {
      'type': state.paymentType,
      'paymentMethod': state.selectedPaymentMethod,
      'amount': state.amountValue ?? 0.0,  // == 使用解析后的数值 ==
      'note': state.note,
    };
  }

  /// 设置错误状态
  void setError(String message) {
    state = state.copyWith(
      hasError: true,
      errorMessage: message,
      isSubmitting: false,
    );
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(
      hasError: false,
      errorMessage: null,
    );
  }

  /// == 新增：获取当前输入的金额数值（用于调试或其他用途）==
  double? getCurrentAmount() => state.amountValue;

  /// == 新增：检查当前表单状态（用于调试）==
  void debugFormState() {
     print('=== CreatePay Form State Debug ===');
    print('Payment Type: ${state.paymentType}');
    print('Amount Text: "${state.amountText}"');
    print('Amount Value: ${state.amountValue}');
    print('Note: "${state.note}"');
    print('Is Amount Valid: ${state.isAmountValid}');
    print('Is Note Valid: ${state.isNoteValid}');
    print('Is Form Valid: ${state.isFormValid}');
    print('Button State: ${state.buttonState}');
    print('Show Field Errors: ${state.showFieldErrors}');
    print('Amount Error: ${state.amountError}');
    print('Note Error: ${state.noteError}');
    print('================================');
  }
}