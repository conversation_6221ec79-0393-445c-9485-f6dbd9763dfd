
import 'dart:math';
import 'package:intl/intl.dart'; // 用于日期格式化

import 'package:kpos/features/more/data/cash_management_repository.dart';
import 'package:kpos/features/more/domain/cash_management_record.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'cash_management_notifier.g.dart';

// 1. 状态类
// 这个类是页面状态的快照,保持不可变性
class CashManagementState {
  CashManagementState({
    this.isLoading = false,
    this.isRefreshing = false,
    this.hasError = false,
    this.errorMessage = '',
    this.cashRecords = const [],
    this.totalRecords = 0,
    this.currentPage = 1,
    this.totalPages = 1,
    this.rowsPerPage = 10,
    this.startDate,
    this.endDate,
    this.selectedType = '全部类型',
    this.selectedPaymentMethod = '全部付款方式',
    this.isAddingRecord = false,  // 添加记录时的加载状态
  });

  //数据和UI状态
  final bool isLoading;
  final bool isRefreshing;
  final bool hasError;
  final String errorMessage;
  final List<CashRecord> cashRecords;
  //分页状态
  final int totalRecords;
  final int currentPage;
  final int totalPages;
  final int rowsPerPage;
  //筛选条件状态
  final DateTime? startDate;
  final DateTime? endDate;
  final String selectedType;
  final String selectedPaymentMethod;
  //添加记录状态
  final bool isAddingRecord;

  // copywith方法,用于创建状态的副本
  CashManagementState copyWith({
    bool? isLoading,
    bool? isRefreshing,
    bool? hasError,
    String? errorMessage,
    List<CashRecord>? cashRecords,
    int? totalRecords,
    int? currentPage,
    int? totalPages,
    int? rowsPerPage,
    DateTime? startDate,
    DateTime? endDate,
    String? selectedType,
    String? selectedPaymentMethod,
    bool? isAddingRecord,
  }) {
    return CashManagementState(
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
      cashRecords: cashRecords ?? this.cashRecords,
      totalRecords: totalRecords ?? this.totalRecords,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      rowsPerPage: rowsPerPage ?? this.rowsPerPage,
      startDate: startDate ?? this.startDate, // 这里允许设置为 null
      endDate: endDate ?? this.endDate,     // 这里允许设置为 null
      selectedType: selectedType ?? this.selectedType,
      selectedPaymentMethod: selectedPaymentMethod ?? this.selectedPaymentMethod,
      isAddingRecord: isAddingRecord ?? this.isAddingRecord,
    );
  }

  /// 获取格式化的日期范围显示文本
  String get formattedDateRange {
    if (startDate == null || endDate == null) {
      return 'mm/dd/yyyy — mm/dd/yyyy';
    }
    
    // 使用与原代码相同的格式
    final formatter = DateFormat('MM/dd/yyyy');
    return '${formatter.format(startDate!)} — ${formatter.format(endDate!)}';
  }
}

// 2. 逻辑控制器(Notifier)
@riverpod
class CashManagementNotifier extends _$CashManagementNotifier{

  // build方法在provider初始化时被调用
  // 他的职责是返回初始状态,并在这里出发第一次数据加载
  @override
  CashManagementState build() {
    // 在初始化后立即开始加载数据
    Future.microtask(() => _fetchRecords());
    
    // 返回一个初始的 / 表示正在加载的状态
    return CashManagementState(isLoading: true);
  }

  // fetch:获取的中文意思
  // 核心数据获取方法(私有)
  // 这是所有数据请求的统一入口,封装了加载状态管理 / 错误处理 / 数据更新
  Future<void> _fetchRecords({bool isRefresh = false}) async {
    // 如果不是刷新操作, 则将页面设置为加载中状态
    if (!isRefresh) {
      state = state.copyWith(isLoading: true, hasError: false);
    } else {
      state = state.copyWith(isRefreshing: true, hasError: false);
    }

    try {
      // 从ref 中读取数据仓库的实例
      final repo = ref.read(cashManagementRepositoryProvider);

      // 调用仓库方法，传入当前状态中的所有筛选和分页参数
      final result = await repo.getFilteredRecords(
        page: state.currentPage,
        rowsPerPage: state.rowsPerPage,
        startDate: state.startDate,
        endDate: state.endDate,
        type: state.selectedType,
        paymentMethod: state.selectedPaymentMethod
      );

      // 根据返回的总记录数计算总页数
      final totalPages = (result.totalCount / state.rowsPerPage).ceil();

      // 更新状态,传入获取到的新数据
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        cashRecords: result.records,
        totalRecords: result.totalCount,
        totalPages: max(1, totalPages), // 确保总页数至少为1
      );
    } catch (error, stackTrace) {
      // 如果发生错误，记录错误并更新状态以显示错误信息
      print("Error cash records:$error\n$stackTrace");
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        hasError: true,
        errorMessage: "数据加载失败,请稍后重试"
      );
    }
  }

  /// 切换页面
  void changePage(int newPage) {
    // 如果页码没有变化，则不执行任何操作
    if (newPage == state.currentPage) return;
    // 更新当前页码，并重新获取数据
    state = state.copyWith(currentPage: newPage);
    _fetchRecords();
  }

  /// 下拉刷新
  void refresh() {
    _fetchRecords(isRefresh: true);
  }

  // == 修改开始：统一方法名 ==
  /// 设置日期范围筛选
  void setDateRange(DateTime? start, DateTime? end) {
    // 更新日期范围，重置到第一页，并重新获取数据
    state = state.copyWith(startDate: start, endDate: end, currentPage: 1);
    _fetchRecords();
  }

  /// 设置交易类型筛选
  void setFilterType(String type) {
    // 更新交易类型，重置到第一页，并重新获取数据
    state = state.copyWith(selectedType: type, currentPage: 1);
    _fetchRecords();
  }

  /// 设置付款方式筛选
  void setFilterPaymentMethod(String method) {
    // 更新付款方式，重置到第一页，并重新获取数据
    state = state.copyWith(selectedPaymentMethod: method, currentPage: 1);
    _fetchRecords();
  }
  // == 修改结束 ==

  /// 添加新的收付款记录
  Future<void> addCashRecord({
    required String type,           // 'pay_in' 或 'pay_out'
    required String paymentMethod,  // 付款方式
    required double amount,         // 金额
    required String note,           // 备注
  }) async {
    // 设置添加记录的加载状态
    state = state.copyWith(isAddingRecord: true, hasError: false);
    
    try {
      // 计算实际金额（收款为正，付款为负）
      final actualAmount = type == 'pay_in' ? amount.abs() : -amount.abs();
      
      // 创建新记录
      final newRecord = CashRecord(
        type: type,
        paymentMethod: paymentMethod,
        amount: actualAmount,
        note: note.isEmpty ? '' : note,
        dateTime: DateTime.now(),
        operator: '当前用户', // 实际应用中应从用户会话获取
      );
      
      // 调用仓库保存记录
      final repo = ref.read(cashManagementRepositoryProvider);
      await repo.addCashRecord(newRecord);
      
      // 刷新数据以显示新记录
      await _fetchRecords();
      
      // 清除添加记录的加载状态
      state = state.copyWith(isAddingRecord: false);
      
    } catch (error, stackTrace) {
      print("Error adding cash record: $error\n$stackTrace");
      // 清除加载状态并设置错误信息
      state = state.copyWith(
        isAddingRecord: false,
        hasError: true,
        errorMessage: "添加记录失败，请重试"
      );
    }
  }
}