// 文件路径: lib/features/more/data/cash_management_repository.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kpos/features/more/domain/cash_management_record.dart';

/// 一个封装了分页查询结果的数据结构。
/// 它不仅包含当前页的数据列表，还包含了符合筛选条件的总记录数。
class PaginatedCashRecords {
  final List<CashRecord> records;
  final int totalCount;

  PaginatedCashRecords({required this.records, required this.totalCount});
}

/// 现金管理数据仓库
/// 这是我们应用中所有现金管理数据的唯一、可靠来源。
/// 它抽象了数据获取的细节，业务逻辑层（Notifier）无需关心数据是来自网络、数据库还是内存。
class CashManagementRepository {
  CashManagementRepository() {
    _generateMockData();
  }

  // 模拟的后端数据库，存储了所有的现金记录。
  late final List<CashRecord> _allRecords;

  /// 生成一个包含1000条随机记录的模拟数据集。
  void _generateMockData() {
    _allRecords = List.generate(
      1000,
      (index) => CashRecord.random(id: 'record_$index'),
    );
    // 按日期降序排序，模拟真实场景
    _allRecords.sort((a, b) => b.dateTime.compareTo(a.dateTime));
  }

  /// 模拟从服务器获取经过筛选和分页的数据。
  /// 这是一个异步方法，模拟了真实的网络延迟。
  Future<PaginatedCashRecords> getFilteredRecords({
    int page = 1,
    int rowsPerPage = 10,
    DateTime? startDate,
    DateTime? endDate,
    String? type, // '全部类型', '收款', '付款'
    String? paymentMethod, // '全部付款方式', '现金', ...
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 1. 应用筛选条件
    var filtered = _allRecords.where((record) {
      // 日期筛选
      if (startDate != null && record.dateTime.isBefore(startDate)) return false;
      // 注意：endDate 通常指一天的结束，所以要包含当天
      if (endDate != null && record.dateTime.isAfter(endDate.add(const Duration(days: 1)))) return false;

      // 类型筛选
      if (type != null && type != '全部类型') {
        final recordType = record.amount >= 0 ? '收款' : '付款';
        if (recordType != type) return false;
      }

      // 付款方式筛选
      if (paymentMethod != null && paymentMethod != '全部付款方式') {
        if (record.paymentMethod != paymentMethod) return false;
      }

      return true;
    }).toList();

    // 2. 获取筛选后的总数
    final totalCount = filtered.length;

    // 3. 应用分页
    final startIndex = (page - 1) * rowsPerPage;
    if (startIndex >= totalCount) {
      // 如果请求的页码超出范围，返回空列表
      return PaginatedCashRecords(records: [], totalCount: totalCount);
    }
    final endIndex = (startIndex + rowsPerPage > totalCount) ? totalCount : (startIndex + rowsPerPage);
    final paginatedRecords = filtered.sublist(startIndex, endIndex);

    return PaginatedCashRecords(records: paginatedRecords, totalCount: totalCount);
  }

  /// 添加新的现金记录
  /// 模拟向服务器提交新记录的操作
  Future<void> addCashRecord(CashRecord record) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));
    // 将新记录添加到列表头部（最新的记录在前面）
    _allRecords.insert(0, record);
    // 可以在这里模拟一些业务验证逻辑
    // 比如检查金额是否合理、操作员权限等
  }

}

/// 为数据仓库创建一个 Provider。
/// 这使得我们可以在应用的其他地方（特别是 Notifier 中）安全地获取到
/// CashManagementRepository 的单例。
final cashManagementRepositoryProvider = Provider<CashManagementRepository>((ref) {
  return CashManagementRepository();
});