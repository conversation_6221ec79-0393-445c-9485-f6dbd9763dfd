package com.kaifupos.app

import android.content.pm.ActivityInfo
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity

class MainActivity: FlutterActivity(){
    override fun onCreate(savedInstanceState: Bundle?) {
        // 在 super.onCreate 之前设置方向
        setOrientationBasedOnDevice()
        super.onCreate(savedInstanceState)
    }

    private fun setOrientationBasedOnDevice() {
        // 判断是否为平板（基于最小宽度 600dp）
        val isTablet = resources.configuration.smallestScreenWidthDp >= 600
        requestedOrientation = if (isTablet) {
            ActivityInfo.SCREEN_ORIENTATION_NOSENSOR  // 平板强制横屏
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT // 手机强制竖屏
        }
    }
}
