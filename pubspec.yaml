name: kpos
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_layout_grid: 2.0.7
  drift: ^2.23.1
  drift_sqflite: ^2.0.1
  path_provider: ^2.1.5
  path: ^1.9.0
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  uuid: ^4.5.1
  shelf: ^1.4.1
  shelf_router: ^1.1.4
  network_info_plus: ^6.1.3
  dio: ^5.8.0+1
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  shelf_web_socket: ^2.0.1
  tdesign_flutter: ^0.1.9
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.4
  go_router: ^15.1.2
  flutter_localizations:
    sdk: flutter
  intl: any
  easy_rich_text: ^2.2.0
  webview_flutter: ^4.10.0
  easy_refresh: ^3.4.0
  lottie: ^3.2.0
  flutter_svg: ^2.0.17
  window_manager: ^0.4.2
  device_info_plus: ^11.3.0
  flutter_form_builder: ^9.5.0
  user_interaction_detector: ^0.0.8
  connectivity_plus: ^6.1.4
  extended_tabs: ^5.0.0
  syncfusion_flutter_datepicker: ^29.1.38
  oktoast: ^3.4.0
  stomp_dart_client: ^2.1.3
  cached_network_image: ^3.4.1
  kpay_component_v2:
    git:
      url: https://gitlab.kpay-group.com/kpay/hongkong/frontend/flutter_pub/kpay-component-v2.git
      ref: v1.0.1
  terminal_serial_number:
    git:
      url: https://gitlab.kpay-group.com/kpay/hongkong/frontend/flutter_pub/terminal_serial_number.git
      ref: main
  win32_registry: ^1.1.5
  flutter_rust_bridge: ^2.11.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

flutter_gen:
  output: lib/assets/
  integrations:
    flutter_svg: true
    lottie: true


dev_dependencies:
  flutter_test:
    sdk: flutter
  drift_dev: ^2.23.1
  #先留一下 build_runner: ^2.4.11
  build_runner: ^2.4.15
  riverpod_generator: ^2.6.3
  riverpod_lint: ^2.6.3
  freezed: ^2.5.8
  json_serializable: ^6.9.5
  go_router_builder: ^3.0.0
  flutter_gen_runner: ^5.10.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

dependency_overrides:
  analyzer: 7.3.0
  analyzer_plugin: 0.12.0
  custom_lint_visitor: 1.0.0+7.3.0
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/lottie/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
